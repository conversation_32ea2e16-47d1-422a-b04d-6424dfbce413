<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="keywords"
      content="MineAdmin基于 Hyperf + Vue3 + CompositionAPI + Typescript + Vite + Element plus 的后台开源免费管理系统"
    />
    <meta
      name="description"
      content="MineAdmin基于 Hyperf + Vue3 + CompositionAPI + Typescript + Vite + Element plus 的后台开源免费管理系统"
    />
    <title>%VITE_APP_TITLE%</title>
  </head>
  <style>
    * {
      margin: 0; padding: 0;
    }
    html, body, #app {
      height: 100%;
    }
    .app-loading {
      background: #181818;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    .app-name__loader {
      line-height: 5em; font-weight: bold; color: #efefef; font-size: 20px;
    }
    .app-name__loader::before {
      content: 'MineAdmin';
    }
    .app-animate__loader {
      color: #fff;
      width: 6px;
      aspect-ratio: 1;
      border-radius: 50%;
      animation:
        animateLoading-1 .75s infinite linear alternate,
        animateLoading-2 1.5s  infinite linear;
    }
    @keyframes animateLoading-1 {
      0%, 20% {box-shadow:30px 0 0 3px, 10px 0 0 3px, -10px 0 0 3px,-30px 0 0 3px}
      60%, 100% {box-shadow:12px 0 0 3px, 14px 0 0 6px, -14px 0 0 6px,-12px 0 0 3px}
    }

    @keyframes animateLoading-2 {
      0%, 25% {transform: rotate(0)}
      50%,100% {transform: rotate(.5turn)}
    }

    .app-text__loader {
      font-size: 14px; color: #aaa; line-height: 7em;
    }
    .app-text__loader::before {
      content: '再等等，拼命载入资源中'
    }
  </style>
  <body>
    <div id="app">
      <div class="app-loading">
        <div class="app-name__loader"></div>
        <div class="app-animate__loader"></div>
        <div class="app-text__loader"></div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
