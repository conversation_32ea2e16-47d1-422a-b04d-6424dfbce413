<template>
  <div class="hydro-work-order-query">
    <!-- 面包屑导航 -->
    <Breadcrumb 
      :show-back-button="true"
      parent-path="/hydrosense/maintenance"
      parent-title="维保中心"
    />
    
    <!-- 查询表单 -->
    <div class="hydro-query-section">
      <h2 class="hydro-section-title">工单查询</h2>
      
      <div class="hydro-query-form">
        <div class="hydro-form-row">
          <div class="hydro-form-item">
            <el-date-picker
              v-model="queryForm.startDate"
              type="date"
              placeholder="开始时间"
              style="width: 100%"
            />
          </div>
          
          <div class="hydro-form-item">
            <el-date-picker
              v-model="queryForm.endDate"
              type="date"
              placeholder="结束时间"
              style="width: 100%"
            />
          </div>
          
          <div class="hydro-form-item">
            <el-select
              v-model="queryForm.type"
              placeholder="维保类型"
              style="width: 100%"
              clearable
            >
              <el-option label="例行巡检" value="routine-inspection" />
              <el-option label="设备保养" value="equipment-maintenance" />
              <el-option label="设备更换" value="equipment-replacement" />
            </el-select>
          </div>
          
          <div class="hydro-form-item">
            <el-select
              v-model="queryForm.person"
              placeholder="人员姓名"
              style="width: 100%"
              clearable
            >
              <el-option label="王某某" value="王某某" />
              <el-option label="李某某" value="李某某" />
              <el-option label="张某某" value="张某某" />
            </el-select>
          </div>
          
          <div class="hydro-form-actions">
            <el-button type="primary" @click="handleQuery" :loading="queryLoading">
              查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 工单列表 -->
    <div class="hydro-list-section">
      <h2 class="hydro-section-title">工单列表</h2>
      
      <el-table
        :data="workOrderList"
        border
        style="width: 100%"
        v-loading="tableLoading"
      >
        <el-table-column
          prop="orderNumber"
          label="工单编号"
          min-width="25%"
          align="center"
        />
        
        <el-table-column
          prop="date"
          label="日期"
          min-width="25%"
          align="center"
        />
        
        <el-table-column
          prop="type"
          label="维保类型"
          min-width="15%"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.typeValue)">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="operator"
          label="操作人员"
          min-width="15%"
          align="center"
        />
        
        <el-table-column
          prop="status"
          label="状态"
          min-width="10%"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          min-width="20%"
          align="center"
          v-if="!isMobile"
        >
          <template #default="scope">
            <el-button
              type="info"
              size="small"
              @click="viewDetail(scope.row)"
              style="background: #63c7d6; color: #fff; border: none; margin-right: 10px;"
            >
              工单详情
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteOrder(scope.row)"
              style="background: #e74c3c; color: #fff; border: none;"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 移动端操作按钮 -->
      <div v-if="isMobile && selectedRow" class="hydro-mobile-actions">
        <el-button type="info" @click="viewDetail(selectedRow)">查看详情</el-button>
        <el-button type="danger" @click="deleteOrder(selectedRow)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/modules/hydrosense/components/Breadcrumb.vue'

defineOptions({
  name: 'HydroSenseWorkOrderQuery'
})

// 响应式数据
const queryForm = reactive({
  startDate: '',
  endDate: '',
  type: '',
  person: ''
})

const queryLoading = ref(false)
const tableLoading = ref(false)
const isMobile = ref(false)
const selectedRow = ref(null)

// 工单列表数据
const workOrderList = ref([
  {
    orderNumber: 'LXXJ20250601',
    date: '2025年06月01日',
    type: '例行巡检',
    typeValue: 'routine-inspection',
    operator: '王某某',
    status: 'completed'
  },
  {
    orderNumber: 'SBBY20250602',
    date: '2025年06月02日',
    type: '设备保养',
    typeValue: 'equipment-maintenance',
    operator: '王某某',
    status: 'completed'
  },
  {
    orderNumber: 'SBGH20250603',
    date: '2025年06月03日',
    type: '设备更换',
    typeValue: 'equipment-replacement',
    operator: '王某某',
    status: 'pending'
  }
])

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 获取类型标签样式
const getTypeTagType = (type: string) => {
  const typeMap = {
    'routine-inspection': 'success',
    'equipment-maintenance': 'warning',
    'equipment-replacement': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  const statusMap = {
    'completed': 'success',
    'pending': 'warning',
    'processing': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'completed': '已完成',
    'pending': '待处理',
    'processing': '处理中'
  }
  return statusMap[status] || '未知'
}

// 查询工单
const handleQuery = async () => {
  try {
    queryLoading.value = true
    tableLoading.value = true
    
    // 模拟API查询
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该根据查询条件过滤数据
    console.log('查询条件:', queryForm)
    
    ElMessage.success('查询完成')
    
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    queryLoading.value = false
    tableLoading.value = false
  }
}

// 重置查询表单
const handleReset = () => {
  queryForm.startDate = ''
  queryForm.endDate = ''
  queryForm.type = ''
  queryForm.person = ''
  
  ElMessage.success('查询条件已重置')
}

// 查看工单详情
const viewDetail = (row: any) => {
  const routeMap = {
    'routine-inspection': '/hydrosense/maintenance/routine-inspection',
    'equipment-maintenance': '/hydrosense/maintenance/equipment-maintenance',
    'equipment-replacement': '/hydrosense/maintenance/equipment-replacement'
  }
  
  const route = routeMap[row.typeValue]
  if (route) {
    // 这里可以传递工单ID作为查询参数
    navigateTo(`${route}?id=${row.orderNumber}&mode=view`)
  } else {
    ElMessage.warning('未知的维保类型')
  }
}

// 删除工单
const deleteOrder = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除工单 ${row.orderNumber} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟删除操作
    const index = workOrderList.value.findIndex(item => item.orderNumber === row.orderNumber)
    if (index > -1) {
      workOrderList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-work-order-query {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-query-section,
.hydro-list-section {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
  margin-bottom: $hydro-spacing-xl;
}

.hydro-section-title {
  color: $hydro-primary;
  font-weight: 900;
  margin-bottom: $hydro-spacing-lg;
  font-size: 1.25rem;
}

.hydro-query-form {
  .hydro-form-row {
    display: flex;
    gap: $hydro-spacing-lg;
    align-items: flex-end;
    flex-wrap: wrap;
  }
  
  .hydro-form-item {
    flex: 1;
    min-width: 200px;
  }
  
  .hydro-form-actions {
    display: flex;
    gap: $hydro-spacing-sm;
  }
}

.hydro-mobile-actions {
  display: flex;
  gap: $hydro-spacing-md;
  justify-content: center;
  margin-top: $hydro-spacing-lg;
  padding-top: $hydro-spacing-lg;
  border-top: 1px solid $hydro-border;
}

// Element Plus 表格样式覆盖
:deep(.el-table) {
  font-size: 14px;
  
  .cell {
    padding: 8px;
  }
}

// 响应式设计
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-work-order-query {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-query-section,
  .hydro-list-section {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-query-form {
    .hydro-form-row {
      flex-direction: column;
      gap: $hydro-spacing-md;
    }
    
    .hydro-form-item {
      min-width: 100%;
    }
    
    .hydro-form-actions {
      justify-content: center;
    }
  }
  
  :deep(.el-table) {
    .cell {
      white-space: nowrap;
      padding: 8px;
    }
  }
}
</style>
