<template>
  <div class="hydro-photo-upload">
    <div class="hydro-photo-grid">
      <div 
        v-for="(photo, index) in photos" 
        :key="index"
        class="hydro-photo-item"
        @click="selectPhoto(index)"
      >
        <img v-if="photo" :src="photo" alt="上传的照片" />
        <el-icon v-else><Plus /></el-icon>
      </div>
      <div 
        v-if="photos.length < maxPhotos"
        class="hydro-photo-item"
        @click="addPhoto"
      >
        <el-icon><Plus /></el-icon>
      </div>
    </div>
    
    <el-input
      v-model="notes"
      type="textarea"
      :rows="4"
      placeholder="请输入备注信息..."
      class="hydro-photo-textarea"
      @input="updateNotes"
    />
    
    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'

interface Props {
  modelValue?: string[]
  notes?: string
  maxPhotos?: number
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'update:notes', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  notes: '',
  maxPhotos: 4
})

const emit = defineEmits<Emits>()

const fileInputRef = ref<HTMLInputElement>()
const photos = ref<string[]>([...props.modelValue])
const notes = ref(props.notes)
const currentPhotoIndex = ref(-1)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  photos.value = [...newVal]
}, { deep: true })

watch(() => props.notes, (newVal) => {
  notes.value = newVal
})

// 添加照片
const addPhoto = () => {
  currentPhotoIndex.value = -1
  fileInputRef.value?.click()
}

// 选择已有照片进行替换
const selectPhoto = (index: number) => {
  currentPhotoIndex.value = index
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  Array.from(files).forEach((file, fileIndex) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        
        if (currentPhotoIndex.value >= 0) {
          // 替换现有照片
          photos.value[currentPhotoIndex.value] = result
        } else {
          // 添加新照片
          if (photos.value.length < props.maxPhotos) {
            photos.value.push(result)
          }
        }
        
        // 发送更新事件
        emit('update:modelValue', [...photos.value])
      }
      reader.readAsDataURL(file)
    }
  })
  
  // 清空文件输入
  target.value = ''
}

// 更新备注
const updateNotes = (value: string) => {
  emit('update:notes', value)
}

// 删除照片
const removePhoto = (index: number) => {
  photos.value.splice(index, 1)
  emit('update:modelValue', [...photos.value])
}

// 暴露删除方法给父组件
defineExpose({
  removePhoto
})
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-photo-upload {
  display: flex;
  gap: $hydro-spacing-md;
  margin-bottom: $hydro-spacing-md;
  align-items: flex-start;

  .hydro-photo-grid {
    display: flex;
    gap: $hydro-spacing-md;
    flex-wrap: wrap;

    .hydro-photo-item {
      width: 100px;
      height: 100px;
      border: 2px dashed $hydro-primary-light;
      border-radius: $hydro-radius-md;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 2rem;
      color: $hydro-primary-light;
      background: $hydro-primary-bg;
      transition: all 0.2s;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: $hydro-primary;
        color: $hydro-primary;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: $hydro-radius-sm;
      }
    }
  }

  .hydro-photo-textarea {
    flex: 1;
    min-height: 100px;

    :deep(.el-textarea__inner) {
      resize: vertical;
      padding: $hydro-spacing-sm $hydro-spacing-md;
      border: 1px solid $hydro-border;
      border-radius: $hydro-radius-md;
      font-size: 1rem;
      min-height: 100px;
    }
  }

  @media (max-width: $hydro-breakpoint-sm) {
    flex-direction: column;

    .hydro-photo-grid {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $hydro-spacing-md;
      justify-content: center;

      .hydro-photo-item {
        width: 100%;
        height: 100px;
      }
    }

    .hydro-photo-textarea {
      margin-top: $hydro-spacing-md;
      width: 100%;
    }
  }
}
</style>
