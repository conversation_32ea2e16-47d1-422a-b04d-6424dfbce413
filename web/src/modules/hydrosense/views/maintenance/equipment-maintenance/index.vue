<template>
  <div class="hydro-equipment-maintenance">
    <!-- 面包屑导航 -->
    <Breadcrumb 
      :show-back-button="true"
      parent-path="/hydrosense/maintenance"
      parent-title="维保中心"
    />
    
    <!-- 表单内容 -->
    <div class="hydro-maintenance-form">
      <div class="hydro-maintenance-steps">
        <!-- STEP1: 风机设备保养 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP1：风机设备保养</h3>
          
          <PhotoUpload
            v-model="step1Data.photos"
            v-model:notes="step1Data.notes"
            :max-photos="4"
          />
          
          <div class="hydro-maintenance-checks">
            <el-checkbox v-model="step1Data.checks.fanSurface">清理风机表面和进气口</el-checkbox>
            <el-checkbox v-model="step1Data.checks.airFilter">清洁空气滤清器</el-checkbox>
            <el-checkbox v-model="step1Data.checks.beltTension">检查三角带张紧度</el-checkbox>
            <el-checkbox v-model="step1Data.checks.safetyValve">检查安全阀</el-checkbox>
            <el-checkbox v-model="step1Data.checks.electricalCheck">电机电气线路检查</el-checkbox>
          </div>
        </div>
        
        <!-- STEP2: 水泵设备保养 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP2：水泵设备保养</h3>
          
          <PhotoUpload
            v-model="step2Data.photos"
            v-model:notes="step2Data.notes"
            :max-photos="4"
          />
          
          <div class="hydro-maintenance-checks">
            <el-checkbox v-model="step2Data.checks.pumpA">污水提升泵A</el-checkbox>
            <el-checkbox v-model="step2Data.checks.pumpB">污水提升泵B</el-checkbox>
            <el-checkbox v-model="step2Data.checks.drainPumpA">排水泵A</el-checkbox>
            <el-checkbox v-model="step2Data.checks.drainPumpB">排水泵B</el-checkbox>
            <el-checkbox v-model="step2Data.checks.refluxPump">回流泵</el-checkbox>
            <el-checkbox v-model="step2Data.checks.floatSwitch">浮球开关保养</el-checkbox>
          </div>
        </div>
        
        <!-- STEP3: 加药设备保养 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP3：加药设备保养</h3>
          
          <PhotoUpload
            v-model="step3Data.photos"
            v-model:notes="step3Data.notes"
            :max-photos="4"
          />
          
          <div class="hydro-maintenance-checks">
            <el-checkbox v-model="step3Data.checks.generator">二氧化氯发生器清洁</el-checkbox>
            <el-checkbox v-model="step3Data.checks.contactPool">接触消毒池清洁</el-checkbox>
            <el-checkbox v-model="step3Data.checks.dosingPump">加药泵清洁</el-checkbox>
            <el-checkbox v-model="step3Data.checks.testPump">水质检测提升泵清洁</el-checkbox>
          </div>
        </div>
        
        <!-- STEP4: 保养总结 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP4：保养总结</h3>
          
          <el-input
            v-model="summaryText"
            type="textarea"
            :rows="4"
            placeholder="输入保养总结"
            class="hydro-summary-textarea"
          />
        </div>
      </div>
      
      <!-- 表单底部 -->
      <div class="hydro-form-footer">
        <div class="hydro-form-row">
          <div class="hydro-form-item">
            <span class="hydro-form-label">保养日期：</span>
            <el-date-picker
              v-model="maintenanceDate"
              type="date"
              placeholder="请选择日期"
              style="width: 200px"
            />
          </div>
          
          <div class="hydro-form-item">
            <span class="hydro-form-label">保养人员：</span>
            <el-input
              v-model="maintainer"
              placeholder="请输入保养人员"
              style="width: 200px"
            />
          </div>
        </div>
        
        <div class="hydro-form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            提交保养报告
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/modules/hydrosense/components/Breadcrumb.vue'
import PhotoUpload from '../components/PhotoUpload.vue'

defineOptions({
  name: 'HydroSenseEquipmentMaintenance'
})

// 响应式数据
const maintenanceDate = ref(new Date())
const maintainer = ref('')
const submitLoading = ref(false)
const summaryText = ref('')

// 步骤数据
const step1Data = reactive({
  photos: [],
  notes: '',
  checks: {
    fanSurface: true,
    airFilter: true,
    beltTension: true,
    safetyValve: true,
    electricalCheck: true
  }
})

const step2Data = reactive({
  photos: [],
  notes: '',
  checks: {
    pumpA: true,
    pumpB: true,
    drainPumpA: true,
    drainPumpB: true,
    refluxPump: true,
    floatSwitch: true
  }
})

const step3Data = reactive({
  photos: [],
  notes: '',
  checks: {
    generator: true,
    contactPool: true,
    dosingPump: true,
    testPump: true
  }
})

// 重置表单
const handleReset = () => {
  // 重置步骤1
  step1Data.photos = []
  step1Data.notes = ''
  Object.keys(step1Data.checks).forEach(key => {
    step1Data.checks[key] = true
  })
  
  // 重置步骤2
  step2Data.photos = []
  step2Data.notes = ''
  Object.keys(step2Data.checks).forEach(key => {
    step2Data.checks[key] = true
  })
  
  // 重置步骤3
  step3Data.photos = []
  step3Data.notes = ''
  Object.keys(step3Data.checks).forEach(key => {
    step3Data.checks[key] = true
  })
  
  // 重置其他字段
  summaryText.value = ''
  maintenanceDate.value = new Date()
  maintainer.value = ''
  
  ElMessage.success('表单已重置')
}

// 提交表单
const handleSubmit = async () => {
  // 验证必填项
  if (!maintainer.value.trim()) {
    ElMessage.error('请输入保养人员')
    return
  }
  
  if (!maintenanceDate.value) {
    ElMessage.error('请选择保养日期')
    return
  }
  
  try {
    submitLoading.value = true
    
    // 构建提交数据
    const submitData = {
      maintenanceDate: maintenanceDate.value,
      maintainer: maintainer.value,
      step1: step1Data,
      step2: step2Data,
      step3: step3Data,
      summary: summaryText.value
    }
    
    console.log('提交保养数据:', submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('保养报告提交成功')
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-equipment-maintenance {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-maintenance-form {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
}

.hydro-maintenance-steps {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-xxl;
  margin-bottom: $hydro-spacing-xxl;
}

.hydro-step-item {
  min-width: 320px;
  
  .hydro-step-title {
    font-size: 1.15rem;
    font-weight: 900;
    color: $hydro-success;
    margin-bottom: $hydro-spacing-sm;
  }
}

.hydro-maintenance-checks {
  display: flex;
  flex-wrap: wrap;
  gap: $hydro-spacing-md;
  margin-top: $hydro-spacing-md;
  
  .el-checkbox {
    min-width: 260px;
    font-size: 1rem;
    color: $hydro-text;
    
    :deep(.el-checkbox__label) {
      color: $hydro-text;
    }
  }
}

.hydro-summary-textarea {
  width: 100%;
  
  :deep(.el-textarea__inner) {
    min-height: 70px;
    resize: vertical;
    padding: $hydro-spacing-sm $hydro-spacing-md;
    border: 1px solid $hydro-border;
    border-radius: $hydro-radius-md;
    font-size: 1rem;
  }
}

.hydro-form-footer {
  border-top: 1px solid $hydro-border;
  padding-top: $hydro-spacing-xl;
}

.hydro-form-row {
  display: flex;
  gap: $hydro-spacing-xl;
  margin-bottom: $hydro-spacing-xl;
  flex-wrap: wrap;
}

.hydro-form-item {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  
  .hydro-form-label {
    font-weight: 900;
    min-width: 90px;
    color: $hydro-text;
  }
}

.hydro-form-actions {
  display: flex;
  gap: $hydro-spacing-md;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-equipment-maintenance {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-maintenance-form {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-form-row {
    flex-direction: column;
    gap: $hydro-spacing-lg;
  }
  
  .hydro-form-actions {
    justify-content: center;
  }
  
  .hydro-maintenance-checks {
    .el-checkbox {
      min-width: 100%;
    }
  }
}
</style>
