<template>
  <div class="hydro-equipment-replacement">
    <!-- 面包屑导航 -->
    <Breadcrumb 
      :show-back-button="true"
      parent-path="/hydrosense/maintenance"
      parent-title="维保中心"
    />
    
    <!-- 表单内容 -->
    <div class="hydro-replacement-form">
      <div class="hydro-replacement-steps">
        <!-- STEP1: 旧设备拆除 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP1：旧设备拆除</h3>
          
          <div class="hydro-equipment-info">
            <div class="hydro-form-row">
              <div class="hydro-form-item">
                <span class="hydro-form-label">设备名称：</span>
                <el-input
                  v-model="step1Data.equipmentName"
                  placeholder="请输入设备名称"
                  style="width: 200px"
                />
              </div>
              
              <div class="hydro-form-item">
                <span class="hydro-form-label">设备型号：</span>
                <el-input
                  v-model="step1Data.equipmentModel"
                  placeholder="请输入设备型号"
                  style="width: 200px"
                />
              </div>
            </div>
            
            <div class="hydro-form-row">
              <div class="hydro-form-item">
                <span class="hydro-form-label">拆除原因：</span>
                <el-select
                  v-model="step1Data.removalReason"
                  placeholder="请选择拆除原因"
                  style="width: 200px"
                >
                  <el-option label="设备老化" value="aging" />
                  <el-option label="设备故障" value="malfunction" />
                  <el-option label="性能不足" value="insufficient" />
                  <el-option label="升级换代" value="upgrade" />
                  <el-option label="其他" value="other" />
                </el-select>
              </div>
            </div>
          </div>
          
          <PhotoUpload
            v-model="step1Data.photos"
            v-model:notes="step1Data.notes"
            :max-photos="4"
          />
        </div>
        
        <!-- STEP2: 新更换设备 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP2：新更换设备</h3>
          
          <div class="hydro-equipment-info">
            <div class="hydro-form-row">
              <div class="hydro-form-item">
                <span class="hydro-form-label">新设备名称：</span>
                <el-input
                  v-model="step2Data.equipmentName"
                  placeholder="请输入新设备名称"
                  style="width: 200px"
                />
              </div>
              
              <div class="hydro-form-item">
                <span class="hydro-form-label">新设备型号：</span>
                <el-input
                  v-model="step2Data.equipmentModel"
                  placeholder="请输入新设备型号"
                  style="width: 200px"
                />
              </div>
            </div>
            
            <div class="hydro-form-row">
              <div class="hydro-form-item">
                <span class="hydro-form-label">设备厂家：</span>
                <el-input
                  v-model="step2Data.manufacturer"
                  placeholder="请输入设备厂家"
                  style="width: 200px"
                />
              </div>
              
              <div class="hydro-form-item">
                <span class="hydro-form-label">安装日期：</span>
                <el-date-picker
                  v-model="step2Data.installDate"
                  type="date"
                  placeholder="请选择安装日期"
                  style="width: 200px"
                />
              </div>
            </div>
            
            <div class="hydro-form-row">
              <div class="hydro-form-item">
                <span class="hydro-form-label">保修期限：</span>
                <el-input
                  v-model="step2Data.warrantyPeriod"
                  placeholder="请输入保修期限"
                  style="width: 200px"
                />
              </div>
              
              <div class="hydro-form-item">
                <span class="hydro-form-label">设备状态：</span>
                <el-select
                  v-model="step2Data.status"
                  placeholder="请选择设备状态"
                  style="width: 200px"
                >
                  <el-option label="正常运行" value="normal" />
                  <el-option label="调试中" value="debugging" />
                  <el-option label="待验收" value="pending" />
                </el-select>
              </div>
            </div>
          </div>
          
          <PhotoUpload
            v-model="step2Data.photos"
            v-model:notes="step2Data.notes"
            :max-photos="4"
          />
        </div>
        
        <!-- STEP3: 更换总结 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP3：更换总结</h3>
          
          <el-input
            v-model="summaryText"
            type="textarea"
            :rows="4"
            placeholder="输入设备更换总结，包括更换过程、遇到的问题、解决方案等"
            class="hydro-summary-textarea"
          />
        </div>
      </div>
      
      <!-- 表单底部 -->
      <div class="hydro-form-footer">
        <div class="hydro-form-row">
          <div class="hydro-form-item">
            <span class="hydro-form-label">更换日期：</span>
            <el-date-picker
              v-model="replacementDate"
              type="date"
              placeholder="请选择日期"
              style="width: 200px"
            />
          </div>
          
          <div class="hydro-form-item">
            <span class="hydro-form-label">更换人员：</span>
            <el-input
              v-model="replacer"
              placeholder="请输入更换人员"
              style="width: 200px"
            />
          </div>
        </div>
        
        <div class="hydro-form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            提交更换报告
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/modules/hydrosense/components/Breadcrumb.vue'
import PhotoUpload from '../components/PhotoUpload.vue'

defineOptions({
  name: 'HydroSenseEquipmentReplacement'
})

// 响应式数据
const replacementDate = ref(new Date())
const replacer = ref('')
const submitLoading = ref(false)
const summaryText = ref('')

// 步骤数据
const step1Data = reactive({
  equipmentName: '',
  equipmentModel: '',
  removalReason: '',
  photos: [],
  notes: ''
})

const step2Data = reactive({
  equipmentName: '',
  equipmentModel: '',
  manufacturer: '',
  installDate: new Date(),
  warrantyPeriod: '',
  status: 'normal',
  photos: [],
  notes: ''
})

// 重置表单
const handleReset = () => {
  // 重置步骤1
  step1Data.equipmentName = ''
  step1Data.equipmentModel = ''
  step1Data.removalReason = ''
  step1Data.photos = []
  step1Data.notes = ''
  
  // 重置步骤2
  step2Data.equipmentName = ''
  step2Data.equipmentModel = ''
  step2Data.manufacturer = ''
  step2Data.installDate = new Date()
  step2Data.warrantyPeriod = ''
  step2Data.status = 'normal'
  step2Data.photos = []
  step2Data.notes = ''
  
  // 重置其他字段
  summaryText.value = ''
  replacementDate.value = new Date()
  replacer.value = ''
  
  ElMessage.success('表单已重置')
}

// 提交表单
const handleSubmit = async () => {
  // 验证必填项
  if (!replacer.value.trim()) {
    ElMessage.error('请输入更换人员')
    return
  }
  
  if (!replacementDate.value) {
    ElMessage.error('请选择更换日期')
    return
  }
  
  if (!step1Data.equipmentName.trim()) {
    ElMessage.error('请输入旧设备名称')
    return
  }
  
  if (!step2Data.equipmentName.trim()) {
    ElMessage.error('请输入新设备名称')
    return
  }
  
  try {
    submitLoading.value = true
    
    // 构建提交数据
    const submitData = {
      replacementDate: replacementDate.value,
      replacer: replacer.value,
      oldEquipment: step1Data,
      newEquipment: step2Data,
      summary: summaryText.value
    }
    
    console.log('提交更换数据:', submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('设备更换报告提交成功')
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-equipment-replacement {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-replacement-form {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
}

.hydro-replacement-steps {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-xxl;
  margin-bottom: $hydro-spacing-xxl;
}

.hydro-step-item {
  min-width: 320px;
  
  .hydro-step-title {
    font-size: 1.15rem;
    font-weight: 900;
    color: $hydro-success;
    margin-bottom: $hydro-spacing-sm;
  }
}

.hydro-equipment-info {
  margin-bottom: $hydro-spacing-lg;
  padding: $hydro-spacing-lg;
  background: $hydro-bg;
  border-radius: $hydro-radius-md;
}

.hydro-form-row {
  display: flex;
  gap: $hydro-spacing-xl;
  margin-bottom: $hydro-spacing-lg;
  flex-wrap: wrap;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.hydro-form-item {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  
  .hydro-form-label {
    font-weight: 700;
    min-width: 100px;
    color: $hydro-text;
  }
}

.hydro-summary-textarea {
  width: 100%;
  
  :deep(.el-textarea__inner) {
    min-height: 100px;
    resize: vertical;
    padding: $hydro-spacing-sm $hydro-spacing-md;
    border: 1px solid $hydro-border;
    border-radius: $hydro-radius-md;
    font-size: 1rem;
  }
}

.hydro-form-footer {
  border-top: 1px solid $hydro-border;
  padding-top: $hydro-spacing-xl;
  
  .hydro-form-row {
    margin-bottom: $hydro-spacing-xl;
  }
}

.hydro-form-actions {
  display: flex;
  gap: $hydro-spacing-md;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-equipment-replacement {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-replacement-form {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-form-row {
    flex-direction: column;
    gap: $hydro-spacing-lg;
  }
  
  .hydro-form-actions {
    justify-content: center;
  }
  
  .hydro-form-item {
    .hydro-form-label {
      min-width: 80px;
    }
  }
}
</style>
