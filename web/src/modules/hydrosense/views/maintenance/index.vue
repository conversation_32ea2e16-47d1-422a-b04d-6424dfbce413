<template>
  <div class="hydro-maintenance">
    <div class="hydro-maintenance-grid">
      <el-button 
        class="hydro-maintenance-btn hydro-routine-btn"
        size="large"
        @click="handleRoutineCheck"
      >
        <el-icon class="hydro-btn-icon"><DocumentChecked /></el-icon>
        例行巡检
      </el-button>
      
      <el-button 
        class="hydro-maintenance-btn hydro-equipment-btn"
        size="large"
        @click="handleMaintenance"
      >
        <el-icon class="hydro-btn-icon"><Tools /></el-icon>
        设备保养
      </el-button>
      
      <el-button 
        class="hydro-maintenance-btn hydro-replacement-btn"
        size="large"
        @click="handleEquipmentChange"
      >
        <el-icon class="hydro-btn-icon"><RefreshRight /></el-icon>
        设备更换
      </el-button>
      
      <el-button 
        class="hydro-maintenance-btn hydro-query-btn"
        size="large"
        @click="handleWorkOrderQuery"
      >
        <el-icon class="hydro-btn-icon"><Search /></el-icon>
        工单查询
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DocumentChecked, Tools, RefreshRight, Search } from '@element-plus/icons-vue'

defineOptions({
  name: 'HydroSenseMaintenance'
})

const router = useRouter()

// 例行巡检
const handleRoutineCheck = () => {
  router.push('/hydrosense/maintenance/routine-inspection')
}

// 设备保养
const handleMaintenance = () => {
  router.push('/hydrosense/maintenance/equipment-maintenance')
}

// 设备更换
const handleEquipmentChange = () => {
  router.push('/hydrosense/maintenance/equipment-replacement')
}

// 工单查询
const handleWorkOrderQuery = () => {
  router.push('/hydrosense/maintenance/work-order-query')
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-maintenance {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hydro-maintenance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $hydro-spacing-xl;
  max-width: 800px;
  width: 100%;
}

.hydro-maintenance-btn {
  min-height: 120px;
  font-size: 1.5rem;
  font-weight: 700;
  border-radius: $hydro-radius-lg;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $hydro-spacing-md;
  transition: all 0.3s ease;
  box-shadow: $hydro-shadow-md;
  border: none;
  
  .hydro-btn-icon {
    font-size: 2.5rem;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $hydro-shadow-lg;
  }
  
  &:active {
    transform: translateY(0);
  }
}

.hydro-routine-btn {
  background: $hydro-primary;
  color: $hydro-white;
  
  &:hover, &:focus {
    background: darken($hydro-primary, 10%);
    color: $hydro-white;
  }
}

.hydro-equipment-btn {
  background: $hydro-primary;
  color: $hydro-white;
  
  &:hover, &:focus {
    background: darken($hydro-primary, 10%);
    color: $hydro-white;
  }
}

.hydro-replacement-btn {
  background: $hydro-primary;
  color: $hydro-white;
  
  &:hover, &:focus {
    background: darken($hydro-primary, 10%);
    color: $hydro-white;
  }
}

.hydro-query-btn {
  background: #DE868F;
  color: $hydro-white;
  
  &:hover, &:focus {
    background: darken(#DE868F, 10%);
    color: $hydro-white;
  }
}

// 响应式设计
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-maintenance {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-maintenance-grid {
    grid-template-columns: 1fr;
    gap: $hydro-spacing-lg;
    max-width: 400px;
  }
  
  .hydro-maintenance-btn {
    min-height: 80px;
    font-size: 1.3rem;
    
    .hydro-btn-icon {
      font-size: 2rem;
    }
  }
}

@media (max-width: $hydro-breakpoint-xs) {
  .hydro-maintenance-btn {
    min-height: 70px;
    font-size: 1.2rem;
    
    .hydro-btn-icon {
      font-size: 1.8rem;
    }
  }
}
</style>
