<template>
  <div class="hydro-routine-inspection">
    <!-- 面包屑导航 -->
    <Breadcrumb 
      :show-back-button="true"
      parent-path="/hydrosense/maintenance"
      parent-title="维保中心"
    />
    
    <!-- 表单内容 -->
    <div class="hydro-inspection-form">
      <div class="hydro-inspection-steps">
        <!-- STEP1: 设备运行检查 -->
        <StepForm
          v-model="step1Data"
          step-title="STEP1：设备运行检查"
          :options="step1Options"
          :max-photos="4"
        />
        
        <!-- STEP2: 加药装置检查 -->
        <StepForm
          v-model="step2Data"
          step-title="STEP2：加药装置检查"
          :options="step2Options"
          :max-photos="4"
        />
        
        <!-- STEP3: 传感器检查 -->
        <StepForm
          v-model="step3Data"
          step-title="STEP3：传感器检查"
          :options="step3Options"
          :max-photos="4"
        />
        
        <!-- STEP4: 例行检查总结 -->
        <div class="hydro-step-item">
          <h3 class="hydro-step-title">STEP4：例行检查总结</h3>
          
          <div class="hydro-summary-checks">
            <el-checkbox v-model="summaryChecks.hygiene">环境卫生已打扫</el-checkbox>
            <el-checkbox v-model="summaryChecks.fireEquipment">消防设备设施已检查</el-checkbox>
          </div>
          
          <el-input
            v-model="summaryText"
            type="textarea"
            :rows="4"
            placeholder="输入例行检查总结"
            class="hydro-summary-textarea"
          />
        </div>
      </div>
      
      <!-- 表单底部 -->
      <div class="hydro-form-footer">
        <div class="hydro-form-row">
          <div class="hydro-form-item">
            <span class="hydro-form-label">巡检日期：</span>
            <el-date-picker
              v-model="inspectionDate"
              type="date"
              placeholder="请选择日期"
              style="width: 200px"
            />
          </div>
          
          <div class="hydro-form-item">
            <span class="hydro-form-label">巡检人员：</span>
            <el-input
              v-model="inspector"
              placeholder="请输入巡检人员"
              style="width: 200px"
            />
          </div>
        </div>
        
        <div class="hydro-form-actions">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            提交巡检报告
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/modules/hydrosense/components/Breadcrumb.vue'
import StepForm from '../components/StepForm.vue'

defineOptions({
  name: 'HydroSenseRoutineInspection'
})

// 响应式数据
const inspectionDate = ref(new Date())
const inspector = ref('')
const submitLoading = ref(false)

// 步骤数据
const step1Data = ref({
  selectedOptions: [],
  status: 'normal',
  photos: [],
  notes: ''
})

const step2Data = ref({
  selectedOptions: [],
  status: 'normal',
  photos: [],
  notes: ''
})

const step3Data = ref({
  selectedOptions: [],
  status: 'normal',
  photos: [],
  notes: ''
})

// 总结数据
const summaryChecks = reactive({
  hygiene: true,
  fireEquipment: true
})

const summaryText = ref('')

// 选项配置
const step1Options = [
  { label: '提升泵正常', value: 'pump1' },
  { label: '回流泵正常', value: 'pump2' },
  { label: '排水泵正常', value: 'pump3' },
  { label: '浮球开关正常', value: 'pump4' },
  { label: '曝气风机正常', value: 'pump5' }
]

const step2Options = [
  { label: '消毒药剂液位正常', value: 'med1' },
  { label: '加药泵工作正常', value: 'med2' },
  { label: '水质检测提升泵工作正常', value: 'med3' }
]

const step3Options = [
  { label: '流量传感器正常', value: 'sen1' },
  { label: 'PH传感器正常', value: 'sen2' },
  { label: '余氯传感器正常', value: 'sen3' },
  { label: '水温传感器正常', value: 'sen4' },
  { label: '集水池传感器正常', value: 'sen5' },
  { label: '环境氯气传感器正常', value: 'sen6' },
  { label: '环境温湿度传感器正常', value: 'sen7' },
  { label: '环境烟雾浓度传感器正常', value: 'sen8' },
  { label: '积水传感器正常', value: 'sen9' },
  { label: '药剂液位传感器正常', value: 'sen10' },
  { label: '视频监控正常', value: 'sen11' }
]

// 重置表单
const handleReset = () => {
  step1Data.value = { selectedOptions: [], status: 'normal', photos: [], notes: '' }
  step2Data.value = { selectedOptions: [], status: 'normal', photos: [], notes: '' }
  step3Data.value = { selectedOptions: [], status: 'normal', photos: [], notes: '' }
  summaryChecks.hygiene = true
  summaryChecks.fireEquipment = true
  summaryText.value = ''
  inspectionDate.value = new Date()
  inspector.value = ''
  
  ElMessage.success('表单已重置')
}

// 提交表单
const handleSubmit = async () => {
  // 验证必填项
  if (!inspector.value.trim()) {
    ElMessage.error('请输入巡检人员')
    return
  }
  
  if (!inspectionDate.value) {
    ElMessage.error('请选择巡检日期')
    return
  }
  
  try {
    submitLoading.value = true
    
    // 构建提交数据
    const submitData = {
      inspectionDate: inspectionDate.value,
      inspector: inspector.value,
      step1: step1Data.value,
      step2: step2Data.value,
      step3: step3Data.value,
      summary: {
        checks: summaryChecks,
        text: summaryText.value
      }
    }
    
    console.log('提交巡检数据:', submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('巡检报告提交成功')
    
    // 可以选择跳转到列表页面或重置表单
    // await navigateTo('/hydrosense/maintenance')
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-routine-inspection {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-inspection-form {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
}

.hydro-inspection-steps {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-xxl;
  margin-bottom: $hydro-spacing-xxl;
}

.hydro-step-item {
  min-width: 320px;
  
  .hydro-step-title {
    font-size: 1.15rem;
    font-weight: 900;
    color: $hydro-success;
    margin-bottom: $hydro-spacing-sm;
  }
}

.hydro-summary-checks {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-md;
  margin-bottom: $hydro-spacing-md;
  
  .el-checkbox {
    font-size: 1rem;
    color: $hydro-text;
  }
}

.hydro-summary-textarea {
  width: 100%;
  
  :deep(.el-textarea__inner) {
    min-height: 70px;
    resize: vertical;
    padding: $hydro-spacing-sm $hydro-spacing-md;
    border: 1px solid $hydro-border;
    border-radius: $hydro-radius-md;
    font-size: 1rem;
  }
}

.hydro-form-footer {
  border-top: 1px solid $hydro-border;
  padding-top: $hydro-spacing-xl;
}

.hydro-form-row {
  display: flex;
  gap: $hydro-spacing-xl;
  margin-bottom: $hydro-spacing-xl;
  flex-wrap: wrap;
}

.hydro-form-item {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  
  .hydro-form-label {
    font-weight: 900;
    min-width: 90px;
    color: $hydro-text;
  }
}

.hydro-form-actions {
  display: flex;
  gap: $hydro-spacing-md;
  justify-content: flex-end;
}

// 响应式设计
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-routine-inspection {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-inspection-form {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-form-row {
    flex-direction: column;
    gap: $hydro-spacing-lg;
  }
  
  .hydro-form-actions {
    justify-content: center;
  }
}
</style>
