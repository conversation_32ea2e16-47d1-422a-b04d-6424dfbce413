<template>
  <div class="hydro-login-container">
    <div class="hydro-main-container">
      <div class="hydro-main-left">
        <div class="hydro-main-welcome">Welcome sign in</div>
        <div class="hydro-main-title">某地社区卫生中心</div>
        <div class="hydro-main-subtitle">医疗污水强化一级处理系统监控平台</div>
      </div>
      <div class="hydro-main-right">
        <div class="hydro-login-box">
          <div class="hydro-login-title">系统登录</div>
          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" @keyup.enter="handleLogin">
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入账号"
                class="hydro-login-input"
                size="large"
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="hydro-login-input"
                size="large"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
                <template #suffix>
                  <el-icon style="cursor: pointer" @click="showPassword = !showPassword">
                    <View v-if="!showPassword" />
                    <Hide v-else />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <div class="hydro-login-remember">
                <el-checkbox v-model="rememberPassword">记住密码</el-checkbox>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                class="hydro-login-btn"
                size="large"
                :loading="loginLoading"
                @click="handleLogin"
              >
                登 录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="hydro-main-footer">
      <div class="hydro-footer-left">
        <img src="@/modules/hydrosense/assets/images/Logo.svg" alt="logo" class="hydro-footer-logo">
        <div class="hydro-footer-company">
          <div>装备 | 智水 | 云控</div>
          <div>江苏海德洛智能装备有限公司</div>
          <div>
            <a href="http://www.hydro.js.cn" target="_blank" rel="noopener" class="hydro-footer-link">
              http://www.hydro.js.cn
            </a>
          </div>
        </div>
      </div>
      <div class="hydro-footer-right">
        <img src="@/modules/hydrosense/assets/images/qrcode.png" alt="二维码" class="hydro-footer-qrcode">
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Lock, View, Hide } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({
  name: 'HydroSenseLogin'
})

// 响应式数据
const loginForm = reactive({
  username: '',
  password: ''
})

const showPassword = ref(false)
const rememberPassword = ref(false)
const loginLoading = ref(false)
const loginFormRef = ref<FormInstance>()

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loginLoading.value = true
    
    // TODO: 集成MineAdmin的登录逻辑
    // 这里应该调用MineAdmin的登录API
    console.log('登录信息:', loginForm)
    
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 登录成功后跳转到污水监控系统
    await navigateTo('/hydrosense/dashboard')
    
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查账号密码')
  } finally {
    loginLoading.value = false
  }
}

// 页面挂载时的处理
onMounted(() => {
  // 如果记住密码，从localStorage读取
  if (localStorage.getItem('hydro_remember_password') === 'true') {
    rememberPassword.value = true
    loginForm.username = localStorage.getItem('hydro_username') || ''
    loginForm.password = localStorage.getItem('hydro_password') || ''
  }
})

// 监听记住密码变化
watch(rememberPassword, (newVal) => {
  if (newVal) {
    localStorage.setItem('hydro_remember_password', 'true')
    localStorage.setItem('hydro_username', loginForm.username)
    localStorage.setItem('hydro_password', loginForm.password)
  } else {
    localStorage.removeItem('hydro_remember_password')
    localStorage.removeItem('hydro_username')
    localStorage.removeItem('hydro_password')
  }
})
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-login-container {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: $hydro-primary;
  min-height: 100vh;
  color: $hydro-white;
  display: flex;
  flex-direction: column;
}

.hydro-main-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 85vh;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hydro-main-left {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 5vw;
}

.hydro-main-welcome {
  font-size: 2.2rem;
  font-weight: 900;
  margin-bottom: 2.5rem;
  color: $hydro-white;
  opacity: 0.9;
}

.hydro-main-title {
  font-size: 3.2rem;
  font-weight: 900;
  margin-bottom: 1.2rem;
  color: $hydro-white;
  letter-spacing: 2px;
}

.hydro-main-subtitle {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: $hydro-white;
}

.hydro-main-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 380px;
}

.hydro-login-box {
  background: $hydro-white;
  border-radius: 10px;
  box-shadow: $hydro-shadow-lg;
  padding: 38px 36px 28px 36px;
  min-width: 340px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.hydro-login-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #222;
  text-align: center;
  margin-bottom: 28px;
  letter-spacing: 6px;
}

.hydro-login-input {
  margin-bottom: 18px;
}

.hydro-login-remember {
  margin-bottom: 18px;
  color: #666;
  font-size: 0.98rem;
}

.hydro-login-btn {
  margin-bottom: 18px;
  font-size: 1.1rem;
  letter-spacing: 4px;
  background: $hydro-primary !important;
  border: none !important;
  color: $hydro-white !important;
  width: 100%;

  &:hover, &:focus {
    background: darken($hydro-primary, 10%) !important;
    color: $hydro-white !important;
  }
}

.hydro-main-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  background: $hydro-white;
  height: 120px;
}

.hydro-footer-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.hydro-footer-logo {
  height: 2.5rem;
}

.hydro-footer-company {
  color: #333;
  opacity: 0.9;
  line-height: 1.5;
  font-size: 0.9rem;
  margin-left: -25px;
}

.hydro-footer-link {
  color: #333;
  text-decoration: none;
  opacity: 0.9;

  &:hover {
    opacity: 1;
  }
}

.hydro-footer-qrcode {
  height: 5.5rem !important;
  width: 5.5rem !important;
  object-fit: contain !important;
}

// 响应式设计
@media screen and (max-width: $hydro-breakpoint-sm) {
  .hydro-main-container {
    flex-direction: column;
    height: auto;
    padding: 2rem 1rem;
    gap: 2rem;
  }

  .hydro-main-left {
    padding-left: 0;
    align-items: center;
    text-align: center;
  }

  .hydro-main-welcome {
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
  }

  .hydro-main-title {
    font-size: 1.8rem;
    margin-bottom: 0.8rem;
  }

  .hydro-main-subtitle {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    line-height: 1.3;
  }

  .hydro-main-right {
    width: 100%;
    min-width: auto;
    margin-top: 15px;
  }

  .hydro-login-box {
    width: 100%;
    min-width: auto;
    padding: 2rem 1.5rem;
  }

  .hydro-login-title {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }

  .hydro-main-footer {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding: 0 1.5rem;
    height: 100px;
    background: $hydro-white;
  }

  .hydro-footer-left {
    flex-direction: row;
    gap: 1rem;
    align-items: center;
  }

  .hydro-footer-logo {
    height: 2rem;
  }

  .hydro-footer-company {
    font-size: 0.75rem;
    line-height: 1.4;
    margin-left: -5px;
  }

  .hydro-footer-qrcode {
    height: 4rem !important;
    width: 4rem !important;
    object-fit: contain !important;
  }
}
</style>
