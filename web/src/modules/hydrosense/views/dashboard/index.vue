<template>
  <div class="hydro-dashboard">
    <!-- 实时时间显示 -->
    <div class="hydro-dashboard-time">
      {{ currentTime }}
    </div>
    
    <!-- 数据卡片行 -->
    <div class="hydro-dashboard-row">
      <!-- 状态监控卡片 -->
      <div class="hydro-dashboard-card hydro-status-card">
        <div class="hydro-card-title">状态监控</div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">实时流量（L/h）</span>
          <div class="hydro-status-value">
            <span>{{ statusData.flowRate }}</span>
          </div>
        </div>
        
        <div class="hydro-status-indicators">
          <div class="hydro-indicator-item">
            <span class="hydro-indicator-label">氯气</span>
            <div class="hydro-indicator-status">
              <div class="hydro-indicator-dot" :class="statusData.chlorine"></div>
            </div>
          </div>
          <div class="hydro-indicator-item">
            <span class="hydro-indicator-label">烟感</span>
            <div class="hydro-indicator-status">
              <div class="hydro-indicator-dot" :class="statusData.smoke"></div>
            </div>
          </div>
        </div>
        
        <div class="hydro-card-title hydro-mt-lg">空气强排</div>
        <div class="hydro-air-controls">
          <el-button 
            :type="airExhaustStatus === 'off' ? 'primary' : 'default'"
            @click="toggleAirExhaust('off')"
          >
            关闭
          </el-button>
          <el-button 
            :type="airExhaustStatus === 'on' ? 'primary' : 'default'"
            @click="toggleAirExhaust('on')"
          >
            开启
          </el-button>
        </div>
      </div>
      
      <!-- 实时出水卡片 -->
      <div class="hydro-dashboard-card hydro-water-card">
        <div class="hydro-card-title">实时出水</div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">PH</span>
          <div class="hydro-status-value">
            <span>{{ waterData.ph }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">COD（mg/L）</span>
          <div class="hydro-status-value">
            <span>{{ waterData.cod }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">氨氮（mg/L）</span>
          <div class="hydro-status-value">
            <span>{{ waterData.ammonia }}</span>
          </div>
        </div>
      </div>
      
      <!-- 实时液位卡片 -->
      <div class="hydro-dashboard-card hydro-level-card">
        <div class="hydro-card-title">实时液位</div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">调节池（%）</span>
          <div class="hydro-status-value">
            <span>{{ levelData.adjustmentPool }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">接触池（%）</span>
          <div class="hydro-status-value">
            <span>{{ levelData.contactPool }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">清水池（%）</span>
          <div class="hydro-status-value">
            <span>{{ levelData.clearWaterPool }}</span>
          </div>
        </div>
      </div>
      
      <!-- 实时环境卡片 -->
      <div class="hydro-dashboard-card hydro-environment-card">
        <div class="hydro-card-title">实时环境</div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">温度（℃）</span>
          <div class="hydro-status-value">
            <span>{{ environmentData.temperature }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">湿度（%）</span>
          <div class="hydro-status-value">
            <span>{{ environmentData.humidity }}</span>
          </div>
        </div>
        
        <div class="hydro-status-item">
          <span class="hydro-status-label">噪音（dB）</span>
          <div class="hydro-status-value">
            <span>{{ environmentData.noise }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 监控摄像头行 -->
    <div class="hydro-monitor-row">
      <div class="hydro-monitor-item" v-for="camera in cameras" :key="camera.id">
        <img :src="camera.image" :alt="camera.name" class="hydro-monitor-img" />
        <div class="hydro-monitor-actions">
          <span @click="viewFullscreen(camera)">
            <el-icon><FullScreen /></el-icon>
            全屏查看
          </span>
          <span @click="downloadSnapshot(camera)">
            <el-icon><Download /></el-icon>
            下载快照
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FullScreen, Download } from '@element-plus/icons-vue'

defineOptions({
  name: 'HydroSenseDashboard'
})

// 响应式数据
const currentTime = ref('')
const airExhaustStatus = ref<'on' | 'off'>('off')

// 模拟数据
const statusData = reactive({
  flowRate: '0.6',
  chlorine: 'normal', // normal, warning, error
  smoke: 'normal'
})

const waterData = reactive({
  ph: '7.6',
  cod: '45',
  ammonia: '8.2'
})

const levelData = reactive({
  adjustmentPool: '75',
  contactPool: '68',
  clearWaterPool: '82'
})

const environmentData = reactive({
  temperature: '24.5',
  humidity: '65',
  noise: '42'
})

const cameras = ref([
  {
    id: 1,
    name: '摄像头1',
    image: new URL('@/modules/hydrosense/assets/images/cam1.jpg', import.meta.url).href
  },
  {
    id: 2,
    name: '摄像头2', 
    image: new URL('@/modules/hydrosense/assets/images/cam2.jpg', import.meta.url).href
  }
])

// 更新时间
const updateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[now.getDay()]
  
  const period = now.getHours() < 12 ? '上午' : '下午'
  const displayHours = now.getHours() > 12 ? now.getHours() - 12 : now.getHours()
  
  currentTime.value = `${year}年${month}月${day}日 ${period}${String(displayHours).padStart(2, '0')}点${minutes}分${seconds}秒 ${weekday}`
}

// 切换空气强排状态
const toggleAirExhaust = (status: 'on' | 'off') => {
  airExhaustStatus.value = status
  ElMessage.success(`空气强排已${status === 'on' ? '开启' : '关闭'}`)
}

// 全屏查看
const viewFullscreen = (camera: any) => {
  ElMessage.info(`全屏查看${camera.name}`)
}

// 下载快照
const downloadSnapshot = (camera: any) => {
  ElMessage.success(`${camera.name}快照下载中...`)
}

// 模拟数据更新
const updateData = () => {
  // 模拟实时数据变化
  statusData.flowRate = (Math.random() * 2 + 0.1).toFixed(1)
  waterData.ph = (Math.random() * 2 + 6.5).toFixed(1)
  waterData.cod = Math.floor(Math.random() * 20 + 40).toString()
  waterData.ammonia = (Math.random() * 5 + 5).toFixed(1)
  
  levelData.adjustmentPool = Math.floor(Math.random() * 30 + 60).toString()
  levelData.contactPool = Math.floor(Math.random() * 30 + 50).toString()
  levelData.clearWaterPool = Math.floor(Math.random() * 30 + 70).toString()
  
  environmentData.temperature = (Math.random() * 10 + 20).toFixed(1)
  environmentData.humidity = Math.floor(Math.random() * 20 + 55).toString()
  environmentData.noise = Math.floor(Math.random() * 10 + 35).toString()
}

// 生命周期
onMounted(() => {
  updateTime()
  updateData()
  
  // 每秒更新时间
  const timeInterval = setInterval(updateTime, 1000)
  
  // 每5秒更新数据
  const dataInterval = setInterval(updateData, 5000)
  
  onUnmounted(() => {
    clearInterval(timeInterval)
    clearInterval(dataInterval)
  })
})
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-dashboard {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-dashboard-time {
  font-size: 1.1rem;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  color: $hydro-text;
  margin-bottom: $hydro-spacing-xl;
  text-align: center;
  font-weight: 600;
}

.hydro-dashboard-row {
  display: flex;
  gap: $hydro-spacing-lg;
  margin-bottom: $hydro-spacing-xl;
  flex-wrap: wrap;
}

.hydro-dashboard-card {
  background: #f3f5f7;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  flex: 1;
  min-width: 220px;
  box-shadow: $hydro-shadow-sm;
  
  .hydro-card-title {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: $hydro-spacing-md;
    color: $hydro-primary;
  }
}

.hydro-status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: $hydro-spacing-md;
  
  .hydro-status-label {
    color: #888;
    font-size: 1.1rem;
    font-weight: 700;
  }
  
  .hydro-status-value {
    background: $hydro-white;
    border: 2px solid #ddd;
    border-radius: 6px;
    height: 50px;
    width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    span {
      font-size: 1.76rem;
      font-weight: 900;
      color: $hydro-primary;
    }
  }
}

.hydro-status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $hydro-spacing-md;
  margin-top: $hydro-spacing-lg;
  
  .hydro-indicator-item {
    display: flex;
    align-items: center;
    
    .hydro-indicator-label {
      color: #888;
      font-size: 1.1rem;
      font-weight: 700;
      margin-right: $hydro-spacing-sm;
    }
    
    .hydro-indicator-status {
      background: $hydro-white;
      border: 2px solid #ddd;
      border-radius: 6px;
      width: 75px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .hydro-indicator-dot {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      
      &.normal {
        background: #A2EF4D;
      }
      
      &.warning {
        background: #FFA500;
      }
      
      &.error {
        background: #FF4444;
      }
    }
  }
}

.hydro-air-controls {
  display: flex;
  gap: $hydro-spacing-md;
  width: 100%;
  
  .el-button {
    flex: 1;
    font-weight: 700;
  }
}

.hydro-monitor-row {
  display: flex;
  gap: $hydro-spacing-xl;
  flex-wrap: wrap;
  
  .hydro-monitor-item {
    display: flex;
    gap: $hydro-spacing-lg;
    align-items: center;
    background: $hydro-white;
    padding: $hydro-spacing-lg;
    border-radius: $hydro-radius-lg;
    box-shadow: $hydro-shadow-sm;
    flex: 1;
    min-width: 400px;
  }
  
  .hydro-monitor-img {
    width: 320px;
    height: 180px;
    object-fit: cover;
    border-radius: $hydro-radius-md;
  }
  
  .hydro-monitor-actions {
    display: flex;
    flex-direction: column;
    gap: $hydro-spacing-sm;
    align-items: flex-end;
    
    span {
      font-size: 0.98rem;
      color: #666;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: $hydro-spacing-xs;
      transition: color 0.2s;
      
      &:hover {
        color: $hydro-primary;
      }
    }
  }
}

// 响应式设计
@media (max-width: $hydro-breakpoint-lg) {
  .hydro-dashboard-row {
    flex-direction: column;
  }
  
  .hydro-dashboard-card {
    min-width: 100%;
  }
  
  .hydro-monitor-row {
    flex-direction: column;
  }
  
  .hydro-monitor-item {
    min-width: 100%;
  }
}

@media (max-width: $hydro-breakpoint-sm) {
  .hydro-dashboard {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-monitor-item {
    flex-direction: column;
    text-align: center;
  }
  
  .hydro-monitor-img {
    width: 272px;
    height: 153px;
  }
  
  .hydro-monitor-actions {
    align-items: center;
  }
}
</style>
