<template>
  <div class="hydro-datacenter">
    <!-- 污水日处理量图表 -->
    <div class="hydro-chart-section">
      <div class="hydro-chart-header">
        <h2 class="hydro-section-title">污水日处理量</h2>
        <div class="hydro-total-info">
          总处理量（m³）：
          <span class="hydro-total-value">{{ totalProcessed }}</span>
        </div>
      </div>
      <div ref="chartContainer" class="hydro-chart-container"></div>
    </div>
    
    <!-- 数据卡片区域 -->
    <div class="hydro-cards-section">
      <!-- PH日均值卡片 -->
      <div class="hydro-data-card">
        <div class="hydro-card-title">PH（日均值）</div>
        <div class="hydro-card-content">
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去24小时</span>
            <span class="hydro-card-value">{{ phData.hours24 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去7天</span>
            <span class="hydro-card-value">{{ phData.days7 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去30天</span>
            <span class="hydro-card-value">{{ phData.days30 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去60天</span>
            <span class="hydro-card-value">{{ phData.days60 }}</span>
          </div>
        </div>
      </div>
      
      <!-- 余氯日均值卡片 -->
      <div class="hydro-data-card">
        <div class="hydro-card-title">余氯（日均值）</div>
        <div class="hydro-card-content">
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去24小时</span>
            <span class="hydro-card-value">{{ chlorineData.hours24 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去7天</span>
            <span class="hydro-card-value">{{ chlorineData.days7 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去30天</span>
            <span class="hydro-card-value">{{ chlorineData.days30 }}</span>
          </div>
          <div class="hydro-card-row">
            <span class="hydro-card-label">过去60天</span>
            <span class="hydro-card-value">{{ chlorineData.days60 }}</span>
          </div>
        </div>
      </div>
      
      <!-- 数据查询卡片 -->
      <div class="hydro-data-card">
        <div class="hydro-card-title">数据查询</div>
        <div class="hydro-card-content">
          <div class="hydro-query-section">
            <label class="hydro-query-label">自定义时间段</label>
            <div class="hydro-date-row">
              <el-date-picker
                v-model="queryForm.startDate"
                type="date"
                placeholder="开始时间"
                class="hydro-date-picker"
              />
              <el-date-picker
                v-model="queryForm.endDate"
                type="date"
                placeholder="结束时间"
                class="hydro-date-picker"
              />
            </div>
            <el-select
              v-model="queryForm.type"
              placeholder="请选择查询项"
              class="hydro-query-select"
            >
              <el-option label="PH" value="ph" />
              <el-option label="余氯" value="chlorine" />
              <el-option label="处理量" value="amount" />
            </el-select>
            <el-button
              type="primary"
              class="hydro-query-btn"
              @click="handleCustomQuery"
              :loading="queryLoading"
            >
              查询
            </el-button>
          </div>
          
          <div class="hydro-query-section">
            <label class="hydro-query-label">当日数据查询</label>
            <el-button
              type="primary"
              class="hydro-query-btn"
              @click="handleTodayQuery"
              :loading="todayQueryLoading"
            >
              立即查询
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 报表打印卡片 -->
      <div class="hydro-data-card">
        <div class="hydro-card-title">报表打印</div>
        <div class="hydro-card-content">
          <div class="hydro-print-section">
            <label class="hydro-print-label">自定义时间段</label>
            <div class="hydro-date-row">
              <el-date-picker
                v-model="printForm.startDate"
                type="date"
                placeholder="开始时间"
                class="hydro-date-picker"
              />
              <el-date-picker
                v-model="printForm.endDate"
                type="date"
                placeholder="结束时间"
                class="hydro-date-picker"
              />
            </div>
            <el-button
              type="danger"
              class="hydro-print-btn"
              @click="handlePrint"
              :loading="printLoading"
            >
              打印
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'

defineOptions({
  name: 'HydroSenseDataCenter'
})

// 响应式数据
const chartContainer = ref<HTMLElement>()
const totalProcessed = ref('4786.45')
const queryLoading = ref(false)
const todayQueryLoading = ref(false)
const printLoading = ref(false)

// PH数据
const phData = reactive({
  hours24: '7.2',
  days7: '7.1',
  days30: '7.3',
  days60: '7.2'
})

// 余氯数据
const chlorineData = reactive({
  hours24: '0.8',
  days7: '0.9',
  days30: '0.7',
  days60: '0.8'
})

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  type: ''
})

// 打印表单
const printForm = reactive({
  startDate: '',
  endDate: ''
})

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  const chart = echarts.init(chartContainer.value)
  
  // 模拟30天的数据
  const dates = []
  const values = []
  const today = new Date()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
    values.push((Math.random() * 50 + 100).toFixed(1))
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '处理量(m³)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '日处理量',
        type: 'line',
        smooth: true,
        data: values,
        itemStyle: {
          color: '#4ec6c8'
        },
        lineStyle: {
          color: '#4ec6c8'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(78, 198, 200, 0.3)' },
              { offset: 1, color: 'rgba(78, 198, 200, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  const resizeChart = () => {
    chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  onUnmounted(() => {
    window.removeEventListener('resize', resizeChart)
    chart.dispose()
  })
}

// 自定义查询
const handleCustomQuery = async () => {
  if (!queryForm.startDate || !queryForm.endDate || !queryForm.type) {
    ElMessage.error('请填写完整的查询条件')
    return
  }
  
  try {
    queryLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('查询条件:', queryForm)
    ElMessage.success('查询完成')
    
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    queryLoading.value = false
  }
}

// 当日数据查询
const handleTodayQuery = async () => {
  try {
    todayQueryLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    ElMessage.success('当日数据查询完成')
    
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    todayQueryLoading.value = false
  }
}

// 打印报表
const handlePrint = async () => {
  if (!printForm.startDate || !printForm.endDate) {
    ElMessage.error('请选择打印时间段')
    return
  }
  
  try {
    printLoading.value = true
    
    // 模拟生成报表
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('打印时间段:', printForm)
    ElMessage.success('报表生成完成，正在打印...')
    
    // 这里可以调用浏览器打印功能
    // window.print()
    
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败，请重试')
  } finally {
    printLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-datacenter {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-chart-section {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
  margin-bottom: $hydro-spacing-xl;
}

.hydro-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $hydro-spacing-lg;
  flex-wrap: wrap;
  gap: $hydro-spacing-md;
}

.hydro-section-title {
  color: $hydro-primary;
  font-weight: 900;
  font-size: 1.25rem;
  margin: 0;
}

.hydro-total-info {
  font-size: 1rem;
  color: $hydro-text;
  
  .hydro-total-value {
    font-size: 1.5rem;
    font-weight: 900;
    color: $hydro-primary;
    margin-left: $hydro-spacing-xs;
  }
}

.hydro-chart-container {
  width: 100%;
  height: 260px;
}

.hydro-cards-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $hydro-spacing-xl;
}

.hydro-data-card {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
}

.hydro-card-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: $hydro-text;
  margin-bottom: $hydro-spacing-lg;
  text-align: center;
}

.hydro-card-content {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-md;
}

.hydro-card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $hydro-spacing-sm 0;
  border-bottom: 1px solid $hydro-border-light;
  
  &:last-child {
    border-bottom: none;
  }
}

.hydro-card-label {
  font-size: 0.9rem;
  color: $hydro-text-secondary;
}

.hydro-card-value {
  font-size: 1rem;
  font-weight: 600;
  color: $hydro-primary;
}

.hydro-query-section,
.hydro-print-section {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-sm;
  padding: $hydro-spacing-md;
  background: $hydro-bg;
  border-radius: $hydro-radius-md;
  
  &:not(:last-child) {
    margin-bottom: $hydro-spacing-md;
  }
}

.hydro-query-label,
.hydro-print-label {
  font-size: 0.9rem;
  color: $hydro-text-secondary;
  text-align: center;
}

.hydro-date-row {
  display: flex;
  gap: $hydro-spacing-xs;
}

.hydro-date-picker {
  flex: 1;
}

.hydro-query-select {
  width: 100%;
}

.hydro-query-btn,
.hydro-print-btn {
  width: 100%;
  margin-top: $hydro-spacing-sm;
}

.hydro-query-btn {
  background: $hydro-primary;
  border-color: $hydro-primary;
}

.hydro-print-btn {
  background: #DE868F;
  border-color: #DE868F;
}

// 响应式设计
@media (max-width: $hydro-breakpoint-md) {
  .hydro-cards-section {
    grid-template-columns: 1fr;
  }
  
  .hydro-chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .hydro-total-info {
    align-self: center;
  }
}

@media (max-width: $hydro-breakpoint-sm) {
  .hydro-datacenter {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-chart-section,
  .hydro-data-card {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-chart-container {
    height: 200px;
  }
  
  .hydro-date-row {
    flex-direction: column;
  }
}
</style>
