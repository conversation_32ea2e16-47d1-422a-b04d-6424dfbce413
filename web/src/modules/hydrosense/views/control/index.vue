<template>
  <div class="hydro-control">
    <div class="hydro-control-sections">
      <!-- 空气强排控制 -->
      <div class="hydro-control-section">
        <h2 class="hydro-section-title">空气强排控制</h2>
        
        <div class="hydro-control-card">
          <div class="hydro-control-rules">
            <!-- 规则1: 氨气浓度控制 -->
            <div class="hydro-control-rule">
              <div class="hydro-rule-condition">
                <span class="hydro-rule-symbol">如</span>
                <div class="hydro-rule-controls">
                  <span class="hydro-rule-label">氨气浓度</span>
                  <el-select v-model="airControl.ammoniaOperator" class="hydro-rule-select">
                    <el-option label=">" value="gt" />
                    <el-option label="<" value="lt" />
                    <el-option label="=" value="eq" />
                  </el-select>
                  <el-select v-model="airControl.ammoniaValue" class="hydro-rule-select">
                    <el-option label="0.1" value="0.1" />
                    <el-option label="0.2" value="0.2" />
                    <el-option label="0.3" value="0.3" />
                  </el-select>
                  <el-select v-model="airControl.ammoniaUnit" class="hydro-rule-select">
                    <el-option label="PPM" value="PPM" />
                  </el-select>
                  <el-switch v-model="airControl.ammoniaEnabled" />
                </div>
              </div>
            </div>
            
            <!-- 规则2: 持续时间控制 -->
            <div class="hydro-control-rule">
              <div class="hydro-rule-condition">
                <span class="hydro-rule-symbol">如</span>
                <div class="hydro-rule-controls">
                  <span class="hydro-rule-label">持续时间</span>
                  <el-select v-model="airControl.durationValue" class="hydro-rule-select">
                    <el-option label="1" value="1" />
                    <el-option label="2" value="2" />
                    <el-option label="3" value="3" />
                  </el-select>
                  <el-select v-model="airControl.durationUnit" class="hydro-rule-select">
                    <el-option label="小时" value="hour" />
                    <el-option label="分钟" value="minute" />
                  </el-select>
                  <el-switch v-model="airControl.durationEnabled" />
                </div>
              </div>
            </div>
            
            <!-- 规则3: 执行动作 -->
            <div class="hydro-control-rule">
              <div class="hydro-rule-condition">
                <span class="hydro-rule-symbol">则</span>
                <div class="hydro-rule-controls">
                  <span class="hydro-rule-label">强排时间</span>
                  <el-select v-model="airControl.actionValue" class="hydro-rule-select">
                    <el-option label="1" value="1" />
                    <el-option label="2" value="2" />
                    <el-option label="3" value="3" />
                  </el-select>
                  <el-select v-model="airControl.actionUnit" class="hydro-rule-select">
                    <el-option label="小时" value="hour" />
                    <el-option label="分钟" value="minute" />
                  </el-select>
                  <el-switch v-model="airControl.actionEnabled" />
                </div>
              </div>
            </div>
          </div>
          
          <div class="hydro-control-actions">
            <el-button type="primary" @click="saveAirControl" :loading="airControlLoading">
              保存设置
            </el-button>
            <el-button @click="resetAirControl">重置</el-button>
          </div>
        </div>
      </div>
      
      <!-- 加药泵控制 -->
      <div class="hydro-control-section">
        <h2 class="hydro-section-title">加药泵控制</h2>
        
        <div class="hydro-control-card">
          <div class="hydro-pump-methods">
            <!-- 恒定小时加药法 -->
            <div class="hydro-pump-method">
              <h3 class="hydro-method-title">恒定小时加药法</h3>
              
              <div class="hydro-method-controls">
                <div class="hydro-control-row">
                  <span class="hydro-control-label">处理量：</span>
                  <div class="hydro-control-inputs">
                    <el-input
                      v-model="pumpControl.constant.amount"
                      placeholder="请输入"
                      class="hydro-control-input"
                    />
                    <el-select v-model="pumpControl.constant.amountUnit" class="hydro-unit-select">
                      <el-option label="L/d" value="L/d" />
                    </el-select>
                  </div>
                </div>
                
                <div class="hydro-control-row">
                  <span class="hydro-control-label">投加准值：</span>
                  <div class="hydro-control-inputs">
                    <el-input
                      v-model="pumpControl.constant.dosage"
                      placeholder="请输入"
                      class="hydro-control-input"
                    />
                    <el-select v-model="pumpControl.constant.dosageUnit" class="hydro-unit-select">
                      <el-option label="g/t" value="g/t" />
                    </el-select>
                  </div>
                </div>
                
                <div class="hydro-control-row">
                  <span class="hydro-control-label">溶质浓度：</span>
                  <div class="hydro-control-inputs">
                    <el-input
                      v-model="pumpControl.constant.concentration"
                      placeholder="请输入"
                      class="hydro-control-input"
                    />
                    <el-select v-model="pumpControl.constant.concentrationUnit" class="hydro-unit-select">
                      <el-option label="PPM" value="PPM" />
                    </el-select>
                  </div>
                </div>
                
                <el-button
                  type="primary"
                  @click="executeConstantMethod"
                  :loading="constantMethodLoading"
                  class="hydro-execute-btn"
                >
                  执行恒定小时加药法
                </el-button>
              </div>
            </div>
            
            <!-- 智能加药法 -->
            <div class="hydro-pump-method">
              <h3 class="hydro-method-title">智能加药法</h3>
              
              <div class="hydro-method-controls">
                <div class="hydro-control-row">
                  <span class="hydro-control-label">启用智能加药：</span>
                  <div class="hydro-control-inputs">
                    <el-switch v-model="pumpControl.smart.enabled" />
                  </div>
                </div>
                
                <div class="hydro-control-row">
                  <span class="hydro-control-label">余氯准值：</span>
                  <div class="hydro-control-inputs">
                    <el-select v-model="pumpControl.smart.chlorineRange" class="hydro-control-select">
                      <el-option label="2-8" value="2-8" />
                      <el-option label="3-10" value="3-10" />
                    </el-select>
                    <el-select v-model="pumpControl.smart.chlorineUnit" class="hydro-unit-select">
                      <el-option label="mg/L" value="mg/L" />
                    </el-select>
                  </div>
                </div>
                
                <div class="hydro-control-row">
                  <span class="hydro-control-label">检测频率：</span>
                  <div class="hydro-control-inputs">
                    <el-select v-model="pumpControl.smart.frequency" class="hydro-control-select">
                      <el-option label="每5分钟" value="5min" />
                      <el-option label="每10分钟" value="10min" />
                      <el-option label="每15分钟" value="15min" />
                    </el-select>
                  </div>
                </div>
                
                <el-button
                  type="success"
                  @click="executeSmartMethod"
                  :loading="smartMethodLoading"
                  class="hydro-execute-btn"
                >
                  执行智能加药法
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="hydro-control-actions">
            <el-button type="primary" @click="savePumpControl" :loading="pumpControlLoading">
              保存设置
            </el-button>
            <el-button @click="resetPumpControl">重置</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'HydroSenseControl'
})

// 空气强排控制数据
const airControl = reactive({
  ammoniaOperator: 'gt',
  ammoniaValue: '0.1',
  ammoniaUnit: 'PPM',
  ammoniaEnabled: true,
  durationValue: '1',
  durationUnit: 'hour',
  durationEnabled: true,
  actionValue: '2',
  actionUnit: 'hour',
  actionEnabled: true
})

// 加药泵控制数据
const pumpControl = reactive({
  constant: {
    amount: '',
    amountUnit: 'L/d',
    dosage: '',
    dosageUnit: 'g/t',
    concentration: '',
    concentrationUnit: 'PPM'
  },
  smart: {
    enabled: false,
    chlorineRange: '2-8',
    chlorineUnit: 'mg/L',
    frequency: '5min'
  }
})

// 加载状态
const airControlLoading = ref(false)
const pumpControlLoading = ref(false)
const constantMethodLoading = ref(false)
const smartMethodLoading = ref(false)

// 保存空气强排控制设置
const saveAirControl = async () => {
  try {
    airControlLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('空气强排控制设置:', airControl)
    ElMessage.success('空气强排控制设置已保存')
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    airControlLoading.value = false
  }
}

// 重置空气强排控制
const resetAirControl = () => {
  airControl.ammoniaOperator = 'gt'
  airControl.ammoniaValue = '0.1'
  airControl.ammoniaUnit = 'PPM'
  airControl.ammoniaEnabled = true
  airControl.durationValue = '1'
  airControl.durationUnit = 'hour'
  airControl.durationEnabled = true
  airControl.actionValue = '2'
  airControl.actionUnit = 'hour'
  airControl.actionEnabled = true
  
  ElMessage.success('空气强排控制已重置')
}

// 执行恒定小时加药法
const executeConstantMethod = async () => {
  if (!pumpControl.constant.amount || !pumpControl.constant.dosage || !pumpControl.constant.concentration) {
    ElMessage.error('请填写完整的参数')
    return
  }
  
  try {
    constantMethodLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('执行恒定小时加药法:', pumpControl.constant)
    ElMessage.success('恒定小时加药法已启动')
    
  } catch (error) {
    console.error('执行失败:', error)
    ElMessage.error('执行失败，请重试')
  } finally {
    constantMethodLoading.value = false
  }
}

// 执行智能加药法
const executeSmartMethod = async () => {
  if (!pumpControl.smart.enabled) {
    ElMessage.error('请先启用智能加药')
    return
  }
  
  try {
    smartMethodLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('执行智能加药法:', pumpControl.smart)
    ElMessage.success('智能加药法已启动')
    
  } catch (error) {
    console.error('执行失败:', error)
    ElMessage.error('执行失败，请重试')
  } finally {
    smartMethodLoading.value = false
  }
}

// 保存加药泵控制设置
const savePumpControl = async () => {
  try {
    pumpControlLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('加药泵控制设置:', pumpControl)
    ElMessage.success('加药泵控制设置已保存')
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    pumpControlLoading.value = false
  }
}

// 重置加药泵控制
const resetPumpControl = () => {
  pumpControl.constant.amount = ''
  pumpControl.constant.amountUnit = 'L/d'
  pumpControl.constant.dosage = ''
  pumpControl.constant.dosageUnit = 'g/t'
  pumpControl.constant.concentration = ''
  pumpControl.constant.concentrationUnit = 'PPM'
  
  pumpControl.smart.enabled = false
  pumpControl.smart.chlorineRange = '2-8'
  pumpControl.smart.chlorineUnit = 'mg/L'
  pumpControl.smart.frequency = '5min'
  
  ElMessage.success('加药泵控制已重置')
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-control {
  padding: $hydro-spacing-xl;
  background: $hydro-bg;
  min-height: calc(100vh - 60px);
}

.hydro-control-sections {
  display: flex;
  gap: $hydro-spacing-xl;
  flex-wrap: wrap;
}

.hydro-control-section {
  flex: 1;
  min-width: 400px;
}

.hydro-section-title {
  color: $hydro-primary;
  font-weight: 900;
  margin-bottom: $hydro-spacing-lg;
  font-size: 1.25rem;
  text-align: left;
}

.hydro-control-card {
  background: $hydro-white;
  border-radius: $hydro-radius-lg;
  padding: $hydro-spacing-xl;
  box-shadow: $hydro-shadow-sm;
}

.hydro-control-rules {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-lg;
  margin-bottom: $hydro-spacing-xl;
}

.hydro-control-rule {
  padding: $hydro-spacing-md;
  background: $hydro-bg;
  border-radius: $hydro-radius-md;
}

.hydro-rule-condition {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-md;
  flex-wrap: wrap;
}

.hydro-rule-symbol {
  font-weight: 700;
  color: $hydro-primary;
  min-width: 30px;
}

.hydro-rule-controls {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  flex-wrap: wrap;
  flex: 1;
}

.hydro-rule-label {
  font-size: 0.9rem;
  color: $hydro-text-secondary;
  min-width: 80px;
}

.hydro-rule-select {
  width: 80px;
}

.hydro-pump-methods {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-xl;
  margin-bottom: $hydro-spacing-xl;
}

.hydro-pump-method {
  padding: $hydro-spacing-lg;
  background: $hydro-bg;
  border-radius: $hydro-radius-md;
}

.hydro-method-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: $hydro-text;
  margin-bottom: $hydro-spacing-lg;
}

.hydro-method-controls {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-md;
}

.hydro-control-row {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-md;
  flex-wrap: wrap;
}

.hydro-control-label {
  font-size: 0.9rem;
  color: $hydro-text-secondary;
  min-width: 100px;
}

.hydro-control-inputs {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  flex: 1;
}

.hydro-control-input {
  width: 120px;
}

.hydro-control-select {
  width: 120px;
}

.hydro-unit-select {
  width: 80px;
}

.hydro-execute-btn {
  margin-top: $hydro-spacing-md;
  width: 100%;
}

.hydro-control-actions {
  display: flex;
  gap: $hydro-spacing-md;
  justify-content: flex-end;
  border-top: 1px solid $hydro-border;
  padding-top: $hydro-spacing-lg;
}

// 响应式设计
@media (max-width: $hydro-breakpoint-lg) {
  .hydro-control-sections {
    flex-direction: column;
  }
  
  .hydro-control-section {
    min-width: 100%;
  }
}

@media (max-width: $hydro-breakpoint-sm) {
  .hydro-control {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-control-card,
  .hydro-pump-method {
    padding: $hydro-spacing-lg;
  }
  
  .hydro-rule-condition,
  .hydro-control-row {
    flex-direction: column;
    align-items: flex-start;
    gap: $hydro-spacing-sm;
  }
  
  .hydro-rule-controls,
  .hydro-control-inputs {
    width: 100%;
    justify-content: flex-start;
  }
  
  .hydro-control-actions {
    justify-content: center;
  }
}
</style>
