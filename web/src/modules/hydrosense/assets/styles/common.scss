/**
 * HydroSense 污水监控系统通用样式
 */

@import './variables.scss';

// 通用容器样式
.hydro-container {
  background: $hydro-white;
  padding: $hydro-spacing-xl;
  border-radius: $hydro-radius-lg;
  box-shadow: $hydro-shadow-md;
  max-width: 1200px;
  margin: auto;
}

// 页面标题样式
.hydro-page-title {
  font-size: 2rem;
  font-weight: 900;
  color: $hydro-text;
  margin-bottom: $hydro-spacing-lg;
}

.hydro-page-subtitle {
  font-size: 1.25rem;
  font-weight: 900;
  color: $hydro-text;
  margin-bottom: $hydro-spacing-sm;
}

// 步骤标题样式
.hydro-step-title {
  font-size: 1.15rem;
  font-weight: 900;
  color: $hydro-success;
  margin-bottom: $hydro-spacing-sm;
}

// 按钮样式
.hydro-btn {
  font-weight: 900;
  font-size: 1rem;
  padding: 0.4rem 1.2rem;
  border: none;
  border-radius: $hydro-radius-sm;
  cursor: pointer;
  transition: all 0.2s;

  &.hydro-btn-primary {
    background: $hydro-primary;
    color: $hydro-white;

    &:hover {
      background: darken($hydro-primary, 10%);
    }
  }

  &.hydro-btn-secondary {
    background: $hydro-secondary;
    color: $hydro-white;

    &:hover {
      background: darken($hydro-secondary, 10%);
    }
  }

  &.hydro-btn-large {
    min-height: 70px;
    font-size: 1.4rem;
    padding: $hydro-spacing-md $hydro-spacing-xl;
  }
}

// 维保按钮网格
.hydro-maintenance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $hydro-spacing-xl;
  padding: $hydro-spacing-xl;

  @media (max-width: $hydro-breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: $hydro-spacing-lg;
  }
}

// 表单样式
.hydro-form {
  .hydro-form-row {
    display: flex;
    gap: $hydro-spacing-lg;
    margin-bottom: $hydro-spacing-lg;
    align-items: center;
    flex-wrap: wrap;

    @media (max-width: $hydro-breakpoint-sm) {
      flex-direction: column;
      align-items: stretch;
      gap: $hydro-spacing-md;
    }
  }

  .hydro-form-label {
    font-weight: 900;
    min-width: 90px;
    display: inline-block;
  }
}

// 照片上传区域
.hydro-photo-upload {
  display: flex;
  gap: $hydro-spacing-md;
  margin-bottom: $hydro-spacing-md;
  align-items: center;

  .hydro-photo-grid {
    display: flex;
    gap: $hydro-spacing-md;
    flex-wrap: wrap;

    .hydro-photo-item {
      width: 100px;
      height: 100px;
      border: 2px dashed $hydro-primary-light;
      border-radius: $hydro-radius-md;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 2rem;
      color: $hydro-primary-light;
      background: $hydro-primary-bg;
      transition: all 0.2s;

      &:hover {
        border-color: $hydro-primary;
        color: $hydro-primary;
      }
    }
  }

  .hydro-photo-textarea {
    flex: 1;
    height: 100px;
    resize: vertical;
    padding: $hydro-spacing-sm $hydro-spacing-md;
    border: 1px solid $hydro-border;
    border-radius: $hydro-radius-md;
    font-size: 1rem;
  }

  @media (max-width: $hydro-breakpoint-sm) {
    flex-direction: column;

    .hydro-photo-grid {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $hydro-spacing-md;
      justify-content: center;

      .hydro-photo-item {
        width: 100%;
        height: 100px;
      }
    }

    .hydro-photo-textarea {
      margin-top: $hydro-spacing-md;
      width: 100%;
    }
  }
}

// 分步表单
.hydro-step-form {
  display: flex;
  flex-direction: column;
  gap: $hydro-spacing-xl;

  .hydro-step-item {
    min-width: 320px;
  }
}

// 选项组
.hydro-options {
  display: flex;
  gap: $hydro-spacing-xxl;
  margin-bottom: $hydro-spacing-md;

  .hydro-option-group {
    display: flex;
    flex-direction: column;
    gap: $hydro-spacing-md;

    label {
      display: flex;
      align-items: center;
      gap: $hydro-spacing-sm;
      cursor: pointer;
      color: $hydro-text;

      input[type="radio"],
      input[type="checkbox"] {
        margin-right: $hydro-spacing-xs;
      }
    }
  }

  @media (max-width: $hydro-breakpoint-sm) {
    flex-direction: column;
    gap: $hydro-spacing-lg;
  }
}

// 面包屑导航
.hydro-breadcrumb {
  display: flex;
  align-items: center;
  gap: $hydro-spacing-sm;
  margin-bottom: $hydro-spacing-lg;
  font-size: 0.9rem;
  color: $hydro-text-light;

  .hydro-breadcrumb-item {
    &:not(:last-child)::after {
      content: '>';
      margin-left: $hydro-spacing-sm;
      color: $hydro-text-lighter;
    }

    &.active {
      color: $hydro-primary;
      font-weight: 600;
    }

    a {
      color: $hydro-text-light;
      text-decoration: none;
      transition: color 0.2s;

      &:hover {
        color: $hydro-primary;
      }
    }
  }
}

// 工具类
.hydro-text-center { text-align: center; }
.hydro-text-right { text-align: right; }
.hydro-mb-0 { margin-bottom: 0; }
.hydro-mb-sm { margin-bottom: $hydro-spacing-sm; }
.hydro-mb-md { margin-bottom: $hydro-spacing-md; }
.hydro-mb-lg { margin-bottom: $hydro-spacing-lg; }
.hydro-mt-lg { margin-top: $hydro-spacing-lg; }
.hydro-p-lg { padding: $hydro-spacing-lg; }

// 响应式工具类
@media (max-width: $hydro-breakpoint-sm) {
  .hydro-mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
  }

  .hydro-mobile-stack {
    flex-direction: column !important;
    align-items: stretch !important;
  }
}
