<template>
  <div class="hydro-breadcrumb">
    <template v-for="(item, index) in breadcrumbItems" :key="index">
      <span class="hydro-breadcrumb-item" :class="{ active: index === breadcrumbItems.length - 1 }">
        <router-link v-if="item.path && index !== breadcrumbItems.length - 1" :to="item.path">
          {{ item.title }}
        </router-link>
        <span v-else>{{ item.title }}</span>
      </span>
    </template>
    
    <!-- 返回按钮 -->
    <el-button 
      v-if="showBackButton && parentPath" 
      type="primary" 
      size="small" 
      class="hydro-back-btn"
      @click="goBack"
    >
      <el-icon><ArrowLeft /></el-icon>
      返回{{ parentTitle }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'

interface BreadcrumbItem {
  title: string
  path?: string
}

interface Props {
  items?: BreadcrumbItem[]
  showBackButton?: boolean
  parentPath?: string
  parentTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  showBackButton: false,
  parentPath: '',
  parentTitle: '上级页面'
})

const route = useRoute()
const router = useRouter()

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  if (props.items.length > 0) {
    return props.items
  }
  
  // 根据路由自动生成面包屑
  const items: BreadcrumbItem[] = []
  
  // 添加污水监控系统根节点
  items.push({
    title: '污水监控系统',
    path: '/hydrosense/dashboard'
  })
  
  // 根据当前路由添加面包屑
  const routeName = route.name as string
  
  if (routeName?.includes('hydrosense:')) {
    const parts = routeName.split(':')
    
    if (parts.includes('dashboard')) {
      items.push({ title: '数据看板' })
    } else if (parts.includes('datacenter')) {
      items.push({ title: '数据中心' })
    } else if (parts.includes('control')) {
      items.push({ title: '控制中心' })
    } else if (parts.includes('maintenance')) {
      items.push({
        title: '维保中心',
        path: '/hydrosense/maintenance'
      })
      
      // 维保子页面
      if (parts.includes('routine-inspection')) {
        items.push({ title: '例行巡检' })
      } else if (parts.includes('equipment-maintenance')) {
        items.push({ title: '设备保养' })
      } else if (parts.includes('equipment-replacement')) {
        items.push({ title: '设备更换' })
      } else if (parts.includes('work-order-query')) {
        items.push({ title: '工单查询' })
      }
    }
  }
  
  return items
})

// 返回上级页面
const goBack = () => {
  if (props.parentPath) {
    router.push(props.parentPath)
  } else {
    router.back()
  }
}
</script>

<style scoped lang="scss">
@import '@/modules/hydrosense/assets/styles/variables.scss';

.hydro-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $hydro-spacing-sm;
  margin-bottom: $hydro-spacing-lg;
  font-size: 0.9rem;
  color: $hydro-text-light;
  padding: $hydro-spacing-md 0;
  border-bottom: 1px solid $hydro-border;

  .hydro-breadcrumb-item {
    &:not(:last-child)::after {
      content: '>';
      margin-left: $hydro-spacing-sm;
      color: $hydro-text-lighter;
    }

    &.active {
      color: $hydro-primary;
      font-weight: 600;
    }

    a {
      color: $hydro-text-light;
      text-decoration: none;
      transition: color 0.2s;

      &:hover {
        color: $hydro-primary;
      }
    }
  }

  .hydro-back-btn {
    margin-left: auto;
    background: $hydro-primary;
    border-color: $hydro-primary;
    
    &:hover {
      background: darken($hydro-primary, 10%);
      border-color: darken($hydro-primary, 10%);
    }
  }
}

@media (max-width: $hydro-breakpoint-sm) {
  .hydro-breadcrumb {
    flex-direction: column;
    align-items: flex-start;
    gap: $hydro-spacing-md;

    .hydro-back-btn {
      margin-left: 0;
      align-self: flex-end;
    }
  }
}
</style>
