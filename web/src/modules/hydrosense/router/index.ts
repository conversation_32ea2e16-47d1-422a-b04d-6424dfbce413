/**
 * HydroSense 污水监控系统路由配置
 */
import type { RouteRecordRaw } from 'vue-router'

const hydroSenseRoutes: RouteRecordRaw = {
  name: 'hydrosense',
  path: '/hydrosense',
  meta: {
    title: '污水监控系统',
    i18n: 'menu.hydrosense',
    icon: 'mdi:water-pump',
    type: 'M',
    breadcrumbEnable: true,
    copyright: true,
    cache: false,
  },
  redirect: '/hydrosense/dashboard',
  children: [
    {
      name: 'hydrosense:dashboard',
      path: '/hydrosense/dashboard',
      meta: {
        title: '数据看板',
        i18n: 'menu.hydrosense:dashboard',
        icon: 'mdi:view-dashboard',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
      },
      component: () => import('../views/dashboard/index.vue'),
    },
    {
      name: 'hydrosense:datacenter',
      path: '/hydrosense/datacenter',
      meta: {
        title: '数据中心',
        i18n: 'menu.hydrosense:datacenter',
        icon: 'mdi:database',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
      },
      component: () => import('../views/datacenter/index.vue'),
    },
    {
      name: 'hydrosense:maintenance',
      path: '/hydrosense/maintenance',
      meta: {
        title: '维保中心',
        i18n: 'menu.hydrosense:maintenance',
        icon: 'mdi:wrench',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
      },
      component: () => import('../views/maintenance/index.vue'),
    },
    {
      name: 'hydrosense:maintenance:routine-inspection',
      path: '/hydrosense/maintenance/routine-inspection',
      meta: {
        title: '例行巡检',
        i18n: 'menu.hydrosense:maintenance:routine-inspection',
        icon: 'mdi:clipboard-check',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
        hidden: true, // 隐藏在菜单中，只能通过维保中心进入
        parent: '维保中心',
      },
      component: () => import('../views/maintenance/routine-inspection/index.vue'),
    },
    {
      name: 'hydrosense:maintenance:equipment-maintenance',
      path: '/hydrosense/maintenance/equipment-maintenance',
      meta: {
        title: '设备保养',
        i18n: 'menu.hydrosense:maintenance:equipment-maintenance',
        icon: 'mdi:tools',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
        hidden: true,
        parent: '维保中心',
      },
      component: () => import('../views/maintenance/equipment-maintenance/index.vue'),
    },
    {
      name: 'hydrosense:maintenance:equipment-replacement',
      path: '/hydrosense/maintenance/equipment-replacement',
      meta: {
        title: '设备更换',
        i18n: 'menu.hydrosense:maintenance:equipment-replacement',
        icon: 'mdi:swap-horizontal',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
        hidden: true,
        parent: '维保中心',
      },
      component: () => import('../views/maintenance/equipment-replacement/index.vue'),
    },
    {
      name: 'hydrosense:maintenance:work-order-query',
      path: '/hydrosense/maintenance/work-order-query',
      meta: {
        title: '工单查询',
        i18n: 'menu.hydrosense:maintenance:work-order-query',
        icon: 'mdi:file-search',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
        hidden: true,
        parent: '维保中心',
      },
      component: () => import('../views/maintenance/work-order-query/index.vue'),
    },
    {
      name: 'hydrosense:control',
      path: '/hydrosense/control',
      meta: {
        title: '控制中心',
        i18n: 'menu.hydrosense:control',
        icon: 'mdi:cog',
        type: 'M',
        breadcrumbEnable: true,
        copyright: true,
        cache: true,
      },
      component: () => import('../views/control/index.vue'),
    },
  ],
}

export default hydroSenseRoutes
