[中文](./README.md) | English
# Projects

<p align="center">  
    <img src="https://doc.mineadmin.com/logo.svg" width="120" />  
</p>  
<p align="center">  
    <a href="https://www.mineadmin.com" target="_blank">Official Website</a> |  
    <a href="https://doc.mineadmin.com" target="_blank">Documentation</a> |  
    <a href="https://demo.mineadmin.com" target="_blank">Demo</a> |  
    <a href="https://hyperf.wiki/3.0/#/" target="_blank">Hyperf Official Docs</a>  
</p>  

## Project Introduction  

There are many excellent PHP backend management systems, but I couldn’t find one based on Swoole that suited my needs.  
So, I developed a backend management system. It can be used for website admin panels, CMS, CRM, OA, ERP, and more.  

The backend system is built on the Hyperf framework. With an enterprise-level architecture, it easily supports startups and individuals in their early stages, delivering performance comparable to static languages with minimal server resources.  
The frontend uses Vue3 + Vite4 + Pinia + Arco, adapting seamlessly to PC, mobile, and tablet devices.  

If you find it useful, please give it a ⭐star—it would mean a lot to me and be a great encouragement!  
Before using MineAdmin, please read the [Disclaimer](https://doc.mineadmin.com/guide/start/declaration.html) carefully and agree to its terms.  

## Default Main Branch (without department, position, or data permission features)  
The current branch includes features such as departments, positions, and data permissions. If you do not need these features, please switch to the [`【Main Branch】`](https://github.com/mineadmin/MineAdmin) to download the code.  

## Official Community  
> QQ group for discussion and learning—please avoid spamming.  

<img src="https://svg.hamm.cn/badge.svg?key=QQ Group&value=150105478" />  

## Strategic Partnership  
[Jingcedun High-Protection CDN - Reliable Anti-DDoS/CC Attack Service](https://www.jcdun.com/guoneigaofangcdn)  

## Built-in Features  

1. **User Management**: Add, modify, and delete users; supports different homepages for different users.  
2. **Role Management**: Assign menu permissions and data permissions to roles.  
3. **Menu Management**: Configure system menus and buttons.  
4. **Operation Logs**: Track and query user actions.  
5. **Login Logs**: Record and query user login history.  
6. **Attachment Management**: Manage uploaded files and images.  
7. **App Market**: Download various basic apps, plugins, frontend components, and more.  

## Requirements  

- Swoole >= 5.0 (with `Short Name` disabled)  
- PHP >= 8.1 with the following extensions enabled:  
  - mbstring  
  - json  
  - pdo  
  - openssl  
  - redis  
  - pcntl  
- [x] MySQL >= 5.7  
- [x] PostgreSQL >= 10  
- [x] SQL Server (Latest)  
- SQLSRV (Latest)  
- Redis >= 4.0  
- Git >= 2.x  

## Download the Project  
- MineAdmin does not use SQL file imports for installation. Instead, it uses Migrate files for setup and data seeding.  

- To download the project (ensure `Composer` is installed):  
```shell  
composer create-project mineadmin/mineadmin --keep-vcs  
```  

## Disclaimer
[Disclaimer](https://doc.mineadmin.com/guide/start/declaration.html)

This software must not be used to develop applications that violate national policies. `MineAdmin` bears no legal responsibility for any misuse.

## Demo Access
[Demo](https://demo.mineadmin.com)
- Username: **superAdmin**
- Password: **admin123**

> Please avoid adding test data.

## Acknowledgments

> Listed in no particular order

[Hyperf - A high-performance enterprise coroutine framework](https://hyperf.io/)  
[Element Plus - A Vue 3-based component library for designers and developers](https://element-plus.org/)  
[Swoole - PHP coroutine framework](https://www.swoole.com)  
[Vue](https://vuejs.org/)  
[Vite](https://vitejs.cn/)  
[Jetbrains - Productivity tools](https://www.jetbrains.com/)

## OSCS Security Certification
[![OSCS Status](https://www.oscs1024.com/platform/badge/kanyxmo/MineAdmin.svg?size=large)](https://www.murphysec.com/dr/9ztZvuSN6OLFjCDGVo)

## Star History

[![Stargazers over time](https://starchart.cc/mineadmin/mineadmin.svg)](https://starchart.cc/mineadmin/mineadmin.svg)

## Contributors

> Thanks to all contributors who helped develop MineAdmin. [[contributors](https://github.com/mineadmin/minedmin/graphs/contributors)]  
<a href="https://github.com/mineadmin/mineadmin/graphs/contributors">  
<img src="https://contrib.rocks/image?repo=mineadmin/mineadmin" />  
</a>

[![Contributor Trends](https://contributor-overtime-api.apiseven.com/contributors-svg?chart=contributorOverTime&repo=mineadmin/mineadmin)](https://www.apiseven.com/en/contributor-graph?chart=contributorOverTime&repo=mineadmin/mineadmin)

## Demo Screenshots
[![pAdQKPJ.png](https://s21.ax1x.com/2024/10/22/pAdQKPJ.png)](https://imgse.com/i/pAdQKPJ)  
[![pAdQlx1.png](https://s21.ax1x.com/2024/10/22/pAdQlx1.png)](https://imgse.com/i/pAdQlx1)  
[![pAdQQ2R.png](https://s21.ax1x.com/2024/10/22/pAdQQ2R.png)](https://imgse.com/i/pAdQQ2R)  
[![pAdQGqK.png](https://s21.ax1x.com/2024/10/22/pAdQGqK.png)](https://imgse.com/i/pAdQGqK)  
[![pAdQYVO.png](https://s21.ax1x.com/2024/10/22/pAdQYVO.png)](https://imgse.com/i/pAdQYVO)  
[![pAdQNIe.png](https://s21.ax1x.com/2024/10/22/pAdQNIe.png)](https://imgse.com/i/pAdQNIe)  
[![pAdQaPH.png](https://s21.ax1x.com/2024/10/22/pAdQaPH.png)](https://imgse.com/i/pAdQaPH)  
[![pAdQdGd.png](https://s21.ax1x.com/2024/10/22/pAdQdGd.png)](https://imgse.com/i/pAdQdGd)
```