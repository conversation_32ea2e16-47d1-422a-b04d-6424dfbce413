# 污水监控物联网应用迁移项目需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
基于MineAdmin框架开发污水监控物联网应用，将现有的HTML原型页面迁移到MineAdmin应用中，实现社区卫生中心医疗污水强化一级处理系统的监控平台。

### 1.2 项目目标
- 将docs/proto文件夹下的HTML原型页面迁移到MineAdmin框架
- 保持原型页面的核心功能和UI设计
- 采用Vue3 + Element Plus技术栈
- 使用静态路由方式创建组件
- 当前阶段不涉及后端API集成

### 1.3 技术栈
- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **路由**: Vue Router (静态路由)
- **构建工具**: Vite
- **基础框架**: MineAdmin

## 2. 功能模块分析

### 2.1 原型页面清单
根据docs/proto目录分析，需要迁移的页面包括：

1. **登录页面** (`index.html`)
   - 完整页面替换，包含自定义样式和布局
   - 品牌标识和公司信息展示
   - 登录表单功能

2. **数据看板** (`dashboard.html`)
   - 实时监控数据展示
   - 状态监控卡片
   - 实时出水、液位、环境数据
   - 现场监控摄像头

3. **数据中心** (`datacenter.html`)
   - 数据查询和展示功能
   - 历史数据分析

4. **维保中心** (`maintenance.html`) - **父子页面结构**
   - **主页面**: 四个功能入口按钮
   - **子页面1**: 例行巡检 (`routine-inspection.html`)
   - **子页面2**: 设备保养 (`equipment-maintenance.html`)
   - **子页面3**: 设备更换 (`equipment-replacement.html`)
   - **子页面4**: 工单查询 (`work-order-query.html`)
   - 每个子页面都有返回按钮，形成父子页面关系

5. **控制中心** (`control.html`)
   - 设备控制操作
   - 参数设置功能

**注意**: 原型中的系统设置页面将被移除，使用MineAdmin原有的系统功能。

### 2.2 详细页面功能

#### 2.2.1 登录页面功能
- **左侧区域**: 品牌展示区
  - 欢迎文字 "Welcom sign in"
  - 主标题 "某地社区卫生中心"
  - 副标题 "医疗污水强化一级处理系统监控平台"
  
- **右侧区域**: 登录表单
  - 用户名输入框（带用户图标）
  - 密码输入框（带锁图标和显示/隐藏切换）
  - 记住密码选项
  - 登录按钮
  
- **底部区域**: 公司信息
  - 公司Logo
  - 公司信息："装备 | 智水 | 云控"
  - 公司名称："江苏海德洛智能装备有限公司"
  - 官网链接
  - 二维码展示

#### 2.2.2 数据看板功能
- **顶部导航**:
  - 系统标题
  - 实时时间显示
  - 移动端菜单切换按钮

- **左侧菜单**:
  - 数据看板（当前页）
  - 数据中心
  - 维保中心
  - 控制中心
  - 退出登录

- **主要内容区域**:
  - **状态监控卡片**: 实时流量、氯气状态、烟感状态、空气强排控制
  - **实时出水卡片**: PH值、余氯浓度、出水温度
  - **实时液位卡片**: 药桶A/B状态、消毒池状态、积水状态、集水池液位
  - **实时环境卡片**: 氯气浓度、环境湿度、环境温度
  - **监控摄像头**: 两路视频监控，支持截图和录像功能

#### 2.2.3 维保中心功能详细说明

**主页面功能**:
- **四个功能入口按钮**:
  - 例行巡检 (主题色: #4ec6c8)
  - 设备保养 (主题色: #4ec6c8)
  - 设备更换 (主题色: #4ec6c8)
  - 工单查询 (主题色: #DE868F)

**子页面功能**:

1. **例行巡检页面** (`routine-inspection.html`)
   - **STEP1**: 污水处理设备检查 (提升泵、回流泵、排水泵、浮球开关、曝气风机)
   - **STEP2**: 加药装置检查 (消毒药剂液位、加药泵、水质检测提升泵)
   - **STEP3**: 传感器检查 (流量、PH、余氯、水温、集水池、环境氯气、温湿度、烟雾、积水、药剂液位、视频监控)
   - **STEP4**: 例行检查总结 (环境卫生、消防设备检查)
   - 每个步骤包含照片上传、备注输入、状态选择功能
   - 底部有巡检日期、巡检人员输入和提交/返回按钮

2. **设备保养页面** (`equipment-maintenance.html`)
   - **STEP1**: 风机设备保养 (曝气风机相关检查项)
   - **STEP2**: 水泵设备保养 (污水提升泵A/B、回流泵A/B、排水泵A/B等)
   - **STEP3**: 加药设备保养 (二氧化氯发生器、加药泵等)
   - 每个步骤包含照片上传、备注输入、设备选择功能
   - 底部有保养日期、保养人员输入和提交/返回按钮

3. **设备更换页面** (`equipment-replacement.html`)
   - 设备更换相关的工单录入功能
   - 包含照片上传、备注输入等功能
   - 底部有更换日期、更换人员输入和提交/返回按钮

4. **工单查询页面** (`work-order-query.html`)
   - **查询条件**: 日期范围、维保类型、人员姓名
   - **工单列表**: 显示日期、维保类型、操作人员
   - **操作功能**: 工单详情查看、工单删除
   - 底部有查询和返回按钮

**面包屑导航**:
- 所有子页面都有返回按钮，点击返回到维保中心主页面
- 形成清晰的父子页面层级关系

## 3. 技术实现方案

### 3.1 项目结构设计
```
web/src/modules/hydrosense/
├── views/
│   ├── login/
│   │   ├── index.vue           # 登录页面
│   │   ├── components/         # 登录页面组件
│   │   └── style.scss         # 登录页面样式
│   ├── dashboard/
│   │   ├── index.vue          # 数据看板
│   │   └── components/        # 看板组件
│   ├── datacenter/
│   │   └── index.vue          # 数据中心
│   ├── maintenance/
│   │   ├── index.vue          # 维保中心主页面
│   │   ├── routine-inspection/
│   │   │   └── index.vue      # 例行巡检子页面
│   │   ├── equipment-maintenance/
│   │   │   └── index.vue      # 设备保养子页面
│   │   ├── equipment-replacement/
│   │   │   └── index.vue      # 设备更换子页面
│   │   ├── work-order-query/
│   │   │   └── index.vue      # 工单查询子页面
│   │   └── components/        # 维保中心共用组件
│   │       ├── MaintenanceForm.vue    # 维保表单组件
│   │       ├── PhotoUpload.vue        # 照片上传组件
│   │       └── StepForm.vue           # 分步表单组件
│   └── control/
│       └── index.vue          # 控制中心
├── components/                 # 共用组件
│   ├── Layout/                # 布局组件
│   ├── Header/                # 头部组件
│   ├── Sidebar/               # 侧边栏组件
│   └── Breadcrumb/            # 面包屑导航组件
├── assets/                    # 静态资源
│   ├── images/               # 图片资源
│   └── styles/               # 样式文件
└── router/                   # 路由配置
    └── index.ts
```

### 3.2 路由配置方案
采用静态路由方式，在MineAdmin的路由系统中添加污水监控模块路由：

```typescript
// 污水监控模块路由
const hydroSenseRoutes = [
  {
    path: '/hydrosense',
    name: 'HydroSense',
    component: () => import('@/modules/hydrosense/views/dashboard/index.vue'),
    meta: { title: '污水监控系统' },
    children: [
      {
        path: 'dashboard',
        name: 'HydroSenseDashboard',
        component: () => import('@/modules/hydrosense/views/dashboard/index.vue'),
        meta: { title: '数据看板' }
      },
      {
        path: 'datacenter',
        name: 'HydroSenseDataCenter',
        component: () => import('@/modules/hydrosense/views/datacenter/index.vue'),
        meta: { title: '数据中心' }
      },
      {
        path: 'maintenance',
        name: 'HydroSenseMaintenance',
        component: () => import('@/modules/hydrosense/views/maintenance/index.vue'),
        meta: { title: '维保中心' },
        children: [
          {
            path: 'routine-inspection',
            name: 'RoutineInspection',
            component: () => import('@/modules/hydrosense/views/maintenance/routine-inspection/index.vue'),
            meta: { title: '例行巡检', parent: '维保中心' }
          },
          {
            path: 'equipment-maintenance',
            name: 'EquipmentMaintenance',
            component: () => import('@/modules/hydrosense/views/maintenance/equipment-maintenance/index.vue'),
            meta: { title: '设备保养', parent: '维保中心' }
          },
          {
            path: 'equipment-replacement',
            name: 'EquipmentReplacement',
            component: () => import('@/modules/hydrosense/views/maintenance/equipment-replacement/index.vue'),
            meta: { title: '设备更换', parent: '维保中心' }
          },
          {
            path: 'work-order-query',
            name: 'WorkOrderQuery',
            component: () => import('@/modules/hydrosense/views/maintenance/work-order-query/index.vue'),
            meta: { title: '工单查询', parent: '维保中心' }
          }
        ]
      },
      {
        path: 'control',
        name: 'HydroSenseControl',
        component: () => import('@/modules/hydrosense/views/control/index.vue'),
        meta: { title: '控制中心' }
      }
    ]
  }
]
```

### 3.3 样式迁移方案
- 保留原型页面的核心样式设计
- 适配MineAdmin的主题色彩体系
- 使用CSS变量管理主题色彩：`--primary-color: #4ec6c8`
- 保持响应式设计，支持移动端适配

### 3.4 组件化设计
- **登录页面**: 完全自定义组件，替换MineAdmin默认登录页
- **其他页面**: 提取核心内容区域，复用MineAdmin的Layout框架
- **数据卡片**: 封装为可复用的Vue组件
- **监控组件**: 独立的视频监控组件
- **维保表单组件**:
  - `MaintenanceForm.vue`: 通用维保表单容器
  - `PhotoUpload.vue`: 照片上传组件
  - `StepForm.vue`: 分步表单组件
- **面包屑导航**: `Breadcrumb.vue` 组件，支持父子页面导航

## 4. 开发任务分解

### 4.1 第一阶段：基础框架搭建
1. **创建模块目录结构**
   - 在`web/src/modules/`下创建`hydrosense`模块
   - 建立标准的目录结构

2. **静态资源迁移**
   - 迁移图片资源（Logo、二维码、监控图片）
   - 迁移并优化CSS样式文件

3. **路由配置**
   - 配置污水监控模块的静态路由
   - 集成到MineAdmin路由系统

### 4.2 第二阶段：登录页面开发
1. **登录页面完整替换**
   - 创建自定义登录页面组件
   - 实现左右布局设计
   - 集成登录表单功能
   - 添加底部公司信息展示

2. **样式适配**
   - 实现响应式设计
   - 移动端适配优化

### 4.3 第三阶段：核心功能页面开发
1. **数据看板页面**
   - 创建看板主页面组件
   - 开发数据卡片组件
   - 实现监控摄像头组件
   - 集成实时时间显示

2. **维保中心页面开发**
   - **主页面**: 四个功能入口按钮
   - **例行巡检子页面**: 4步骤表单，包含照片上传、状态选择
   - **设备保养子页面**: 3步骤表单，包含设备选择、照片上传
   - **设备更换子页面**: 设备更换工单录入
   - **工单查询子页面**: 查询条件、列表展示、操作功能
   - **面包屑导航**: 实现父子页面导航

3. **其他功能页面**
   - 数据中心页面开发
   - 控制中心页面开发

### 4.4 第四阶段：集成测试与优化
1. **功能测试**
   - 页面跳转测试
   - 响应式布局测试
   - 组件交互测试

2. **性能优化**
   - 代码分割优化
   - 资源加载优化
   - 移动端性能优化

## 5. 验收标准

### 5.1 功能验收
- [ ] 所有原型页面成功迁移到MineAdmin框架
- [ ] 登录页面完全替换，保持原有设计风格
- [ ] 其他页面核心内容区域正确显示
- [ ] 维保中心父子页面结构正确实现
- [ ] 面包屑导航功能正常，支持返回上级页面
- [ ] 维保表单功能完整（照片上传、状态选择、数据输入）
- [ ] 工单查询功能正常（查询、列表展示、详情查看）
- [ ] 页面间导航功能正常
- [ ] 响应式设计在各设备上正常显示

### 5.2 技术验收
- [ ] 代码符合MineAdmin开发规范
- [ ] 使用Vue3 Composition API
- [ ] TypeScript类型定义完整
- [ ] 组件化程度高，可复用性强
- [ ] 样式使用SCSS，结构清晰

### 5.3 性能验收
- [ ] 页面加载速度满足要求
- [ ] 移动端性能表现良好
- [ ] 内存使用合理，无明显泄漏

## 6. 风险评估与应对

### 6.1 技术风险
- **风险**: Element Plus组件与原型设计差异
- **应对**: 通过自定义样式覆盖，保持设计一致性

- **风险**: MineAdmin框架兼容性问题
- **应对**: 深入了解框架机制，采用标准化开发方式

### 6.2 进度风险
- **风险**: 样式迁移工作量超预期
- **应对**: 优先实现核心功能，样式细节后续优化

## 7. 后续扩展计划

### 7.1 API集成阶段
- 集成后端数据接口
- 实现实时数据更新
- 添加数据持久化功能

### 7.2 功能增强阶段
- 添加数据图表展示
- 实现报警通知功能
- 增加数据导出功能

---

**文档版本**: v1.0  
**创建日期**: 2025-01-03  
**最后更新**: 2025-01-03
