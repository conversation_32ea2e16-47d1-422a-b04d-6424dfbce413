<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .page-main-title {
                font-size: 1.5rem !important;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .inspection-form-area .inspection-form-footer-row {
                flex-direction: column !important;
                gap: 1rem !important;
                align-items: stretch !important;
            }
            .inspection-form-area .inspection-form-footer-row > div {
                width: 100% !important;
                justify-content: flex-start !important;
            }
            /* 移动端照片上传和备注布局调整 */
            .photo-upload-section {
                flex-direction: column !important;
            }
            .photo-upload-section .photo-grid {
                width: 100% !important;
                display: grid !important;
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 0.75rem !important;
                justify-content: center !important;
            }
            .photo-upload-section .photo-grid > div {
                width: 100% !important;
                height: 100px !important;
                min-height: 100px !important;
                max-height: 100px !important;
            }
            .photo-upload-section textarea {
                margin-top: 0.75rem !important;
                width: 100% !important;
                height: 100px !important;
                min-height: 100px !important;
                max-height: 100px !important;
                resize: none !important;
            }
        }
        /* 表单内容区域所有文字默认灰色 */
        .inspection-form-area, .inspection-form-area label, .inspection-form-area span, .inspection-form-area input, .inspection-form-area textarea, .inspection-form-area button {
            color: #333 !important;
        }
        /* 提交、清除按钮文字白色 */
        .inspection-form-area button[type="submit"],
        .inspection-form-area button[type="reset"] {
            color: #fff !important;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content">
            <div class="inspection-form-area" style="background:#fff;padding:2rem 2.5rem 2.5rem 2.5rem;border-radius:12px;box-shadow:0 2px 12px rgba(0,0,0,0.04);max-width:1200px;margin:auto;">
                <div style="font-size:1.25rem;font-weight:900;margin-bottom:0.5rem;">社区卫生服务中心</div>
                <div style="font-size:2rem;font-weight:900;margin-bottom:1.5rem;" class="page-main-title">医疗污水处理系统设备更换工单录入</div>
                <hr style="margin-bottom:2rem;">
                <form id="inspectionForm">
                    <div style="display:flex;flex-direction:column;gap:2rem;">
                        <!-- STEP1 -->
                        <div style="min-width:320px;">
                            <div style="font-size:1.15rem;font-weight:900;color:#23b3b5;margin-bottom:0.5rem;">STEP1：需更换设备</div>
                            <div style="display:flex;gap:0.75rem;margin-bottom:0.75rem;align-items:flex-start;" class="photo-upload-section">
                                <div style="display:flex;gap:0.75rem;flex-wrap:wrap;" class="photo-grid">
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                </div>
                                <textarea placeholder="输入备注" style="flex:1;height:100px;box-sizing:border-box;resize:vertical;padding:0.5rem 0.75rem;border:1px solid #e0e0e0;border-radius:8px;font-size:1rem;"></textarea>
                            </div>
                            <div style="display:flex;gap:1rem;margin-bottom:0.75rem;">
                                <div style="display:flex;flex:1;gap:1rem;flex-wrap:wrap;">
                                </div>
                            </div>
                        </div>
                        <!-- STEP2 -->
                        <div style="min-width:320px;">
                            <div style="font-size:1.15rem;font-weight:900;color:#23b3b5;margin-bottom:0.5rem;">STEP2：新更换设备</div>
                            <div style="display:flex;gap:0.75rem;margin-bottom:0.75rem;align-items:flex-start;" class="photo-upload-section">
                                <div style="display:flex;gap:0.75rem;flex-wrap:wrap;" class="photo-grid">
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                    <div style="width:100px;height:100px;box-sizing:border-box;border:2px dashed #b2e6e7;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;font-size:2rem;color:#b2e6e7;background:#f8fefe;">+</div>
                                </div>
                                <textarea placeholder="输入备注" style="flex:1;height:100px;box-sizing:border-box;resize:vertical;padding:0.5rem 0.75rem;border:1px solid #e0e0e0;border-radius:8px;font-size:1rem;"></textarea>
                            </div>
                            <div style="display:flex;gap:1rem;flex-wrap:wrap;margin-bottom:0.75rem;">
                            </div>
                        </div>

                    </div>
                    <div class="inspection-form-footer-row" style="display:flex;gap:2rem;margin-top:2rem;align-items:center;flex-wrap:wrap;justify-content:space-between;">
                        <div style="display:flex;align-items:center;gap:0.5rem;">
                            <span style="font-weight:900;min-width:90px;display:inline-block;">更换日期：</span>
                            <el-date-picker v-model="inspectionDate" type="date" placeholder="请选择日期" class="dashboard-query-date" style="width: 100%;"></el-date-picker>
                        </div>
                        <div style="display:flex;align-items:center;gap:0.5rem;">
                            <span style="font-weight:900;min-width:90px;display:inline-block;">更换人员：</span>
                            <el-input v-model="inspector" placeholder="请输入" class="dashboard-query-date"></el-input>
                        </div>
                        <div style="display:flex;gap:1.5rem;">
                            <button type="submit" style="background:#4ec6c8;color:#fff;font-weight:900;font-size:1rem;padding:0.4rem 1.2rem;border:none;border-radius:4px;cursor:pointer;">提交</button>
                            <button type="button" onclick="window.location.href='maintenance.html'" style="background:#DE868F;color:#fff !important;font-weight:900;font-size:1rem;padding:0.4rem 1.2rem;border:none;border-radius:4px;cursor:pointer;">返回</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });

        const app = Vue.createApp({
            data() {
                return {
                    inspectionDate: '',
                    inspector: ''
                }
            }
        });
        app.use(ElementPlus);
        app.mount('.inspection-form-area');
    </script>
</body>
</html> 