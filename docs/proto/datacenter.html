<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { 
            font-size: 16px; 
            overflow-x: hidden;
        }
        body {
            margin: 0;
            background: #f7f7f7;
            overflow-x: hidden;
            width: 100vw;
            box-sizing: border-box;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
            width: 100%;
            box-sizing: border-box;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
            width: 100%;
            box-sizing: border-box;
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 18px 2vw 2vw 2vw;
            overflow-y: auto;
            width: calc(100% - 13.75rem);
            box-sizing: border-box;
        }
        /* 卡片和行布局核心样式，仅迁移必要部分 */
        .dashboard-row {
            display: flex;
            gap: 18px;
            margin-bottom: 0;
            width: 100%;
            box-sizing: border-box;
            flex-wrap: wrap;
        }
        .dashboard-card {
            background: #f3f5f7;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.5rem 0 rgba(0,0,0,0.04);
            padding: 1.5rem 1.25rem 1.125rem 1.25rem;
            min-width: 13.75rem;
            max-width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            box-sizing: border-box;
            margin-bottom: 18px;
            height: 410px;
        }
        .dashboard-row .dashboard-card:last-child {
            margin-bottom: 0;
        }
        .dashboard-card-title {
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: #4ec6c8;
        }
        .dashboard-card-value {
            font-size: 1.76rem;
            font-weight: 900;
            color: #4ec6c8;
            margin-bottom: 0.5rem;
            background: #fff;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 50px;
            text-align: center;
            box-sizing: border-box;
        }
        .dashboard-card-label {
            color: #888;
            font-size: 1.1rem;
            margin-bottom: 0;
            display: block;
            font-weight: 700;
        }
        .dashboard-card-value-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            width: 100%;
        }
        .dashboard-card-value-row:last-child {
            margin-bottom: 0;
        }
        .dashboard-total-value {
            background: #fff;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 0;
            width: 150px;
            height: 50px;
            text-align: center;
            font-size: 1.76rem;
            font-weight: 900;
            color: #4ec6c8;
            box-sizing: border-box;
            vertical-align: middle;
        }
        .wastewater-header-row-title,
        .dashboard-total-label {
            font-size: 1.1rem;
            font-weight: 900;
            color: #333;
            height: 32px;
            line-height: 32px;
            display: flex;
            align-items: center;
        }
        @media (max-width: 1200px) {
            .dashboard-row {
                flex-direction: column !important;
                gap: 1vw;
                width: 100% !important;
            }
            .dashboard-row > div {
                width: 100% !important;
                flex: 1 1 100% !important;
            }
            .dashboard-card {
                min-width: 0;
                width: 100%;
                max-width: 100%;
                height: auto;
            }
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
            .dashboard-header-title { font-size: 1.2rem; }
            .dashboard-card-value {
                font-size: 1.76rem;
                width: 120px;
                height: 50px;
            }
            .wastewater-header-row {
                flex-direction: row !important;
                align-items: center !important;
                gap: 4px !important;
            }
            .dashboard-row {
                flex-direction: column !important;
                gap: 4px !important;
                width: 100% !important;
            }
            .dashboard-row > div {
                width: 100% !important;
                flex: 1 1 100% !important;
            }
            #wastewater-chart {
                width: 100% !important;
                min-width: 0 !important;
            }
            .dashboard-total-value {
                font-size: 1.76rem;
                width: 150px;
                height: 50px;
            }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
                width: 13.75rem;
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .dashboard-monitor-img {
                width: 272px !important;  /* 320px * 0.85 */
                height: 153px !important; /* 180px * 0.85 */
            }
            .dashboard-row {
                flex-direction: column;
                gap: 12px;
            }
            .dashboard-card {
                min-width: 0;
                width: 100%;
                max-width: 100%;
            }
            .dashboard-content {
                width: 100%;
            }
        }
        .dashboard-query-block,
        .dashboard-print-block {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 0;
            align-items: center;
            width: 100%;
        }
        .dashboard-query-date-row,
        .dashboard-print-date-row {
            display: flex;
            width: 100%;
            gap: 0;
            flex-direction: column;
        }
        .dashboard-query-date-row .dashboard-query-date,
        .dashboard-print-date-row .dashboard-print-date {
            width: 100%;
            flex: none;
            min-width: 0;
            margin-bottom: 18px;
        }
        .dashboard-query-date-row .dashboard-query-date:last-child,
        .dashboard-print-date-row .dashboard-print-date:last-child {
            margin-bottom: 18px;
        }
        .dashboard-query-select,
        .dashboard-query-btn,
        .dashboard-print-btn {
            width: 100%;
            max-width: none;
            margin-left: 0;
            margin-right: 0;
        }
        .dashboard-query-date-row .dashboard-query-date,
        .dashboard-print-date-row .dashboard-print-date,
        .dashboard-query-select,
        .dashboard-query-btn,
        .dashboard-print-btn {
            flex: 1;
            min-width: 0;
        }
        .dashboard-card-label.small-label {
            font-size: 0.95rem;
        }
    </style>
</head>
<body>
  <div id="app">
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li class="active"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content" style="background:#fff;">
            <!-- 污水日处理量（过去30天） -->
            <div class="dashboard-row" style="align-items:flex-end;">
                <div style="flex:1;width:100%;">
                    <div class="wastewater-header-row" style="display:flex;justify-content:space-between;align-items:center;">
                        <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;">污水日处理量</h2>
                        <div class="dashboard-total-label">总处理量（m³）：
                            <span class="dashboard-total-value">4786.45</span>
                        </div>
                    </div>
                    <div id="wastewater-chart" style="width:100%;height:260px;margin-top:10px;"></div>
                </div>
            </div>
            <div class="dashboard-row" style="margin-top:32px;">
                <!-- PH（日均值）卡片 -->
                <div class="dashboard-card">
                    <div class="dashboard-card-title">PH（日均值）</div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去24小时</span>
                        <span class="dashboard-card-value">0.8</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去7天</span>
                        <span class="dashboard-card-value">64%</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去30天</span>
                        <span class="dashboard-card-value">24.3</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去60天</span>
                        <span class="dashboard-card-value">7.82</span>
                    </div>
                </div>
                <!-- 余氯（日均值）卡片 -->
                <div class="dashboard-card">
                    <div class="dashboard-card-title">余氯（日均值）</div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去24小时</span>
                        <span class="dashboard-card-value">0.8</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去7天</span>
                        <span class="dashboard-card-value">64%</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去30天</span>
                        <span class="dashboard-card-value">24.3</span>
                    </div>
                    <div class="dashboard-card-value-row">
                        <span class="dashboard-card-label">过去60天</span>
                        <span class="dashboard-card-value">7.82</span>
                    </div>
                </div>
                <!-- 数据查询卡片 -->
                <div class="dashboard-card">
                    <div class="dashboard-card-title">数据查询</div>
                    <div class="dashboard-query-block">
                        <label class="dashboard-card-label small-label">自定义时间段</label>
                        <div class="dashboard-query-date-row">
                            <el-date-picker v-model="startDate" type="date" placeholder="开始时间" class="dashboard-query-date"></el-date-picker>
                            <el-date-picker v-model="endDate" type="date" placeholder="结束时间" class="dashboard-query-date"></el-date-picker>
                        </div>
                        <el-select v-model="queryType" placeholder="请选择查询项" class="dashboard-query-select">
                            <el-option label="PH" value="ph"></el-option>
                            <el-option label="余氯" value="cl"></el-option>
                            <el-option label="处理量" value="amount"></el-option>
                        </el-select>
                        <el-button type="primary" class="dashboard-query-btn" style="margin-top:10px;background:#4ec6c8;border-color:#4ec6c8;">查询</el-button>
                    </div>
                    <div class="dashboard-query-block" style="margin-top:18px;">
                        <label class="dashboard-card-label small-label">当日数据查询</label>
                        <el-button type="primary" class="dashboard-query-btn" style="margin-top:10px;background:#4ec6c8;border-color:#4ec6c8;">立即查询</el-button>
                    </div>
                </div>
                <!-- 报表打印卡片 -->
                <div class="dashboard-card">
                    <div class="dashboard-card-title">报表打印</div>
                    <div class="dashboard-print-block">
                        <label class="dashboard-card-label small-label">自定义时间段</label>
                        <div class="dashboard-print-date-row">
                            <el-date-picker v-model="printStartDate" type="date" placeholder="开始时间" class="dashboard-print-date"></el-date-picker>
                            <el-date-picker v-model="printEndDate" type="date" placeholder="结束时间" class="dashboard-print-date"></el-date-picker>
                        </div>
                        <el-button type="danger" class="dashboard-print-btn" style="background: #DE868F; border-color: #DE868F;">打印</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://unpkg.com/element-plus"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <script>
    const app = Vue.createApp({
      data() {
        return {
          startDate: '',
          endDate: '',
          queryType: '',
          printStartDate: '',
          printEndDate: ''
        }
      }
    });
    app.use(ElementPlus);
    app.mount('#app');

    // 动态更新时间
    function updateTime() {
        const el = document.getElementById('dashboardTime');
        if (!el) return;
        const week = ['日','一','二','三','四','五','六'];
        const now = new Date();
        const y = now.getFullYear();
        const m = String(now.getMonth()+1).padStart(2,'0');
        const d = String(now.getDate()).padStart(2,'0');
        const h = String(now.getHours()).padStart(2,'0');
        const min = String(now.getMinutes()).padStart(2,'0');
        const s = String(now.getSeconds()).padStart(2,'0');
        const w = week[now.getDay()];
        el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
    }
    setInterval(updateTime, 1000);
    updateTime();

    // 移动端菜单切换
    function toggleMenu() {
        const sider = document.querySelector('.dashboard-sider');
        sider.classList.toggle('active');
    }

    // 点击菜单项时关闭菜单（移动端）
    document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
        item.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });
    });

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.remove('active');
        }
    });

    // 这里可后续补充ECharts渲染逻辑
    document.addEventListener('DOMContentLoaded', function () {
        var chartDom = document.getElementById('wastewater-chart');
        if (chartDom) {
            var myChart = echarts.init(chartDom);
            var option = {
                grid: {
                    left: '5px',
                    right: '5px',
                    top: 20,
                    bottom: 30,
                    containLabel: true
                },
                title: { show: false },
                tooltip: { trigger: 'axis' },
                xAxis: {
                    type: 'category',
                    data: Array.from({length: 30}, (_, i) => 30 - i),
                    boundaryGap: false
                },
                yAxis: { type: 'value' },
                series: [{
                    data: [18, 22, 15, 20, 13, 12, 14, 16, 19, 21, 17, 15, 13, 12, 14, 18, 20, 22, 19, 17, 15, 13, 12, 14, 16, 19, 21, 17, 15, 13],
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    areaStyle: { color: '#e6f7fa' },
                    lineStyle: { color: '#4ec6c8', width: 3 }
                }]
            };
            myChart.setOption(option);
            window.addEventListener('resize', function () { myChart.resize(); });
        }
    });
  </script>
</body>
</html> 