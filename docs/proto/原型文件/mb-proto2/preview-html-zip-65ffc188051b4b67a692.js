try{let dt=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},St=new dt.Error().stack;St&&(dt._sentryDebugIds=dt._sentryDebugIds||{},dt._sentryDebugIds[St]="ec63e087-b346-401b-9f52-2a02f2e7dbc8",dt._sentryDebugIdIdentifier="sentry-dbid-ec63e087-b346-401b-9f52-2a02f2e7dbc8")}catch(dt){}{let dt=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};dt.SENTRY_RELEASE={id:"21.3.4"}}(()=>{var dt={56310:(d,S,i)=>{"use strict";i.d(S,{sI:()=>o});const m="_cmt",o=r=>r.endsWith(m),M=r=>{if(o(r))throw new Error("bad datKey: "+r);return""+r+m},y=r=>{if(!o(r))throw new Error("bad cmtKey: "+r);return r.slice(0,-m.length)},k="_cur",t=r=>r.endsWith(k),e=r=>{if(t(r))throw new Error("bad datKey: "+r);return""+r+k},s=r=>{if(!t(r))throw new Error("bad curKey: "+r);return r.slice(0,-k.length)}},79390:(d,S,i)=>{"use strict";i.d(S,{f:()=>M,i:()=>o});var m=i(51044);const o=(0,m.createContext)(null),M=()=>(0,m.useContext)(o)},21398:(d,S,i)=>{"use strict";i.d(S,{A:()=>V});var m=i(51044),o=i(21676),M=i(68496),y=i(25944),k=i(4098),t=i(45309),e=i(63555);const s={prepare:0,animate:1,finish:2},r=(l,f)=>{const{key:g,hotAttr:{type:E,fixPosTo:j}}=f;if(j!=="none")return!1;const Q=e.Q.USE_CLIP_TEXT.includes(E)?(0,t.HN)(g,l):E==="wWrap"?(0,t.PH)(g,l):l.getLocalBound(g);if(Q)return(0,k.Pb)(Q)},p=(l,f,g)=>{const E=r(l,f);return E?E[1]<g:!1},n=(l,f,g,E)=>{const j=r(l,f);return j?g<j[3]+E:!1},a=(l,f)=>{if(!l||!f||sdkStore.isTrashed(f))return{};const g=sdkStore.getHotItem(f),{hotAttr:{stickyHeaderHeight:E,stickyFooterHeight:j,h:Q}}=g,u=fromWBoundToAABB(l),h=E>0&&u[1]<E,D=j>0&&Q<u[3]+j;return{isFixedOnHeader:h,isFixedOnFooter:D}},c=(l,f)=>!l||!f||sdkStore.isTrashed(l)||sdkStore.isTrashed(f)?{}:a(sdkStore.getRelocateLocalBound(l,f),f);var w=i(79390),T=i(57464);const C=l=>{const{canvasKey:f,deviceScroll:g="",isRenderScroll:E}=l,j=(0,w.f)(),{sdkStore:Q,isPanelPreviewMode:u}=j,h=l.transition||j.transition||{},{name:D,duration:O,callback:Y}=h,[A,F]=(0,m.useState)(s.finish),G=(0,m.useRef)(f),st=(0,m.useRef)(A),J=(0,m.useRef)(),nt=D==="auto"&&O>0,ot=(0,m.useRef)(h),rt=(0,m.useRef)(!1);if(ot.current=h,(0,m.useEffect)(()=>{if(!E)return MB.runnerController.destroyScrollBarByCid(f);const N=g==="hide"&&!u,it=["auto","horizontal","hide"].includes(g),ct=["auto","vertical","hide"].includes(g),xt={scrollX:u?!0:it,scrollY:u?!0:ct,scrollbars:u?g!=="hide":!0,fadeScrollbars:u?g==="hide":!0,mouseWheel:u?g!=="hide":!0};return MB.runnerController.renderScrollbar(f,xt),N&&MB.runnerController.disableScrollBar(f),()=>{MB.runnerController.destroyScrollBarByCid(f)}},[f,g,u,A,E]),(0,m.useEffect)(()=>()=>{clearTimeout(J.current),Q.deleteTransitionScope(G.current)},[Q]),(0,m.useEffect)(()=>{if(!rt.current){rt.current=!0;return}const{name:N,callback:it}=ot.current;(N==="auto"||N==="none")&&A===s.finish&&(it==null||it())},[f,A]),f&&f!==G.current){const N=nt?s.prepare:s.finish;clearTimeout(J.current),F(N),Q.deleteTransitionScope(G.current),nt&&Q.recordTransitionScope(G.current,f),G.current=f}st.current!==A&&(nt&&Q.setScopeTransitionState(f,A),A===s.prepare?J.current=setTimeout(()=>{F(s.animate)},0):A===s.animate&&(J.current=setTimeout(()=>{F(s.finish),Q.deleteTransitionScope(f)},O)),st.current=A);const q=f?Q.getTransitionHotItem(f):null,{headerItems:et,footerItems:Z}=(0,m.useMemo)(()=>{if(!q)return{headerItems:[],contentItems:[],footerItems:[]};const{h:N,stickyHeaderHeight:it,stickyFooterHeight:ct}=q.hotAttr,xt=Q.getRenderableSubHotItems(f),Et=[],_t=[];return(it>0||ct>0)&&xt.forEach(bt=>{const Tt=r(Q,bt);Tt&&(it>0&&Tt[1]<it&&Et.push(bt),ct>0&&N<Tt[3]+ct&&_t.push(bt))}),{headerItems:Et,contentItems:xt,footerItems:_t}},[Q,q]);if(!q)return null;const{w:mt,h:pt,fill:lt,stickyHeaderHeight:tt,stickyFooterHeight:ut,asOverlay:gt}=q.hotAttr,ft={height:!gt&&u?pt:pt-(tt+ut),width:mt,...lt.fillIsVisible&&!gt&&u&&{backgroundColor:(0,M.t0)(lt.solidColor)}},Mt={height:pt,marginTop:u?0:-tt};return(0,T.jsxs)(T.Fragment,{children:[tt>0&&(0,T.jsx)("div",{className:"screen-header",style:{height:tt},children:et.map(N=>(0,T.jsx)(y.o,{hotItem:N,mode:"preview"},N.key))}),(0,T.jsx)("div",{className:"screen-content",children:(0,T.jsx)("div",{className:"widgets",style:ft,children:(0,T.jsx)("div",{className:"scontainer",style:Mt,children:(0,T.jsx)(y.o,{hotItem:q,mode:"preview"},f)})})}),ut>0&&(0,T.jsx)("div",{className:"screen-footer",style:{height:ut},children:(0,T.jsx)("div",{className:"footer-widgets",style:{transform:"translateY("+(ut-pt)+"px)"},children:Z.map(N=>(0,T.jsx)(y.o,{hotItem:N,mode:"preview"},N.key))})}),A<s.finish&&O&&(0,T.jsx)(x,{canvasKey:f,duration:O})]})},x=(0,o.DU)(['.pcanvas,.pstate{&[data-cid="','"]{.tree-node,.widget,.group,.panel{transition:all ',"ms linear,z-index 0s linear;}}}"],l=>l.canvasKey,l=>l.duration),V=C},61595:(d,S,i)=>{"use strict";i.d(S,{Cz:()=>C,Ky:()=>c,Lt:()=>s,NV:()=>r,Nb:()=>a,RL:()=>w,Um:()=>n,Zk:()=>p,Zv:()=>x,c9:()=>Q,lI:()=>T,ut:()=>j,wA:()=>g,xc:()=>V});var m=i(68496),o=i(59742),M=i(4098),y=i(17383),k=i(91416),t=i(63555);const e=[t.x.wFlowClosed,t.x.WFlowOpened,t.x.WMTooltip,t.x.WTable,t.x.WLine,t.x.WArrow,t.x.WPaginationMobile,t.x.WPaginationWeb,t.x.WSegmentedControl,t.x.WCollapse,t.x.WVector,t.x.WTriangle,t.x.WTriangleB,t.x.WTriangleTL,t.x.WStar,t.x.WPolygon],s=(u,h)=>u?(0,m.t0)(h):"",r=u=>{let h=0,D=0,O=0,Y=0;if(u){const{border:A,borderVisibility:F}=u;if(A){const{bdrIsVisible:G,bdrWidth:st}=A,J=st||0;if(G)if(F){const{bdrIsVisibleT:nt,bdrIsVisibleR:ot,bdrIsVisibleB:rt,bdrIsVisibleL:q}=F;h=q?J:0,D=nt?J:0,O=ot?J:0,Y=rt?J:0}else h=D=O=Y=J}}return{borderLeft:h,borderTop:D,borderRight:O,borderBottom:Y}},p=(u,h)=>u.bdrIsVisible?Math.min(u.bdrWidth,h||u.bdrWidth)+"px "+u.bdrStyle+" "+(0,m.t0)(u.bdrColor):"",n=(u,h)=>{const{border:D,borderVisibility:O,type:Y,w:A=0,h:F=0}=u,{bdrIsVisible:G,bdrColor:st,bdrWidth:J,bdrStyle:nt}=D||{};if(!G||!h&&e.includes(Y))return{border:"none"};const{bdrIsVisibleT:ot,bdrIsVisibleR:rt,bdrIsVisibleB:q,bdrIsVisibleL:et}=O||{},Z=O?[ot,rt,q,et].map(lt=>lt?J:0):Array(4).fill(J),mt=[Z[0]+Z[2],Z[1]+Z[3]];return[F,A].forEach((lt,tt)=>{if(mt[tt]>lt)if(Z[tt]>0&&Z[tt+2]>0)Z[tt]=Z[tt+2]=Math.min(Math.max(lt/2,1),Z[tt]);else{const ut=Z[tt]>0?tt:tt+2;Z[ut]=Math.min(Math.max(F,1),Z[ut])}}),{borderColor:(0,m.t0)(st),borderStyle:nt,borderWidth:Z.join("px ")+"px"}},a=function(u,h){h===void 0&&(h=1);const{type:D,borderRadius:O,borderRadiusIsVisible:Y}=u;if(D===t.x.WOval)return{borderRadius:"100%"};if(Y===!1)return{borderRadius:void 0};if(typeof O=="number")return{borderRadius:O*h+"px"};if(typeof O=="object"){const{radiusTL:A,radiusTR:F,radiusBR:G,radiusBL:st}=O;return{borderRadius:A*h+"px "+F*h+"px "+G*h+"px "+st*h+"px"}}},c=u=>{const{shadow:h}=u||{};if(!h||!h.shadowIsVisible)return{boxShadow:"none"};const{offsetX:D,offsetY:O,blurRadius:Y,spreadRadius:A,shadowColor:F}=h;return{boxShadow:D+"px "+O+"px "+Y+"px "+A+"px "+(0,m.t0)(F)}},w=u=>{let{textDecoration:h}=u;return h==="normal"?void 0:h},T=u=>{let{italic:h}=u;return h?"italic":"normal"},C=u=>{let{bold:h}=u;return h?"bold":"normal"},x=u=>{const{textV0:h,type:D}=u;if(!h||[t.x.WPaginationMobile,t.x.WTable,t.x.WChart].includes(D))return;const{fontFamily:O="PingFangSC",fontWeight:Y,italic:A,bold:F,textColor:G,fontSize:st,lineHeight:J,letterSpacing:nt,textDecoration:ot="normal",horizontalAlign:rt,verticalAlign:q,color:et}=h,Z=ot==="normal"?"none":ot;return{fontFamily:O,fontSize:st,fontWeight:(0,y._y)(Y,F),lineHeight:J+"px",fontStyle:A?"italic":"normal",color:et||(0,m.t0)(G),letterSpacing:nt,textDecoration:Z,alignItems:q,justifyContent:rt}},V=u=>{if(!u||!u.flip)return"";const{flip:{flipH:h,flipV:D}}=u;return(h?"scaleX(-1)":"")+" "+(D?"scaleY(-1)":"")},l=u=>{const{animation:h}=u;if(h&&h.aniName!=="none")return{animationDuration:h.aniDuration,animationIterationCount:h.aniCount,animationDelay:h.aniDelay}},f=u=>{const h=u.type;if(h===t.x.Canvas)return{borderColor:void 0,borderStyle:void 0,borderWidth:void 0};if(h===t.x.WImage){const{image:D}=u;return{color:"#bec2c9",...(D==null?void 0:D.imageClipType)==="circular"?{borderRadius:"100%"}:void 0}}if(h===t.x.WMTooltip)return{overflow:"visible",boxShadow:"none",background:"none"};if(h===t.x.WBasket||u._isInValid)return{overflow:"hidden"};if(h===t.x.WMTextInput)return{overflow:"hidden"};if(h===t.x.WWebpage)return{overflow:"hidden"};if(h===t.x.WQRCode)return{overflow:"hidden"};if(h===t.x.WMSelect)return{minHeight:"17px"};if(h===t.x.WMapView)return{overflow:"hidden"};if(h===t.x.WIcon||h===t.x.WIconButton){const{shadowIsVisible:D,offsetX:O,offsetY:Y,blurRadius:A,shadowColor:F}=u.shadow,G="drop-shadow("+O+"px "+Y+"px "+A+"px "+(0,m.t0)(F)+")";return{filter:D?G:"none",background:"none",display:"flex",alignItems:"center",justifyContent:"center"}}if(h===t.x.WTabItem)return{height:"100%",color:"#444",fontSize:"12px"};if(h===t.x.WTable)return{fontSize:"14px",background:"none"};if(h===t.x.WSearchBar)return{width:"100%",height:"100%",fontSize:"14px"};if(h===t.x.WPaginationMobile||h===t.x.WPaginationWeb||h===t.x.WCollapse)return{background:"none",borderRadius:0};if(h===t.x.WSlider)return{display:"flex"};if(h===t.x.WDropMenu)return{overflow:"hidden",padding:0};if(h===t.x.WTabs)return{background:"none",border:"none"};if(h===t.x.WLine)return{boxShadow:"none",borderWidth:"unset",borderStyle:"unset",pointerEvents:"none",background:"none"};if(h===t.x.WArrow)return{boxShadow:"none",background:"none",pointerEvents:"none"};if(h===t.x.WElbow)return{background:"none",pointerEvents:"none"};if(h===t.x.WVector||h===t.x.WTriangle||h===t.x.WTriangleB||h===t.x.WTriangleTL||h===t.x.WStar||h===t.x.WPolygon)return{background:"unset",boxShadow:"none",overflow:"unset"};if(h===t.x.WMind)return{color:"#101010"};if(h===t.x.WTear)return{background:"none"};if(h===t.x.WNavigationMenu)return{border:"none"};if(h===t.x.WDigitalStepper)return{border:"none",background:"none"};if(h===t.x.WSegmentedControl)return{background:"none"};if(h!=null&&h.startsWith("wFlow"))return{background:"none"}},g=u=>{const{x:h,y:D,r:O,w:Y,h:A,zIndex:F,opacity:G,fill:st}=u;return{position:"absolute",zIndex:F||"inherit",width:Y,height:A,transform:"translate("+(h-Y*.5)+"px,"+(D-A*.5)+"px) rotate("+O+"deg)",opacity:G,background:st?(0,k.n7)(u):void 0,...c(u),...n(u),...a(u),...x(u),...f(u)}},E=(u,h,D,O,Y)=>{const{type:A}=u;if(A===t.x.Canvas){const{asOverlay:F,fill:G}=u;return{left:0,top:0,overflow:"hidden",zIndex:"auto",...Y.locUpHotBasketKey(h)&&{pointerEvents:!G.fillIsVisible&&O.length===0?"none":"auto"},...!F&&{boxShadow:"none",borderRadius:"none",background:"none"}}}if(A===t.x.WWrap)return{pointerEvents:"none"};if(A===t.x.WCarousel)return{overflow:"hidden"}},j=(u,h,D,O,Y)=>{var A;const{zIndex:F,opacity:G,isVisible:st,fill:J,fixPosTo:nt}=u,{x:ot,y:rt,r:q,w:et,h:Z}=Y.getCCSBound2308(h),mt=Y.getHotItem(D),pt=(mt==null||(A=mt.hotAttr)==null?void 0:A.type)===t.x.Canvas,lt=nt===t.Q.FIXED_TYPE.top&&pt,tt=nt===t.Q.FIXED_TYPE.bottom&&pt,ut=lt||tt?"fixed":"absolute";let gt,wt,ft=ot-et/2;if(lt){if(gt=rt-Z/2,(0,o.OO)(h)){const N=Y.locUpCanvasKey(h);if(N){const it=Y.getHotItem(N);it&&(ft=ft+it.hotAttr.x-it.hotAttr.w/2)}}}else if(tt){const N=Y.locUpCanvasKey(h);if(N){const it=(0,M.Pb)(Y.getRelocateLocalBound(h,N)),ct=Y.getHotItem(N);ct&&(wt=ct.hotAttr.h-it[3],(0,o.OO)(h)&&(ft=ft+ct.hotAttr.x-ct.hotAttr.w/2))}}else gt=rt-Z/2;const Mt=O.some(N=>{let{interactionTrigger:it}=N;return it==="tap"||it==="click"});return{position:ut,top:gt,bottom:wt,left:ft,zIndex:F||"inherit",opacity:G,visibility:st?"visible":"hidden",width:et,height:Z,background:J?(0,k.n7)(u):void 0,transform:q?"rotate("+q+"deg)":"",cursor:Mt?"pointer":"",pointerEvents:"auto",...l(u),...c(u),...n(u),...a(u),...x(u),...f(u),...E(u,h,D,O,Y)}},Q=(u,h,D)=>{if(u.bunch===t.x.RbPage){const{bgcolor:rt,bgimage:q}=u;return{position:"absolute",width:"100%",height:"100%",backgroundSize:"100% auto",backgroundColor:rt,backgroundImage:q?"url("+q+")":""}}const{zIndex:O,opacity:Y,fill:A,isVisible:F}=u,{x:G,y:st,r:J,w:nt,h:ot}=D.getCCSBound2308(h);return{position:"absolute",zIndex:O||"inherit",width:nt,height:ot,transform:"translate("+(G-nt*.5)+"px, "+(st-ot*.5)+"px) rotate("+J+"deg)",transformOrigin:"center center",opacity:F?Y:0,background:A?(0,k.n7)(u):void 0,...!F&&{pointerEvents:"none"},...c(u),...n(u),...a(u),...x(u),...f(u)}}},37319:(d,S,i)=>{"use strict";i.d(S,{D:()=>M,c:()=>y});var m=i(21676),o=i(87954);const M=(0,m.Ay)(o.W).withConfig({displayName:"styles__StyledPanelScroll",componentId:"sc-13mo03b-0"})(["position:absolute;width:100%;height:100%;top:0;left:0;z-index:",";.handler{left:10%;width:80%;}.y-handler{top:10%;height:80%;}.x-track{.handler{.thumb{height:6px;background:#d2d2d8;}}}.y-track{.handler{.thumb{width:6px;background:#d2d2d8;}}}.track{z-index:",";}"],k=>k.zIndex,k=>k.zIndex+1),y=m.Ay.div.withConfig({displayName:"styles__StyledBasketRunner",componentId:"sc-13mo03b-1"})(["position:absolute;width:100%;height:100%;z-index:5;.state{position:absolute;width:100%;height:100%;z-index:5;background-size:100% auto;&.active{z-index:6;}}&.overFlowHide{overflow:hidden;}&.overFlowAuto{overflow:auto;overflow:overlay;}&.overFlowX{overflow-x:auto;overflow-x:overlay;overflow-y:hidden;}&.overFlowY{overflow-y:auto;overflow-y:overlay;overflow-x:hidden;}"])},29534:(d,S,i)=>{"use strict";i.d(S,{Z:()=>M,j:()=>y});const m=new Map;let o;const M=k=>{const{key:t,hotAttr:e,sub:s}=k;e.type==="rResBunch"&&e.bunch==="rbPage"&&(o=t,m.set(o,0)),o&&m.set(o,y(o)+s.length)},y=k=>m.get(k)||0},41449:(d,S,i)=>{"use strict";i.d(S,{Dw:()=>o,H:()=>y,QQ:()=>e,X9:()=>t,o$:()=>m});const m="/proto",o=p=>m+"/"+p,M=p=>{const n=/^\/proto\/design\/(?:([^/]+?))$/i.exec(p);return n?n[1]:new Error("project cid error")},y=p=>{let{search:n,pathname:a}=p;if(a.includes("/sharing"))return a.includes("dashboard")||a.includes("htmlzip")?a.split("/").at(-3):a.includes("embed/v2")?location.pathname.split("/").at(-4):a.split("/").at(-2);{const c=new URLSearchParams(n).get("token");if(c)return c;const w=/^\/proto\/([^/]+?)(?:\/forum|\/uichina|\/embed|\/embed\/v2|\/htmlzip|\/sharing|\/ro)?\/?$/i.exec(a);return w?w[1]:""}},k=p=>{let{pathname:n}=p;const a=/^\/proto\/design\/([^/]+)\/?$/i.exec(n);return a?a[1]:""},t=p=>{let{search:n,pathname:a}=p;return y({search:n,pathname:a})||k({pathname:a})},e="/p2mkt_view",s=p=>e+"/"+p,r=p=>/^\/p2mkt_view\/(?:([^/]+?))$/i.exec(p)[1]},44580:(d,S,i)=>{"use strict";i.d(S,{LN:()=>a,o0:()=>e,rR:()=>r,uT:()=>s});var m=i(53955),o=i(42781),M=i(43858),y=i(78869),k=i.n(y);const t=()=>MB.action?MB.__store__.dispatch:MB.webpackInterface.store.dispatch,e=function(c,w){w===void 0&&(w=sdkStore);const T=new Set;return w.walkHotItemSubtree2(c,C=>{C.hotAttr.mktCid&&T.add(C.hotAttr.mktCid)}),[...T]},s=k()(async c=>{let w=(0,m.fV)();if(!w)return{mdWMMktList:new Set,mtWMMktList:new Set,noWMMktList:new Set};const{mdWMMktList:T,mtWMMktList:C,noWMMktList:x}=w,V=c.filter(j=>!T.has(j)&&!C.has(j)&&!x.has(j)),l=await(0,o.E5)(V);w=(0,m.fV)();const f=new Set([...w.mdWMMktList].concat(l.mdWMMktList)),g=new Set([...w.mtWMMktList].concat(l.mtWMMktList)),E=new Set([...w.noWMMktList].concat(l.noWMMktList));return t()({type:"reducer:watermark:update-state",payload:{mdWMMktList:f,mtWMMktList:g,noWMMktList:E}}),{mdWMMktList:f,mtWMMktList:g,noWMMktList:E}},1e3),r=()=>{const c=(0,m.fV)();if(!c)return;const{mdWMMktList:w,mtWMMktList:T,noWMMktList:C}=c,x=new Set([...w]),V=new Set([...T]),l=new Set([...C]),f=[x,V,l];let g=!1;return f.forEach(E=>{for(const j of E)M.Ay.mktCid.has(j)||(E.delete(j),(0,o.g7)(j),g=!0)}),g&&t()({type:"reducer:watermark:update-state",payload:{mdWMMktList:x,mtWMMktList:V,noWMMktList:l}}),{mdWMMktList:x,mtWMMktList:V,noWMMktList:l}},p=async c=>{const w=await splitMktListByCid(c),T=new Set(w.mdWMMktList),C=new Set(w.mtWMMktList),x=new Set(w.noWMMktList);return t()({type:"reducer:watermark:update-state",payload:{mdWMMktList:T,mtWMMktList:C,noWMMktList:x}}),{mdWMMktList:T,mtWMMktList:C,noWMMktList:x}},n=c=>{const w=new Set,T=new Set,C=new Set(c);t()({type:"reducer:watermark:update-state",payload:{mdWMMktList:w,mtWMMktList:T,noWMMktList:C}})},a=c=>{const w=new Set((c==null?void 0:c.md_vip_mkt_list)||[]),T=new Set((c==null?void 0:c.mt_vip_mkt_list)||[]),C=new Set((c==null?void 0:c.no_wm_mkt_list)||[]);t()({type:"reducer:watermark:update-state",payload:{mdWMMktList:w,mtWMMktList:T,noWMMktList:C}})}},42276:(d,S,i)=>{"use strict";var m=i(26114),o=i(94947),M=i(13005),y=i(56656);const k={"sk-bouncedelay":"ZdD58bXGNcNoQjiPeXLu"},t={"sk-bouncedelay":"ZBrU7Yes6CevGkk6hArA"};i(16708),i(85991),i(97381);var e=i(74059),s=i(51044),r=i(46479),p=i(45982),n=i(72605),a=i(56096),c=i(54852),w=i(55793),T=i(39271),C=i(49269),x=i(57464);class V extends s.PureComponent{constructor(v){super(v),(0,e.A)(this,"handleGetViewMode",()=>{var L;const{store:K,isHtmlZip:$}=this.props,ht=new URLSearchParams(location.search).get("view_mode");return $?K==null||(L=K.getState())==null||(L=L.editor)==null||(L=L.state)==null||(L=L.mode)==null?void 0:L.previewViewMode:ht});const{store:_,projectBasic:W,projectMeta:I,isHtmlZip:X}=v,H=new URLSearchParams(location.search);let U="",b="";const P=H.get("view_mode");if(["read_only","inspect"].includes(P)){var z;U=H.get("screen")||(_==null||(z=_.getState())==null||(z=z.current.currentScreen)==null?void 0:z.cid)||"",b=H.get("canvasId")||""}this.state={visibleIsEmpty:!1};try{const L=(0,T.rV)(U);_.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:W,projectMeta:I,screenCid:L,canvasCid:b,isHtmlZip:X}})}catch(L){this.state={visibleIsEmpty:!0}}}componentDidMount(){this.props.onComponentDidMount(),window.addEventListener("message",C.a,!1),this.handleGetViewMode()==="inspect"&&MB.__store__.dispatch({type:"entry:handle-tool:toggle:inspect-mode",payload:{mode:!0}})}componentWillUnmount(){window.removeEventListener("message",C.a,!1)}render(){const{store:v}=this.props;return this.state.visibleIsEmpty?(0,w.V)():(0,x.jsx)(n.Kq,{store:v,children:(0,x.jsx)(c.A,{children:(0,x.jsx)(a.A,{})})})}}var l=i(82218);const f=async R=>{let{projectMetaCid:v}=R;await(0,l.F2)({userId:void 0,flpakKey:v,onTransferError:l.z0,onReadOnlyError:l.sH,isDummyCmt:!0,__fetchFlpakAsync:o.BD},void 0)};var g=i(98148);const E=function(R,v,_){const W=v.getGuidesData()||"[]",I=new Set(JSON.parse(W)),X=new Set,H=new Map,U={checkGuideHasShown:b=>ENV.IS_ON_PREMISES||I.has(b),checkGuideToBeShown:b=>X.has(b),getState:()=>({commonGuidesHasShown:Array.from(I),commonGuidesToBeShown:Array.from(X),mountGuidesMap:H}),tryToShowGuide:b=>{var P;if(I.has(b)||!((P=MB)!=null&&(P=P.user)!=null&&P.id)||ENV.IS_ON_PREMISES)return;const z=document.getElementById("MD_"+b);if(z){z.style.display="block";return}X.add(b),(0,g.o)(b,R,_)},markGuideAsRead:b=>{I.add(b),v.setGuidesData(JSON.stringify(Array.from(I)))},batchMarkGuideAsRead:b=>{b.forEach(P=>I.add(P)),v.setGuidesData(JSON.stringify(Array.from(I)))},closeAndMarkGuideAsRead:b=>{U.checkGuideHasShown(b)||(U.closeGuide(b),U.markGuideAsRead(b))},closeGuide:b=>{const P=document.getElementById("MD_"+b);P&&P.parentNode&&P.parentNode.removeChild(P)},closeAllGuide:()=>{const b=document.getElementById("GUIDES");b&&b.parentNode&&b.parentNode.removeChild(b)},removeGuide:b=>{I.delete(b),v.setGuidesData(JSON.stringify(Array.from(I)))},tryToContinueMountGuide:(b,P)=>{const z=H.get(b);z&&typeof(z==null?void 0:z.handleContinue)=="function"&&z.handleContinue(P)},tryToResizeMountGuide:b=>{const P=H.get(b);P&&typeof(P==null?void 0:P.handleResize)=="function"&&P.handleResize()},tryToCloseMountGuide:b=>{const P=H.get(b);P&&typeof(P==null?void 0:P.handleClose)=="function"&&P.handleClose()},registerMountGuideCallback:(b,P)=>{const z=H.get(b)||{};H.set(b,{...z,...P})},removeMountGuideCallback:(b,P)=>{if(!P){H.delete(b);return}const z=H.get(b)||{};H.set(b,{...z,[P]:void 0})}};return U},j=R=>{let{store:v,commonGuidesData:_,isDesign:W,isPreview:I}=R;MB.commonGuideHelper=E(v,{getGuidesData:()=>_,setGuidesData:updateUserCommonGuidesData},{isDesign:W,isPreview:I})},Q=R=>{let{store:v,isDesign:_,isPreview:W}=R;const I={guide:{dashboard_guides:"",workspace_guides:"",square_guides:null,workspace_guides_version:"",default_team_cid:"",root_project_team_cid:"",workspace_guides_2203:"",workspace_guides_2203_version:"",aboard_guides:""},guide_project_template_cid:"",guide_project_template_2203_cid:"",guide_flow_template_cid:"",guide_design_template_cid:"",guide_prototype_template_cid:""},{guide:{workspace_guides:X}}=I;MB.commonGuideHelper=E(v,{getGuidesData:()=>X,setGuidesData:()=>{}},{isDesign:_,isPreview:W})};var u=i(77757),h=i(25058),D=i(67306),O=i(42226),Y=i(68673),A=i(29601),F=i(79619);const G=R=>{let{initialData:v,designOptions:_,global:W,onUpdateSharing:I}=R;const{user:X,org:H,team:U,preference:b,orgList:P,user_fcg:z,org_fcg:L,space:K,canEditByCurrentUser:$}=v,{currentUser:at,currentOrg:ht}=(0,F.n)({user:X,org:H});(0,O.kZ)((0,A.w)()),Object.assign(MB,{user:at||{},orgList:P,currentOrg:ht,canEditByCurrentUser:$,global:{...W,popupHelper:(0,h.L)(),experienceLoginModalHelper:u.F}}),MB.action("current:update:state",{currentOrg:ht,currentTeam:U,currentUser:at,orgList:P,user_fcg:z,org_fcg:L,currentSpace:K}),MB.action("update:design:options",{designOptions:_}),X!=null&&X.id&&MB.action("entry:init:preference",{preference:b}),I&&I()},st=async R=>{let{designStore:v}=R,_="[]";try{const{guide:W}=await requestUserCommonGuidesData();_=W==null?void 0:W.workspace_guides}catch(W){console.error("\u83B7\u53D6 guide_data \u5931\u8D25")}await initCommonGuide({store:v,commonGuidesData:_,isDesign:!0,isPreview:!1})},J=async R=>{let{type:v,projectBasicCid:_}=R;switch(v){case DesignType.Design:return requestProjectFullDataFlat(_);case DesignType.Experience:return requestExperienceFullDataFlat(_);default:throw new Error(v+" not found in known design types for "+_)}};var nt=i(45307),ot=i(64334),rt=i(19332),q=i(12201),et=i(9376);const Z=R=>{const{isEmbedV1:v=!1}=R||{};return{isDesktop:!((0,et.Fr)()||v),isInApp:!1,isMultiLink:!1,isMobile:(0,et.Fr)(),isIOSClient:(0,et.Hv)(),isStandAlone:(0,et.Gd)(),isEmbedV1:v,isEmbedV2:!1,isHTMLZip:!1,isUIChina:(0,et.II)(),isForum:(0,et.uf)(),isElectron:(0,et.b8)(),isSquare:!1,isFeishu:(0,et.Fl)(),isWechat:(0,et.vq)(),...R}},mt=R=>{const{isMobile:v,isEmbedV2:_,isSquare:W}=R;return v?rt.A:_||W?ot.A:q.A};var pt=i(44580);const lt=R=>{let{designOptions:v,previewOptions:_,initialData:W,WMListConfig:I,onComponentDidMount:X,sharing:H}=R,U=!1;const b=(0,p.s)(MB);G({initialData:W,designOptions:v,global:{designOptions:v,previewOptions:_}}),(0,pt.LN)(I);const{isIOSClient:P,isStandAlone:z}=_;(z||P)&&(document.documentElement.style.height="100vh");const{runnerController:L,messageBucket:K,webpackInterface:$,PreviewApp:at}=(0,nt._)(mt(_));return Object.assign(MB,{runnerController:L,messageBucket:K,webpackInterface:$,global:{...MB.global||{},popupHelper:(0,h.L)(),designOptions:v,previewOptions:_}}),MB.webpackInterface.store.dispatch({type:"preview:update:state",payload:{previewOptions:_}}),{requestData:async ht=>{const{user:yt,org:Pt,preference:Bt,project_basic:kt,project_meta:Dt}=W;return Q({store:b,isDesign:!0,isPreview:!1}),Object.assign(MB,{tag:kt==null?void 0:kt.version}),MB.action("update:design:options",{designOptions:MB.global.designOptions}),await f({projectMetaCid:Dt.cid}),MB.webpackInterface.store.dispatch({type:"preview:set:current-projectShare",payload:{projectShare:H}}),MB.webpackInterface.store.dispatch({type:"entry:init:preview:preference",payload:{preference:Bt||{}}}),MB.action("entry:handle-tool:preview:view_mode",{previewViewMode:ht}),Object.assign(MB,{user:yt||{},canEditByCurrentUser:!1,org:Pt||void 0}),MB.webpackInterface.store.dispatch({type:"entry:state:init"}),U=!0,{projectAlike:kt,projectMeta:Dt,projectShare:H}},renderDesign:ht=>{let{projectAlike:yt,projectMeta:Pt}=ht;return U?(0,x.jsx)(V,{store:b,projectBasic:yt,projectMeta:Pt,onComponentDidMount:X,isHtmlZip:!0}):null},renderPreview:ht=>{let{projectAlike:yt,projectMeta:Pt,projectShare:Bt}=ht;return U?((0,w.X)(Bt),(0,x.jsx)(at,{store:$.store,previewMode:"preview",projectAlike:yt,projectMeta:Pt,projectShare:Bt})):null}}};var tt=i(21676);const ut=tt.Ay.div.withConfig({displayName:"styles__StyledExampleApp",componentId:"sc-1r36j4k-0"})(["&{position:absolute;top:0;left:0;bottom:0;right:0;transition:all 0.2s ease-in-out;.loading-container{position:absolute;top:50%;left:50%;.mb-loading{width:80px;height:80px;margin-top:-40px;margin-left:-40px;}.chris-icon{position:absolute;left:68%;top:-8%;}}}.mb-design-page{height:100%;&,#mb-toolbar{transition:all 0.2s ease-in-out;}&.is-collapse{opacity:0;z-index:0;#mb-toolbar{transform:translateY(-100%);}.mb-right-panel{transform:translateX(100%);}}}.mb-preview-page{position:absolute;top:0;left:0;width:100%;bottom:0;font-size:12px;transition:all 0.2s ease-in-out;&.is-collapse{opacity:0;z-index:0;}}"]),gt=tt.Ay.div.withConfig({displayName:"styles__StyledROExampleApp",componentId:"sc-1r36j4k-1"})(["&{position:absolute;top:0;left:0;bottom:0;right:0;transition:all 0.2s ease-in-out;.loading-container{position:absolute;top:50%;left:50%;.mb-loading{width:80px;height:80px;margin-top:-40px;margin-left:-40px;}.chris-icon{position:absolute;left:68%;top:-8%;}}}.mb-design-page{height:100%;position:relative;z-index:1;&.is-collapse{opacity:0;z-index:0;}}.mb-preview-page{position:absolute;top:0;left:0;width:100%;bottom:0;font-size:12px;&.is-collapse{opacity:0;z-index:0;}}"]);var wt=i(1694),ft=i(21952),Mt=i(86634),N=i.n(Mt);class it extends s.PureComponent{render(){const{isCollapse:v,renderReadOnly:_,projectAlike:W,projectMeta:I}=this.props;return(0,x.jsx)("div",{ref:X=>this.$element=X,className:N()("mb-design-page",{"is-collapse":v}),children:_({projectAlike:W,projectMeta:I})})}}class ct extends s.PureComponent{constructor(v){super(v),(0,e.A)(this,"setElementRef",_=>this.$element=_),MB.f.inPreview=!v.isCollapse}componentDidMount(){this.$element.addEventListener("wheel",this.handleWheel,{passive:!1})}componentWillUnmount(){MB.f.inPreview=!1,this.$element.removeEventListener("wheel",this.handleWheel)}handleWheel(v){(v.ctrlKey||v.metaKey)&&v.preventDefault()}render(){const{isCollapse:v,renderPreview:_,projectAlike:W,projectMeta:I,projectShare:X}=this.props;return(0,x.jsx)("div",{ref:this.setElementRef,className:N()("mb-preview-page",{"is-collapse":v}),children:_({projectAlike:W,projectMeta:I,projectShare:X})})}}var xt=i(51387),Et=i(14941),_t=i(86431),bt=i(24013),Tt=i(31900),It=i(15139),At=i(40684),vt=i(76445),Ct=i(6637);const Lt=(R,v)=>{const{current:{currentProject:_},ui:{toolbar:{isShowSticky:W},leftLayout:{leftPanel:{show:I,width:X},directory:{screen:{currentPage:H},bottom:{height:U},screen:{screenCollapseKeySet:b}}},rightLayout:{rightPanel:{show:P,resizedWidth:z}},fixedLayout:{modals:{previewCanvasModal:L,previewModal:K}}},editor:{state:{mode:{fullScreenMode:$}}}}=MB.__store__.getState(),at=()=>{MB.__store__.dispatch({type:"entry:handle-tool:preview:view_mode",payload:{previewViewMode:"device"}});const{pageKey:ht,canvasKey:yt}=It.A.validateAndTryCorrectPGAndCVKey(H.key,R,null,_.category===At.t_);MB.webpackInterface.store.dispatch({type:"entry:runner:activate:canvas",payload:{pageKey:ht,canvasKey:yt,saveHistory:!1,forceInit:!0,keepScale:!1}}),MB.webpackInterface.store.dispatch({type:"preview:set:leftPaneWidth",payload:{leftPaneWidth:X!=null?X:250}}),MB.webpackInterface.store.dispatch({type:"preview:update:state",payload:{hasChangedScaleManually:!1}}),MB.webpackInterface.store.dispatch({type:"preview:panel:set:height",payload:{panelHeight:U}}),MB.webpackInterface.store.dispatch({type:"preview:set:rightPaneWidth",payload:{rightPaneWidth:z,projectCid:_.cid}}),$&&MB.webpackInterface.store.dispatch({type:"reducer:preview-toolbar:fullscreen",payload:{isFullScreenMode:!0}}),MB.webpackInterface.store.dispatch({type:"ST:update",payload:{STMode:W}}),MB.webpackInterface.store.dispatch({type:"entry:preview-setting:toggle:isMinimized",payload:{isMinimized:!I,isMemorize:!$}}),MB.webpackInterface.store.dispatch({type:"entry:preview-setting:toggle:rightPane",payload:{isShowRightPane:P,isMemorize:!$}}),MB.webpackInterface.store.dispatch({type:"preview:set:collapsedRBPageKeySet",payload:{collapsedRBPageKeySet:b}}),MB.webpackInterface.store.dispatch({type:"entry:preview:toggle:isHiddenToolBar",payload:{value:!1}}),MB.action("modal:reset:state"),MB.action("entry:exit:edit:basket"),MB.__store__.dispatch({type:"entry:comment:disabled",payload:{noWigglingCommentCid:!0}}),MB.__store__.dispatch({type:"versions:update",payload:{isVersionManagementPaneShow:!1}}),MB.__store__.dispatch(vt.GO.toggleInspectMode(!1)),MB.__store__.dispatch(vt.GO.toggleHandMode(!1)),MB.__store__.dispatch(vt.GO.select([])),MB.__store__.dispatch({type:"elbow:update",payload:{isInElbowMode:!1}}),MB.__store__.dispatch({type:"entry:ST:STMode:disable"}),MB.__store__.dispatch({type:"ST:clear"})};L!=null&&L.isOpen||K!=null&&K.isOpen?(MB.action("modal:reset:preview:modal:state"),setTimeout(()=>{at(),v==null||v()},600)):(at(),v==null||v())},Ot=(R,v,_)=>{const{container:{current:{rootProject:W},common:{leftPaneWidth:I,panelHeight:X,rightPaneWidth:H,collapsedRBPageKeySet:U},runner:{activePageKey:b},previewSetting:{isFullScreenMode:P,isMinimized:z,isShowRightPane:L}},ST:{STMode:K}}=MB.webpackInterface.store.getState(),{ui:{leftLayout:{directory:{screen:{currentPage:$}}}},editor:{state:{mode:{isHtmlZipPreview:at}}}}=MB.__store__.getState();_==="inspect"?(MB.__store__.dispatch({type:"entry:comment:disabled"}),MB.__store__.dispatch(vt.GO.toggleInspectMode(!1)),MB.__store__.dispatch(vt.GO.toggleHandMode(!1)),MB.__store__.dispatch(vt.GO.select([]))):($.key!==b&&MB.__store__.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:R,projectMeta:v,screenCid:b,isHtmlZip:at}}),MB.__store__.dispatch({type:"screen:add:collapseKeySet",payload:{refs:U,projectCid:W.cid}}),P&&MB.__store__.dispatch(vt.GO.toggleFullScreenMode(!0)),MB.webpackInterface.store.dispatch({type:"entry:runner:cleanup:audio"}),MB.webpackInterface.store.dispatch({type:"runner:reset:state:except:history"}),MB.webpackInterface.store.dispatch({type:"container:comment:reset:state"}),MB.webpackInterface.store.dispatch({type:"preview:previewPanel:navindex",payload:{previewPanelNavIndex:0}}),MB.__store__.dispatch({type:"left-panel:set:width",payload:{width:I,projectCid:W.cid}}),MB.__store__.dispatch({type:"directory-panel:bottom:set:height",payload:{height:X}}),MB.__store__.dispatch({type:"right-side-panel:update:resize:width",payload:{width:H,projectCid:W.cid}}),MB.__store__.dispatch({type:"right-side-panel:collapse:panel",payload:{show:L,isMemorize:!P}}),MB.__store__.dispatch({type:"left-panel:collapse:panel:show",payload:{show:!z,isMemorize:!P}}),MB.__store__.dispatch({type:"entry:observe:dom:size:update-viewport-rect"}),MB.__store__.dispatch({type:"entry:isShowSticky:toggle",payload:{isShowSticky:K}}),requestAnimationFrame(()=>MB.__store__.dispatch({type:"entry:update:viewport:rect"})),MB.webpackInterface.store.dispatch({type:"entry:comment:disabled"}),(0,Ct.RF)(b))},Xt=(R,v,_)=>{if(_!=="read_only"){const{container:{current:{rootProject:W},common:{leftPaneWidth:I,panelHeight:X,rightPaneWidth:H,collapsedRBPageKeySet:U},runner:{activePageKey:b},previewSetting:{isFullScreenMode:P,isMinimized:z,isShowRightPane:L}},ST:{STMode:K}}=MB.webpackInterface.store.getState(),{ui:{leftLayout:{directory:{screen:{currentPage:$}}}},editor:{state:{mode:{isHtmlZipPreview:at}}}}=MB.__store__.getState();$.key!==b&&MB.__store__.dispatch({type:"entry:root-project:readOnly:activate",payload:{projectBasic:R,projectMeta:v,screenCid:b,isHtmlZip:at}}),MB.__store__.dispatch({type:"screen:add:collapseKeySet",payload:{refs:U,projectCid:W.cid}}),P&&MB.__store__.dispatch(vt.GO.toggleFullScreenMode(!0)),MB.webpackInterface.store.dispatch({type:"entry:runner:cleanup:audio"}),MB.webpackInterface.store.dispatch({type:"runner:reset:state:except:history"}),MB.webpackInterface.store.dispatch({type:"container:comment:reset:state"}),MB.webpackInterface.store.dispatch({type:"preview:previewPanel:navindex",payload:{previewPanelNavIndex:0}}),MB.__store__.dispatch({type:"left-panel:set:width",payload:{width:I,projectCid:W.cid}}),MB.__store__.dispatch({type:"directory-panel:bottom:set:height",payload:{height:X}}),MB.__store__.dispatch({type:"right-side-panel:update:resize:width",payload:{width:H,projectCid:W.cid}}),MB.__store__.dispatch({type:"right-side-panel:collapse:panel",payload:{show:L,isMemorize:!P}}),MB.__store__.dispatch({type:"left-panel:collapse:panel:show",payload:{show:!z,isMemorize:!P}}),MB.__store__.dispatch({type:"entry:observe:dom:size:update-viewport-rect"}),MB.__store__.dispatch({type:"entry:isShowSticky:toggle",payload:{isShowSticky:K}}),requestAnimationFrame(()=>{MB.__store__.dispatch({type:"entry:update:viewport:rect"})}),(0,Ct.RF)(b)}MB.__store__.dispatch({type:"entry:handle-tool:toggle:inspect-mode",payload:{mode:!0}})};class Rt extends s.PureComponent{constructor(v){super(v),(0,e.A)(this,"handleWheelChanged",L=>{if(L.deltaX===0)return;const K=document.getElementById("workspace"),$=K.scrollLeft+K.offsetWidth===K.scrollWidth&&L.deltaX>0,at=K.scrollLeft===0&&L.deltaX<0;($||at)&&L.preventDefault()}),(0,e.A)(this,"handleDesignDidMount",()=>{MB.reduxEntry.setEntryMap(xt.T),this.setState({isLoading:!1})}),(0,e.A)(this,"keyboardManager",new wt.Rr);const{viewMode:_,designOptions:W,previewOptions:I,initialData:X,WMListConfig:H,sharing:U}=v;MB.switchToPreview=L=>{this.state.viewMode!=="device"&&((0,bt.N8)("device"),Lt(L),this.setState({viewMode:"device"}))},MB.switchToReadOnly=()=>{if(this.state.viewMode==="read_only")return;const{projectAlike:L,projectMeta:K,viewMode:$}=this.state;(0,bt.N8)("read_only"),Ot(L,K,$),this.setState({viewMode:"read_only"})},MB.switchToInspect=()=>{if(this.state.viewMode==="inspect")return;const{projectAlike:L,projectMeta:K,viewMode:$}=this.state;(0,bt.N8)("inspect"),Xt(L,K,$),this.setState({viewMode:"inspect"})},this.state={isLoading:!0,isDataLoaded:!1,viewMode:_,projectAlike:null,projectMeta:null,projectShare:void 0};const{requestData:b,renderDesign:P,renderPreview:z}=lt({initialData:X,WMListConfig:H,designOptions:W,previewOptions:I,onComponentDidMount:this.handleDesignDidMount,sharing:U});this.requestData=b,this.renderDesign=P,this.renderPreview=z}componentDidMount(){Promise.all([this.requestData(this.state.viewMode)]).then(v=>{let[{projectAlike:_,projectMeta:W,projectShare:I}]=v;this.setState({projectAlike:_,projectMeta:W,projectShare:I},()=>{this.setState({isDataLoaded:!0})});const X=document.getElementById("workspace");X&&X.addEventListener("wheel",this.handleWheelChanged)})}componentWillUnmount(){const v=document.getElementById("workspace");v&&v.removeEventListener("wheel",this.handleWheelChanged,{capture:!0})}render(){const{isLoading:v,isDataLoaded:_,viewMode:W,projectAlike:I,projectMeta:X,projectShare:H}=this.state,{renderDesign:U,renderPreview:b}=this;return(0,x.jsx)(Tt.G.Provider,{value:W,children:(0,x.jsx)(wt.M.Provider,{value:this.keyboardManager,children:(0,x.jsx)(ft.$,{children:(0,x.jsxs)(gt,{className:"example-app",children:[_&&(0,x.jsx)(it,{isCollapse:!["read_only","inspect"].includes(W),projectAlike:I,projectMeta:X,renderReadOnly:U}),_&&(0,x.jsx)(ct,{isCollapse:W!=="device",projectAlike:I,projectMeta:X,projectShare:H,renderPreview:b}),v&&(0,x.jsxs)("div",{className:"loading-container",children:[(0,x.jsx)(r.Tw,{className:"mb-loading"}),(0,_t.X)()&&(0,x.jsx)(Et.n,{className:"chris-icon"})]})]})})})})}}var jt=i(98794),Yt=i(88223),Ht=i(10286),zt=i(36429);i.p="./mb-proto2/";const Ft=document.getElementById("workspace"),Nt=async()=>{var R,v;const _=Z({isHTMLZip:!0});(0,et.Fr)()&&((R=document.querySelector("body"))==null||R.classList.add("mobile"));let W=await(0,o.Rz)();const{projectUpper:I,projectMeta:X}=W;W={...W,projectName:I.name,project_basic:I,project_meta:X};const{sharing:H}=await(0,Ht.J_)({project:I}),U=await(0,o.CS)(),{generateLang:b}=await zt.qu.loadLLG();await b(),document.title=""+(0,Yt.qk)(I.name||I.title),!ENV.IS_ON_PREMISES&&(0,jt.I2)();const P=(v=new URLSearchParams(location.search))==null?void 0:v.get("view_mode"),z=window.HZv5_PREVIEW_MODE||"",L=P||z,K=["device","read_only","inspect"].includes(L)?L:"read_only",$={isDesign:!0,isExperience:!1,isLoggedExperience:!1};(0,m.createRoot)(Ft).render((0,x.jsx)(Rt,{initialData:W,WMListConfig:U,viewMode:K,designOptions:$,previewOptions:_,sharing:H}))};document.addEventListener("DOMContentLoaded",async()=>{await Nt()})},9376:(d,S,i)=>{"use strict";i.d(S,{Bd:()=>f,Fl:()=>t,Fr:()=>n,Gd:()=>C,H8:()=>s,Hv:()=>T,II:()=>x,Md:()=>l,VK:()=>Q,b8:()=>M,cX:()=>y,gm:()=>r,lg:()=>g,m0:()=>a,nr:()=>p,uF:()=>k,uf:()=>V,un:()=>w,vq:()=>e});var m,o;const M=()=>window.MB_DESKTOP_VERSION||window.isElectron||/Electron/i.test(navigator.userAgent),y=()=>/(Macintosh)/i.test(navigator.userAgent),k=()=>/(Windows)/i.test(navigator.userAgent),t=()=>/(Lark)/i.test(navigator.userAgent),e=()=>/MicroMessenger/i.test(navigator.userAgent),s=()=>/Chrome/i.test(navigator.userAgent),r=()=>/Firefox/i.test(navigator.userAgent),p=()=>/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent),n=()=>{const u=new URLSearchParams(location.search);return u&&u.get("inapp")==="1"&&(i.g.isInAPP=!0),/(iPod|iPhone|iPad|Android|MobileClient)/.test(navigator.userAgent)||window.isInAPP},a=()=>/Android/i.test(navigator.userAgent),c=()=>/(iPad)/.test(navigator.userAgent),w=()=>/(iPod|iPhone|iPad)/.test(navigator.userAgent),T=()=>!!(window.webkit&&window.webkit.messageHandlers),C=()=>navigator.standalone,x=()=>/\/uichina$/.test(location.pathname),V=()=>/\/forum$/.test(location.pathname),l=()=>/(MobileClient)/.test(navigator.userAgent),f=!!(typeof window<"u"&&window.document&&window.document.createElement),g=/(mac|iphone|ipod|ipad)/i.test(typeof navigator<"u"?((m=navigator)==null?void 0:m.platform)||((o=navigator)==null||(o=o.userAgentData)==null?void 0:o.platform):""),E=()=>{var u;if(!((u=navigator)!=null&&u.plugins))return!1;for(const h in navigator.plugins)if(navigator.plugins[h]&&navigator.plugins[h].filename==="np-mswmp.dll")return!0;return!1},j=u=>{u=u.toLowerCase();const h=u.includes("qihu"),D=u.includes("360se"),O=u.includes("360ee");return h&&(D||O)},Q=()=>{let u,h;const D=navigator.userAgent;if(h=void 0,u=D.match(/(opera|chrome|qq|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i)||[],/trident/i.test(u[1]))return h=/\brv[ :]+(\d+)/g.exec(D)||[],{name:"IE",version:parseFloat(h[1]||"")};if(u[1]==="Chrome"){if(j(D)||E())return{name:"360Browser",version:"unknown"};if(/Edg/i.test(D))return h=D.match(/Edg\/(\d+)/i),{name:"Edge",version:h?parseFloat(h[1]):"unknown"};if(h=D.match(/\b(OPR|Edge)\/(\d+)/),h!==null)return{name:h[1].replace("OPR","Opera"),version:parseFloat(h[2])}}return/QQBrowser/i.test(D)?(h=D.match(/QQBrowser\/(\d+)/i),{name:"QQBrowser",version:h?parseFloat(h[1]):"unknown"}):(u=u[2]?[u[1],u[2]]:[navigator.appName,navigator.appVersion,"-?"],(h=D.match(/version\/(\d+)/i))!==null&&u.splice(1,1,h[1]),{name:u[0],version:parseFloat(u[1])})}},85991:()=>{/*! iScroll v5.2.0-snapshot ~ (c) 2008-2017 Matteo Spinelli ~ http://cubiq.org/license */(function(d,S,i){var m=d.requestAnimationFrame||d.webkitRequestAnimationFrame||d.mozRequestAnimationFrame||d.oRequestAnimationFrame||d.msRequestAnimationFrame||function(t){d.setTimeout(t,16.666666666666668)},o=function(){var t={},e=S.createElement("div").style,s=function(){for(var n=["t","webkitT","MozT","msT","OT"],a,c=0,w=n.length;c<w;c++)if(a=n[c]+"ransform",a in e)return n[c].substr(0,n[c].length-1);return!1}();function r(n){return s===!1?!1:s===""?n:s+n.charAt(0).toUpperCase()+n.substr(1)}t.getTime=Date.now||function(){return new Date().getTime()},t.extend=function(n,a){for(var c in a)n[c]=a[c]},t.addEvent=function(n,a,c,w){n.addEventListener(a,c,!!w)},t.removeEvent=function(n,a,c,w){n.removeEventListener(a,c,!!w)},t.prefixPointerEvent=function(n){return d.MSPointerEvent?"MSPointer"+n.charAt(7).toUpperCase()+n.substr(8):n},t.momentum=function(n,a,c,w,T,C){var x=n-a,V=i.abs(x)/c,l,f;return C=C===void 0?6e-4:C,l=n+V*V/(2*C)*(x<0?-1:1),f=V/C,l<w?(l=T?w-T/2.5*(V/8):w,x=i.abs(l-n),f=x/V):l>0&&(l=T?T/2.5*(V/8):0,x=i.abs(n)+l,f=x/V),{destination:i.round(l),duration:f}};var p=r("transform");return t.extend(t,{hasTransform:p!==!1,hasPerspective:r("perspective")in e,hasTouch:"ontouchstart"in d,hasPointer:!!(d.PointerEvent||d.MSPointerEvent),hasTransition:r("transition")in e}),t.isBadAndroid=function(){var n=d.navigator.appVersion;if(/Android/.test(n)&&!/Chrome\/\d/.test(n)){var a=n.match(/Safari\/(\d+.\d)/);return a&&typeof a=="object"&&a.length>=2?parseFloat(a[1])<535.19:!0}else return!1}(),t.extend(t.style={},{transform:p,transitionTimingFunction:r("transitionTimingFunction"),transitionDuration:r("transitionDuration"),transitionDelay:r("transitionDelay"),transformOrigin:r("transformOrigin"),touchAction:r("touchAction")}),t.hasClass=function(n,a){var c=new RegExp("(^|\\s)"+a+"(\\s|$)");return c.test(n.className)},t.addClass=function(n,a){if(!t.hasClass(n,a)){var c=n.className.split(" ");c.push(a),n.className=c.join(" ")}},t.removeClass=function(n,a){if(t.hasClass(n,a)){var c=new RegExp("(^|\\s)"+a+"(\\s|$)","g");n.className=n.className.replace(c," ")}},t.offset=function(n){for(var a=-n.offsetLeft,c=-n.offsetTop;n=n.offsetParent;)a-=n.offsetLeft,c-=n.offsetTop;return{left:a,top:c}},t.preventDefaultException=function(n,a){for(var c in a)if(a[c].test(n[c]))return!0;return!1},t.extend(t.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),t.extend(t.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(n){return n*(2-n)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(n){return i.sqrt(1- --n*n)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(n){var a=4;return(n=n-1)*n*((a+1)*n+a)+1}},bounce:{style:"",fn:function(n){return(n/=1)<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375}},elastic:{style:"",fn:function(n){var a=.22,c=.4;return n===0?0:n==1?1:c*i.pow(2,-10*n)*i.sin((n-a/4)*(2*i.PI)/a)+1}}}),t.tap=function(n,a){var c=S.createEvent("Event");c.initEvent(a,!0,!0),c.pageX=n.pageX,c.pageY=n.pageY,n.target.dispatchEvent(c)},t.click=function(n){var a=n.target,c;/(SELECT|INPUT|TEXTAREA)/i.test(a.tagName)||(c=S.createEvent(d.MouseEvent?"MouseEvents":"Event"),c.initEvent("click",!0,!0),c.view=n.view||d,c.detail=1,c.screenX=a.screenX||0,c.screenY=a.screenY||0,c.clientX=a.clientX||0,c.clientY=a.clientY||0,c.ctrlKey=!!n.ctrlKey,c.altKey=!!n.altKey,c.shiftKey=!!n.shiftKey,c.metaKey=!!n.metaKey,c.button=0,c.relatedTarget=null,c._constructed=!0,a.dispatchEvent(c))},t.getTouchAction=function(n,a){var c="none";return n==="vertical"?c="pan-y":n==="horizontal"&&(c="pan-x"),a&&c!="none"&&(c+=" pinch-zoom"),c},t.getRect=function(n){if(n instanceof SVGElement){var a=n.getBoundingClientRect();return{top:a.top,left:a.left,width:a.width,height:a.height}}else return{top:n.offsetTop,left:n.offsetLeft,width:n.offsetWidth,height:n.offsetHeight}},t}();function M(t,e){this.wrapper=typeof t=="string"?S.querySelector(t):t,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={resizeScrollbars:!0,mouseWheelSpeed:20,snapThreshold:.334,disablePointer:!o.hasPointer,disableTouch:o.hasPointer||!o.hasTouch,disableMouse:o.hasPointer||o.hasTouch,startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0,bindToWrapper:typeof d.onmousedown>"u"};for(var s in e)this.options[s]=e[s];this.translateZ=this.options.HWCompositing&&o.hasPerspective?" translateZ(0)":"",this.options.useTransition=o.hasTransition&&this.options.useTransition,this.options.useTransform=o.hasTransform&&this.options.useTransform,this.options.eventPassthrough=this.options.eventPassthrough===!0?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY=this.options.eventPassthrough=="vertical"?!1:this.options.scrollY,this.options.scrollX=this.options.eventPassthrough=="horizontal"?!1:this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing=typeof this.options.bounceEasing=="string"?o.ease[this.options.bounceEasing]||o.ease.circular:this.options.bounceEasing,this.options.resizePolling=this.options.resizePolling===void 0?60:this.options.resizePolling,this.options.tap===!0&&(this.options.tap="tap"),!this.options.useTransition&&!this.options.useTransform&&(/relative|absolute/i.test(this.scrollerStyle.position)||(this.scrollerStyle.position="relative")),this.options.shrinkScrollbars=="scale"&&(this.options.useTransition=!1),this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1,this.options.probeType==3&&(this.options.useTransition=!1),this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()}M.prototype={version:"5.2.0-snapshot",_init:function(){this._initEvents(),(this.options.scrollbars||this.options.indicators)&&this._initIndicators(),this.options.mouseWheel&&this._initWheel(),this.options.snap&&this._initSnap(),this.options.keyBindings&&this._initKeys()},destroy:function(){this._initEvents(!0),clearTimeout(this.resizeTimeout),this.resizeTimeout=null,this._execEvent("destroy")},_transitionEnd:function(t){t.target!=this.scroller||!this.isInTransition||(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))},_start:function(t){if(o.eventType[t.type]!=1){var e;if(t.which?e=t.button:e=t.button<2?0:t.button==4?1:2,e!==0)return}if(!(!this.enabled||this.initiated&&o.eventType[t.type]!==this.initiated)&&!(this.options.ignoreEventException&&this.options.ignoreEventException(t))){this.options.preventDefault&&!o.isBadAndroid&&!o.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();var s=t.touches?t.touches[0]:t,r;this.initiated=o.eventType[t.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this.startTime=o.getTime(),this.options.useTransition&&this.isInTransition?(this._transitionTime(),this.isInTransition=!1,r=this.getComputedPosition(),this._translate(i.round(r.x),i.round(r.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=s.pageX,this.pointY=s.pageY,this._execEvent("beforeScrollStart")}},_move:function(t){if(!(!this.enabled||o.eventType[t.type]!==this.initiated)&&!(this.options.ignoreEventException&&this.options.ignoreEventException(t))){this.options.preventDefault&&t.preventDefault();var e=t.touches?t.touches[0]:t,s=e.pageX-this.pointX,r=e.pageY-this.pointY,p=o.getTime(),n,a,c,w;if(this.pointX=e.pageX,this.pointY=e.pageY,this.distX+=s,this.distY+=r,c=i.abs(this.distX),w=i.abs(this.distY),!(p-this.endTime>300&&c<10&&w<10)){if(!this.directionLocked&&!this.options.freeScroll&&(c>w+this.options.directionLockThreshold?this.directionLocked="h":w>=c+this.options.directionLockThreshold?this.directionLocked="v":this.directionLocked="n"),this.directionLocked=="h"){if(this.options.eventPassthrough=="vertical")t.preventDefault();else if(this.options.eventPassthrough=="horizontal"){this.initiated=!1;return}r=0}else if(this.directionLocked=="v"){if(this.options.eventPassthrough=="horizontal")t.preventDefault();else if(this.options.eventPassthrough=="vertical"){this.initiated=!1;return}s=0}s=this.hasHorizontalScroll?s:0,r=this.hasVerticalScroll?r:0,n=this.x+s,a=this.y+r,(n>0||n<this.maxScrollX)&&(n=this.options.bounce?this.x+s/3:n>0?0:this.maxScrollX),(a>0||a<this.maxScrollY)&&(a=this.options.bounce?this.y+r/3:a>0?0:this.maxScrollY),this.directionX=s>0?-1:s<0?1:0,this.directionY=r>0?-1:r<0?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(n,a),p-this.startTime>300&&(this.startTime=p,this.startX=this.x,this.startY=this.y,this.options.probeType==1&&this._execEvent("scroll")),this.options.probeType>1&&this._execEvent("scroll")}}},_end:function(t){if(!(!this.enabled||o.eventType[t.type]!==this.initiated)){this.options.preventDefault&&!o.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();var e=t.changedTouches?t.changedTouches[0]:t,s,r,p=o.getTime()-this.startTime,n=i.round(this.x),a=i.round(this.y),c=i.abs(n-this.startX),w=i.abs(a-this.startY),T=0,C="";if(this.isInTransition=0,this.initiated=0,this.endTime=o.getTime(),!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(n,a),!this.moved){this.options.tap&&o.tap(t,this.options.tap),this.options.click&&o.click(t),this._execEvent("scrollCancel");return}if(this._events.flick&&p<200&&c<100&&w<100){this._execEvent("flick");return}if(this.options.momentum&&p<300&&(s=this.hasHorizontalScroll?o.momentum(this.x,this.startX,p,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:n,duration:0},r=this.hasVerticalScroll?o.momentum(this.y,this.startY,p,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:a,duration:0},n=s.destination,a=r.destination,T=i.max(s.duration,r.duration),this.isInTransition=1),this.options.snap){var x=this._nearestSnap(n,a);this.currentPage=x,T=this.options.snapSpeed||i.max(i.max(i.min(i.abs(n-x.x),1e3),i.min(i.abs(a-x.y),1e3)),300),n=x.x,a=x.y,this.directionX=0,this.directionY=0,C=this.options.bounceEasing}if(n!=this.x||a!=this.y){(n>0||n<this.maxScrollX||a>0||a<this.maxScrollY)&&(C=o.ease.quadratic),this.scrollTo(n,a,T,C);return}this._execEvent("scrollEnd")}}},_resize:function(){var t=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t.refresh()},this.options.resizePolling)},resetPosition:function(t){var e=this.x,s=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?e=0:this.x<this.maxScrollX&&(e=this.maxScrollX),!this.hasVerticalScroll||this.y>0?s=0:this.y<this.maxScrollY&&(s=this.maxScrollY),e==this.x&&s==this.y?!1:(this.scrollTo(e,s,t,this.options.bounceEasing),!0)},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){o.getRect(this.wrapper),this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight;var t=o.getRect(this.scroller);this.scrollerWidth=t.width,this.scrollerHeight=t.height,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,o.hasPointer&&!this.options.disablePointer&&(this.wrapper.style[o.style.touchAction]=o.getTouchAction(this.options.eventPassthrough,!0),this.wrapper.style[o.style.touchAction]||(this.wrapper.style[o.style.touchAction]=o.getTouchAction(this.options.eventPassthrough,!1))),this.wrapperOffset=o.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()},on:function(t,e){this._events[t]||(this._events[t]=[]),this._events[t].push(e)},off:function(t,e){if(this._events[t]){var s=this._events[t].indexOf(e);s>-1&&this._events[t].splice(s,1)}},_execEvent:function(t){if(this._events[t]){var e=0,s=this._events[t].length;if(s)for(;e<s;e++)this._events[t][e].apply(this,[].slice.call(arguments,1))}},scrollBy:function(t,e,s,r){t=this.x+t,e=this.y+e,s=s||0,this.scrollTo(t,e,s,r)},scrollTo:function(t,e,s,r){r=r||o.ease.circular,this.isInTransition=this.options.useTransition&&s>0;var p=this.options.useTransition&&r.style;!s||p?(p&&(this._transitionTimingFunction(r.style),this._transitionTime(s)),this._translate(t,e)):this._animate(t,e,s,r.fn)},scrollToElement:function(t,e,s,r,p){if(t=t.nodeType?t:this.scroller.querySelector(t),!!t){var n=o.offset(t);n.left-=this.wrapperOffset.left,n.top-=this.wrapperOffset.top;var a=o.getRect(t),c=o.getRect(this.wrapper);s===!0&&(s=i.round(a.width/2-c.width/2)),r===!0&&(r=i.round(a.height/2-c.height/2)),n.left-=s||0,n.top-=r||0,n.left=n.left>0?0:n.left<this.maxScrollX?this.maxScrollX:n.left,n.top=n.top>0?0:n.top<this.maxScrollY?this.maxScrollY:n.top,e=e==null||e==="auto"?i.max(i.abs(this.x-n.left),i.abs(this.y-n.top)):e,this.scrollTo(n.left,n.top,e,p)}},_transitionTime:function(t){if(this.options.useTransition){t=t||0;var e=o.style.transitionDuration;if(e){if(this.scrollerStyle[e]=t+"ms",!t&&o.isBadAndroid){this.scrollerStyle[e]="0.0001ms";var s=this;m(function(){s.scrollerStyle[e]==="0.0001ms"&&(s.scrollerStyle[e]="0s")})}if(this.indicators)for(var r=this.indicators.length;r--;)this.indicators[r].transitionTime(t)}}},_transitionTimingFunction:function(t){if(this.scrollerStyle[o.style.transitionTimingFunction]=t,this.indicators)for(var e=this.indicators.length;e--;)this.indicators[e].transitionTimingFunction(t)},_translate:function(t,e){if(this.options.useTransform?this.scrollerStyle[o.style.transform]="translate("+t+"px,"+e+"px)"+this.translateZ:(t=i.round(t),e=i.round(e),this.scrollerStyle.left=t+"px",this.scrollerStyle.top=e+"px"),this.x=t,this.y=e,this.indicators)for(var s=this.indicators.length;s--;)this.indicators[s].updatePosition()},_initEvents:function(t){var e=t?o.removeEvent:o.addEvent,s=this.options.bindToWrapper?this.wrapper:d;e(d,"orientationchange",this),e(d,"resize",this),this.options.click&&e(this.wrapper,"click",this,!0),this.options.disableMouse||(e(this.wrapper,"mousedown",this),e(s,"mousemove",this),e(s,"mousecancel",this),e(s,"mouseup",this)),o.hasPointer&&!this.options.disablePointer&&(e(this.wrapper,o.prefixPointerEvent("pointerdown"),this),e(s,o.prefixPointerEvent("pointermove"),this),e(s,o.prefixPointerEvent("pointercancel"),this),e(s,o.prefixPointerEvent("pointerup"),this)),o.hasTouch&&!this.options.disableTouch&&(e(this.wrapper,"touchstart",this),e(s,"touchmove",this),e(s,"touchcancel",this),e(s,"touchend",this)),e(this.scroller,"transitionend",this),e(this.scroller,"webkitTransitionEnd",this),e(this.scroller,"oTransitionEnd",this),e(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var t=d.getComputedStyle(this.scroller,null),e,s;return this.options.useTransform?(t=t[o.style.transform].split(")")[0].split(", "),e=+(t[12]||t[4]),s=+(t[13]||t[5])):(e=+t.left.replace(/[^-\d.]/g,""),s=+t.top.replace(/[^-\d.]/g,"")),{x:e,y:s}},_initIndicators:function(){var t=this.options.interactiveScrollbars,e=typeof this.options.scrollbars!="string",s=[],r,p=this;this.indicators=[],this.options.scrollbars&&(this.options.scrollY&&(r={el:y("v",t,this.options.scrollbars),interactive:t,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenX:!1},this.wrapper.appendChild(r.el),s.push(r)),this.options.scrollX&&(r={el:y("h",t,this.options.scrollbars),interactive:t,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenY:!1},this.wrapper.appendChild(r.el),s.push(r))),this.options.indicators&&(s=s.concat(this.options.indicators));for(var n=s.length;n--;)this.indicators.push(new k(this,s[n]));function a(c){if(p.indicators)for(var w=p.indicators.length;w--;)c.call(p.indicators[w])}this.options.fadeScrollbars&&(this.on("scrollEnd",function(){a(function(){this.fade()})}),this.on("scrollCancel",function(){a(function(){this.fade()})}),this.on("scrollStart",function(){a(function(){this.fade(1)})}),this.on("beforeScrollStart",function(){a(function(){this.fade(1,!0)})})),this.on("refresh",function(){a(function(){this.refresh()})}),this.on("destroy",function(){a(function(){this.destroy()}),delete this.indicators})},_initWheel:function(){o.addEvent(this.wrapper,"wheel",this),o.addEvent(this.wrapper,"mousewheel",this),o.addEvent(this.wrapper,"DOMMouseScroll",this),this.on("destroy",function(){clearTimeout(this.wheelTimeout),this.wheelTimeout=null,o.removeEvent(this.wrapper,"wheel",this),o.removeEvent(this.wrapper,"mousewheel",this),o.removeEvent(this.wrapper,"DOMMouseScroll",this)})},_wheel:function(t){if(this.enabled){if(t.metaKey||t.ctrlKey||t.buttons===4)return!0;t.preventDefault();var e,s,r,p,n=this;if(this.wheelTimeout===void 0&&n._execEvent("scrollStart"),clearTimeout(this.wheelTimeout),this.wheelTimeout=setTimeout(function(){n.options.snap||n._execEvent("scrollEnd"),n.wheelTimeout=void 0},400),"deltaX"in t)t.deltaMode===1?(e=-t.deltaX*this.options.mouseWheelSpeed,s=-t.deltaY*this.options.mouseWheelSpeed):(e=-t.deltaX,s=-t.deltaY);else if("wheelDeltaX"in t)e=t.wheelDeltaX/120*this.options.mouseWheelSpeed,s=t.wheelDeltaY/120*this.options.mouseWheelSpeed;else if("wheelDelta"in t)e=s=t.wheelDelta/120*this.options.mouseWheelSpeed;else if("detail"in t)e=s=-t.detail/3*this.options.mouseWheelSpeed;else return;if(e*=this.options.invertWheelDirection,s*=this.options.invertWheelDirection,this.options.snap){r=this.currentPage.pageX,p=this.currentPage.pageY,e>0?r--:e<0&&r++,s>0?p--:s<0&&p++,this.goToPage(r,p);return}r=this.x+i.round(this.hasHorizontalScroll?e:0),p=this.y+i.round(this.hasVerticalScroll?s:0),this.directionX=e>0?-1:e<0?1:0,this.directionY=s>0?-1:s<0?1:0,r>0?r=0:r<this.maxScrollX&&(r=this.maxScrollX),p>0?p=0:p<this.maxScrollY&&(p=this.maxScrollY),this.scrollTo(r,p,0),this.options.probeType>1&&this._execEvent("scroll")}},_initSnap:function(){this.currentPage={},typeof this.options.snap=="string"&&(this.options.snap=this.scroller.querySelectorAll(this.options.snap)),this.on("refresh",function(){var t=0,e,s=0,r,p,n,a=0,c,w=this.options.snapStepX||this.wrapperWidth,T=this.options.snapStepY||this.wrapperHeight,C,x;if(this.pages=[],!(!this.wrapperWidth||!this.wrapperHeight||!this.scrollerWidth||!this.scrollerHeight)){if(this.options.snap===!0)for(p=i.round(w/2),n=i.round(T/2);a>-this.scrollerWidth;){for(this.pages[t]=[],e=0,c=0;c>-this.scrollerHeight;)this.pages[t][e]={x:i.max(a,this.maxScrollX),y:i.max(c,this.maxScrollY),width:w,height:T,cx:a-p,cy:c-n},c-=T,e++;a-=w,t++}else for(C=this.options.snap,e=C.length,r=-1;t<e;t++)x=o.getRect(C[t]),(t===0||x.left<=o.getRect(C[t-1]).left)&&(s=0,r++),this.pages[s]||(this.pages[s]=[]),a=i.max(-x.left,this.maxScrollX),c=i.max(-x.top,this.maxScrollY),p=a-i.round(x.width/2),n=c-i.round(x.height/2),this.pages[s][r]={x:a,y:c,width:x.width,height:x.height,cx:p,cy:n},a>this.maxScrollX&&s++;this.goToPage(this.currentPage.pageX||0,this.currentPage.pageY||0,0),this.options.snapThreshold%1===0?(this.snapThresholdX=this.options.snapThreshold,this.snapThresholdY=this.options.snapThreshold):(this.snapThresholdX=i.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].width*this.options.snapThreshold),this.snapThresholdY=i.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].height*this.options.snapThreshold))}}),this.on("flick",function(){var t=this.options.snapSpeed||i.max(i.max(i.min(i.abs(this.x-this.startX),1e3),i.min(i.abs(this.y-this.startY),1e3)),300);this.goToPage(this.currentPage.pageX+this.directionX,this.currentPage.pageY+this.directionY,t)})},_nearestSnap:function(t,e){if(!this.pages.length)return{x:0,y:0,pageX:0,pageY:0};var s=0,r=this.pages.length,p=0;if(i.abs(t-this.absStartX)<this.snapThresholdX&&i.abs(e-this.absStartY)<this.snapThresholdY)return this.currentPage;for(t>0?t=0:t<this.maxScrollX&&(t=this.maxScrollX),e>0?e=0:e<this.maxScrollY&&(e=this.maxScrollY);s<r;s++)if(t>=this.pages[s][0].cx){t=this.pages[s][0].x;break}for(r=this.pages[s].length;p<r;p++)if(e>=this.pages[0][p].cy){e=this.pages[0][p].y;break}return s==this.currentPage.pageX&&(s+=this.directionX,s<0?s=0:s>=this.pages.length&&(s=this.pages.length-1),t=this.pages[s][0].x),p==this.currentPage.pageY&&(p+=this.directionY,p<0?p=0:p>=this.pages[0].length&&(p=this.pages[0].length-1),e=this.pages[0][p].y),{x:t,y:e,pageX:s,pageY:p}},goToPage:function(t,e,s,r){r=r||this.options.bounceEasing,t>=this.pages.length?t=this.pages.length-1:t<0&&(t=0),e>=this.pages[t].length?e=this.pages[t].length-1:e<0&&(e=0);var p=this.pages[t][e].x,n=this.pages[t][e].y;s=s===void 0?this.options.snapSpeed||i.max(i.max(i.min(i.abs(p-this.x),1e3),i.min(i.abs(n-this.y),1e3)),300):s,this.currentPage={x:p,y:n,pageX:t,pageY:e},this.scrollTo(p,n,s,r)},next:function(t,e){var s=this.currentPage.pageX,r=this.currentPage.pageY;s++,s>=this.pages.length&&this.hasVerticalScroll&&(s=0,r++),this.goToPage(s,r,t,e)},prev:function(t,e){var s=this.currentPage.pageX,r=this.currentPage.pageY;s--,s<0&&this.hasVerticalScroll&&(s=0,r--),this.goToPage(s,r,t,e)},_initKeys:function(t){var e={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40},s;if(typeof this.options.keyBindings=="object")for(s in this.options.keyBindings)typeof this.options.keyBindings[s]=="string"&&(this.options.keyBindings[s]=this.options.keyBindings[s].toUpperCase().charCodeAt(0));else this.options.keyBindings={};for(s in e)this.options.keyBindings[s]=this.options.keyBindings[s]||e[s];o.addEvent(d,"keydown",this),this.on("destroy",function(){o.removeEvent(d,"keydown",this)})},_key:function(t){if(this.enabled){var e=this.options.snap,s=e?this.currentPage.pageX:this.x,r=e?this.currentPage.pageY:this.y,p=o.getTime(),n=this.keyTime||0,a=.25,c;switch(this.options.useTransition&&this.isInTransition&&(c=this.getComputedPosition(),this._translate(i.round(c.x),i.round(c.y)),this.isInTransition=!1),this.keyAcceleration=p-n<200?i.min(this.keyAcceleration+a,50):0,t.keyCode){case this.options.keyBindings.pageUp:this.hasHorizontalScroll&&!this.hasVerticalScroll?s+=e?1:this.wrapperWidth:r+=e?1:this.wrapperHeight;break;case this.options.keyBindings.pageDown:this.hasHorizontalScroll&&!this.hasVerticalScroll?s-=e?1:this.wrapperWidth:r-=e?1:this.wrapperHeight;break;case this.options.keyBindings.end:s=e?this.pages.length-1:this.maxScrollX,r=e?this.pages[0].length-1:this.maxScrollY;break;case this.options.keyBindings.home:s=0,r=0;break;case this.options.keyBindings.left:s+=e?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.up:r+=e?1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.right:s-=e?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.down:r-=e?1:5+this.keyAcceleration>>0;break;default:return}if(e){this.goToPage(s,r);return}s>0?(s=0,this.keyAcceleration=0):s<this.maxScrollX&&(s=this.maxScrollX,this.keyAcceleration=0),r>0?(r=0,this.keyAcceleration=0):r<this.maxScrollY&&(r=this.maxScrollY,this.keyAcceleration=0),this.scrollTo(s,r,0),this.keyTime=p}},_animate:function(t,e,s,r){var p=this,n=this.x,a=this.y,c=o.getTime(),w=c+s;function T(){var C=o.getTime(),x,V,l;if(C>=w){p.isAnimating=!1,p._translate(t,e),p.resetPosition(p.options.bounceTime)||p._execEvent("scrollEnd");return}C=(C-c)/s,l=r(C),x=(t-n)*l+n,V=(e-a)*l+a,p._translate(x,V),p.isAnimating&&m(T),p.options.probeType==3&&p._execEvent("scroll")}this.isAnimating=!0,T()},handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(t);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(t);break;case"keydown":this._key(t);break;case"click":this.enabled&&!t._constructed&&(t.preventDefault(),t.stopPropagation());break}}};function y(t,e,s){var r=S.createElement("div"),p=S.createElement("div");return s===!0&&(r.style.cssText="position:absolute;z-index:9999",p.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);border-radius:3px"),p.className="iScrollIndicator",t=="h"?(s===!0&&(r.style.cssText+=";height:7px;left:2px;right:2px;bottom:0",p.style.height="100%"),r.className="iScrollHorizontalScrollbar"):(s===!0&&(r.style.cssText+=";width:7px;bottom:2px;top:2px;right:1px",p.style.width="100%"),r.className="iScrollVerticalScrollbar"),r.style.cssText+=";overflow:hidden",e||(r.style.pointerEvents="none"),r.appendChild(p),r}function k(t,e){this.wrapper=typeof e.el=="string"?S.querySelector(e.el):e.el,this.wrapperStyle=this.wrapper.style,this.indicator=this.wrapper.children[0],this.indicatorStyle=this.indicator.style,this.scroller=t,this.options={listenX:!0,listenY:!0,interactive:!1,resize:!0,defaultScrollbars:!1,shrink:!1,fade:!1,speedRatioX:0,speedRatioY:0};for(var s in e)this.options[s]=e[s];if(this.sizeRatioX=1,this.sizeRatioY=1,this.maxPosX=0,this.maxPosY=0,this.options.interactive&&(this.options.disableTouch||(o.addEvent(this.indicator,"touchstart",this),o.addEvent(d,"touchend",this)),this.options.disablePointer||(o.addEvent(this.indicator,o.prefixPointerEvent("pointerdown"),this),o.addEvent(d,o.prefixPointerEvent("pointerup"),this)),this.options.disableMouse||(o.addEvent(this.indicator,"mousedown",this),o.addEvent(d,"mouseup",this))),this.options.fade){this.wrapperStyle[o.style.transform]=this.scroller.translateZ;var r=o.style.transitionDuration;if(!r)return;this.wrapperStyle[r]=o.isBadAndroid?"0.0001ms":"0ms";var p=this;o.isBadAndroid&&m(function(){p.wrapperStyle[r]==="0.0001ms"&&(p.wrapperStyle[r]="0s")}),this.wrapperStyle.opacity="0"}}k.prototype={handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break}},destroy:function(){this.options.fadeScrollbars&&(clearTimeout(this.fadeTimeout),this.fadeTimeout=null),this.options.interactive&&(o.removeEvent(this.indicator,"touchstart",this),o.removeEvent(this.indicator,o.prefixPointerEvent("pointerdown"),this),o.removeEvent(this.indicator,"mousedown",this),o.removeEvent(d,"touchmove",this),o.removeEvent(d,o.prefixPointerEvent("pointermove"),this),o.removeEvent(d,"mousemove",this),o.removeEvent(d,"touchend",this),o.removeEvent(d,o.prefixPointerEvent("pointerup"),this),o.removeEvent(d,"mouseup",this)),this.options.defaultScrollbars&&this.wrapper.parentNode&&this.wrapper.parentNode.removeChild(this.wrapper)},_start:function(t){var e=t.touches?t.touches[0]:t;t.preventDefault(),t.stopPropagation(),this.transitionTime(),this.initiated=!0,this.moved=!1,this.lastPointX=e.pageX,this.lastPointY=e.pageY,this.startTime=o.getTime(),this.options.disableTouch||o.addEvent(d,"touchmove",this),this.options.disablePointer||o.addEvent(d,o.prefixPointerEvent("pointermove"),this),this.options.disableMouse||o.addEvent(d,"mousemove",this),this.scroller._execEvent("beforeScrollStart")},_move:function(t){var e=t.touches?t.touches[0]:t,s,r,p,n,a=o.getTime();this.moved||this.scroller._execEvent("scrollStart"),this.moved=!0,s=e.pageX-this.lastPointX,this.lastPointX=e.pageX,r=e.pageY-this.lastPointY,this.lastPointY=e.pageY,p=this.x+s,n=this.y+r,this._pos(p,n),this.scroller.options.probeType==1&&a-this.startTime>300?(this.startTime=a,this.scroller._execEvent("scroll")):this.scroller.options.probeType>1&&this.scroller._execEvent("scroll"),t.preventDefault(),t.stopPropagation()},_end:function(t){if(this.initiated){if(this.initiated=!1,t.preventDefault(),t.stopPropagation(),o.removeEvent(d,"touchmove",this),o.removeEvent(d,o.prefixPointerEvent("pointermove"),this),o.removeEvent(d,"mousemove",this),this.scroller.options.snap){var e=this.scroller._nearestSnap(this.scroller.x,this.scroller.y),s=this.options.snapSpeed||i.max(i.max(i.min(i.abs(this.scroller.x-e.x),1e3),i.min(i.abs(this.scroller.y-e.y),1e3)),300);(this.scroller.x!=e.x||this.scroller.y!=e.y)&&(this.scroller.directionX=0,this.scroller.directionY=0,this.scroller.currentPage=e,this.scroller.scrollTo(e.x,e.y,s,this.scroller.options.bounceEasing))}this.moved&&this.scroller._execEvent("scrollEnd")}},transitionTime:function(t){t=t||0;var e=o.style.transitionDuration;if(e&&(this.indicatorStyle[e]=t+"ms",!t&&o.isBadAndroid)){this.indicatorStyle[e]="0.0001ms";var s=this;m(function(){s.indicatorStyle[e]==="0.0001ms"&&(s.indicatorStyle[e]="0s")})}},transitionTimingFunction:function(t){this.indicatorStyle[o.style.transitionTimingFunction]=t},refresh:function(){this.transitionTime(),this.options.listenX&&!this.options.listenY?this.indicatorStyle.display=this.scroller.hasHorizontalScroll?"block":"none":this.options.listenY&&!this.options.listenX?this.indicatorStyle.display=this.scroller.hasVerticalScroll?"block":"none":this.indicatorStyle.display=this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none",this.scroller.hasHorizontalScroll&&this.scroller.hasVerticalScroll?(o.addClass(this.wrapper,"iScrollBothScrollbars"),o.removeClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="8px":this.wrapper.style.bottom="8px")):(o.removeClass(this.wrapper,"iScrollBothScrollbars"),o.addClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="2px":this.wrapper.style.bottom="2px")),o.getRect(this.wrapper),this.options.listenX&&(this.wrapperWidth=this.wrapper.clientWidth,this.options.resize?(this.indicatorWidth=i.max(i.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8),this.indicatorStyle.width=this.indicatorWidth+"px"):this.indicatorWidth=this.indicator.clientWidth,this.maxPosX=this.wrapperWidth-this.indicatorWidth,this.options.shrink=="clip"?(this.minBoundaryX=-this.indicatorWidth+8,this.maxBoundaryX=this.wrapperWidth-8):(this.minBoundaryX=0,this.maxBoundaryX=this.maxPosX),this.sizeRatioX=this.options.speedRatioX||this.scroller.maxScrollX&&this.maxPosX/this.scroller.maxScrollX),this.options.listenY&&(this.wrapperHeight=this.wrapper.clientHeight,this.options.resize?(this.indicatorHeight=i.max(i.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8),this.indicatorStyle.height=this.indicatorHeight+"px"):this.indicatorHeight=this.indicator.clientHeight,this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.options.shrink=="clip"?(this.minBoundaryY=-this.indicatorHeight+8,this.maxBoundaryY=this.wrapperHeight-8):(this.minBoundaryY=0,this.maxBoundaryY=this.maxPosY),this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.sizeRatioY=this.options.speedRatioY||this.scroller.maxScrollY&&this.maxPosY/this.scroller.maxScrollY),this.updatePosition()},updatePosition:function(){var t=this.options.listenX&&i.round(this.sizeRatioX*this.scroller.x)||0,e=this.options.listenY&&i.round(this.sizeRatioY*this.scroller.y)||0;this.options.ignoreBoundaries||(t<this.minBoundaryX?(this.options.shrink=="scale"&&(this.width=i.max(this.indicatorWidth+t,8),this.indicatorStyle.width=this.width+"px"),t=this.minBoundaryX):t>this.maxBoundaryX?this.options.shrink=="scale"?(this.width=i.max(this.indicatorWidth-(t-this.maxPosX),8),this.indicatorStyle.width=this.width+"px",t=this.maxPosX+this.indicatorWidth-this.width):t=this.maxBoundaryX:this.options.shrink=="scale"&&this.width!=this.indicatorWidth&&(this.width=this.indicatorWidth,this.indicatorStyle.width=this.width+"px"),e<this.minBoundaryY?(this.options.shrink=="scale"&&(this.height=i.max(this.indicatorHeight+e*3,8),this.indicatorStyle.height=this.height+"px"),e=this.minBoundaryY):e>this.maxBoundaryY?this.options.shrink=="scale"?(this.height=i.max(this.indicatorHeight-(e-this.maxPosY)*3,8),this.indicatorStyle.height=this.height+"px",e=this.maxPosY+this.indicatorHeight-this.height):e=this.maxBoundaryY:this.options.shrink=="scale"&&this.height!=this.indicatorHeight&&(this.height=this.indicatorHeight,this.indicatorStyle.height=this.height+"px")),this.x=t,this.y=e,this.scroller.options.useTransform?this.indicatorStyle[o.style.transform]="translate("+t+"px,"+e+"px)"+this.scroller.translateZ:(this.indicatorStyle.left=t+"px",this.indicatorStyle.top=e+"px")},_pos:function(t,e){t<0?t=0:t>this.maxPosX&&(t=this.maxPosX),e<0?e=0:e>this.maxPosY&&(e=this.maxPosY),t=this.options.listenX?i.round(t/this.sizeRatioX):this.scroller.x,e=this.options.listenY?i.round(e/this.sizeRatioY):this.scroller.y,this.scroller.scrollTo(t,e)},fade:function(t,e){if(!(e&&!this.visible)){clearTimeout(this.fadeTimeout),this.fadeTimeout=null;var s=t?250:500,r=t?0:300;t=t?"1":"0",this.wrapperStyle[o.style.transitionDuration]=s+"ms",this.fadeTimeout=setTimeout((function(p){this.wrapperStyle.opacity=p,this.visible=+p}).bind(this,t),r)}}},M.utils=o,d.IScroll=M})(window,document,Math)},2937:(d,S,i)=>{"use strict";i.d(S,{$G:()=>T,Op:()=>c,Uj:()=>n,XB:()=>o,fQ:()=>p,nK:()=>M,u5:()=>k});const m=(l,f,g)=>{if(l[f]===g)return l;const E=[...l];return E[f]=g,E},o=(l,f)=>f>=0&&f<=l.length-1?[...l.slice(0,f),...l.slice(f+1)]:l,M=(l,f,g)=>[...l.slice(0,f),g,...l.slice(f)],y=(l,f,g)=>g===f?l:g<f?[...l.slice(0,g),...l.slice(g+1,f+1),l[g],...l.slice(f+1)]:[...l.slice(0,f),l[g],...l.slice(f,g),...l.slice(g+1)],k=(l,f)=>[...l,f],t=(l,f)=>[f,...l],e=l=>{if(l.length===0)return l;const f=[...l];return f.pop(),f},s=l=>{if(l.length===0)return l;const f=[...l];return f.shift(),f},r=(l,f)=>f&&f.length?[...l,...f]:l,p=(l,f)=>l.includes(f)?l:[...l,f],n=(l,f)=>{const g=l.indexOf(f);return~g?[...l.slice(0,g),...l.slice(g+1)]:l},a=(l,f,g)=>{const E=l.indexOf(g);return~E?y(l,f,E):l},c=(l,f)=>{const g=l.findIndex(f);return~g?[...l.slice(0,g),...l.slice(g+1)]:l},w=(l,f,g)=>{const E=l.findIndex(f);return~E?y(l,g,E):l},T=(l,f,g)=>{const E=l.findIndex(f);if(!~E||l[E]===g)return l;const j=[...l];return j[E]=g,j},C=(l,f,g)=>{const E=l.findIndex(f);if(!~E)return[...l,g];if(l[E]===g)return l;const j=[...l];return j[E]=g,j},x=(l,f,g)=>l.find(f)===void 0?[...l,g]:l,V=(l,f)=>{const g=[];for(let E=0,j=l.length;E<j;E+=f)g.push(l.slice(E,E+f));return g}},73518:(d,S,i)=>{var m=i(78869),o=i(27221),M="Expected a function";function y(k,t,e){var s=!0,r=!0;if(typeof k!="function")throw new TypeError(M);return o(e)&&(s="leading"in e?!!e.leading:s,r="trailing"in e?!!e.trailing:r),m(k,t,{leading:s,maxWait:t,trailing:r})}d.exports=y},16700:d=>{"use strict";d.exports=canvg},94994:d=>{"use strict";d.exports=dompurify},9820:d=>{"use strict";d.exports=html2canvas}},St={};function B(d){var S=St[d];if(S!==void 0)return S.exports;var i=St[d]={id:d,loaded:!1,exports:{}};return dt[d].call(i.exports,i,i.exports,B),i.loaded=!0,i.exports}B.m=dt,B.amdO={},(()=>{var d=[];B.O=(S,i,m,o)=>{if(i){o=o||0;for(var M=d.length;M>0&&d[M-1][2]>o;M--)d[M]=d[M-1];d[M]=[i,m,o];return}for(var y=1/0,M=0;M<d.length;M++){for(var[i,m,o]=d[M],k=!0,t=0;t<i.length;t++)(o&!1||y>=o)&&Object.keys(B.O).every(a=>B.O[a](i[t]))?i.splice(t--,1):(k=!1,o<y&&(y=o));if(k){d.splice(M--,1);var e=m();e!==void 0&&(S=e)}}return S}})(),B.n=d=>{var S=d&&d.__esModule?()=>d.default:()=>d;return B.d(S,{a:S}),S},(()=>{var d=Object.getPrototypeOf?i=>Object.getPrototypeOf(i):i=>i.__proto__,S;B.t=function(i,m){if(m&1&&(i=this(i)),m&8||typeof i=="object"&&i&&(m&4&&i.__esModule||m&16&&typeof i.then=="function"))return i;var o=Object.create(null);B.r(o);var M={};S=S||[null,d({}),d([]),d(d)];for(var y=m&2&&i;typeof y=="object"&&!~S.indexOf(y);y=d(y))Object.getOwnPropertyNames(y).forEach(k=>M[k]=()=>i[k]);return M.default=()=>i,B.d(o,M),o}})(),B.d=(d,S)=>{for(var i in S)B.o(S,i)&&!B.o(d,i)&&Object.defineProperty(d,i,{enumerable:!0,get:S[i]})},B.f={},B.e=d=>Promise.all(Object.keys(B.f).reduce((S,i)=>(B.f[i](d,S),S),[])),B.u=d=>""+{214:"lazy-lib-paper",437:"lazy-lib-fontkit",703:"lazy-lib-rehype",956:"lazy-lib-i18n",976:"lazy-lib-dl"}[d]+"-"+{214:"652e1d1aaf5a025c5f3d",437:"c05f27c8c0bb64d649b9",703:"3b3b0636cb31e98989ed",956:"dac35b1b3d6973f4675f",976:"df7058e941c9b6857595"}[d]+".js",B.miniCssF=d=>{},B.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch(d){if(typeof window=="object")return window}}(),B.o=(d,S)=>Object.prototype.hasOwnProperty.call(d,S),(()=>{var d={},S="@mb2024/mb-proto:";B.l=(i,m,o,M)=>{if(d[i]){d[i].push(m);return}var y,k;if(o!==void 0)for(var t=document.getElementsByTagName("script"),e=0;e<t.length;e++){var s=t[e];if(s.getAttribute("src")==i||s.getAttribute("data-webpack")==S+o){y=s;break}}y||(k=!0,y=document.createElement("script"),y.charset="utf-8",y.timeout=120,B.nc&&y.setAttribute("nonce",B.nc),y.setAttribute("data-webpack",S+o),y.src=i),d[i]=[m];var r=(n,a)=>{y.onerror=y.onload=null,clearTimeout(p);var c=d[i];if(delete d[i],y.parentNode&&y.parentNode.removeChild(y),c&&c.forEach(w=>w(a)),n)return n(a)},p=setTimeout(r.bind(null,void 0,{type:"timeout",target:y}),12e4);y.onerror=r.bind(null,y.onerror),y.onload=r.bind(null,y.onload),k&&document.head.appendChild(y)}})(),B.r=d=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(d,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(d,"__esModule",{value:!0})},B.nmd=d=>(d.paths=[],d.children||(d.children=[]),d),B.j=699,B.p="/mb-proto2/",(()=>{var d={699:0};B.f.j=(m,o)=>{var M=B.o(d,m)?d[m]:void 0;if(M!==0)if(M)o.push(M[2]);else{var y=new Promise((s,r)=>M=d[m]=[s,r]);o.push(M[2]=y);var k=B.p+B.u(m),t=new Error,e=s=>{if(B.o(d,m)&&(M=d[m],M!==0&&(d[m]=void 0),M)){var r=s&&(s.type==="load"?"missing":s.type),p=s&&s.target&&s.target.src;t.message="Loading chunk "+m+" failed.\n("+r+": "+p+")",t.name="ChunkLoadError",t.type=r,t.request=p,M[1](t)}};B.l(k,e,"chunk-"+m,m)}},B.O.j=m=>d[m]===0;var S=(m,o)=>{var[M,y,k]=o,t,e,s=0;if(M.some(p=>d[p]!==0)){for(t in y)B.o(y,t)&&(B.m[t]=y[t]);if(k)var r=k(B)}for(m&&m(o);s<M.length;s++)e=M[s],B.o(d,e)&&d[e]&&d[e][0](),d[e]=0;return B.O(r)},i=self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[];i.forEach(S.bind(null,0)),i.push=S.bind(null,i.push.bind(i))})(),B.nc=void 0,B.O(void 0,[31,788,908,347],()=>B(23372));var Wt=B.O(void 0,[31,788,908,347],()=>B(42276));Wt=B.O(Wt)})();

//# sourceMappingURL=preview-html-zip-65ffc188051b4b67a692.js.map