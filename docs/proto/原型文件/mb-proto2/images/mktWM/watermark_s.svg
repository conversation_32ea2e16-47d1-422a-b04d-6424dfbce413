<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_150472_123402)">
<path d="M80.8999 2L49.552 33.3479M0.899902 82L33.3999 49.5" stroke="black" stroke-opacity="0.3" stroke-width="2"/>
<path d="M-1.7998 1L30.7002 33.5M78.2002 81L46.5724 49.3722" stroke="black" stroke-opacity="0.3" stroke-width="2"/>
<g filter="url(#filter0_d_150472_123402)">
<path d="M61.6579 43.1381C59.8154 43.1381 58.3548 41.5231 58.557 39.6352C58.7143 38.1795 59.9052 36.9967 61.3658 36.8602C62.2871 36.7693 63.1185 37.0877 63.7476 37.6563C63.8151 37.7246 63.9274 37.7246 63.9723 37.6563L64.8712 36.7465C64.9386 36.6783 64.9386 36.5645 64.8712 36.4963C63.9049 35.5865 62.5342 35.0861 61.0512 35.268C58.939 35.541 57.2313 37.2924 56.9841 39.4533C56.6695 42.342 58.8716 44.7758 61.6579 44.7758C62.8938 44.7758 64.0173 44.2982 64.8712 43.502C64.9386 43.4338 64.9386 43.3201 64.8712 43.2518L63.9723 42.342C63.9049 42.2738 63.8151 42.2738 63.7476 42.342C63.1859 42.8197 62.4444 43.1381 61.6579 43.1381Z" fill="white" fill-opacity="0.4"/>
<path d="M71.1296 43.1381C69.287 43.1381 67.8265 41.5231 68.0287 39.6352C68.186 38.1795 69.3769 36.9967 70.8375 36.8602C71.7588 36.7693 72.5902 37.0877 73.2193 37.6563C73.2867 37.7246 73.3991 37.7246 73.444 37.6563L74.3428 36.7465C74.4102 36.6783 74.4102 36.5645 74.3428 36.4963C73.3766 35.5865 72.0059 35.0861 70.5229 35.268C68.4107 35.541 66.7029 37.2924 66.4558 39.4533C66.1412 42.342 68.3433 44.7758 71.1296 44.7758C72.3655 44.7758 73.489 44.2982 74.3428 43.502C74.4102 43.4338 74.4102 43.3201 74.3428 43.2518L73.444 42.342C73.3766 42.2738 73.2867 42.2738 73.2193 42.342C72.6576 42.8197 71.9385 43.1381 71.1296 43.1381Z" fill="white" fill-opacity="0.4"/>
<path d="M55.67 44.7533H54.5015C54.4117 44.7533 54.3218 44.685 54.3218 44.5713V43.3885C54.3218 43.2975 54.3892 43.2065 54.5015 43.2065H55.67C55.7599 43.2065 55.8498 43.2748 55.8498 43.3885V44.5713C55.8498 44.6623 55.7599 44.7533 55.67 44.7533Z" fill="white" fill-opacity="0.4"/>
<path d="M48.6709 35.2236C46.0644 35.2236 43.9521 37.3618 43.9521 40.0003C43.9521 42.6388 46.0644 44.7769 48.6709 44.7769C51.2775 44.7769 53.3897 42.6388 53.3897 40.0003C53.3897 37.3618 51.2775 35.2236 48.6709 35.2236ZM48.6709 43.1847C46.9407 43.1847 45.5475 41.7745 45.5475 40.023C45.5475 38.2716 46.9407 36.8613 48.6709 36.8613C50.4011 36.8613 51.7943 38.2716 51.7943 40.023C51.7943 41.7517 50.4011 43.1847 48.6709 43.1847Z" fill="white" fill-opacity="0.4"/>
<path d="M29.9751 35.3594H26.5821C26.4922 35.3594 26.4023 35.4276 26.4023 35.5413V44.4577C26.4023 44.5487 26.4698 44.6397 26.5821 44.6397H30.1099C32.7165 44.6397 34.8062 42.4334 34.6939 39.7721C34.5591 37.2928 32.4469 35.3594 29.9751 35.3594ZM30.1099 43.1385H27.9303V36.8834H30.1099C31.7503 36.8834 33.1659 38.3618 33.1659 39.9996C33.1434 41.66 31.7503 43.1385 30.1099 43.1385Z" fill="white" fill-opacity="0.4"/>
<path d="M42.3001 44.7758H43.5809C43.6933 44.7758 43.7831 44.6393 43.7382 44.5255L39.8733 35.3134C39.8508 35.2452 39.7834 35.1997 39.716 35.1997H38.8172C38.7498 35.1997 38.6824 35.2452 38.6599 35.3134L34.795 44.5255C34.7501 44.6393 34.84 44.7758 34.9523 44.7758H36.2331C36.3005 44.7758 36.3679 44.7303 36.3904 44.662L37.4915 42.0462H41.0418L42.1428 44.662C42.1877 44.753 42.2327 44.7758 42.3001 44.7758ZM38.1206 40.5678L39.2891 37.7927L40.4575 40.5678H38.1206Z" fill="white" fill-opacity="0.4"/>
<path d="M20.1445 35.2236C17.538 35.2236 15.4258 37.3618 15.4258 40.0003C15.4258 42.6388 17.538 44.7769 20.1445 44.7769C22.7511 44.7769 24.8633 42.6388 24.8633 40.0003C24.8633 37.3618 22.7511 35.2236 20.1445 35.2236ZM20.1445 43.162C18.4143 43.162 17.0212 41.7517 17.0212 40.0003C17.0212 38.2488 18.4143 36.8386 20.1445 36.8386C21.8748 36.8386 23.2679 38.2488 23.2679 40.0003C23.2679 41.7517 21.8748 43.162 20.1445 43.162Z" fill="white" fill-opacity="0.4"/>
<path d="M13.1452 35.2691L9.23532 39.2269L5.3255 35.2691C5.30303 35.2464 5.25809 35.2236 5.21315 35.2236H4.78621C4.69632 35.2236 4.60645 35.2919 4.60645 35.4056V44.6177C4.60645 44.7087 4.67385 44.7997 4.78621 44.7997H6.11196C6.20184 44.7997 6.29172 44.7314 6.29172 44.6177V38.6355L9.14545 41.5243C9.21286 41.5925 9.32521 41.5925 9.39262 41.5243L12.2463 38.6355V44.6177C12.2463 44.7087 12.3138 44.7997 12.4261 44.7997H13.7519C13.8417 44.7997 13.9316 44.7314 13.9316 44.6177V35.4056C13.9316 35.3146 13.8642 35.2236 13.7519 35.2236H13.3249C13.235 35.2236 13.1901 35.2464 13.1452 35.2691Z" fill="white" fill-opacity="0.4"/>
</g>
<path d="M80 0L48 32M0 80L32 48" stroke="white" stroke-opacity="0.4" stroke-width="2"/>
<path d="M0 0L32 32M80 80L48 48" stroke="white" stroke-opacity="0.4" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_150472_123402" x="4.60645" y="35.1997" width="71.5871" height="10.1001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.8" dy="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_150472_123402"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_150472_123402" result="shape"/>
</filter>
<clipPath id="clip0_150472_123402">
<rect width="80" height="80" fill="white"/>
</clipPath>
</defs>
</svg>
