try{let Ne=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},de=new Ne.Error().stack;de&&(Ne._sentryDebugIds=Ne._sentryDebugIds||{},Ne._sentryDebugIds[de]="95f5fef7-a90d-4b79-8636-2151a91d926c",Ne._sentryDebugIdIdentifier="sentry-dbid-95f5fef7-a90d-4b79-8636-2151a91d926c")}catch(Ne){}{let Ne=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};Ne.SENTRY_RELEASE={id:"21.3.4"}}(self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[]).push([[908],{93839:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>Z});var s=n(74059),p=n(51044),M=n(86634),u=n.n(M),k=n(87822),te=n(57464);class Z extends p.PureComponent{constructor(){super(...arguments),(0,s.A)(this,"getClassName",()=>{const{className:T,type:E,size:U,corner:q,disabled:J}=this.props;return u()(T,"button-root",{["type-"+E]:E,["size-"+U]:U,["corner-"+q]:q,"is-disabled":J})}),(0,s.A)(this,"onButtonClick",T=>{if(this.props.disabled){T.preventDefault();return}const{onClick:E}=this.props;E&&E(T)}),(0,s.A)(this,"onButtonMouseDown",T=>{if(this.props.disabled){T.preventDefault();return}const{onMouseDown:E}=this.props;E&&E(T)}),(0,s.A)(this,"getChildrenNode",()=>(0,te.jsx)("span",{className:"btn-text",children:this.props.children}))}render(){const T=this.getClassName(),E=this.getChildrenNode();return(0,te.jsx)(k.O,{className:T,onClick:this.onButtonClick,onMouseDown:this.onButtonMouseDown,children:(0,te.jsx)("div",{className:"btn-icon-text-container",children:E})})}}(0,s.A)(Z,"defaultProps",{type:"linear",disabled:!1,size:"common",corner:"smooth"})},15388:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>T});var s=n(74059),p=n(51044),M=n(86634),u=n.n(M),k=n(57464);function te(E){let{className:U,renderIconList:q}=E;return(0,k.jsx)("span",{className:u()("btn-icon",U),children:q.map((J,he)=>(0,k.jsx)("span",{className:"btn-icon-container",children:(0,k.jsx)(k.Fragment,{children:J})},he))})}const Z=te;var z=n(87822);class T extends p.PureComponent{constructor(U){super(U),(0,s.A)(this,"getClassName",()=>{const{className:q,type:J,size:he,corner:V,disabled:Y,icon:H,children:w}=this.props;return u()(q,"button-root",{["type-"+J]:J,["size-"+he]:he,["corner-"+V]:V,"is-disabled":Y,"has-icon":H&&w,"only-icon":H&&w===void 0})}),(0,s.A)(this,"onButtonClick",q=>{if(this.props.disabled){q.preventDefault();return}const{onClick:J}=this.props;J&&J(q)}),(0,s.A)(this,"getIconNode",()=>(0,k.jsx)(Z,{renderIconList:this.state.iconList,className:this.props.className})),(0,s.A)(this,"getChildrenNode",()=>(0,k.jsx)("span",{className:"btn-text",children:this.props.children})),this.state={iconList:this.props.icon?[this.props.icon]:[]}}componentDidUpdate(U){let{icon:q}=U;const{icon:J}=this.props;q!==J&&this.setState({iconList:J?[this.props.icon]:[]})}render(){const U=this.getClassName(),q=this.getIconNode(),J=this.getChildrenNode();return(0,k.jsx)(z.O,{className:U,onClick:this.onButtonClick,children:(0,k.jsxs)("div",{className:u()("btn-icon-text-container"),children:[q,J]})})}}(0,s.A)(T,"defaultProps",{type:"linear",disabled:!1,size:"common",corner:"smooth"})},87822:(Ne,de,n)=>{"use strict";n.d(de,{O:()=>M});var s=n(21676),p=n(73542);const M=s.Ay.div.withConfig({displayName:"styles__StyledButton",componentId:"sc-166yiuz-0"})(["border-radius:6px;display:inline-block;justify-content:center;align-items:center;font-family:inherit;font-size:14px;font-weight:500;line-height:18px;user-select:none;cursor:pointer;.btn-icon-text-container{display:flex;justify-content:center;align-items:center;}.btn-icon{display:flex;justify-content:center;align-items:center;.btn-icon-container{margin-right:4px;width:16px;height:16px;overflow:hidden;}}&[class*='is-disabled']{cursor:not-allowed;opacity:0.4;}&[class*='type-linear']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='type-primary']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='type-secondary']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";border:1px solid ",";}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}}&[class*='type-link']{border:1px solid transparent;padding:0;color:",";&:hover:not([class*='is-disabled']){color:",";}&:active:not([class*='is-disabled']){color:",";}&.link-gray{color:",";&:hover{color:",";}&:active{color:",";}}}&[class*='type-danger']{background:",";color:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";}&:active:not([class*='is-disabled']){background:",";}}&[class*='size-tiny']{padding:4px 8px;max-height:24px;font-size:12px;line-height:14px;.btn-icon-text-container{min-width:46px;}&[class*='type-link']{padding:0;}}&[class*='size-common']{padding:6px 12px;max-height:32px;.btn-icon-text-container{min-width:54px;}&[class*='type-link']{padding:0;}}&[class*='size-medium']{padding:6px 16px;max-height:32px;.btn-icon-text-container{min-width:60px;}&[class*='type-link']{padding:0;}}&[class*='size-large']{padding:8px 16px;max-height:36px;.btn-icon-text-container{min-width:76px;}&[class*='type-link']{padding:0;}.btn-icon-container{margin-right:6px;}}&[class*='corner-smooth']{border-radius:6px;}&[class*='corner-soft']{border-radius:4px;}&[class*='corner-round']{border-radius:calc(infinity * 1px);}&[class*='only-icon']{padding:0;border:none;background:transparent;.btn-icon-text-container{min-width:0;}.btn-icon-container{margin-right:0;svg > *{fill:",";}}&:hover:not([class*='is-disabled']){background:none;.btn-icon-container{svg > *{fill:",";}}}&:active:not([class*='is-disabled']){background:none;.btn-icon-container{svg > *{fill:",";}}}}"],p.q.color_text_L2,p.q.color_bg_normal,p.q.color_split_line,p.q.color_bg_btn_normal_hover,p.q.color_bg_btn_normal_active,p.q.color_text_white,p.q.color_btn_primary_normal,p.q.color_btn_primary_hover,p.q.color_btn_primary_click,p.q.color_text_L2,p.q.color_bg_secondary_btn,p.q.color_btn_secondary_border_normal,p.q.color_btn_secondary_hover,p.q.color_btn_secondary_border_hover,p.q.color_btn_secondary_click,p.q.color_btn_primary_normal,p.q.color_btn_primary_hover,p.q.color_btn_primary_click,p.q.color_text_L2,p.q.color_text_L3,p.q.color_text_L1,p.q.color_btn_danger_normal,p.q.color_text_white,p.q.color_btn_danger_hover,p.q.color_btn_danger_click,p.q.color_text_L2,p.q.color_text_L3,p.q.color_text_L1)},73542:(Ne,de,n)=>{"use strict";n.d(de,{q:()=>p});const s={common_white:"#FFFFFF",common_black:"#000000",primary_blue_01:"#126ACA",primary_blue_02:"#1684FC",primary_blue_03:"#459DFD",primary_blue_04:"#8AC1FD",primary_blue_05:"#B9DAFE",primary_blue_06:"#D0E6FE",primary_blue_07:"#E8F3FF",primary_blue_08:"#F5F8FD",primary_gray_01:"#35445D",primary_gray_02:"#5D6F8F",primary_gray_03:"#7D8CA5",primary_gray_04:"#9EA9BC",primary_gray_05:"#BEC5D2",primary_gray_06:"#D2D9E4",primary_gray_07:"#E1E6EF",primary_gray_08:"#F0F2F7",primary_gray_09:"#F3F5F9",primary_red_01:"#DD4747",primary_red_02:"#F85050",primary_red_03:"#F97373",primary_red_04:"#FDC2C2",primary_red_05:"#FEE5E5"},p={color_btn_primary_normal:s.primary_blue_02,color_btn_primary_hover:s.primary_blue_03,color_btn_primary_click:s.primary_blue_01,color_btn_primary_disable:s.primary_blue_05,color_btn_secondary_click:s.primary_blue_06,color_btn_secondary_normal:s.primary_blue_08,color_btn_secondary_hover:s.primary_blue_07,color_btn_outline_click:s.primary_gray_07,color_btn_outline_normal:s.common_white,color_btn_outline_hover:s.primary_gray_08,color_bg_normal:s.common_white,color_bg_card:s.primary_blue_08,color_bg_menu_click:s.primary_blue_06,color_bg_card_hover:s.primary_gray_07,color_bg_menu_hover:s.primary_blue_07,color_bg_input_hover:s.primary_gray_08,color_bg_input_normal:s.primary_gray_09,color_bg_item_gray:s.primary_gray_08,color_bg_item_hover:s.primary_gray_09,color_bg_fill_common_blue:s.primary_blue_02,color_bg_menu_drag_border:s.primary_blue_06,color_bg_card_hover_fusion_gray:s.primary_gray_06,color_bg_card_hover_fusion_blue:s.primary_blue_05,color_bg_card_hover_fusion:s.primary_gray_06,color_bg_secondary_btn:s.primary_blue_08,color_bg_btn_normal_hover:s.primary_gray_08,color_bg_btn_normal_active:s.primary_gray_07,color_bg_icon_gray:s.primary_gray_04,color_bg_icon_hover:s.primary_gray_03,color_text_L1:s.primary_gray_01,color_text_L2:s.primary_gray_02,color_text_L3:s.primary_gray_03,color_text_L4:s.primary_gray_04,color_text_L5:s.primary_blue_02,color_text_white:s.common_white,color_text_blue:s.primary_blue_02,color_text_blue_hover:s.primary_blue_01,color_text_blue_active:s.primary_blue_03,color_text_disable_01:s.primary_gray_05,color_tab_line:s.primary_gray_06,color_split_line:s.primary_gray_07,color_border_blue_common:s.primary_blue_02,color_btn_border_normal:s.primary_blue_05,color_btn_border_disable:s.primary_blue_07,color_btn_secondary_border_normal:s.primary_blue_06,color_btn_secondary_border_hover:s.primary_blue_07,color_btn_danger_normal:s.primary_red_02,color_btn_danger_hover:s.primary_red_03,color_btn_danger_click:s.primary_red_01,color_btn_danger_disable:s.primary_red_04,color_bg_modal_item_header:s.primary_gray_09,color_bg_light_red:s.primary_red_05,color_alarm_common:s.primary_red_02}},56511:(Ne,de,n)=>{"use strict";n.d(de,{GT:()=>k,Nc:()=>M,lq:()=>u});var s=n(85782),p=n(35236);const M=async(te,Z,z)=>{const[T,E]=await(0,s.Vz)(p.r,te),U="/flpak/w-paknewft?"+new URLSearchParams({upperType:"project-basic",type:"proto2",teamCid:Z,treLen:String(E.byteLength),treRawLen:String(T)}),q=await fetch(U,{method:"POST",body:E,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify(z))}});if(!q.ok)throw Object.assign(new Error("failed to create new flpak: "+U),{message:await q.text(),status:q.status});return q.json()},u=async(te,Z,z)=>{const[T,E]=await(0,s.Vz)(p.r,te),U="/flpak/w-pakfcgmkt?"+new URLSearchParams({upperType:"combo-group",upperAction:"create-reviewable-project",type:"proto2",teamCid:Z,treLen:String(E.byteLength),treRawLen:String(T)}),q=await fetch(U,{method:"POST",body:E,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify(z))}});if(!q.ok)throw Object.assign(new Error("failed to create new fcgmkt: "+U),{message:await q.text(),status:q.status});return q.json()},k=async(te,Z,z)=>{const[T,E]=await(0,s.Vz)(p.r,te),U="/flpak/w-paksclibft?"+new URLSearchParams({upperType:"project-basic",type:"proto2",teamCid:Z,upperCid:z,treLen:String(E.byteLength),treRawLen:String(T)}),q=await fetch(U,{method:"POST",body:E,headers:{"mb-client-opt":encodeURIComponent(JSON.stringify({}))}});if(!q.ok)throw Object.assign(new Error("failed to create new sclib: "+U),{message:await q.text(),status:q.status});return q.json()}},81811:(Ne,de,n)=>{"use strict";n.d(de,{pX:()=>Eo,O3:()=>Ao,tr:()=>Oo,ur:()=>Zs});var s={};if(n.r(s),n.d(s,{Decoder:()=>Ln,Encoder:()=>ts,PacketType:()=>pt,protocol:()=>eo}),n.j!=15)var p=n(62427);if(n.j!=15)var M=n(99478);const u=Object.create(null);u.open="0",u.close="1",u.ping="2",u.pong="3",u.message="4",u.upgrade="5",u.noop="6";const k=Object.create(null);Object.keys(u).forEach(a=>{k[u[a]]=a});const te={type:"error",data:"parser error"},Z=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",z=typeof ArrayBuffer=="function",T=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a&&a.buffer instanceof ArrayBuffer,E=(a,t,o)=>{let{type:h,data:F}=a;return Z&&F instanceof Blob?t?o(F):U(F,o):z&&(F instanceof ArrayBuffer||T(F))?t?o(F):U(new Blob([F]),o):o(u[h]+(F||""))},U=(a,t)=>{const o=new FileReader;return o.onload=function(){const h=o.result.split(",")[1];t("b"+(h||""))},o.readAsDataURL(a)};function q(a){return a instanceof Uint8Array?a:a instanceof ArrayBuffer?new Uint8Array(a):new Uint8Array(a.buffer,a.byteOffset,a.byteLength)}let J;function he(a,t){if(Z&&a.data instanceof Blob)return a.data.arrayBuffer().then(q).then(t);if(z&&(a.data instanceof ArrayBuffer||T(a.data)))return t(q(a.data));E(a,!1,o=>{J||(J=new TextEncoder),t(J.encode(o))})}const V="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Y=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let a=0;a<V.length;a++)Y[V.charCodeAt(a)]=a;const H=a=>{let t=new Uint8Array(a),o,h=t.length,F="";for(o=0;o<h;o+=3)F+=V[t[o]>>2],F+=V[(t[o]&3)<<4|t[o+1]>>4],F+=V[(t[o+1]&15)<<2|t[o+2]>>6],F+=V[t[o+2]&63];return h%3===2?F=F.substring(0,F.length-1)+"=":h%3===1&&(F=F.substring(0,F.length-2)+"=="),F},w=a=>{let t=a.length*.75,o=a.length,h,F=0,ce,Oe,De,Ue;a[a.length-1]==="="&&(t--,a[a.length-2]==="="&&t--);const Ke=new ArrayBuffer(t),Re=new Uint8Array(Ke);for(h=0;h<o;h+=4)ce=Y[a.charCodeAt(h)],Oe=Y[a.charCodeAt(h+1)],De=Y[a.charCodeAt(h+2)],Ue=Y[a.charCodeAt(h+3)],Re[F++]=ce<<2|Oe>>4,Re[F++]=(Oe&15)<<4|De>>2,Re[F++]=(De&3)<<6|Ue&63;return Ke},C=typeof ArrayBuffer=="function",f=(a,t)=>{if(typeof a!="string")return{type:"message",data:v(a,t)};const o=a.charAt(0);return o==="b"?{type:"message",data:y(a.substring(1),t)}:k[o]?a.length>1?{type:k[o],data:a.substring(1)}:{type:k[o]}:te},y=(a,t)=>{if(C){const o=w(a);return v(o,t)}else return{base64:!0,data:a}},v=(a,t)=>{switch(t){case"blob":return a instanceof Blob?a:new Blob([a]);case"arraybuffer":default:return a instanceof ArrayBuffer?a:a.buffer}},S="",x=(a,t)=>{const o=a.length,h=new Array(o);let F=0;a.forEach((ce,Oe)=>{E(ce,!1,De=>{h[Oe]=De,++F===o&&t(h.join(S))})})},I=(a,t)=>{const o=a.split(S),h=[];for(let F=0;F<o.length;F++){const ce=f(o[F],t);if(h.push(ce),ce.type==="error")break}return h};function D(){return new TransformStream({transform(a,t){he(a,o=>{const h=o.length;let F;if(h<126)F=new Uint8Array(1),new DataView(F.buffer).setUint8(0,h);else if(h<65536){F=new Uint8Array(3);const ce=new DataView(F.buffer);ce.setUint8(0,126),ce.setUint16(1,h)}else{F=new Uint8Array(9);const ce=new DataView(F.buffer);ce.setUint8(0,127),ce.setBigUint64(1,BigInt(h))}a.data&&typeof a.data!="string"&&(F[0]|=128),t.enqueue(F),t.enqueue(o)})}})}let l;function L(a){return a.reduce((t,o)=>t+o.length,0)}function R(a,t){if(a[0].length===t)return a.shift();const o=new Uint8Array(t);let h=0;for(let F=0;F<t;F++)o[F]=a[0][h++],h===a[0].length&&(a.shift(),h=0);return a.length&&h<a[0].length&&(a[0]=a[0].slice(h)),o}function se(a,t){l||(l=new TextDecoder);const o=[];let h=0,F=-1,ce=!1;return new TransformStream({transform(Oe,De){for(o.push(Oe);;){if(h===0){if(L(o)<1)break;const Ue=R(o,1);ce=(Ue[0]&128)===128,F=Ue[0]&127,F<126?h=3:F===126?h=1:h=2}else if(h===1){if(L(o)<2)break;const Ue=R(o,2);F=new DataView(Ue.buffer,Ue.byteOffset,Ue.length).getUint16(0),h=3}else if(h===2){if(L(o)<8)break;const Ue=R(o,8),Ke=new DataView(Ue.buffer,Ue.byteOffset,Ue.length),Re=Ke.getUint32(0);if(Re>Math.pow(2,21)-1){De.enqueue(te);break}F=Re*Math.pow(2,32)+Ke.getUint32(4),h=3}else{if(L(o)<F)break;const Ue=R(o,F);De.enqueue(f(ce?Ue:l.decode(Ue),t)),h=0}if(F===0||F>a){De.enqueue(te);break}}}})}const g=4;function A(a){if(a)return ee(a)}function ee(a){for(var t in A.prototype)a[t]=A.prototype[t];return a}A.prototype.on=A.prototype.addEventListener=function(a,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+a]=this._callbacks["$"+a]||[]).push(t),this},A.prototype.once=function(a,t){function o(){this.off(a,o),t.apply(this,arguments)}return o.fn=t,this.on(a,o),this},A.prototype.off=A.prototype.removeListener=A.prototype.removeAllListeners=A.prototype.removeEventListener=function(a,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var o=this._callbacks["$"+a];if(!o)return this;if(arguments.length==1)return delete this._callbacks["$"+a],this;for(var h,F=0;F<o.length;F++)if(h=o[F],h===t||h.fn===t){o.splice(F,1);break}return o.length===0&&delete this._callbacks["$"+a],this},A.prototype.emit=function(a){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),o=this._callbacks["$"+a],h=1;h<arguments.length;h++)t[h-1]=arguments[h];if(o){o=o.slice(0);for(var h=0,F=o.length;h<F;++h)o[h].apply(this,t)}return this},A.prototype.emitReserved=A.prototype.emit,A.prototype.listeners=function(a){return this._callbacks=this._callbacks||{},this._callbacks["$"+a]||[]},A.prototype.hasListeners=function(a){return!!this.listeners(a).length};const le=typeof self<"u"?self:typeof window<"u"?window:Function("return this")();function re(a){for(var t=arguments.length,o=new Array(t>1?t-1:0),h=1;h<t;h++)o[h-1]=arguments[h];return o.reduce((F,ce)=>(a.hasOwnProperty(ce)&&(F[ce]=a[ce]),F),{})}const X=le.setTimeout,we=le.clearTimeout;function ze(a,t){t.useNativeTimers?(a.setTimeoutFn=X.bind(le),a.clearTimeoutFn=we.bind(le)):(a.setTimeoutFn=le.setTimeout.bind(le),a.clearTimeoutFn=le.clearTimeout.bind(le))}const He=1.33;function je(a){return typeof a=="string"?Ee(a):Math.ceil((a.byteLength||a.size)*He)}function Ee(a){let t=0,o=0;for(let h=0,F=a.length;h<F;h++)t=a.charCodeAt(h),t<128?o+=1:t<2048?o+=2:t<55296||t>=57344?o+=3:(h++,o+=4);return o}function tt(a){let t="";for(let o in a)a.hasOwnProperty(o)&&(t.length&&(t+="&"),t+=encodeURIComponent(o)+"="+encodeURIComponent(a[o]));return t}function Pe(a){let t={},o=a.split("&");for(let h=0,F=o.length;h<F;h++){let ce=o[h].split("=");t[decodeURIComponent(ce[0])]=decodeURIComponent(ce[1])}return t}class Q extends Error{constructor(t,o,h){super(t),this.description=o,this.context=h,this.type="TransportError"}}class K extends A{constructor(t){super(),this.writable=!1,ze(this,t),this.opts=t,this.query=t.query,this.socket=t.socket}onError(t,o,h){return super.emitReserved("error",new Q(t,o,h)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const o=f(t,this.socket.binaryType);this.onPacket(o)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,o){return o===void 0&&(o={}),t+"://"+this._hostname()+this._port()+this.opts.path+this._query(o)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const o=tt(t);return o.length?"?"+o:""}}const me="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),Ce=64,ke={};let $e=0,Me=0,Ie;function Je(a){let t="";do t=me[a%Ce]+t,a=Math.floor(a/Ce);while(a>0);return t}function st(a){let t=0;for(Me=0;Me<a.length;Me++)t=t*Ce+ke[a.charAt(Me)];return t}function yt(){const a=Je(+new Date);return a!==Ie?($e=0,Ie=a):a+"."+Je($e++)}for(;Me<Ce;Me++)ke[me[Me]]=Me;let xt=!1;try{xt=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch(a){}const bt=xt;function St(a){const t=a.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||bt))return new XMLHttpRequest}catch(o){}if(!t)try{return new le[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(o){}}function r(){}function ue(){}const ve=function(){return new St({xdomain:!1}).responseType!=null}();class Ae extends K{constructor(t){if(super(t),this.polling=!1,typeof location<"u"){const h=location.protocol==="https:";let F=location.port;F||(F=h?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||F!==t.port}const o=t&&t.forceBase64;this.supportsBinary=ve&&!o,this.opts.withCredentials&&(this.cookieJar=void 0)}get name(){return"polling"}doOpen(){this.poll()}pause(t){this.readyState="pausing";const o=()=>{this.readyState="paused",t()};if(this.polling||!this.writable){let h=0;this.polling&&(h++,this.once("pollComplete",function(){--h||o()})),this.writable||(h++,this.once("drain",function(){--h||o()}))}else o()}poll(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const o=h=>{if(this.readyState==="opening"&&h.type==="open"&&this.onOpen(),h.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(h)};I(t,this.socket.binaryType).forEach(o),this.readyState!=="closed"&&(this.polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this.poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,x(t,o=>{this.doWrite(o,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",o=this.query||{};return this.opts.timestampRequests!==!1&&(o[this.opts.timestampParam]=yt()),!this.supportsBinary&&!o.sid&&(o.b64=1),this.createUri(t,o)}request(t){return t===void 0&&(t={}),Object.assign(t,{xd:this.xd,cookieJar:this.cookieJar},this.opts),new Ve(this.uri(),t)}doWrite(t,o){const h=this.request({method:"POST",data:t});h.on("success",o),h.on("error",(F,ce)=>{this.onError("xhr post error",F,ce)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(o,h)=>{this.onError("xhr poll error",o,h)}),this.pollXhr=t}}class Ve extends A{constructor(t,o){super(),ze(this,o),this.opts=o,this.method=o.method||"GET",this.uri=t,this.data=o.data!==void 0?o.data:null,this.create()}create(){var t;const o=re(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");o.xdomain=!!this.opts.xd;const h=this.xhr=new St(o);try{h.open(this.method,this.uri,!0);try{if(this.opts.extraHeaders){h.setDisableHeaderCheck&&h.setDisableHeaderCheck(!0);for(let F in this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(F)&&h.setRequestHeader(F,this.opts.extraHeaders[F])}}catch(F){}if(this.method==="POST")try{h.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(F){}try{h.setRequestHeader("Accept","*/*")}catch(F){}(t=this.opts.cookieJar)===null||t===void 0||t.addCookies(h),"withCredentials"in h&&(h.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(h.timeout=this.opts.requestTimeout),h.onreadystatechange=()=>{var F;h.readyState===3&&((F=this.opts.cookieJar)===null||F===void 0||F.parseCookies(h)),h.readyState===4&&(h.status===200||h.status===1223?this.onLoad():this.setTimeoutFn(()=>{this.onError(typeof h.status=="number"?h.status:0)},0))},h.send(this.data)}catch(F){this.setTimeoutFn(()=>{this.onError(F)},0);return}typeof document<"u"&&(this.index=Ve.requestsCount++,Ve.requests[this.index]=this)}onError(t){this.emitReserved("error",t,this.xhr),this.cleanup(!0)}cleanup(t){if(!(typeof this.xhr>"u"||this.xhr===null)){if(this.xhr.onreadystatechange=ue,t)try{this.xhr.abort()}catch(o){}typeof document<"u"&&delete Ve.requests[this.index],this.xhr=null}}onLoad(){const t=this.xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this.cleanup())}abort(){this.cleanup()}}if(Ve.requestsCount=0,Ve.requests={},typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",qe);else if(typeof addEventListener=="function"){const a="onpagehide"in le?"pagehide":"unload";addEventListener(a,qe,!1)}}function qe(){for(let a in Ve.requests)Ve.requests.hasOwnProperty(a)&&Ve.requests[a].abort()}const ut=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,o)=>o(t,0),jt=le.WebSocket||le.MozWebSocket,Ut=!0,Zt="arraybuffer",$t=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Dn extends K{constructor(t){super(t),this.supportsBinary=!t.forceBase64}get name(){return"websocket"}doOpen(){if(!this.check())return;const t=this.uri(),o=this.opts.protocols,h=$t?{}:re(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(h.headers=this.opts.extraHeaders);try{this.ws=Ut&&!$t?o?new jt(t,o):new jt(t):new jt(t,o,h)}catch(F){return this.emitReserved("error",F)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let o=0;o<t.length;o++){const h=t[o],F=o===t.length-1;E(h,this.supportsBinary,ce=>{const Oe={};Ut||(h.options&&(Oe.compress=h.options.compress),this.opts.perMessageDeflate&&(typeof ce=="string"?Buffer.byteLength(ce):ce.length)<this.opts.perMessageDeflate.threshold&&(Oe.compress=!1));try{Ut?this.ws.send(ce):this.ws.send(ce,Oe)}catch(De){}F&&ut(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",o=this.query||{};return this.opts.timestampRequests&&(o[this.opts.timestampParam]=yt()),this.supportsBinary||(o.b64=1),this.createUri(t,o)}check(){return!!jt}}class dn extends K{get name(){return"webtransport"}doOpen(){typeof WebTransport=="function"&&(this.transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name]),this.transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this.transport.ready.then(()=>{this.transport.createBidirectionalStream().then(t=>{const o=se(Number.MAX_SAFE_INTEGER,this.socket.binaryType),h=t.readable.pipeThrough(o).getReader(),F=D();F.readable.pipeTo(t.writable),this.writer=F.writable.getWriter();const ce=()=>{h.read().then(De=>{let{done:Ue,value:Ke}=De;Ue||(this.onPacket(Ke),ce())}).catch(De=>{})};ce();const Oe={type:"open"};this.query.sid&&(Oe.data='{"sid":"'+this.query.sid+'"}'),this.writer.write(Oe).then(()=>this.onOpen())})}))}write(t){this.writable=!1;for(let o=0;o<t.length;o++){const h=t[o],F=o===t.length-1;this.writer.write(h).then(()=>{F&&ut(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this.transport)===null||t===void 0||t.close()}}const In={websocket:Dn,webtransport:dn,polling:Ae},un=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,jn=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function mn(a){if(a.length>2e3)throw"URI too long";const t=a,o=a.indexOf("["),h=a.indexOf("]");o!=-1&&h!=-1&&(a=a.substring(0,o)+a.substring(o,h).replace(/:/g,";")+a.substring(h,a.length));let F=un.exec(a||""),ce={},Oe=14;for(;Oe--;)ce[jn[Oe]]=F[Oe]||"";return o!=-1&&h!=-1&&(ce.source=t,ce.host=ce.host.substring(1,ce.host.length-1).replace(/;/g,":"),ce.authority=ce.authority.replace("[","").replace("]","").replace(/;/g,":"),ce.ipv6uri=!0),ce.pathNames=En(ce,ce.path),ce.queryKey=tn(ce,ce.query),ce}function En(a,t){const o=/\/{2,9}/g,h=t.replace(o,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&h.splice(0,1),t.slice(-1)=="/"&&h.splice(h.length-1,1),h}function tn(a,t){const o={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(h,F,ce){F&&(o[F]=ce)}),o}class pn extends A{constructor(t,o){o===void 0&&(o={}),super(),this.binaryType=Zt,this.writeBuffer=[],t&&typeof t=="object"&&(o=t,t=null),t?(t=mn(t),o.hostname=t.host,o.secure=t.protocol==="https"||t.protocol==="wss",o.port=t.port,t.query&&(o.query=t.query)):o.host&&(o.hostname=mn(o.host).host),ze(this,o),this.secure=o.secure!=null?o.secure:typeof location<"u"&&location.protocol==="https:",o.hostname&&!o.port&&(o.port=this.secure?"443":"80"),this.hostname=o.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=o.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=o.transports||["polling","websocket","webtransport"],this.writeBuffer=[],this.prevBufferLen=0,this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},o),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Pe(this.opts.query)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingTimeoutTimer=null,typeof addEventListener=="function"&&(this.opts.closeOnBeforeunload&&(this.beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this.beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this.offlineEventListener=()=>{this.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",this.offlineEventListener,!1))),this.open()}createTransport(t){const o=Object.assign({},this.opts.query);o.EIO=g,o.transport=t,this.id&&(o.sid=this.id);const h=Object.assign({},this.opts,{query:o,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new In[t](h)}open(){let t;if(this.opts.rememberUpgrade&&pn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1)t="websocket";else if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}else t=this.transports[0];this.readyState="opening";try{t=this.createTransport(t)}catch(o){this.transports.shift(),this.open();return}t.open(),this.setTransport(t)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",o=>this.onClose("transport close",o))}probe(t){let o=this.createTransport(t),h=!1;pn.priorWebsocketSuccess=!1;const F=()=>{h||(o.send([{type:"ping",data:"probe"}]),o.once("packet",Xe=>{if(!h)if(Xe.type==="pong"&&Xe.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",o),!o)return;pn.priorWebsocketSuccess=o.name==="websocket",this.transport.pause(()=>{h||this.readyState!=="closed"&&(Re(),this.setTransport(o),o.send([{type:"upgrade"}]),this.emitReserved("upgrade",o),o=null,this.upgrading=!1,this.flush())})}else{const _e=new Error("probe error");_e.transport=o.name,this.emitReserved("upgradeError",_e)}}))};function ce(){h||(h=!0,Re(),o.close(),o=null)}const Oe=Xe=>{const _e=new Error("probe error: "+Xe);_e.transport=o.name,ce(),this.emitReserved("upgradeError",_e)};function De(){Oe("transport closed")}function Ue(){Oe("socket closed")}function Ke(Xe){o&&Xe.name!==o.name&&ce()}const Re=()=>{o.removeListener("open",F),o.removeListener("error",Oe),o.removeListener("close",De),this.off("close",Ue),this.off("upgrading",Ke)};o.once("open",F),o.once("error",Oe),o.once("close",De),this.once("close",Ue),this.once("upgrading",Ke),this.upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{h||o.open()},200):o.open()}onOpen(){if(this.readyState="open",pn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush(),this.readyState==="open"&&this.opts.upgrade){let t=0;const o=this.upgrades.length;for(;t<o;t++)this.probe(this.upgrades[t])}}onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),this.resetPingTimeout(),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":const o=new Error("server error");o.code=t.data,this.onError(o);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this.resetPingTimeout()}resetPingTimeout(){this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn(()=>{this.onClose("ping timeout")},this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}onDrain(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this.getWritablePackets();this.transport.send(t),this.prevBufferLen=t.length,this.emitReserved("flush")}}getWritablePackets(){if(!(this.maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let o=1;for(let h=0;h<this.writeBuffer.length;h++){const F=this.writeBuffer[h].data;if(F&&(o+=je(F)),h>0&&o>this.maxPayload)return this.writeBuffer.slice(0,h);o+=2}return this.writeBuffer}write(t,o,h){return this.sendPacket("message",t,o,h),this}send(t,o,h){return this.sendPacket("message",t,o,h),this}sendPacket(t,o,h,F){if(typeof o=="function"&&(F=o,o=void 0),typeof h=="function"&&(F=h,h=null),this.readyState==="closing"||this.readyState==="closed")return;h=h||{},h.compress=h.compress!==!1;const ce={type:t,data:o,options:h};this.emitReserved("packetCreate",ce),this.writeBuffer.push(ce),F&&this.once("flush",F),this.flush()}close(){const t=()=>{this.onClose("forced close"),this.transport.close()},o=()=>{this.off("upgrade",o),this.off("upgradeError",o),t()},h=()=>{this.once("upgrade",o),this.once("upgradeError",o)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?h():t()}):this.upgrading?h():t()),this}onError(t){pn.priorWebsocketSuccess=!1,this.emitReserved("error",t),this.onClose("transport error",t)}onClose(t,o){(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")&&(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),typeof removeEventListener=="function"&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",t,o),this.writeBuffer=[],this.prevBufferLen=0)}filterUpgrades(t){const o=[];let h=0;const F=t.length;for(;h<F;h++)~this.transports.indexOf(t[h])&&o.push(t[h]);return o}}pn.protocol=g;const Ys=pn.protocol;function Rt(a,t,o){t===void 0&&(t="");let h=a;o=o||typeof location<"u"&&location,a==null&&(a=o.protocol+"//"+o.host),typeof a=="string"&&(a.charAt(0)==="/"&&(a.charAt(1)==="/"?a=o.protocol+a:a=o.host+a),/^(https?|wss?):\/\//.test(a)||(typeof o<"u"?a=o.protocol+"//"+a:a="https://"+a),h=mn(a)),h.port||(/^(http|ws)$/.test(h.protocol)?h.port="80":/^(http|ws)s$/.test(h.protocol)&&(h.port="443")),h.path=h.path||"/";const ce=h.host.indexOf(":")!==-1?"["+h.host+"]":h.host;return h.id=h.protocol+"://"+ce+":"+h.port+t,h.href=h.protocol+"://"+ce+(o&&o.port===h.port?"":":"+h.port),h}const Qs=typeof ArrayBuffer=="function",_n=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a.buffer instanceof ArrayBuffer,bs=Object.prototype.toString,Js=typeof Blob=="function"||typeof Blob<"u"&&bs.call(Blob)==="[object BlobConstructor]",Xs=typeof File=="function"||typeof File<"u"&&bs.call(File)==="[object FileConstructor]";function Rn(a){return Qs&&(a instanceof ArrayBuffer||_n(a))||Js&&a instanceof Blob||Xs&&a instanceof File}function Bn(a,t){if(!a||typeof a!="object")return!1;if(Array.isArray(a)){for(let o=0,h=a.length;o<h;o++)if(Bn(a[o]))return!0;return!1}if(Rn(a))return!0;if(a.toJSON&&typeof a.toJSON=="function"&&arguments.length===1)return Bn(a.toJSON(),!0);for(const o in a)if(Object.prototype.hasOwnProperty.call(a,o)&&Bn(a[o]))return!0;return!1}function es(a){const t=[],o=a.data,h=a;return h.data=kn(o,t),h.attachments=t.length,{packet:h,buffers:t}}function kn(a,t){if(!a)return a;if(Rn(a)){const o={_placeholder:!0,num:t.length};return t.push(a),o}else if(Array.isArray(a)){const o=new Array(a.length);for(let h=0;h<a.length;h++)o[h]=kn(a[h],t);return o}else if(typeof a=="object"&&!(a instanceof Date)){const o={};for(const h in a)Object.prototype.hasOwnProperty.call(a,h)&&(o[h]=kn(a[h],t));return o}return a}function an(a,t){return a.data=Tn(a.data,t),delete a.attachments,a}function Tn(a,t){if(!a)return a;if(a&&a._placeholder===!0){if(typeof a.num=="number"&&a.num>=0&&a.num<t.length)return t[a.num];throw new Error("illegal attachments")}else if(Array.isArray(a))for(let o=0;o<a.length;o++)a[o]=Tn(a[o],t);else if(typeof a=="object")for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&(a[o]=Tn(a[o],t));return a}const qs=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],eo=5;var pt;(function(a){a[a.CONNECT=0]="CONNECT",a[a.DISCONNECT=1]="DISCONNECT",a[a.EVENT=2]="EVENT",a[a.ACK=3]="ACK",a[a.CONNECT_ERROR=4]="CONNECT_ERROR",a[a.BINARY_EVENT=5]="BINARY_EVENT",a[a.BINARY_ACK=6]="BINARY_ACK"})(pt||(pt={}));class ts{constructor(t){this.replacer=t}encode(t){return(t.type===pt.EVENT||t.type===pt.ACK)&&Bn(t)?this.encodeAsBinary({type:t.type===pt.EVENT?pt.BINARY_EVENT:pt.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let o=""+t.type;return(t.type===pt.BINARY_EVENT||t.type===pt.BINARY_ACK)&&(o+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(o+=t.nsp+","),t.id!=null&&(o+=t.id),t.data!=null&&(o+=JSON.stringify(t.data,this.replacer)),o}encodeAsBinary(t){const o=es(t),h=this.encodeAsString(o.packet),F=o.buffers;return F.unshift(h),F}}function wn(a){return Object.prototype.toString.call(a)==="[object Object]"}class Ln extends A{constructor(t){super(),this.reviver=t}add(t){let o;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");o=this.decodeString(t);const h=o.type===pt.BINARY_EVENT;h||o.type===pt.BINARY_ACK?(o.type=h?pt.EVENT:pt.ACK,this.reconstructor=new ns(o),o.attachments===0&&super.emitReserved("decoded",o)):super.emitReserved("decoded",o)}else if(Rn(t)||t.base64)if(this.reconstructor)o=this.reconstructor.takeBinaryData(t),o&&(this.reconstructor=null,super.emitReserved("decoded",o));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let o=0;const h={type:Number(t.charAt(0))};if(pt[h.type]===void 0)throw new Error("unknown packet type "+h.type);if(h.type===pt.BINARY_EVENT||h.type===pt.BINARY_ACK){const ce=o+1;for(;t.charAt(++o)!=="-"&&o!=t.length;);const Oe=t.substring(ce,o);if(Oe!=Number(Oe)||t.charAt(o)!=="-")throw new Error("Illegal attachments");h.attachments=Number(Oe)}if(t.charAt(o+1)==="/"){const ce=o+1;for(;++o&&!(t.charAt(o)===","||o===t.length););h.nsp=t.substring(ce,o)}else h.nsp="/";const F=t.charAt(o+1);if(F!==""&&Number(F)==F){const ce=o+1;for(;++o;){const Oe=t.charAt(o);if(Oe==null||Number(Oe)!=Oe){--o;break}if(o===t.length)break}h.id=Number(t.substring(ce,o+1))}if(t.charAt(++o)){const ce=this.tryParse(t.substr(o));if(Ln.isPayloadValid(h.type,ce))h.data=ce;else throw new Error("invalid payload")}return h}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(o){return!1}}static isPayloadValid(t,o){switch(t){case pt.CONNECT:return wn(o);case pt.DISCONNECT:return o===void 0;case pt.CONNECT_ERROR:return typeof o=="string"||wn(o);case pt.EVENT:case pt.BINARY_EVENT:return Array.isArray(o)&&(typeof o[0]=="number"||typeof o[0]=="string"&&qs.indexOf(o[0])===-1);case pt.ACK:case pt.BINARY_ACK:return Array.isArray(o)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ns{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const o=an(this.reconPack,this.buffers);return this.finishedReconstruction(),o}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function nn(a,t,o){return a.on(t,o),function(){a.off(t,o)}}const to=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class vs extends A{constructor(t,o,h){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=o,h&&h.auth&&(this.auth=h.auth),this._opts=Object.assign({},h),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[nn(t,"open",this.onopen.bind(this)),nn(t,"packet",this.onpacket.bind(this)),nn(t,"error",this.onerror.bind(this)),nn(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(){for(var t=arguments.length,o=new Array(t),h=0;h<t;h++)o[h]=arguments[h];return o.unshift("message"),this.emit.apply(this,o),this}emit(t){if(to.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');for(var o=arguments.length,h=new Array(o>1?o-1:0),F=1;F<o;F++)h[F-1]=arguments[F];if(h.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(h),this;const ce={type:pt.EVENT,data:h};if(ce.options={},ce.options.compress=this.flags.compress!==!1,typeof h[h.length-1]=="function"){const Ue=this.ids++,Ke=h.pop();this._registerAckCallback(Ue,Ke),ce.id=Ue}const Oe=this.io.engine&&this.io.engine.transport&&this.io.engine.transport.writable;return this.flags.volatile&&(!Oe||!this.connected)||(this.connected?(this.notifyOutgoingListeners(ce),this.packet(ce)):this.sendBuffer.push(ce)),this.flags={},this}_registerAckCallback(t,o){var h=this,F;const ce=(F=this.flags.timeout)!==null&&F!==void 0?F:this._opts.ackTimeout;if(ce===void 0){this.acks[t]=o;return}const Oe=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let Ue=0;Ue<this.sendBuffer.length;Ue++)this.sendBuffer[Ue].id===t&&this.sendBuffer.splice(Ue,1);o.call(this,new Error("operation has timed out"))},ce),De=function(){h.io.clearTimeoutFn(Oe);for(var Ue=arguments.length,Ke=new Array(Ue),Re=0;Re<Ue;Re++)Ke[Re]=arguments[Re];o.apply(h,Ke)};De.withError=!0,this.acks[t]=De}emitWithAck(t){for(var o=arguments.length,h=new Array(o>1?o-1:0),F=1;F<o;F++)h[F-1]=arguments[F];return new Promise((ce,Oe)=>{const De=(Ue,Ke)=>Ue?Oe(Ue):ce(Ke);De.withError=!0,h.push(De),this.emit(t,...h)})}_addToQueue(t){var o=this;let h;typeof t[t.length-1]=="function"&&(h=t.pop());const F={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push(function(ce){if(F!==o._queue[0])return;if(ce!==null)F.tryCount>o._opts.retries&&(o._queue.shift(),h&&h(ce));else if(o._queue.shift(),h){for(var De=arguments.length,Ue=new Array(De>1?De-1:0),Ke=1;Ke<De;Ke++)Ue[Ke-1]=arguments[Ke];h(null,...Ue)}return F.pending=!1,o._drainQueue()}),this._queue.push(F),this._drainQueue()}_drainQueue(t){if(t===void 0&&(t=!1),!this.connected||this._queue.length===0)return;const o=this._queue[0];o.pending&&!t||(o.pending=!0,o.tryCount++,this.flags=o.flags,this.emit.apply(this,o.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:pt.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,o){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,o),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(h=>String(h.id)===t)){const h=this.acks[t];delete this.acks[t],h.withError&&h.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case pt.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case pt.EVENT:case pt.BINARY_EVENT:this.onevent(t);break;case pt.ACK:case pt.BINARY_ACK:this.onack(t);break;case pt.DISCONNECT:this.ondisconnect();break;case pt.CONNECT_ERROR:this.destroy();const h=new Error(t.data.message);h.data=t.data.data,this.emitReserved("connect_error",h);break}}onevent(t){const o=t.data||[];t.id!=null&&o.push(this.ack(t.id)),this.connected?this.emitEvent(o):this.receiveBuffer.push(Object.freeze(o))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const o=this._anyListeners.slice();for(const h of o)h.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const o=this;let h=!1;return function(){if(!h){h=!0;for(var F=arguments.length,ce=new Array(F),Oe=0;Oe<F;Oe++)ce[Oe]=arguments[Oe];o.packet({type:pt.ACK,id:t,data:ce})}}}onack(t){const o=this.acks[t.id];typeof o=="function"&&(delete this.acks[t.id],o.withError&&t.data.unshift(null),o.apply(this,t.data))}onconnect(t,o){this.id=t,this.recovered=o&&this._pid===o,this._pid=o,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:pt.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const o=this._anyListeners;for(let h=0;h<o.length;h++)if(t===o[h])return o.splice(h,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const o=this._anyOutgoingListeners;for(let h=0;h<o.length;h++)if(t===o[h])return o.splice(h,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const o=this._anyOutgoingListeners.slice();for(const h of o)h.apply(this,t.data)}}}function Sn(a){a=a||{},this.ms=a.min||100,this.max=a.max||1e4,this.factor=a.factor||2,this.jitter=a.jitter>0&&a.jitter<=1?a.jitter:0,this.attempts=0}Sn.prototype.duration=function(){var a=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),o=Math.floor(t*this.jitter*a);a=Math.floor(t*10)&1?a+o:a-o}return Math.min(a,this.max)|0},Sn.prototype.reset=function(){this.attempts=0},Sn.prototype.setMin=function(a){this.ms=a},Sn.prototype.setMax=function(a){this.max=a},Sn.prototype.setJitter=function(a){this.jitter=a};class Fn extends A{constructor(t,o){var h;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(o=t,t=void 0),o=o||{},o.path=o.path||"/socket.io",this.opts=o,ze(this,o),this.reconnection(o.reconnection!==!1),this.reconnectionAttempts(o.reconnectionAttempts||1/0),this.reconnectionDelay(o.reconnectionDelay||1e3),this.reconnectionDelayMax(o.reconnectionDelayMax||5e3),this.randomizationFactor((h=o.randomizationFactor)!==null&&h!==void 0?h:.5),this.backoff=new Sn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(o.timeout==null?2e4:o.timeout),this._readyState="closed",this.uri=t;const F=o.parser||s;this.encoder=new F.Encoder,this.decoder=new F.Decoder,this._autoConnect=o.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var o;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(o=this.backoff)===null||o===void 0||o.setMin(t),this)}randomizationFactor(t){var o;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(o=this.backoff)===null||o===void 0||o.setJitter(t),this)}reconnectionDelayMax(t){var o;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(o=this.backoff)===null||o===void 0||o.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new pn(this.uri,this.opts);const o=this.engine,h=this;this._readyState="opening",this.skipReconnect=!1;const F=nn(o,"open",function(){h.onopen(),t&&t()}),ce=De=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",De),t?t(De):this.maybeReconnectOnOpen()},Oe=nn(o,"error",ce);if(this._timeout!==!1){const De=this._timeout,Ue=this.setTimeoutFn(()=>{F(),ce(new Error("timeout")),o.close()},De);this.opts.autoUnref&&Ue.unref(),this.subs.push(()=>{this.clearTimeoutFn(Ue)})}return this.subs.push(F),this.subs.push(Oe),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(nn(t,"ping",this.onping.bind(this)),nn(t,"data",this.ondata.bind(this)),nn(t,"error",this.onerror.bind(this)),nn(t,"close",this.onclose.bind(this)),nn(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(o){this.onclose("parse error",o)}}ondecoded(t){ut(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,o){let h=this.nsps[t];return h?this._autoConnect&&!h.active&&h.connect():(h=new vs(this,t,o),this.nsps[t]=h),h}_destroy(t){const o=Object.keys(this.nsps);for(const h of o)if(this.nsps[h].active)return;this._close()}_packet(t){const o=this.encoder.encode(t);for(let h=0;h<o.length;h++)this.engine.write(o[h],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close"),this.engine&&this.engine.close()}disconnect(){return this._close()}onclose(t,o){this.cleanup(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,o),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const o=this.backoff.duration();this._reconnecting=!0;const h=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(F=>{F?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",F)):t.onreconnect()}))},o);this.opts.autoUnref&&h.unref(),this.subs.push(()=>{this.clearTimeoutFn(h)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const An={};function ss(a,t){typeof a=="object"&&(t=a,a=void 0),t=t||{};const o=Rt(a,t.path||"/socket.io"),h=o.source,F=o.id,ce=o.path,Oe=An[F]&&ce in An[F].nsps,De=t.forceNew||t["force new connection"]||t.multiplex===!1||Oe;let Ue;return De?Ue=new Fn(h,t):(An[F]||(An[F]=new Fn(h,t)),Ue=An[F]),o.query&&!t.query&&(t.query=o.queryKey),Ue.socket(o.path,t)}if(Object.assign(ss,{Manager:Fn,Socket:vs,io:ss,connect:ss}),n.j!=15)var os=n(5904);if(n.j!=15)var Ct=n(17255);if(n.j!=15)var sn=n(33386);if(n.j!=15)var _s=n(99681);const rs=n.j!=15?2*1e3:null,is=n.j!=15?24*1e3:null,zn=n.j!=15?48*1e3:null,Un=()=>{},no=()=>({destroy:Un,reset:Un,emitAsync:so,emitLossy:Un,getIsConnected:()=>!0,getSocketId:()=>"",getReadyPromise:()=>Promise.resolve(),getOffsetMsec:()=>0,DUMMY:!0}),so=async(a,t)=>({type:a,payload:t,isDummy:!0}),oo=a=>{let{query:{uId:t,fURL:o=globalThis.location.href.slice(0,128),uTag:h=Ct.CH,vTag:F=_s.r,rKey:ce},lossyListenerMap:Oe={},queueListenerMap:De={},socketUrl:Ue=globalThis.location.origin,socketPath:Ke=Ct.$r,usePolling:Re=!1}=a;if(!t||!ce)throw new Error("SKT bad query");const Xe=(0,os.I6)();let _e,Se=(0,p.wT)(),We=0,Qe=null;const lt=()=>{_e!==void 0&&(_e.close(),_e=void 0,Se=(0,p.wT)(),wt=!1,Qe&&clearTimeout(Qe),Qe=null)},Nt=()=>{lt();const ct=new Fn(Ue,{path:Ke,query:{uId:t,fURL:o,uTag:h,vTag:F,rKey:ce},transports:Re?["polling","websocket"]:["websocket"],timeout:zn,reconnectionDelay:Math.round((rs+is)/2),reconnectionDelayMax:zn,randomizationFactor:(is-rs)/(is+rs),closeOnBeforeunload:!1}).socket(Ct.I4);ct.on("connect_error",Ze=>{console.log("[socket] connect_error",Ze),Ze.message.includes(Ct.uB)&&(lt(),Xe.dispatchEvent({type:Ct.VU,payload:{error:Ze}})),Xe.dispatchEvent({type:Ct.Ei,payload:{error:Ze}})}),ct.on("connect",async()=>{console.log("[socket] connect");{const Ze=Date.now(),ot=await(0,p.hq)(Ht(Ct.Ip),10*1e3,Ct.Ip).catch(async ht=>{throw(0,M.et)(Nt,2*1e3),console.warn(Ct.Ip,ht),Se.reject(ht),ht}),et=Date.now();We=ot-(Ze+et)*.5}Qe=(0,M.et)(()=>{!_e||!_e.connected||wt||Nt()},(1+Math.random())*30*60*1e3),console.log("[socket] ready"),Xe.dispatchEvent({type:Ct.zS}),Se.resolve()});{let Ze=Promise.resolve();const ot=(et,ht)=>{const mt=Ze.then(et);return Ze=mt.then(Un,Ft=>{(0,sn.p)("SKT-QA-"+ht,Ft)}),mt};Xe.clear();for(const[et,ht]of Object.entries(Oe))ct.on(et,mt=>ht({type:et,payload:mt})),Xe.addEventListener(et,mt=>{let{payload:Ft}=mt;return ht({type:et,payload:Ft})});for(const[et,ht]of Object.entries(De))ct.on(et,mt=>ot(()=>ht({type:et,payload:mt}),et)),Xe.addEventListener(et,mt=>{let{payload:Ft}=mt;return ot(()=>ht({type:et,payload:Ft}),et)})}_e=ct};let wt=!1;const Ht=async(qt,ct)=>{wt=!0;const{error:Ze,result:ot}=await(0,p.hq)(new Promise(et=>_e.emit(qt,ct,et)),42*1e3,"SKT-USR "+qt).finally(()=>{wt=!1});if(Ze!==void 0)throw Object.assign(new Error(typeof Ze=="object"&&Ze.message||"SKT-USR "+qt),Ze);return ot},zt=(qt,ct)=>{_e.volatile.emit(qt,ct)};return Nt(),{destroy:lt,reset:Nt,emitAsync:Ht,emitLossy:zt,getIsConnected:()=>_e&&_e.connected||!1,getSocketId:()=>_e&&_e.id||"",getReadyPromise:()=>Se.promise,getOffsetMsec:()=>We}};var fn=n(77885),Yt=n(96828),Cn=n(78463),Bt=n(49646);if(n.j!=15)var Et=n(833);if(n.j!=15)var Wt=n(60307);var yn=n(49950);const On=0,ws={cDebugMin:On,cCmtPack:On+1,cCmtThread:On+2,cCmtContent:On+3,cDebugMax:35},Mn=(0,yn.W)("cmt-type",ws),Ss=Mn.pack("cCmtPack"),Wn=Mn.pack("cCmtThread"),Cs=Mn.pack("cCmtContent"),Ms=new Set(["cCmtPack","cCmtThread","cCmtContent"]),ro=new Set([Ss,Wn,Cs]);if(n.j!=15)var Ns=n(45043);const io={unstyled:0,"header-one":1,"header-two":2,"header-three":3,"ordered-list-item":4,"unordered-list-item":5,atomic:6},Is=(0,yn.W)("DraftBlockType",io),js={left:0,center:1,right:2,justify:3},Hn=(0,yn.W)("DraftAlignment",js),ao={LINK:0,IMAGE:1},Es=(0,yn.W)("DraftEntityMapType",ao),lo={MUTABLE:0,IMMUTABLE:1,SEGMENTED:2},as=(0,yn.W)("DraftEntityMapMutability",lo),ks={_blank:0,_self:1},Ts=(0,yn.W)("DraftLinkTargetOption",ks),co=a=>{const t={};return Object.keys(a).forEach(o=>{const{type:h,mutability:F,data:ce}=a[o],Oe=[Es.pack(h),as.pack(F)],De=[];h==="LINK"?(Oe.push(Ts.pack(ce.targetOption)),De.push(ce.url||"")):h==="IMAGE"&&(Oe.push((0,Et.c5)(ce.width),Hn.pack(ce.alignment)),De.push(ce.src||"",ce.alt||"")),t["e/"+o]=[Oe.join(Wt.xi),...De]}),t},uo=a=>{const t={};for(const o of Object.keys(a)){if(!o.startsWith("e/"))continue;const h=o.split("e/")[1],[F,...ce]=a[o],[Oe,De,Ue,Ke]=F.split(Wt.xi),Re=Es.parse(Oe),Xe=as.parse(De);t[h]=Re==="LINK"?{type:Re,mutability:Xe,data:{url:ce[0],targetOption:Ts.parse(Ue)}}:{type:Re,mutability:Xe,data:{src:ce[0],alt:ce[1],width:(0,Et._3)(Ue),alignment:Hn.parse(Ke)}}}return t},Ls=a=>{const t={};return Object.keys(a).forEach(o=>{const{text:h,type:F,depth:ce,inlineStyleRanges:Oe,entityRanges:De,data:{textAlign:Ue,textIndent:Ke,paraSpacing:Re}}=a[o],Xe=[Is.packDef(F,"unstyled"),(0,Et.c5)(ce),Ue?Hn.pack(Ue):"",Ke!==void 0?(0,Et.c5)(Ke):"",Re!==void 0?(0,Bt.ZI)(Re):""];t["b/"+o]=[Xe.join(Wt.xi),h,Oe.map(_e=>{let{offset:Se,length:We,style:Qe}=_e;return ln(Se,We,Qe)}),De.map(_e=>{let{offset:Se,length:We,key:Qe}=_e;return ls(Se,We,Qe)})]}),t},ls=(a,t,o)=>""+(0,Et.c5)(a)+Wt.xi+(0,Et.c5)(t)+Wt.xi+(0,Et.c5)(o),po=a=>{const t=a.indexOf(Wt.xi),o=a.indexOf(Wt.xi,t+1);return[(0,Et._3)(a.slice(0,t)),(0,Et._3)(a.slice(t+1,o)),(0,Et._3)(a.slice(o+1))]},ln=(a,t,o)=>""+(0,Et.c5)(a)+Wt.xi+(0,Et.c5)(t)+Wt.xi+o,ho=a=>{const t=a.indexOf(Wt.xi),o=a.indexOf(Wt.xi,t+1);return[(0,Et._3)(a.slice(0,t)),(0,Et._3)(a.slice(t+1,o)),a.slice(o+1)]},_t=a=>{const t={};for(const o of Object.keys(a)){if(!o.startsWith("b/"))continue;const h=o.split("b/")[1],[F,ce,Oe,De]=a[o],[Ue,Ke,Re,Xe,_e]=F.split(Wt.xi),Se={};Re!==""&&(Se.textAlign=Hn.parse(Re)),Xe!==""&&(Se.textIndent=(0,Et._3)(Xe)),_e!==""&&_e!==void 0&&(Se.paraSpacing=(0,Bt.bp)(_e)),t[h]={text:ce,type:Is.parse(Ue),depth:(0,Et._3)(Ke),inlineStyleRanges:Oe.map(We=>{const[Qe,lt,Nt]=ho(We);return{offset:Qe,length:lt,style:Nt}}),entityRanges:De.map(We=>{const[Qe,lt,Nt]=po(We);return{offset:Qe,length:lt,key:Nt}}),data:Se}}return t},As=a=>{let{message:t}=a;const{blocks:o={},entityMap:h={}}=t;return{...Ls(o),...co(h)}},Vn=a=>({message:{blocks:_t(a),entityMap:uo(a)}}),cs=a=>({T:Mn.pack(a.type),IC:[(0,Et.c5)(a.index),(0,Et.vH)(a.ctime)].join(Wt.xi)}),ds=a=>{const[t,o]=a.IC.split(Wt.xi);return{type:Mn.parse(a.T),index:(0,Et._3)(t),ctime:(0,Et.TT)(o)}},Os=a=>({...cs(a),R:a.refKey}),mo=a=>({...ds(a),refKey:a.R}),Ps=a=>({...cs(a),...As(a),xy:(0,Bt.x3)(a.x,a.y),wh:(0,Bt.x3)(a.w,a.h),z:(0,Bt.ZI)(a.z),userInfo:a.userInfo,userId:(0,Bt.ZI)(a.userId),mtime:(0,Et.vH)(a.mtime),isCompleted:(0,Ns.rK)(a.isCompleted),canvasVec2:a.canvasVec2?{x:(0,Bt.ZI)(a.canvasVec2.x),y:(0,Bt.ZI)(a.canvasVec2.y)}:a.canvasVec2,canvasCid:a.canvasCid,threadType:a.threadType}),Ds=a=>{const[t,o]=(0,Bt.fm)(a.xy),[h,F]=(0,Bt.fm)(a.wh),ce=a.canvasVec2?(0,Bt.bp)(a.canvasVec2.x):void 0,Oe=a.canvasVec2?(0,Bt.bp)(a.canvasVec2.y):void 0,De=(0,Bt.bp)(a.z);return{...ds(a),...Vn(a),x:t,y:o,w:h,h:F,z:De,mtime:(0,Et.TT)(a.mtime),userInfo:a.userInfo,userId:(0,Bt.bp)(a.userId),isCompleted:(0,Ns.Vf)(a.isCompleted),canvasCid:a.canvasCid,threadType:a.threadType,canvasVec2:ce&&Oe?{x:ce,y:Oe}:void 0}},Gn=a=>({...cs(a),...As(a),mtime:(0,Et.vH)(a.mtime),userInfo:a.userInfo}),Rs=a=>({...ds(a),...Vn(a),mtime:(0,Et.TT)(a.mtime),userInfo:a.userInfo}),go=a=>{try{switch(a.T){case void 0:return a;case Ss:return mo(a);case Wn:return Ds(a);case Cs:return Rs(a);default:throw new Error("invalid attr: "+a.T+" -> "+Mn.parse(a.T))}}catch(t){throw(0,sn.p)("toP2CHA badDat",t,a),t}},Bs=a=>{try{switch(a.type){case void 0:return a;case"cCmtPack":return Os(a);case"cCmtThread":return Ps(a);case"cCmtContent":return Gn(a);default:throw new Error("invalid attr: "+a.type)}}catch(t){throw(0,sn.p)("toP2CCA badDat",t,a),t}};if(n.j!=15)var fo=n(42490);const Fs=(a,t)=>{const o=De=>{const Ue=a(De);return Ue===void 0?[]:Ue.sub.map(Ke=>a(Ke))},h=(0,fo.MX)(De=>De.sub.map(Ue=>a(Ue))),F=(De,Ue)=>{const Ke=a(De);Ke!==void 0&&(Ue(Ke)||Ke.sub.length!==0&&h(Ke,Ue))};return{getHotItem:a,getSubHotItemList:o,walkHotItemSubtree:F,isTrashed:De=>(0,Yt.DM)(De,t.getFlatTree()),__devHotTree:function(De){De===void 0&&(De=Yt.gT);const Ue=new Map,Ke=Re=>{let Xe=Ue.get(Re);return Xe===void 0&&Ue.set(Re,Xe={}),Xe};return F(De,Re=>{Ke(Re.sup)[Re.key]=Re.hotAttr,Re.sub.length!==0&&(Ke(Re.sup)[Re.key+" SUB"]=Ke(Re.key))}),Ke(a(De).sup)}}};if(n.j!=15)var zs=n(57947);if(n.j!=15)var us=n(25813);var yo=n(99142),Xt=n(56927);if(n.j!=15)var xo=n(19371);const bo=(a,t,o)=>{const h=_e=>{if(_e===void 0||_e.length===0)return;const Se=new Set;for(const Qe of _e)Se.add(Qe.key),Qe.type==="I"&&Se.add(Qe.keySup);const We=(0,Yt.CF)([...Se].filter(Yt.xe),o.getFlatTree());a({type:xo.a,payload:We})},F=_e=>{const Se=[],We=_e(Se);return h(o.applyDiff(Se)),We},ce=_e=>F(Se=>{const We=[],Qe=_e(We);for(const lt of We)lt.type==="update"?Ke(lt.hotItem,Se):lt.type==="delete"&&Re(lt.key,Se);return Qe}),Oe=function(){const _e=[],Se=[];let We,Qe=!1;for(var lt=arguments.length,Nt=new Array(lt),wt=0;wt<lt;wt++)Nt[wt]=arguments[wt];for(const Ht of Nt){We=Ht(_e);const zt=o.applyDiff(_e);zt!==void 0&&(Se.push(...zt),Qe?o.combineLastUndo():Qe=!0)}return h(Se),We},De=function(){for(var _e=arguments.length,Se=new Array(_e),We=0;We<_e;We++)Se[We]=arguments[We];const Qe=Se.map(lt=>Nt=>{const wt=[],Ht=lt(wt);for(const zt of wt)zt.type==="update"?Ke(zt.hotItem,Nt):zt.type==="delete"&&Re(zt.key,Nt);return Ht});return Oe(...Qe)},Ue=(_e,Se)=>{for(const We of Object.keys(_e)){const Qe=_e[We],lt=Se[We];Qe!==lt&&(0,zs.sw)(Qe)&&(0,zs.sw)(lt)&&JSON.stringify(Qe)===JSON.stringify(lt)&&(_e[We]=lt)}},Ke=(_e,Se)=>{const We=(0,Yt.PV)(_e.key,o.getFlatTree());if(We===void 0)Se.push({type:Xt.UF,key:_e.key,keySup:_e.sup}),Se.push({type:Xt.qC,key:_e.key,attrDiff:Bs(_e.hotAttr)});else{We.sup!==_e.sup&&Se.push({type:Xt.UF,key:_e.key,keySup:_e.sup});const Qe=Bs(_e.hotAttr);Ue(Qe,We.attr);const lt=(0,yo.m)(Qe,We.attr);lt!==void 0&&Se.push({type:Xt.qC,key:_e.key,attrDiff:lt})}},Re=(_e,Se)=>{o.getFlatItem(_e)&&Se.push({type:Xt.UF,key:_e,keySup:Yt.Ri})},Xe=(_e,Se,We)=>{o.getFlatItem(_e)&&o.getFlatItem(Se)&&We.push({type:Xt.UF,key:_e,keySup:Se})};return{_edit:F,_editAction:ce,_editBatch:Oe,_editActionBatch:De,onRemotePatchList:_e=>{const Se=[];for(const We of _e){const Qe=o.applyDiffRemote(We.diffList,We.clock);Qe!==void 0&&Se.push(...Qe)}h(Se)},updateHotItem:_e=>F(Se=>{Ke(_e,Se)}),updateHotItemBatch:_e=>F(Se=>{for(const We of _e)Ke(We,Se)}),deleteHotItem:_e=>F(Se=>{Re(_e,Se)}),deleteHotItemBatch:_e=>F(Se=>{for(const We of _e)Re(We,Se)}),moveHotItem:(_e,Se)=>F(We=>{Xe(_e,Se,We)}),moveHotItemBatch:_e=>F(Se=>{for(const[We,Qe]of _e)Xe(We,Qe,Se)}),editHotItemBatch:_e=>F(Se=>{for(const We of _e)We.type==="update"?Ke(We.hotItem,Se):We.type==="delete"&&Re(We.key,Se)}),updateHotAttrMerge:(_e,Se)=>F(We=>{const Qe=t(_e);if(Qe===void 0)throw new Error("no item: "+_e);Ke((0,us.MK)(Qe,"hotAttr",{...Qe.hotAttr,...Se}),We)}),updateHotAttrKV:(_e,Se,We)=>F(Qe=>{const lt=t(_e);if(lt===void 0)throw new Error("no item: "+_e);Ke((0,us.MK)(lt,"hotAttr",(0,us.MK)(lt.hotAttr,Se,We)),Qe)}),canUndo:o.canUndo,canRedo:o.canRedo,combineMergeMark:o.combineMergeMark,combineMerge:o.combineMerge,undo:()=>{h(o.undo())},redo:()=>{h(o.redo())}}};var Zn=n(66451);const vo=a=>{const t=(0,os.wz)(),o=t.subscribe,h=t.unsubscribe,F=Us(a),ce=Fs(F,a),Oe=()=>_o(a.getFlatTree),De=bo(t.send,F,a),{_edit:Ue,_editAction:Ke,_editBatch:Re,_editActionBatch:Xe,..._e}=De;return{subscribe:o,unsubscribe:h,...ce,createRoCmtStore:Oe,..._e}},Us=a=>t=>{const o=a.getFlatTree().get(t);if(o===void 0)return;const h=a.getOCI(o);return h.hotItem===void 0&&(h.hotItem={key:o.key,sup:o.sup,sub:o.sub,hotAttr:go(o.attr)}),h.hotItem},_o=a=>{const t=(0,Zn.b$)(a(),(0,fn.aF)({})),o=()=>t.getFlatTree()!==a(),h=()=>t.sync(a()),F=Us(t),ce=Fs(F,t);return{hasUpdate:o,syncUpdate:h,...ce}};var wo=n(56511),ps=n(99611);if(n.j!=15)var So=n(17036);if(n.j!=15)var Co=n(50544);var xn=n(73051),Kn=n(85782),Pn=n(35236);const hs=a=>{const[t,o,h]=a,F=async()=>(0,Kn.BT)(Pn.r,t.tre),ce=async function(Re){Re===void 0&&(Re=-1);const Xe=[];for(const{pbx:_e,t10s:Se}of o){if(Se<Re)continue;const We=(0,Co.CE)(await Pn.r.gunzipU8AAsync(new Uint8Array(_e)));for(const Qe of(0,xn.Pb)(We))Xe.push(Qe)}for(const{pch:_e,t10s:Se}of h)Se<Re||Xe.push(_e);return Xe},Oe=async function(Re){Re===void 0&&(Re=-1);const Xe=[];for(const _e of await ce(Re))Xe.push((0,Xt.iz)(_e));return Xe},De=async function(Re){Re===void 0&&(Re=-1);const Xe=[];for(const _e of await ce(Re)){const{clock:Se,diffList:We}=(0,Xt.iz)(_e);Xe.push([(0,fn._r)(Se).slice(5),We,_e])}return Xe};return{getParsedTre:F,getPchList:ce,getPatchList:Oe,getReadablePatchList:De,filterReadablePatchList:async function(Re,Xe){return Xe===void 0&&(Xe=-1),(await De(Xe)).filter(_e=>{let[,,Se]=_e;return Se.includes(Re)})},getMergedReadablePatchList:async function(Re,Xe){Re===void 0&&(Re=-1),Xe===void 0&&(Xe=16);const _e=[];let Se={timePrefix:"",countDI:0,countDA:0,userIdSet:new Set};const We=Qe=>{Se.timePrefix!==Qe&&(Se.timePrefix!==""&&_e.push(Se.timePrefix.slice(5)+"..Z I*"+(Se.countDI||"-")+" A*"+(Se.countDA||"-")+" ["+[...Se.userIdSet].join(",")+"]"),Se={timePrefix:Qe,countDI:0,countDA:0,userIdSet:new Set})};for(const{clock:[Qe,lt],diffList:Nt}of await Oe(Re)){const wt=new Date(Qe).toISOString().slice(0,Xe);We(wt),Se.userIdSet.add(lt);for(const{type:Ht}of Nt)Ht===Xt.UF?Se.countDI++:Se.countDA++}return We(""),_e},flpak:a}},ms=a=>Math.floor(a/10/1e3),gs=a=>{const[t]=(0,Xt.cx)(a);return ms(t)},Mo=a=>{let[t,o,h]=a;return h.length!==0?h[h.length-1].t10s:o.length!==0?o[o.length-1].t10s:t.t10s};if(n.j!=15)var fs=n(71544);const gn=(a,t,o)=>{let h=!1,F=0,ce=0;const Oe=new Map;let De=[],Ue,Ke,Re,Xe;const _e=Ze=>{let[ot,,et]=Ze;if(ot!==a)return!1;const ht=(0,xn.vZ)(et);for(const mt of ht)De.push([mt,gs(mt),!1]);return Re!==void 0&&Re(ht.map(mt=>(0,Xt.iz)(mt))),!0},Se=async(Ze,ot)=>{if(Ke!==void 0)throw new Error("no re-init flatStore");Ue=Ze,Xe=ot;const et=await Xe(a);{const At="INSP-INIT-FLPAK";location.hash.includes(At)&&Object.assign(globalThis,{[At+"/"+a]:{...hs(et),__flpakKey:a,__name:t}})}F=Mo(et);const ht=(0,Zn.OR)(),mt=await(0,Kn.TN)(Pn.r,et,(At,Be)=>(0,xn.qG)(At,Be,ht.mutateTFAAFNB));let Ft=Yt.xL;{const At="BYPASS-FLPAK";location.hash.includes(At)&&(Ft=Be=>console.warn("["+At+"|vFT]",Be))}for(const[At,Be,dt]of De)!dt&&F>=Be||(0,xn.qG)(mt,At,ht.mutateTFAAFNB);Ke=(0,Zn.b$)(mt,Ze,(At,Be,dt)=>{if(h&&console.log("flat|onPatch",{diffList:At,clock:Be,isLocal:dt}),De.push([(0,Xt.AO)({diffList:At,clock:Be}),ms(Be[0]),dt]),dt)return;const[Vt,en,Nn,bn]=Be;ce=Math.max(ce,Vt);const hn=(0,fn.au)(en,Nn);if(Ue.selfCCK===hn)throw new Error("unexpected selfCck: "+hn);const Yn=Oe.get(hn)||-1;Yn>bn&&console.warn("bad editId for cck: "+hn+", last: "+Yn+", new: "+bn),Oe.set(hn,bn)},(At,Be)=>{console.warn("flat|requestResync",{error:At,message:Be})}),Re=At=>{for(const Be of At)Ke.applyDiffRemote(Be.diffList,Be.clock)};const Lt=ht.getFixDiffList(mt);Lt.length&&(Ke.applyDiff(Lt),Ke.resetUndo()),o===!0&&Ft(mt)},We=Ze=>{Re=Ze},Qe=()=>Ue,lt=()=>Ke,Nt=async function(Ze){if(Ze===void 0&&(Ze=""),o===!0)try{if(!Ke||!F||!zt())return;const ot=Ke.getFlatTree(),[et,ht]=await(0,Kn.Vz)(Pn.r,ot);await(0,fs.hp)("//FSS/"+t+"/"+a,new Blob([ht],{type:"application/octet-stream"}),{message:Ze,treRawSize:et,treSize:ht.byteLength})}catch(ot){console.warn("saveSOS",Ze,ot)}},wt=async()=>{if(o===!0)try{const Ze=await(0,fs.Ey)("//FSS/"+t+"/"+a);if(Ze.length){console.warn("[loadSOS] found "+Ze.length+" record");const{data:ot,extra:et}=Ze[0];console.warn("[loadSOS] load record from "+new Date(et.time).toISOString());const ht=await(0,So.WL)(ot),mt=await(0,Kn.BT)(Pn.r,ht);return console.warn("[loadSOS] record tree: "+mt.slowSize()),{tree:mt,...et}}}catch(Ze){console.warn("loadSOS",Ze)}},Ht=async()=>{if(o===!0)try{return await(0,fs.EP)("//FSS/"+t+"/"+a)}catch(Ze){console.warn("freeSOS",Ze)}},zt=()=>{if(o!==!0)return!1;for(const[,,Ze]of De)if(Ze)return!0;return!1};return{flpakKey:a,name:t,__setIsDev:Ze=>{h=Ze},onRemotePayloadSave:_e,bind:Se,useExtORPL:We,getClockStore:Qe,getFlatStore:lt,saveSOS:Nt,loadSOS:wt,freeSOS:Ht,hasPatch:zt,preSettlePatch:()=>{if(o!==!0)return[];const Ze=[];for(const[ot,,et]of De)et&&Ze.push(ot);return Ze},postSettlePatch:Ze=>{o===!0&&(De=De.filter(ot=>{let[et,ht,mt]=ot;return!mt||!Ze.includes(et)}))}}},Uo=(a,t,o)=>{let h,F;return{flpakKey:a,name:"[DUMMY]"+t,__setIsDev:ce=>{},onRemotePayloadSave:ce=>{let[Oe,,De]=ce;return!1},bind:async(ce,Oe)=>{if(F!==void 0)throw new Error("no re-init flatStore");h=ce,F=(0,Zn.b$)((0,Yt.Id)(),ce)},useExtORPL:ce=>{},getClockStore:()=>h,getFlatStore:()=>F,saveSOS:async ce=>{},loadSOS:async()=>{},freeSOS:async()=>0,hasPatch:()=>!1,preSettlePatch:()=>[],postSettlePatch:ce=>{}}};if(n.j!=15)var No=n(54704);const Ws=n.j!=15?Bt.x3:null,Io=n.j!=15?Bt.fm:null,Hs=a=>{const t=new Map;let o=!1,h=!1,F,ce,Oe=()=>"";const De=()=>{t.clear(),Re()};let Ue=0;const Ke=ct=>{const Ze={socketId:ct,index:Ue++,lastCurAt:0,cur:void 0,lastStatAt:0,stat:void 0};return t.set(ct,Ze),o=!0,Ze},Re=(0,p.Ds)(async()=>{if(await(0,M.Y_)(2*1e3),F===void 0)return;const ct=[];for(const{socketId:ht,lastStatAt:mt}of t.values())mt!==0&&ct.push(ht);for(const[ht,mt,Ft]of await F([a,ct])){const Lt=t.get(ht)||Ke(ht);Lt.lastStatAt=Date.now(),Lt.stat=[mt,Ft]}const[Ze,ot,et]=Se;Ze&&ce&&ce([a,Ze,Ws(ot,et)])},ct=>{console.warn("_tPS",ct)}).trigger,Xe=ct=>{let[Ze,ot,et]=ct;const[ht,mt]=Io(et),Ft=[ht,mt,ot],Lt=t.get(Ze)||Ke(Ze);Lt.lastCurAt=Date.now(),Lt.cur=Ft,Lt.lastStatAt===0?Re():h=!0},_e=ct=>{let[Ze,ot]=ct;const et=t.has(ot);Ze===0&&et&&(t.delete(ot),o=!0)};let Se=["",NaN,NaN],We=0;const Qe=function(ct){ct===void 0&&(ct=Se[0]);const Ze=Oe();if(Ze==="")return!1;for(const{socketId:ot,cur:et}of t.values())if(et&&et[2]===ct&&ot!==Ze)return!0;return!1},lt=function(ct,Ze,ot){Ze===void 0&&(Ze=Bt.Ni),ot===void 0&&(ot=Bt.Ni),!(Se[0]===ct&&Se[1]===Ze&&Se[2]===ot)&&(Se[0]!==ct&&(We=1),!(!We&&(t.size<2||!Qe(ct)))&&(Se=[ct,Ze,ot],We&&We--,ce&&ce([a,ct,Ws(Ze,ot)])))},Nt=async(ct,Ze,ot)=>{if(F!==void 0)throw new Error("no re-init fetchCurStat");F=ct,ce=Ze,Oe=ot},wt=()=>{const ct=Oe(),Ze=[];if(ct==="")return Ze;for(const{socketId:ot,stat:et}of t.values()){if(et===void 0)continue;const[ht,mt]=et;ct===ot?Ze.unshift([ot,ht,mt]):Ze.push([ot,ht,mt])}return Ze},Ht=ct=>{const Ze=Oe(),ot=[];if(Ze==="")return ot;for(const{socketId:et,index:ht,cur:mt,stat:Ft}of t.values()){if(Ze===et||mt===void 0||Ft===void 0)continue;const[Lt,At,Be]=mt;if(Be!==ct||Lt===Bt.Ni)continue;const[dt,Vt]=Ft;ot.push([ht,Lt,At,dt,Vt])}return ot},zt=()=>o?(o=!1,!0):!1,qt=()=>h?(h=!1,!0):!1;return{flpakKey:a,__curStatMap:t,onSocketConnect:De,onRemoteCur:Xe,onRemoteSocket:_e,hasOtherUser:Qe,tryPackCur:lt,bind:Nt,getRoomStat:wt,getSocketStat:wt,getCurStat:Ht,pollRoomChg:zt,pollCurChg:qt,pollChange:qt}},jo=a=>{const t=()=>{},o=Re=>{let[Xe,_e,Se]=Re},h=Re=>{let[Xe,_e]=Re},F=Re=>!1,ce=(Re,Xe,_e)=>{},Oe=async(Re,Xe,_e)=>{},De=()=>[],Ue=Re=>[],Ke=()=>!1;return{flpakKey:a,onSocketConnect:t,onRemoteCur:o,onRemoteSocket:h,hasOtherUser:F,tryPackCur:ce,bind:Oe,getRoomStat:De,getSocketStat:De,getCurStat:Ue,pollRoomChg:Ke,pollCurChg:Ke,pollChange:Ke}},Vs=()=>{};let Gs=!1;const Eo=a=>{Gs=a},ko="dat",To="cmt",Lo="mkt:dat",ys="sclib:dat",Zs=async a=>{let{flpakKey:t,userId:o,uTag:h,token:F,passwd:ce,isDummyCmt:Oe=!1,isAllowDat:De=!1,isAllowCmt:Ue=!1,isAllowCur:Ke=!1,afterStoreCreate:Re=Vs,__fetchFlpakAsync:Xe=ps.TP,onTransferError:_e=Vs}=a;const Se=gn(t,ko,De),We=(Oe?Uo:gn)((0,No.J6)(t),To,Ue),Qe=(Ke?Hs:jo)(t);await Re({fssDat:Se});const lt=async(Be,dt)=>{await Se.saveSOS("OTE|"+Be+"|"+dt),await _e(Be,dt)},Nt=(0,Ct.LZ)({flpakKey:t,needDat:!!De,needCmt:!!Ue,needCur:!!Ke}),wt=!o||!Nt?no():oo({query:{uId:String(o),uTag:h,rKey:Nt},usePolling:Gs,lossyListenerMap:{[Ct.NX]:Be=>{const{payload:dt}=Be;Qe.onRemoteCur(dt)}},queueListenerMap:{[Ct.VU]:Be=>{const{error:dt}=Be;console.log("flat-ws drop-client",dt),lt("4XX",dt)},[Ct.Ei]:Be=>{const{error:dt}=Be;console.log("flat-ws error",dt),lt("5XX",dt)},[Ct.zS]:()=>{console.log("flat-ws (re)connected"),Qe.onSocketConnect()},[Ct.sP]:Be=>{const{payload:dt}=Be;Se.onRemotePayloadSave(dt)||We.onRemotePayloadSave(dt)||console.warn("drop save payload:",dt)},[Ct.$7]:Be=>{const{payload:dt}=Be;console.log("flat-ws room cursor:",dt),Qe.onRemoteSocket(dt)}}});await wt.getReadyPromise();const Ht=async(Be,dt,Vt)=>{try{return await Xe(Be,{token:F,passwd:ce,skipTTre:String(dt||""),skipTPbxList:(Vt||[]).join("")})}catch(en){const Nn=xs(en);throw $n(en)!=="Fch"&&(0,sn.p)("fFS "+$n(en),en),await lt(Nn,en),en}};await Promise.all([Se.bind((0,fn.aF)({userId:o,mtimeOffset:wt.getOffsetMsec()}),Ht).catch(Be=>{throw(0,sn.p)("bindDat",Be),Be}),We.bind((0,fn.aF)({userId:o,mtimeOffset:wt.getOffsetMsec()}),Ht).catch(Be=>{throw(0,sn.p)("bindCmt",Be),Be}),Qe.bind(async Be=>{try{if(wt.getIsConnected())return await wt.emitAsync(Ct.Am,Be)}catch(dt){await(0,M.Y_)(42*1e3)}return[]},Be=>wt.getIsConnected()&&wt.emitLossy(Ct.pL,Be),wt.getSocketId).catch(Be=>{throw(0,sn.p)("bindCur",Be),Be})]);const zt=new Error("[tracked-error]"),qt=async Be=>{if(!Be.hasPatch())return;const dt=Be.preSettlePatch(),Vt=(0,xn.ok)((0,xn.oG)(dt));for(let en=0,Nn=Vt.length;en<Nn;en++){const bn=Vt[en];try{await wt.emitAsync(Ct.s5,[Be.flpakKey,bn.length,(0,xn.iM)(bn)])}catch(hn){const Yn=xs(hn);throw Yn==="5XX"&&(0,sn.p)("saveFss "+$n(hn),hn,Be.name,Be.flpakKey,en+1+"/"+Nn,bn),await lt(Yn,hn),zt}}Be.postSettlePatch(dt)},ct=(0,p.Ds)(async()=>(0,p.g7)(async()=>{await qt(Se),await qt(We),await Se.freeSOS()},5*60*1e3,"lFFss.lA"),Be=>{if(Be===zt)return;const dt=xs(Be);(0,sn.p)("lFFss "+$n(Be),Be),Promise.resolve(lt(dt,Be)).catch(Vt=>{(0,sn.p)("lFFss "+dt+" Err",Vt)})}),Ze=(0,p.Ds)(async()=>{ct.trigger(),await ct.getRunningPromise();for(let Be=0;Be++<16;)!Qe.hasOtherUser()&&await(0,M.Y_)(1e3)});setInterval(Ze.trigger,2*1e3),Ke&&setInterval(()=>Qe.onSocketConnect(),20*60*1e3);const ot=Se.getFlatStore(),et=(0,Cn.uq)(Se.getFlatStore());Se.useExtORPL(et.onRemotePatchList);const ht=vo(We.getFlatStore());We.useExtORPL(ht.onRemotePatchList);const mt=()=>Se.hasPatch()||We.hasPatch(),Ft=(0,p.B1)(async()=>(0,p.JN)(async()=>{let Be=0;for(;mt();)await(0,M.Y_)(Math.min(Be,5)*1e3),ct.trigger(),await ct.getRunningPromise(),Be++})),Lt=()=>globalThis.MB.getSelectionItems()[0].key,At=(0,p.J9)(Qe.tryPackCur,125);return{fssDat:Se,fssCmt:We,cssCur:Qe,flatSocket:wt,flatStore:ot,sdkStore:et,cmtStore:ht,sendCur:At,hasSave:mt,saveAsync:Ft,theTree:function(Be){return Be===void 0&&(Be=Yt.gT),console.log((0,Yt.Wr)(ot.getFlatTree(),Be))},hotTree:function(Be){return Be===void 0&&(Be=Yt.gT),console.log(et.__devHotTree(Be))},slctTree:function(Be){return Be===void 0&&(Be=Lt()),console.log(et.__devHotTree(Be))},upTree:function(Be,dt){return Be===void 0&&(Be=Lt()),dt===void 0&&(dt=Yt.gT),console.log(et.findUpHotItemList(Be,dt))},newFFT:async function(Be,dt,Vt){return Vt===void 0&&(Vt=dt.name+"-\u4FEE\u590D\u6570\u636E"),(0,wo.Nc)(Be,dt.team_cid,{...dt,name:Vt})},cmtTree:function(Be){return Be===void 0&&(Be=Yt.gT),console.log(ht.__devHotTree(Be))}}},xs=a=>{const t=a&&a.status;return t>=400&&t<500?"4XX":String(a).toLowerCase().includes("timeout")?"Tot":"5XX"},$n=a=>{const t=a&&a.status,o=[];t>=400&&o.push(String(t));const h=String(a).toLowerCase();return h.includes("timeout")&&o.push("Tot"),h.includes("fetch")&&o.push("Fch"),o.join("")||"Err"},Ao=async a=>{let{flpakKey:t}=a;const o=gn(t,Lo,!1),h=async(ce,Oe,De)=>(0,ps.TP)(ce,{skipTTre:String(Oe||""),skipTPbxList:(De||[]).join("")});return await o.bind((0,fn.aF)({}),h),{sdkStore:(0,Cn.uq)(o.getFlatStore())}},Oo=async a=>{let{flpakKey:t}=a;const o=gn(t,ys,!1),h=async(ce,Oe,De)=>(0,ps.O0)(ce,{skipTTre:String(Oe||""),skipTPbxList:(De||[]).join("")});return await o.bind((0,fn.aF)({}),h),{sdkStore:(0,Cn.uq)(o.getFlatStore())}}},27374:(Ne,de,n)=>{"use strict";n.d(de,{$I:()=>te,Rj:()=>T,SR:()=>p,Tq:()=>k,UP:()=>z});var s=n(4114);let p=function(E){return E.AIAssistant="AIAssistant",E.Page="Page",E.Flow="flow",E.Mind="mind",E.Table="table",E.Chart="chart",E.AutoFill="autofill",E.Semantic="semantic",E.Component="Component",E.PageV9="PageV9",E.Chat="Chat",E}({}),M=function(E){return E.page="page",E.component="component",E.other="other",E.semantic="semantic ",E}({}),u=function(E){return E.hot="hot",E.other="other",E.semantic="semantic ",E}({});const k="GENERATE_AI_COMPONENT",te=(0,s.R)(()=>[{label:I18N.ChatGPT.ai_prototype,title:I18N.ChatGPT.ai_prototype_title,type:p.PageV9,points:400,recommends:[I18N.ChatGPT.ai_prototype_label_1,I18N.ChatGPT.ai_prototype_label_2,I18N.ChatGPT.ai_prototype_label_3],aiType:"\u751F\u6210\u539F\u578B\u9875\u9762",source:"",menu:M.page,hotMenu:u.hot,icon:"chatGPT/ai-component-page",desc:I18N.ChatGPT.ai_prototype_desc,className:"ai-page",componentTitle:I18N.ChatGPT.ai_prototype_component_title},{label:k,title:I18N.ChatGPT.ai_component_title,type:p.Component,points:200,aiType:"\u751F\u6210AI\u7EC4\u4EF6",source:"",menu:M.component,hotMenu:u.hot,icon:"chatGPT/ai-component-component",desc:I18N.ChatGPT.ai_component_desc,className:"ai-component",componentTitle:I18N.ChatGPT.ai_component_component_title},{label:I18N.ArtBoard.flow,title:I18N.ChatGPT.ai_flow_title,type:p.Flow,points:200,recommends:[I18N.ChatGPT.ai_flow_label_1,I18N.ChatGPT.ai_flow_label_2,I18N.ChatGPT.ai_flow_label_3],aiType:"\u751F\u6210\u6D41\u7A0B\u56FE",source:"",menu:M.component,hotMenu:u.other,icon:"chatGPT/ai-component-flow",desc:I18N.ChatGPT.ai_flow_desc,className:"ai-flow",componentTitle:I18N.ChatGPT.ai_flow_component_title},{label:I18N.ArtBoard.mind,title:I18N.ChatGPT.ai_mind_title,type:p.Mind,points:200,recommends:[I18N.ChatGPT.ai_mind_label_1,I18N.ChatGPT.ai_mind_label_2,I18N.ChatGPT.ai_mind_label_3],aiType:"\u751F\u6210\u601D\u7EF4\u5BFC\u56FE",source:"",menu:M.component,hotMenu:u.other,icon:"chatGPT/ai-component-mind",desc:I18N.ChatGPT.ai_mind_desc,className:"ai-mind",componentTitle:I18N.ChatGPT.ai_mind_component_title},{label:I18N.w.table,title:I18N.ChatGPT.ai_table_title,type:p.Table,points:200,recommends:[I18N.ChatGPT.ai_table_label_1,I18N.ChatGPT.ai_table_label_2,I18N.ChatGPT.ai_table_label_3],aiType:"\u751F\u6210\u8868\u683C",source:"",menu:M.component,hotMenu:u.other,icon:"chatGPT/ai-component-table",desc:I18N.ChatGPT.ai_table_desc,className:"ai-table",componentTitle:I18N.ChatGPT.ai_table_component_title},{label:I18N.w.wChart,title:I18N.ChatGPT.ai_Chart_title,type:p.Chart,points:200,recommends:[I18N.ChatGPT.ai_wChart_label_1,I18N.ChatGPT.ai_wChart_label_2,I18N.ChatGPT.ai_wChart_label_3],aiType:"\u751F\u6210\u56FE\u8868",source:"",menu:M.component,hotMenu:u.other,icon:"chatGPT/ai-component-chart",desc:I18N.ChatGPT.ai_Chart_desc,className:"ai-chart",componentTitle:I18N.ChatGPT.ai_Chart_component_title},{label:I18N.ChatGPT.magic_fill,title:I18N.ChatGPT.ai_magicFill_title,type:p.AutoFill,points:200,recommends:[I18N.ChatGPT.ai_smart_fill_label_2,I18N.ChatGPT.ai_smart_fill_label_1,I18N.ChatGPT.ai_smart_fill_label_3],aiType:"\u667A\u80FD\u586B\u5145",source:"",menu:M.other,hotMenu:u.other,icon:"chatGPT/ai-component-autofill",desc:I18N.ChatGPT.ai_magicFill_desc,className:"ai-magic-fill"},{label:"\u8BED\u4E49\u5316",title:"AI \u8BED\u4E49\u5316",type:p.Semantic,points:200,recommends:["\u5E2E\u6211\u751F\u6210\u4E00\u4E2A\u7535\u5546\u7C7BAPP\uFF0C\u5E76\u4E14\u8F93\u5165\u4E00\u4EFDprd\u6587\u6863","`\u6211\u7231\u52A0\u73ED`\u3002\u8BF7\u5E2E\u6211\u628A\u4E0A\u9762\u8FD9\u6BB5\u8BDD\u8C03\u6574\u7684\u66F4\u52A0\u4E30\u5BCC\u751F\u52A8\u4E00\u70B9","\u5E2E\u6211\u751F\u6210\u4E00\u4EFD2022\u5E74\u56FD\u5BB6\u7ECF\u6D4E\u66F2\u7EBF\u56FE\u30012022\u5E74\u5404\u7701\u7ECF\u6D4E\u6536\u5165\u8868\u683C"],aiType:"MD\u8BED\u4E49\u5316",source:"AI\u8BED\u4E49\u5316\u9762\u677F",menu:M.semantic,hotMenu:u.semantic,icon:"chatGPT/ai-component-autofill",desc:I18N.ChatGPT.ai_magicFill_desc,className:"ai-magic-fill"}]),Z=(0,s.R)(()=>[{title:I18N.ChatGPT.ai_prototype,items:te().filter(U=>U.menu===M.page)},{title:I18N.ChatGPT.ai_advanced_components,items:te().filter(U=>U.menu===M.component)},{title:I18N.ChatGPT.ai_magic_fill,items:te().filter(U=>U.menu===M.other)}]),z=(0,s.R)(()=>[{title:I18N.ChatGPT.ai_hot_menu,items:te().filter(U=>U.hotMenu===u.hot)},{title:I18N.ChatGPT.ai_other_menu,items:te().filter(U=>U.hotMenu===u.other)}]),T=(0,s.R)(()=>te().reduce((E,U)=>(E[U.type]=U,E),{}))},46664:(Ne,de,n)=>{"use strict";n.d(de,{A1:()=>D,A9:()=>x,CH:()=>Je,Cp:()=>s.Cp,E8:()=>f,F7:()=>ke,G8:()=>Me,Gx:()=>J,Ip:()=>xt,Jm:()=>L,Kc:()=>z,OJ:()=>w,PK:()=>r,Qs:()=>Q,Qy:()=>we,SD:()=>Pe,Tf:()=>U,V7:()=>S,Wr:()=>v,XG:()=>me,Xw:()=>A,Yu:()=>E,bX:()=>Y,cn:()=>T,dC:()=>St,hY:()=>g,kZ:()=>re,l9:()=>C,ob:()=>le,qi:()=>he,r3:()=>R,s_:()=>q,sw:()=>yt,ty:()=>je,up:()=>st,vS:()=>He,vs:()=>$e,wR:()=>l,wZ:()=>bt,yV:()=>tt,yY:()=>Ce,yk:()=>Ie,yr:()=>H});var s=n(52781),p=n(46795),M=n(45465),u=n(72907);if(n.j!=15)var k=n(79619);if(n.j!=15)var te=n(63967);const Z=(ue,ve)=>{const Ae=new Error("[NO SENTRY] "+ue);return Object.assign(Ae,ve),Ae},z=(ue,ve)=>(0,p.DE)("/api/upper/web_v1/design/readonly_initial?access_token="+ue+"&password="+ve),T=async(ue,ve)=>{const Ae=ve==="inspect"?"read_only":ve,Ve="/api/flat/web_v1/preview/initial?access_token="+ue+"&view_mode="+Ae,qe=await fetch(Ve,{credentials:"same-origin"}),ut=await qe.json();if(qe.status!==200)throw Z("[fetchGetHead] failed with "+qe.status+": "+Ve,{status:qe.status,error_type:ut==null?void 0:ut.error_type});return ut},E=(ue,ve)=>(0,p.SN)("/api/upper/web_v1/basics/"+ue,{...ve}),U=async()=>{let ue=[];try{const ve=await(0,p.DE)("/api/library/v4/recent_keywords");ve!=null&&ve.keywords&&(ue=ve==null?void 0:ve.keywords)}catch(ve){(0,u.$r)()}return ue},q=()=>(0,p.sM)("/api/library/v4/recent_keywords"),J=ue=>(0,p.Ds)("/api/library/v4/recent_keywords",{keyword:ue}),he=async()=>{if((0,k.Z)())return V();{var ue;let ve;try{ve=await(0,p.DE)("/api/library/v3/project_upper/user_combo_groups")}catch(Ae){return await(0,u.FY)(Ae),{}}return{userComboGroupList:(ue=ve)==null?void 0:ue.user_combo_groups}}},V=async()=>{var ue;let ve;try{ve=await(0,p.DE)("/api/library/v3/project_upper/org_combo_groups?org_cid="+MB.user.solo_org.cid)}catch(Ae){return await(0,u.FY)(Ae),{}}return{userComboGroupList:(ue=ve)==null?void 0:ue.org_combo_groups}},Y=async ue=>{var ve;let{orgCid:Ae}=ue,Ve;try{Ve=await(0,p.DE)("/api/library/v3/project_upper/org_combo_groups?org_cid="+Ae)}catch(qe){return await(0,u.FY)(qe),{}}return{orgComboGroupList:(ve=Ve)==null?void 0:ve.org_combo_groups}},H=async ue=>{const{icon_group:ve,market_template:Ae}=await(0,s.Yk)(ue.cid),{source:Ve,title:qe}=ve||{},ut=await(0,p.DE)(Ve),jt=M.I1[qe]||qe||"svg",{iconGroupData:Ut}=(0,M.u2)(ut,jt);return{iconGroupData:Ut,marketTemplate:Ae}},w=async()=>{const ue=MB.user.solo_org.cid;try{const{asset_groups:ve=[],assets:Ae}=await(0,p.Yo)("/api/library/v6/asset_groups?org_cid="+ue);return{userAssetGroupList:ve,userAssets:Ae}}catch(ve){return(0,u.$r)(),{userAssetGroupList:[],userAssets:[]}}},C=async function(ue){ue===void 0&&(ue="");const ve=ue||MB.currentOrg.cid;try{const{asset_groups:Ae,assets:Ve}=await(0,p.Yo)("/api/library/v6/asset_groups?org_cid="+ve);return{orgAssetGroupList:Ae,orgAssets:Ve}}catch(Ae){return(0,u.$r)(),{orgAssetGroupList:[],orgAssets:[]}}},f=async ue=>{if((0,k.Z)())return y(ue);try{const{asset:ve,user_asset:Ae}=await(0,p.B7)("/api/library/v3/asset_data/me/search_by_url",{image_url:ue});return{asset:ve,user_asset:Ae}}catch(ve){await(0,u.FY)(ve)}},y=async ue=>{const ve=MB.user.solo_org.cid;try{const{asset:Ae,flat_asset:Ve}=await(0,p.B7)("/api/library/v6/assets/search_by_url",{image_url:ue,org_cid:ve});return{asset:Ae,user_asset:Ve}}catch(Ae){await(0,u.FY)(Ae)}},v=async(ue,ve)=>{try{const{asset:Ae,flat_asset:Ve}=await(0,p.B7)("/api/library/v6/assets/search_by_url",{image_url:ue,org_cid:ve});return{asset:Ae,org_asset:Ve}}catch(Ae){await(0,u.FY)(Ae)}},S=async ue=>{if((0,k.Z)())return I(ue);try{const{user_asset_group:ve}=await(0,p.uP)("/api/library/v4/users/asset_groups/"+ue.cid,{...ue});return{user_asset_group:ve}}catch(ve){await(0,u.FY)(ve)}},x=async(ue,ve)=>{const{name:Ae,parentCid:Ve=null,position:qe}=ve,{asset_group:ut}=await(0,p.zi)("/api/library/v6/asset_groups",{org_cid:ue,name:Ae,parent_cid:Ve,position:qe});return{asset_group:ut}},I=async ue=>{const{cid:ve,name:Ae,parent_cid:Ve,position:qe}=ue;try{const{asset_group:ut}=await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{name:Ae,parent_cid:Ve,position:qe});return{user_asset_group:ut}}catch(ut){await(0,u.FY)(ut)}},D=async ue=>{const{cid:ve,name:Ae,parent_cid:Ve,position:qe}=ue;try{const{asset_group:ut}=await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{name:Ae,parent_cid:Ve,position:qe});return{org_asset_group:ut}}catch(ut){await(0,u.FY)(ut)}},l=async ue=>{if((0,k.Z)())return se(ue);try{return await(0,p.uP)("/api/library/v3/asset_data/me/assets/"+ue.asset_cid,{...ue})}catch(ve){await(0,u.FY)(ve)}},L=async ue=>(await(0,p.SN)("/api/library/v6/asset_groups/",{asset_groups:ue})).asset_groups,R=async(ue,ve)=>{const{name:Ae,groupCid:Ve}=ve;return await(0,p.SN)("/api/library/v6/assets",{cids:ue,name:Ae,group_cid:Ve})},se=async ue=>{try{return await(0,p.uP)("/api/library/v6/assets",{...ue})}catch(ve){await(0,u.FY)(ve)}},g=async ue=>{let{cids:ve,...Ae}=ue;try{return await(0,p.uP)("/api/library/v6/assets",{cids:ve,...Ae})}catch(Ve){await(0,u.FY)(Ve)}},A=async ue=>{let{assetCid:ve,name:Ae,groupCid:Ve}=ue;if((0,k.Z)())return ee({assetCid:ve,name:Ae,groupCid:Ve});try{await(0,p.B7)("/api/library/v3/asset_data/me/assets",{cid,name:Ae,group_cid:Ve})}catch(qe){await(0,u.FY)(qe)}},ee=async ue=>{let{assetCid:ve,name:Ae,groupCid:Ve}=ue;try{await(0,p.B7)("/api/library/v6/assets",{asset_cid:[ve],name:Ae,group_cid:Ve})}catch(qe){await(0,u.FY)(qe)}},le=async ue=>{let{assetCid:ve,name:Ae,groupCid:Ve}=ue;try{const qe=await(0,p.B7)("/api/library/v6/assets",{asset_cid:[ve],name:Ae,group_cid:Ve});return qe==null?void 0:qe.cid}catch(qe){await(0,u.FY)(qe)}},re=async ue=>{let{org_cid:ve,name:Ae,group_cid:Ve}=ue;if((0,k.Z)())return X({org_cid:ve,name:Ae,group_cid:Ve});try{return(0,p.B7)("/api/library/v3/asset_data/org/asset_group_from_user",{user_asset_group_cid,org_cid:ve,org_asset_group_cid})}catch(qe){await(0,u.FY)(qe)}},X=async ue=>{let{org_cid:ve,name:Ae,group_cid:Ve}=ue;try{return(0,p.B7)("/api/library/v6/asset_groups",{org_cid:ve,name:Ae,parent_cid:null,group_cid:Ve,position:-99})}catch(qe){await(0,u.FY)(qe)}},we=async ue=>{(0,k.Z)()?await ze(ue):await(0,p.sM)("/api/library/v3/asset_data/me/assets/"+ue.cid)},ze=async ue=>{await(0,te.uz)("/api/library/v6/assets",{cids:[ue.cid]})},He=async ue=>{await(0,te.uz)("/api/library/v6/assets",{cids:[ue.cid]})},je=async ue=>{(0,k.Z)()?await Ee(ue):await(0,p.sM)("/api/library/v3/asset_data/me/asset_groups/"+ue)},Ee=async ue=>{await(0,p.sM)("/api/library/v6/asset_groups/"+ue)},tt=async ue=>{await(0,p.sM)("/api/library/v6/asset_groups/"+ue)},Pe=ue=>(0,p.iv)("/preferences/update",{...ue}),Q=async(ue,ve)=>{if((0,k.Z)())return K(ue,ve);try{return await(0,p.uP)("/api/library/v4/users/asset_groups/"+ue,{...ve})}catch(Ae){await(0,u.FY)(Ae)}},K=async(ue,ve)=>{const Ae=MB.user.solo_org.cid;try{return await(0,p.uP)("/api/library/v6/asset_groups/"+ue,{...ve,org_cid:Ae})}catch(Ve){await(0,u.FY)(Ve)}},me=ue=>async(ve,Ae)=>{try{const Ve={...Ae,org_cid:ue};return await(0,p.uP)("/api/library/v6/asset_groups/"+ve,{...Ve})}catch(Ve){await(0,u.FY)(Ve)}},Ce=async(ue,ve)=>{let Ae=null;try{const Ve=await(0,p.Yo)("/api/community/v1/workspace?category="+ue+"&plabel="+ve);Ve!=null&&Ve.market_templates&&(Ae=Ve==null?void 0:Ve.market_templates)}catch(Ve){(0,u.$r)()}return Ae},ke=async(ue,ve)=>{let Ae=null,Ve=null;try{const qe=await(0,p.DE)("/flpak/ww-p2wsearch?"+ue);if(qe!=null&&qe.data){const ut=qe.data,{user_flat_assets:jt,user_flat_asset_groups:Ut,org_flat_assets:Zt,org_flat_asset_groups:$t}=ut.asset||{};return Ae={...ut,asset:{user_assets:jt,user_asset_groups:Ut,org_assets:Zt,org_asset_groups:$t}},Ve=qe.p2mMMap,{data:Ae,p2mMMap:Ve}}}catch(qe){(0,u.$r)({onClick:ut=>ut({type:"entry:search-panel:search:from:keyword",payload:{from:ve}})})}},$e=async ue=>{let ve=null;try{const Ae=await(0,p.DE)("/flpak/ww-p2meta/"+ue);Ae!=null&&Ae.length&&(ve=Ae)}catch(Ae){(0,u.$r)()}return ve},Me=async ue=>{let{currentComboPanel:ve,projectMetaCid:Ae}=ue;try{await(0,p.zi)("/flpak/ww-migrate-"+ve+"-combo-group/"+Ae+"?mode=normal")}catch(Ve){await(0,u.FY)(Ve)}},Ie=async ue=>{let{projectBasicCid:ve}=ue;try{const{userId:Ae,flatKey:Ve}=await(0,p.DE)("/api/flat/web_v1/proj2-edit-info?prj2Cid="+ve);return{userId:Ae,flatKey:Ve}}catch(Ae){await(0,u.FY)(Ae)}},Je=async ue=>{let{projectBasicCid:ve,accessToken:Ae,password:Ve}=ue;try{const{userId:qe,flatKey:ut}=await(0,p.DE)("/api/flat/web_v1/proj2-preview-info?prj2Cid="+ve+"&access_token="+Ae+"&password="+Ve);return{userId:qe,flatKey:ut}}catch(qe){await(0,u.FY)(qe)}},st=ue=>(0,p.DE)("/api/dsh2/web_v1/org_entry/"+ue),yt=async ue=>{const{project:ve}=await(0,p.DE)("/api/dashboard/v5/projects/"+ue);return ve},xt=async(ue,ve)=>{await(0,p.SN)("/api/dashboard/v5/projects/"+ue+"/cover",{project:ve})},bt=async ue=>(0,p.DE)("/api/upper/web_v1/basics/statstics/"+ue),St=async()=>{try{const{result:ue}=await(0,p.DE)("/api/community/v1/community/scene_tag_labels?label_type=combo_group");return ue}catch(ue){console.log(ue)}},r=async ue=>{try{const{stickers:ve}=await(0,p.zi)("/api/community/v1/sticker_search_es",{q:ue,page:1,page_size:200});return ve}catch(ve){return console.log(ve),[]}}},52781:(Ne,de,n)=>{"use strict";if(n.d(de,{$r:()=>U,$z:()=>Z,Aq:()=>q,Cp:()=>V,Jv:()=>w,PJ:()=>f,Uy:()=>E,Wu:()=>k,X:()=>T,Yk:()=>z,ZF:()=>H,a3:()=>te,je:()=>C,rd:()=>Y}),n.j!=15)var s=n(46795);var p=n(72907),M=n(81811);const u=300,k=async function(y){y===void 0&&(y="");const v=1;let S=[];try{const x=await(0,s.Yo)("/api/community/v1/workspace/my_star?page="+v+(y&&"&category="+y)+"&page_size="+u);x&&(S=x==null?void 0:x.market_templates.filter(I=>(I==null?void 0:I.version)!=="v1"))}catch(x){(0,p.$r)()}return S.map(x=>({...x,is_star:!0}))},te=async y=>{await(0,s.Ds)("/api/community/v1/market_template/star/"+y)},Z=async y=>{await(0,s.DW)("/api/community/v1/market_template/star/"+y)},z=async function(y,v){v===void 0&&(v=!0);let S;try{S=await(0,s.DE)("/api/community/v1/market_template/"+y)}catch(x){v&&(0,p.ai)(x)}return S},T=function(y){return y===void 0&&(y=""),(0,s.rh)("/api/community/v1/market_template?is_from_workspace=true&page="+1+y+"&page_size="+u)},E=async()=>{let y;try{y=await(0,s.DE)("/flpak/ww-p2recent")}catch(v){(0,p.$r)()}return y},U=async()=>{try{return await(0,s.DE)("/api/community/v1/hot_icons")}catch(y){(0,p.$r)()}},q=async()=>{try{return await(0,s.DE)("/api/community/v1/hot_color_icons")}catch(y){(0,p.$r)()}},J=y=>{let{category:v="combo_group",keyword:S=""}=y;return fetchGetJSON("/api/community/v1/es/basic_resource/search_"+v+"?q="+encodeURIComponent(S))},he={screen_list:"screen",combo_group:"combo",icon_group:"icon",user_combo:"user_combo"},V=y=>{let{projectUpperType:v="",projectUpperCid:S="",itemCid:x=""}=y;return(0,s.Ds)("/api/community/v1/recent_template",{project_upper_type:he[v]||v,project_upper_cid:S,item_cid:x})},Y=async y=>{const{sdkStore:v}=await(0,M.O3)({flpakKey:y});return v},H=async function(y){y===void 0&&(y="mobile");try{return(await(0,s.DE)("/api/market_template/v4/workspace/new_page_types?platform="+y)).types}catch(v){return console.error(v),[]}},w=async function(y){y===void 0&&(y="mobile");try{return await(0,s.DE)("/api/community/v1/workspace/recommend_page?platform="+y)}catch(v){return(0,p.$r)({onClick:S=>S({type:"entry:resources:set:pagePanel:find:update:data",payload:{platformType:y,isReRequest:!0}})}),{}}},C=async y=>{let{platform:v,firstTypeCid:S,secondTypeCid:x=null}=y;const I={first_type_cid:S,second_type_cid:x,platform:v};let D=new URLSearchParams;D=Object.assign(D,I);for(const[l,L]of Object.entries(D))L?L==="default"?D.delete(l):D.set(l,L):D.delete(l);try{return await(0,s.DE)("/flpak/ww-p2pagemkt?"+D.toString())}catch(l){return(0,p.$r)({onClick:L=>L({type:"entry:resources:set:pagePanel:find:update:data",payload:{platformType:v,isReRequest:!0}})}),{}}},f=async()=>{try{const{color_icon_groups:y}=await(0,s.DE)("/api/community/v1/color_icon_groups");return y&&y.length>0?y.reduce((v,S)=>(v[S.source_upper_cid]=S,v),{}):{}}catch(y){return(0,p.$r)(),{}}}},15668:(Ne,de,n)=>{"use strict";n.d(de,{Oc:()=>k,Qi:()=>T,jf:()=>te,jn:()=>Z,lV:()=>V,tA:()=>z,z9:()=>U});var s=n(51044),p=n(82152),M=n(7290),u=n(57464);const k=(0,s.memo)(Y=>{let{type:H="linear",className:w,disabled:C=!1,size:f="common",corner:y="smooth",forceTheme:v,children:S,onClick:x}=Y;return(0,u.jsx)(p.Yr,{type:H,className:w,disabled:C,size:f,corner:y,forceTheme:v,onClick:x,children:S})}),te=n.j!=15?(0,s.memo)(Y=>{let{type:H="linear",className:w,disabled:C=!1,size:f="common",corner:y="smooth",icon:v,children:S,forceTheme:x,onClick:I}=Y;return(0,u.jsx)(p.hE,{type:H,className:w,disabled:C,size:f,corner:y,icon:v,forceTheme:x,onClick:I,children:S})}):null,Z=(0,s.memo)(Y=>{let{className:H,children:w,disabled:C=!1,onClick:f}=Y;return(0,u.jsx)(p.Yr,{type:"primary",size:"common",className:H,disabled:C,onClick:f,children:w})}),z=Y=>{let{className:H,children:w,disabled:C=!1,onClick:f}=Y;return(0,u.jsx)(p.Yr,{type:"secondary",size:"common",className:H,disabled:C,onClick:f,children:w})},T=(0,s.memo)(Y=>{let{className:H,children:w,disabled:C=!1,onClick:f}=Y;return(0,u.jsx)(p.Yr,{type:"danger",size:"common",className:H,disabled:C,onClick:f,children:w})}),E=null,U=n.j!=15?(0,s.memo)(Y=>{let{className:H,children:w,disabled:C=!1,onClick:f}=Y;return(0,u.jsx)(p.Yr,{type:"link",className:H,disabled:C,onClick:f,children:w})}):null,q=null,J=null,he=null,V=n.j!=15?(0,s.memo)(Y=>{let{className:H,isLoading:w=!1,children:C,disabled:f=!1,corner:y="soft",onClick:v}=Y;return(0,u.jsx)(p.hE,{type:"secondary",size:"tiny",corner:y,className:H,icon:w?(0,u.jsx)(M.C,{name:"general/loading",className:"btn-loading"}):null,disabled:f,onClick:v,children:C})}):null},82152:(Ne,de,n)=>{"use strict";n.d(de,{Yr:()=>Z,hE:()=>z,in:()=>te});var s=n(21676),p=n(93839),M=n(15388),u=n(54909);const k=(T,E)=>{const U=T.forceTheme;return U?u.f[E]["value_"+U]:T.theme[E]},te=(0,s.AH)(["line-height:18px;&[class*='type-primary']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:",";border:1px solid transparent;}}&[class*='type-secondary']{color:",";background:",";border:1px solid ",";&:hover:not([class*='is-disabled']){background:",";border:1px solid ",";}&:active:not([class*='is-disabled']){background:",";border:1px solid ",";}&[class*='is-disabled']{color:",";background:",";border:1px solid ",";}}&[class*='type-danger']{color:",";background:",";border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:",";border:1px solid transparent;}}&[class*='type-text']{color:",";background:transparent;border:1px solid transparent;&:hover:not([class*='is-disabled']){background:",";border:1px solid transparent;}&:active:not([class*='is-disabled']){background:",";border:1px solid transparent;}&[class*='is-disabled']{color:",";background:transparent;border:1px solid transparent;}}&[class*='size-tiny']{max-height:28px;font-size:12px;line-height:14px;padding:6px 8px;.btn-icon-text-container{min-width:46px;}}&[class*='size-common']{max-height:32px;font-size:13px;padding:6px 10px;.btn-icon-text-container{min-width:58px;}}&[class*='size-medium']{max-height:32px;font-size:13px;padding:6px 12px;.btn-icon-text-container{min-width:70px;}}&[class*='is-disabled']{opacity:1;}"],T=>k(T,"color_text_btn"),T=>k(T,"color_btn_primary_normal"),T=>k(T,"color_btn_primary_hover"),T=>k(T,"color_btn_primary_clicked"),T=>k(T,"color_text_disabled02"),T=>k(T,"color_btn_primary_disabled"),T=>k(T,"color_text_L1"),T=>k(T,"color_bg_white"),T=>k(T,"color_bg_border_02"),T=>k(T,"color_btn_secondary_hover"),T=>k(T,"color_bg_border_02"),T=>k(T,"color_btn_secondary_active"),T=>k(T,"color_bg_border_02"),T=>k(T,"color_text_disabled01"),T=>k(T,"color_bg_white"),T=>k(T,"color_bg_border_02"),T=>k(T,"color_text_btn"),T=>k(T,"color_btn_danger_normal"),T=>k(T,"color_btn_danger_hover"),T=>k(T,"color_btn_danger_clicked"),T=>k(T,"color_text_disabled02"),T=>k(T,"color_btn_danger_disabled"),T=>k(T,"color_text_L1"),T=>k(T,"color_btn_secondary_hover"),T=>k(T,"color_btn_secondary_active"),T=>k(T,"color_text_disabled01")),Z=(0,s.Ay)(p.A).withConfig({displayName:"styles__StyledProtoButton",componentId:"sc-1pmgkeq-0"})(["",";"],te),z=(0,s.Ay)(M.A).withConfig({displayName:"styles__StyledProtoIconButton",componentId:"sc-1pmgkeq-1"})(["",";"],te)},60081:(Ne,de,n)=>{"use strict";n.d(de,{s:()=>L,A:()=>D});var s=n(74059),p=n(51044),M=n(86634),u=n.n(M),k=n(67231),te=n.n(k),Z=n(11334),z=n(19418),T=n(7775),E=n(7290),U=n(11777),q=n(89535),J=n(73773),he=n(57465),V=n(21676),Y=n(17084);const H=(0,V.DU)([".no-pointer-events{pointer-events:none;user-select:none;&.col-resize{cursor:",";}&.row-resize{cursor:",";}}"],Y.M.ewResizeActive,Y.M.nsResizeActive),w=V.Ay.label.withConfig({displayName:"styles__StyledInputNumber",componentId:"sc-1hqayf3-0"})(['position:relative;display:flex;justify-content:space-between;align-items:center;min-width:2em;border:1px solid transparent;font-family:-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;height:28px;font-size:12px;border-radius:6px;overflow:hidden;.title{display:flex;align-items:center;justify-content:center;padding:0 3px 0 5px;width:max-content;height:100%;user-select:none;color:',";&.title-cursor{&.col-resize{cursor:",";}&.row-resize{cursor:",";}}}.left-title{padding:0 13px 0 0;}.normal-right{margin-left:","px;}input{color:",";width:100%;height:100%;margin-left:8px;cursor:default;}.text-align-right{text-align:right;padding-right:2px;}.suffix{position:absolute;top:0;display:flex;align-items:center;height:100%;pointer-events:none;user-select:none;width:100%;color:",";&::before{content:attr(data-value);display:inline-block;margin-right:0.125em;width:fit-content;overflow:hidden;opacity:0;}}.action{position:absolute;top:0;right:0;width:14px;height:100%;text-align:center;visibility:hidden;svg{height:4px;fill:currentColor;fill-rule:evenodd;}button{display:block;margin:0;padding:0;width:100%;height:50%;line-height:0;color:",";&.no-transition{transition:none;}&:hover{color:",";}&:active{color:",";}&[data-action=up] svg{margin:5px 0 2px;}&[data-action=down] svg{margin:2px 0 5px;}}&.caret{width:26px;svg{width:26px;height:26px;margin-right:0;color:",";}button{height:100%;}&.active{visibility:visible;}}}&.is-disabled{> *{color:",";cursor:not-allowed;}}&.is-readOnly{pointer-events:none;.action{visibility:hidden;}}&:hover:not(.has-menu):not(.is-disabled){border:1px solid ",";.action{visibility:visible;color:#c8cdd0;}}&.is-active:not(.has-menu):not(.is-disabled){border:1px solid ",";outline:1px solid ",";outline-offset:-2px;.action{&:not(.caret){visibility:visible;}}}&.is-active-title{border:1px solid ",";outline:1px solid ",";outline-offset:-2px;cursor:pointer;}&.has-menu{border:1px solid transparent;input{width:calc(100% - 26px);border:1px solid transparent;border-radius:4px 0 0 4px;margin-left:0;padding-left:5px;}&:hover:not(.is-active):not(.is-disabled){border:1px solid ",";.action.caret{visibility:visible;background:",";svg.icon{color:",";}}}&:active:not(.is-active):not(.is-disabled){border:1px solid ",";.action.caret{visibility:visible;background:",";svg.icon{color:",";}}}&.is-active{overflow:visible;input{border:1px solid ",";outline:1px solid ",";position:relative;z-index:2;}.action.caret{visibility:visible;color:#c8cdd0;position:relative;z-index:1;svg.icon{color:",";}&:hover{background:",";border-radius:0 4px 4px 0;outline:1px solid ",";}}}}"],R=>R.theme.color_text_L3,Y.M.ewResize,Y.M.nsResize,R=>31-R.valueLength*6,R=>R.theme.color_text_L1,R=>R.theme.color_text_L2,R=>R.theme.color_text_L3,R=>R.theme.color_text_L2,R=>R.theme.color_text_L2,R=>R.theme.color_text_L3,R=>R.theme.color_text_disabled01,R=>R.theme.color_bg_border_02,R=>R.theme.color_proto,R=>R.theme.color_proto,R=>R.theme.color_proto,R=>R.theme.color_proto,R=>R.theme.color_bg_border_02,R=>R.theme.color_btn_secondary_hover,R=>R.theme.color_text_L1,R=>R.theme.color_bg_border_02,R=>R.theme.color_btn_secondary_active,R=>R.theme.color_text_L1,R=>R.theme.color_proto,R=>R.theme.color_proto,R=>R.theme.color_text_L1,R=>R.theme.color_btn_secondary_hover,R=>R.theme.color_bg_border_02);var C=n(57464);const f=500,y=30,v=(R,se)=>Number(Number(R).toFixed(se));function S(R,se,g){return g===void 0&&(g=0),se?R:v(R!==""&&te()(Number(R))?Number(R):"",g)}const x=function(R,se){let{shiftKey:g,metaKey:A}=R;return se===void 0&&(se=1),g?se*10:A?se*100:se},I=R=>{let{currentTarget:se}=R;return setTimeout(()=>se.select(),50)};class D extends p.PureComponent{constructor(se){super(se),(0,s.A)(this,"setElemRef",g=>this.$title=g),(0,s.A)(this,"setLabelRef",g=>this.$label=g),(0,s.A)(this,"setInputRef",g=>this.$input=g),(0,s.A)(this,"onChange",g=>{const{target:{value:A}}=g,{isEditing:ee}=this.state;!ee&&this.setState({isEditing:!0});const le=A.trim();this.setValue(le,g)}),(0,s.A)(this,"correctNumber",g=>{const{value:A,min:ee,max:le,precision:re,suffix:X,parser:we}=this.props,ze=we(g,X),He=v(Math.min(Math.max(ze,ee),le),re);return isNaN(He)?A:He}),(0,s.A)(this,"checkValidity",g=>/^\+$/.test(g)?this.canBePositive:/^-$/.test(g)?this.canBeNegative:g===""||isFinite(g)&&this.correctNumber(g)===Number(g)),(0,s.A)(this,"setValue",(g,A,ee)=>{A.persist&&A.persist(),this.setState({value:g},ee)}),(0,s.A)(this,"setConfirmedValue",(g,A)=>this.setValue(g,A,()=>this.onConfirm(A))),(0,s.A)(this,"onConfirm",g=>{const{attr:A,value:ee,precision:le,onConfirm:re,shouldCorrectOnConfirm:X,onlyPreview:we,fontSize:ze,fontFamily:He,isWRichText:je}=this.props,{value:Ee}=this.state,tt=this.checkValidity(Ee);if(this.props.isDisabled||this.props.disabled)return;g.persist&&g.persist();const Q=this.correctNumber(Ee),K=isNaN(Q)?ee:Q;let me=Ee===""?ee:tt?/^[+-]$/.test(Ee)?0:we?Math.min(Math.max(Number(Ee),20),400):Number(Ee):Q===v(Ee,le)?Q:X?K:ee||K;(!tt||Ee==="")&&je&&(me=(0,he.qp)(He,ze));const Ce=me;return this.setState({value:me,isEditing:!1},()=>re(Ce,A,g,Ee))}),(0,s.A)(this,"focusOnInput",g=>{try{const A=g.currentTarget.closest("label").querySelector("input");setTimeout(()=>{A.focus()})}catch(A){console.error(A)}}),(0,s.A)(this,"onStep",async g=>{g.persist(),g.nativeEvent.stopPropagation(),this.handleBeforeChangeValue();const{parser:A,suffix:ee}=this.props,{value:le}=this.state,re=A(le,ee),{action:X}=g.currentTarget.dataset,we=x(g,this.props.step)*(X==="up"?1:-1);await this.focusOnInput(g),this.setConfirmedValue(this.correctNumber(re+we),g),Object.assign(this,{longPressedTimeout:setTimeout(()=>Object.assign(this,{steppingInterval:setInterval(()=>{const{parser:ze,suffix:He}=this.props,{value:je}=this.state,Ee=ze(je,He);this.setConfirmedValue(this.correctNumber(Ee+we),g)},y)}),f)})}),(0,s.A)(this,"onRelease",()=>{clearTimeout(this.longPressedTimeout),clearInterval(this.steppingInterval),this.handleAfterChangeValue()}),(0,s.A)(this,"onFocus",g=>{const{attr:A,dontSelectOnFocus:ee,onFocus:le=ee?void 0:I}=this.props;this.state.isActive||this.setActive(),le(g,A)}),(0,s.A)(this,"onKeyDown",g=>{const{key:A,currentTarget:ee}=g,{disableKeyboardEvents:le}=this.props;if(le&&(A===J._.ArrowUp||A===J._.ArrowDown)){g.preventDefault();return}const re=A===J._.ArrowUp?"up":A===J._.ArrowDown?"down":A===J._.Enter?"enter":A===J._.Tab?"tab":null,X=ee instanceof Element&&ee.matches("input");if(re){if(g.persist&&g.persist(),re!=="tab"&&g.preventDefault(),X&&re==="tab")return this.setInactive(),this.setState({isEditing:!1}),this.onConfirm(g);if(X&&re==="enter"){this.onConfirm(g),this.setInactive(),this.setState({isEditing:!1}),this.$input.blur();return}if(X){const{parser:we,suffix:ze}=this.props,{value:He}=this.state,je=we(He,ze),Ee=x(g,this.props.step)*(re==="up"?1:-1);this.handleBeforeChangeValue(),this.setState({isEditing:!1}),this.setConfirmedValue(this.correctNumber(je+Ee),g)}}}),(0,s.A)(this,"setActive",()=>{this.setState({isActive:!0}),MB&&MB.f&&(MB.f.isInSetting=!0);const{withToolTip:g,onChangeTooltipsHover:A}=this.props;g&&(this.setState({showTooltip:!1}),A&&A(!1))}),(0,s.A)(this,"setInactive",()=>{this.setState({isActive:!1,isEditing:!1}),MB&&MB.f&&(MB.f.isInSetting=!1)}),(0,s.A)(this,"toggleMenu",()=>this.setState({isMenuOpen:!this.state.isMenuOpen})),(0,s.A)(this,"closeMenu",()=>this.setState({isMenuOpen:!1})),(0,s.A)(this,"onSelect",g=>{g.persist();const{attr:A,onConfirm:ee}=this.props,{currentTarget:le}=g,{value:re}=le.dataset;this.setState({value:re,isEditing:!1},()=>ee(re,A,g)),this.setInactive(),this.closeMenu()}),(0,s.A)(this,"onClickOutside",g=>{const{target:A}=g,{onClickOutside:ee}=this.props;ee&&!(ee!=null&&ee(g))||A.closest("label")&&this.$label.contains(A)||A.closest(".SelectOption")||(this.onConfirm(g),this.setInactive(),this.setState({isEditing:!1}))}),(0,s.A)(this,"onBlur",async g=>{await this.onConfirm(g),this.setState({isEditing:!1}),this.props.onBlur(g)}),(0,s.A)(this,"handleRectResize",g=>{g.stopPropagation();const{isDisabled:A,disabled:ee,cursorSize:le,cursorDirection:re,onResizeMove:X,onResizeEnd:we,attr:ze}=this.props;if(A||ee)return;const{left:He,right:je,top:Ee,bottom:tt}=this.$title.getBoundingClientRect();this.lastRecordedClientX=le==="col-resize"?(He+je)/2:(Ee+tt)/2,this.lastRecordedValue=Number(this.state.value),this.setState({isTitleActive:!0}),document.querySelector("html").classList.add("no-pointer-events",le),sdkStore.combineMergeMark("input-number-"+ze),this.handleBeforeChangeValue();const Pe=K=>{X&&X();const me=(le==="col-resize"?K.pageX:K.pageY)-this.lastRecordedClientX,Ce=this.correctNumber(this.lastRecordedValue+(re?Number(me):-Number(me)));this.setConfirmedValue(Ce,K)},Q=()=>{we&&we(),this.setState({isTitleActive:!1}),document.querySelector("html").classList.remove("no-pointer-events",le),this.handleAfterChangeValue(),sdkStore.combineMerge("input-number-"+ze),document.removeEventListener("mousemove",Pe),document.removeEventListener("mouseup",Q)};document.addEventListener("mousemove",Pe),document.addEventListener("mouseup",Q)}),(0,s.A)(this,"handleChangeCursor",()=>{const{isDisabled:g,disabled:A}=this.props;g||A||this.setState({isCursor:!this.state.isCursor})}),(0,s.A)(this,"handleAfterChangeValue",()=>{const{onAfterChangeValue:g}=this.props;g()}),(0,s.A)(this,"handleBeforeChangeValue",()=>{const{onBeforeChangeValue:g}=this.props;g()}),(0,s.A)(this,"onInputNumberMouseEnter",g=>{const{onMouseEnter:A,withToolTip:ee,onChangeTooltipsHover:le}=this.props;A(g),this.setState({mouseEntered:!0});const re=()=>this.setState({showTooltip:!0});ee&&(le?le(!0,re):re())}),(0,s.A)(this,"onInputNumberMouseLeave",g=>{const{onMouseLeave:A,withToolTip:ee,onChangeTooltipsHover:le}=this.props;A(g),this.setState({mouseEntered:!1}),ee&&(this.setState({showTooltip:!1}),le&&le(!1))}),this.state={prevProps:this.props,value:S(this.props.value,this.props.isNotVerify,this.props.precision),isActive:!1,isMenuOpen:!1,isTitleActive:!1,isCursor:!1,isEditing:!1,showTooltip:!1,mouseEntered:!1},this.lastRecordedClientX=null,this.lastRecordedValue=S(this.props.value,this.props.isNotVerify,this.props.precision),this.toolTipNode=document.getElementById("IBOT_TOOLTIP_ROOT")}static getDerivedStateFromProps(se,g){let{prevProps:A,value:ee}=g;return A.value!==se.value||A.isNotVerify!==se.isNotVerify||A.precision!==se.precision?{prevProps:se,value:S(se.value,se.isNotVerify,se.precision)}:null}componentDidMount(){this.props.isSelect&&(this.$input.select(),this.setState({isActive:!0}))}componentDidUpdate(se){!se.isSelect&&this.props.isSelect&&this.$input.select()}get canBePositive(){return this.props.max>0}get canBeNegative(){return this.props.min<0}render(){const{inputClassName:se,menuClassName:g,className:A="",placeholder:ee,title:le,rightTitle:re,cursorSize:X,suffix:we,actionButton:ze,formatter:He,optionList:je,canSlidingAdjustment:Ee,readOnly:tt,toolTipContent:Pe,showCaretIcon:Q,normalRight:K,isLongTimeHoverForInput:me=!1}=this.props,{value:Ce,isActive:ke,isMenuOpen:$e,isTitleActive:Me,isCursor:Ie,isEditing:Je,showTooltip:st,mouseEntered:yt}=this.state,xt=Ce,bt=this.props.isDisabled||this.props.disabled,St=je&&je.length>0,r=u()(A,"WorkspaceInputNumber",{"is-disabled":bt,"is-active":ke&&!bt,"is-menu-open":$e,"is-active-title":Me,"can-sliding-adjustment":Ee,"has-menu":St,"is-readOnly":tt}),ue=ee,ve=String(Ce).split(".").join("").length-1,Ae=me?U.A:q.A;return(0,C.jsx)(Ae,{content:Pe,direction:"down",children:(0,C.jsxs)(w,{className:r,ref:this.setLabelRef,onMouseDown:St?void 0:this.setActive,onMouseEnter:this.onInputNumberMouseEnter,onMouseLeave:this.onInputNumberMouseLeave,valueLength:ve,children:[le&&(0,C.jsx)("span",{className:u()("title",{"title-cursor":Ie},X),ref:this.setElemRef,onMouseDown:this.handleRectResize,onMouseEnter:this.handleChangeCursor,onMouseLeave:this.handleChangeCursor,children:le}),(0,C.jsx)("input",{className:u()(se,{"text-align-right":K&&!yt&&!ke}),type:"text",value:Je?xt:He(xt,we),placeholder:ue,ref:this.setInputRef,disabled:bt,onChange:this.onChange,onKeyDown:this.onKeyDown,onKeyUp:this.handleAfterChangeValue,onMouseDown:St?this.setActive:void 0,onFocus:this.onFocus,onBlur:this.onBlur}),re&&(0,C.jsx)("span",{className:u()("title","left-title",{"title-cursor":Ie},X),ref:this.setElemRef,onMouseDown:this.handleRectResize,onMouseEnter:this.handleChangeCursor,onMouseLeave:this.handleChangeCursor,children:re}),ze&&(0,C.jsx)(l,{hasMenu:St,onToggleMenu:this.toggleMenu,onStep:this.onStep,onRelease:this.onRelease,showCaretIcon:Q}),St&&$e&&(0,C.jsx)(T.V0,{isOpen:$e,menuClassName:u()("SelectNumberMenu",g),$select:this.$label,optionList:je,value:xt,onChange:this.onSelect,onClose:this.closeMenu}),ke&&(0,C.jsx)(z.A,{target:document,onMouseDown:(0,z.t)(this.onClickOutside,{capture:!0})})]})})}}(0,s.A)(D,"defaultProps",{unstyled:!1,value:"",placeholder:"",cursorSize:"col-resize",inputClassName:"",menuClassName:"",cursorDirection:!0,step:1,isNotVerify:!1,formatter:(R,se)=>""+R+(se||""),parser:(R,se)=>{const g=R.toString();return se&&g.endsWith(se)?Number(g.slice(0,g.length-se.length)):Number(g)},min:0,max:1/0,isDisabled:!1,disabled:!1,readOnly:!1,actionButton:!0,isSelect:!1,onConfirm:()=>null,onMouseEnter:()=>null,onMouseLeave:()=>null,onAfterChangeValue:()=>null,onBeforeChangeValue:()=>null,onBlur:()=>null,shouldCorrectOnConfirm:!0,precision:0,dontSelectOnFocus:!1,canSlidingAdjustment:!0,isLineHeight:!1,disableKeyboardEvents:!1});const l=(0,p.memo)(R=>{let{hasMenu:se,onToggleMenu:g,onStep:A,onRelease:ee,showCaretIcon:le}=R;return(0,C.jsxs)(p.Fragment,{children:[se?(0,C.jsx)("div",{className:u()("action caret",{active:le}),children:(0,C.jsx)(Z.Ay,{className:"no-transition",type:"text",tabIndex:"-1",onClick:g,children:(0,C.jsx)(E.C,{name:"common/expand@26"})})}):(0,C.jsxs)("div",{className:"action",children:[(0,C.jsx)(Z.Ay,{className:"no-transition",type:"text",tabIndex:"-1","data-action":"up",onMouseDown:A,onMouseLeave:ee,onMouseUp:ee,children:(0,C.jsx)(E.C,{name:"general/triangle_up"})}),(0,C.jsx)(Z.Ay,{className:"no-transition",type:"text",tabIndex:"-1","data-action":"down",onMouseDown:A,onMouseLeave:ee,onMouseUp:ee,children:(0,C.jsx)(E.C,{name:"general/triangle_down"})})]}),(0,C.jsx)(H,{})]})}),L=R=>(0,C.jsx)(D,{...R,isLongTimeHoverForInput:!0})},46479:(Ne,de,n)=>{"use strict";n.d(de,{Os:()=>E,Tw:()=>U,ft:()=>q});var s=n(51044),p=n(57464);const M="/mb-static/2308/loading.gif",u="/mb-static/2308/loading-mo.gif",k=(J,he)=>he?"."+J:J,te=J=>(0,p.jsx)("img",{className:J.className,alt:"Loading...",src:k(J.isMockitt?u:M,J.isHtmlZip)}),Z=J=>(0,p.jsx)("img",{className:J.className,alt:"Loading...",src:k(ENV.IS_WONDER_SHARE?u:M,J.isHtmlZip)}),z=J=>(0,p.jsx)("img",{className:J.className,alt:"Loading...",src:k(u,J.isHtmlZip)});var T=n(94947);const E=n.j!=15?(0,s.memo)(J=>(0,p.jsx)(te,{...J,isHtmlZip:(0,T.FM)()})):null,U=n.j!=15?(0,s.memo)(J=>(0,p.jsx)(Z,{...J,isHtmlZip:(0,T.FM)()})):null,q=(0,s.memo)(J=>(0,p.jsx)(z,{...J,isHtmlZip:(0,T.FM)()}))},54608:(Ne,de,n)=>{"use strict";n.d(de,{n:()=>E,A:()=>U});var s=n(51044),p=n(86634),M=n.n(p),u=n(7290),k=n(11777),te=n(21676);const Z=te.Ay.ol.withConfig({displayName:"styles__StyledNewSlideNav",componentId:"sc-17e9w47-0"})(["display:flex;height:48px;.nav-label{position:relative;height:100%;padding:0 3px;display:flex;align-items:center;justify-content:center;white-space:nowrap;}.nav-item{position:relative;display:flex;flex:none;justify-content:center;align-items:center;color:",";padding:0 3px;cursor:pointer;.nav-underline{position:absolute;display:none;bottom:0;width:70%;border-bottom:2px solid ",";}&:lang(ja){font-size:10px;}&.active{color:",";&:not(:first-child:nth-last-child(1)){.nav-underline{display:inline;}}&.background_active{background-color:transparent;}&.line_active::after{content:'';position:absolute;bottom:0;width:30px;height:2px;background:",";}}&.disabled{color:",";cursor:not-allowed;}&:not(:first-child){}.tabNewIcon{position:absolute;width:30px;top:0;right:3px;path:first-child{fill:#1684fc;}path:last-child{fill:#fff;}}}.nav-item-small:lang(en){font-size:10px;}min-height:48px;padding:0 6px;.nav-item{font-size:13px;&.active{font-weight:bold;}}"],q=>q.theme.color_text_L3,q=>q.theme.color_text_L1,q=>q.theme.color_text_L1,q=>q.theme.color_text_L1,q=>q.theme.color_text_disabled01);var z=n(57464);const T=(0,s.memo)(q=>{let{activeIndex:J=0,className:he,layoutV9:V="old",children:Y,onTabChange:H}=q;return(0,z.jsx)(Z,{className:M()(he),children:s.Children.map(Y,(w,C)=>{const f={index:C,active:C===J,onClick:H};return s.cloneElement(w,f)})})}),E=(0,s.memo)(q=>{let{className:J,index:he,active:V,onClick:Y,label:H,tooltip:w,isNew:C,disabled:f,tempDisabled:y}=q;const v=()=>{if(f)return y&&MB.notice({text:I18N.Common.feature_is_not_available,type:"warning"}),null;Y(he)},S=(0,z.jsxs)("li",{className:M()("nav-item",{active:V},{disabled:f},J),onClick:v,children:[H&&(0,z.jsxs)("span",{className:"nav-label",children:[H,w&&w]}),C&&(0,z.jsx)(u.C,{name:"design/tab/new",className:"tabNewIcon"})]});return f?(0,z.jsx)(k.A,{content:"\u6682\u4E0D\u5F00\u653E",direction:"down",children:S}):S}),U=T},39719:(Ne,de,n)=>{"use strict";n.d(de,{eY:()=>v,At:()=>f,pU:()=>y,DV:()=>S,Ay:()=>C});var s=n(74059),p=n(51044);if(n.j!=15)var M=n(15336);var u=n(72605),k=n(21676);const te=(0,k.DU)(["div.notice-tips-container{z-index:9999;.notice-tips{min-height:38px;p{color:",";}:not(.pure-svg-icon).svg-icon{fill:",";}&.success{.tip-svg{path:nth-child(1){fill:",";}}}&.warning{.tip-svg{path:nth-child(1){fill:",";}}}&.error{.tip-svg{circle{fill:",";}}.button-a{cursor:pointer;}.button-p{margin-left:0;}}&.super{background-color:",";p{margin:0 16px 0 4px;}.times-icon{path{fill:",";}}}}}"],x=>x.theme.color_text_btn,x=>x.theme.color_text_btn,x=>x.theme.color_success,x=>x.theme.color_warning,x=>x.theme.color_error,x=>x.theme.color_error,x=>x.theme.color_text_btn);var Z=n(43441),z=n(90503),T=n(37337);const E=k.Ay.div.withConfig({displayName:"styles__StyledNoticeTips",componentId:"sc-1ehf3rt-0"})([".notice-tips{width:100%;height:100%;min-height:38px;padding:8px 16px;display:flex;align-items:center;font-size:14px;color:",";box-shadow:0 2px 8px 0 rgba(0,0,0,0.05),0 4px 12px 0 rgba(0,0,0,0.15);border-radius:6px;p{margin:0 0 0 4px;}&.info{background-color:",";p{margin:0 4px;}a{margin-left:12px;color:#80BCFF;&:hover{color:#A6D2FF;}}}&.success{background:",";box-shadow:0 3px 6px -4px rgba(0,0,0,0.12),0 6px 16px rgba(0,0,0,0.08),0 9px 28px 8px rgba(0,0,0,0.05);border-radius:6px;}&.warning{background-color:",";}&.error{background-color:",";}&.super{background-color:",";}&.loading{background-color:",";.tip-svg{animation:design-ani-spinning 1s infinite linear;fill:rgb(69,70,71);}}@keyframes design-ani-spinning{0%{transform:rotate(0deg);}100%{transform:rotate(359deg);}}.times-icon{width:20px;display:flex;flex-shrink:0;opacity:1;cursor:pointer;transition:opacity 0.15s ease-out;margin-left:16px;align-self:center;&:hover{opacity:1;}}.tip-svg{width:20px;height:20px;}}a{text-decoration:underline;color:",";&:hover{color:",";}}.fade{&-enter{transform:translateY(-50%);opacity:0;}&-enter-active{opacity:1;transform:translateY(0);transition:all 0.15s ease-out;}&-exit{opacity:1;}&-exit-active{opacity:0;transition:opacity 0.25s ease-in-out;}&-exit-done{opacity:0;}}"],T.f.color_bg_white.value_light,T.f.color_btn_secondary_active.value_dark,T.f.color_btn_secondary_active.value_dark,T.f.color_btn_secondary_active.value_dark,T.f.color_btn_secondary_active.value_dark,T.f.color_error.value_light,T.f.color_btn_secondary_active.value_dark,T.f.color_text_link_normal.value_dark,T.f.color_text_link_hover.value_dark);var U=n(57464);const q={success:"design/toast/success",error:"design/toast/error",warning:"design/toast/warning",super:"design/toast/offline",loading:"design/toast/loading"};class J extends p.PureComponent{render(){const{isShow:I,type:D,text:l,showClose:L,isReactDom:R,CustomChildComponent:se,svgName:g,onDisappear:A}=this.props;return(0,U.jsx)(E,{children:(0,U.jsx)(Z.A,{in:I,timeout:2500,classNames:"fade",unmountOnExit:!0,children:(0,U.jsxs)("div",{className:"notice-tips "+D,children:[g&&(0,U.jsx)(z.C,{name:g,className:"tip-svg",isColorPure:!0}),!g&&D&&q[D]&&(0,U.jsx)(z.C,{name:q[D],className:"tip-svg",isColorPure:!0}),(0,U.jsxs)(p.Fragment,{children:[R?l:(0,U.jsxs)(U.Fragment,{children:[(0,U.jsx)("p",{dangerouslySetInnerHTML:{__html:l}}),(typeof se=="function"||typeof se=="object")&&(0,U.jsx)(se,{})]}),L&&(0,U.jsx)(z.C,{className:"times-icon",name:"general/times",onClick:A})]})]})})})}}(0,s.A)(J,"defaultProps",{isShow:!1,type:"info",showClose:!1,isReactDom:!1,svgName:""});const he=k.Ay.div.withConfig({displayName:"styles__StyledNoticeTipsContainer",componentId:"sc-1ewb7bf-0"})(["position:fixed;left:50%;top:56px;max-width:480px;transform:translateX(-50%);z-index:1052;"]);var V=n(60185);class Y extends p.PureComponent{constructor(I){super(I),(0,s.A)(this,"timerId",null),(0,s.A)(this,"handleDisappear",()=>{typeof this.props.closeCallback=="function"&&this.props.closeCallback()}),this.el=document.createElement("div")}componentDidMount(){document.body.appendChild(this.el),this.props.isShow&&this.updateTimer()}componentDidUpdate(I,D,l){const{isShow:L,id:R}=this.props,{isShow:se,id:g}=I;(R!==g&&L||L&&L!==se)&&this.updateTimer()}updateTimer(){this.timerId&&clearTimeout(this.timerId);const{duration:I}=this.props;this.timerId=I&&setTimeout(this.handleDisappear,I)}componentWillUnmount(){this.el&&document.body.contains(this.el)&&document.body.removeChild(this.el),this.timerId&&clearTimeout(this.timerId)}render(){const{isShow:I,type:D,text:l,showClose:L,isReactDom:R,CustomChildComponent:se,svgName:g,className:A}=this.props;return(0,V.createPortal)(I&&(0,U.jsx)(he,{className:A,children:(0,U.jsx)(J,{isShow:I,type:D,showClose:L,onDisappear:this.handleDisappear,isReactDom:R,text:l,CustomChildComponent:se,svgName:g})}),this.el)}}(0,s.A)(Y,"defaultProps",{text:"",isShow:!1,type:"info",showClose:!1,isReactDom:!1,CustomChildComponent:void 0,svgName:"",id:"",duration:2e3,closeCallback:()=>null});const H=Y,w={isShow:!1,text:"",type:"info",showClose:!1,isReactDom:!1,CustomChildComponent:void 0,svgName:"",duration:2e3,id:""};class C extends p.PureComponent{constructor(I){super(I),(0,s.A)(this,"closeCallback",null),(0,s.A)(this,"handleDisappear",()=>{this.setState({isShow:!1}),typeof this.closeCallback=="function"&&this.closeCallback(),this.priority=0}),this.state=w,this.priority=0}componentDidMount(){MB.notice&&(this.prevNotice=MB.notice),MB.notice=I=>{let{text:D,type:l="info",CustomChildComponent:L=void 0,duration:R=2e3,showClose:se=!1,isReactDom:g=!1,closeCallback:A=null,priority:ee=0,svgName:le="",id:re=Math.random().toString(36).slice(2),isShow:X=!0}=I;this.priority>ee||(clearTimeout(this.timer),this.setState({isShow:X,text:D,type:l,showClose:se,id:re,isReactDom:g,CustomChildComponent:L,svgName:le,duration:R}),this.closeCallback=A,this.priority=ee)}}componentWillUnmount(){this.setState({isShow:!1}),this.prevNotice&&(MB.notice=this.prevNotice)}render(){const{isShow:I,type:D,duration:l,text:L,showClose:R,id:se,isReactDom:g,CustomChildComponent:A,svgName:ee}=this.state;return(0,U.jsxs)(U.Fragment,{children:[(0,U.jsx)(H,{className:"notice-tips-container",isShow:I,type:D,text:L,id:se,duration:l,showClose:R,isReactDom:g,CustomChildComponent:A,svgName:ee,closeCallback:this.handleDisappear}),(0,U.jsx)(te,{})]})}}const f=x=>(0,p.memo)(()=>{const I=()=>(0,M.JW)("/hc/articles/393","_blank","noreferrer");return(0,U.jsx)("a",{style:{cursor:"pointer"},onClick:I,children:x})}),y=n.j!=15?(0,p.memo)(()=>{const x=(0,u.wA)(),I=()=>x({type:"toolbar:sharingManager:open"});return(0,U.jsx)("a",{onClick:I,children:I18N.ScreenPanel.share_panel})}):null,v=x=>(0,p.memo)(()=>{const I=()=>(0,M.JW)(I18N.link.link_article_business_font_state,"_blank","noreferrer");return(0,U.jsx)("a",{className:"button-a",onClick:I,children:x})}),S=x=>{let{onClick:I}=x;return(0,p.memo)(()=>{const D=(0,u.wA)(),l=()=>{I&&I(D)};return(0,U.jsxs)(U.Fragment,{children:[(0,U.jsx)("a",{className:"button-a",onClick:l,children:I18N.dModule.network_api_error_2}),(0,U.jsx)("p",{className:"button-p",children:I18N.dModule.network_api_error_3})]})})}},3895:(Ne,de,n)=>{"use strict";n.d(de,{FU:()=>u,OO:()=>Z,Z8:()=>p,au:()=>te,lY:()=>k,p_:()=>M,zB:()=>s});const s={OWNER:1e4,SUPERMANAGER:9999,MANAGER:999,MEMBER:99,LIMITER:10,VIEWER:9,BANNED:1,UNKNOWN:0,UNJOINED:-1},p={space_owner:16,space_manager:15,space_member:14,space_viewer:13,space_limiter:12,team_owner:11,team_manager:10,team_member:9,team_viewer:8,project_owner:6,project_manager:5,project_member:4,project_viewer:3,project_ban_viewer:2,project_team_owner:1,team_ban_viewer:1},M={org_owner:s.OWNER,org_manager:s.SUPERMANAGER,org_admin:s.MANAGER,org_member:s.MEMBER,org_viewer:s.VIEWER},u={space_owner:s.MANAGER,space_manager:s.MANAGER,space_member:s.MEMBER,space_viewer:s.VIEWER,space_limiter:s.LIMITER},k={team_owner:s.MANAGER,team_manager:s.MANAGER,team_member:s.MEMBER,team_viewer:s.VIEWER,team_none:s.VIEWER,team_ban_viewer:s.BANNED},te=Object.assign({project_owner:s.MANAGER,project_team_owner:s.MANAGER,project_manager:s.MANAGER,project_member:s.MEMBER,project_viewer:s.VIEWER,project_none:s.VIEWER,project_ban_viewer:s.BANNED},k),Z={space_owner:"project_manager",space_manager:"project_manager",space_member:"project_member",space_viewer:"project_viewer",team_owner:"project_manager",team_manager:"project_manager",team_member:"project_member",team_viewer:"project_viewer",team_ban_viewer:"project_ban_viewer",team_none:"project_viewer",project_owner:"project_manager"},z={space_owner:"team_manager",space_manager:"team_manager",space_member:"team_member",space_viewer:"team_viewer",team_none:"team_viewer",team_owner:"team_manager"}},96510:(Ne,de,n)=>{"use strict";n.d(de,{s:()=>U});var s=n(46795);const p=async(q,J)=>{const{project:he}=await(0,s.zi)("/api/dashboard/v5/projects/"+q+"/invite",J);return he},M=async(q,J)=>(0,s.SN)("/api/dashboard/v5/permissions/projects/"+q,J),u=async(q,J)=>{await(0,s.uz)("/api/dashboard/v5/permissions/projects/"+q,J)};var k=n(15573),te=n(61654),Z=n(50378);const z=async(q,J)=>await(0,s.DE)("/api/dashboard/v5/spaces/"+q+"?org_cid="+J),T={project_manager:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u7BA1\u7406\u5458\u300D",project_member:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u53EF\u7F16\u8F91\u300D",project_viewer:"\u7684\u6743\u9650\u53D8\u4E3A\u300C\u4EC5\u67E5\u770B\u300D",project_ban_viewer:"\u7684\u6743\u9650\u53D8\u66F4\u4E3A\u300C\u7981\u6B62\u67E5\u770B\u300D"};let E=function(q){return q.Org="org",q.OrgSpace="space",q.Folder="folder",q.Project="project",q}({});const U={"entry:projectAccess:initProjectData":async(q,J)=>{let{getState:he,dispatch:V}=q,{payload:Y}=J;try{const{initData:H,members:w}=Y,C=H==null?void 0:H.org,{user:f,space:y,project:v,team:S,top_parent_team:x,lang:I}=H||{},D=new Map,l=new Map;return D.set(S==null?void 0:S.cid,S),D.set(x==null?void 0:x.cid,x),S!=null&&S.parent_cid&&(S==null?void 0:S.parent_cid)!==x.cid&&D.set(S==null?void 0:S.parent_cid,x),l.set(v==null?void 0:v.cid,v),V({type:Z.X2.UpdateState,payload:{user:f,org:{...C,members:w},space:y,folderListMap:D,projectListMap:l,lang:I}}),!0}catch(H){return console.error(H.message),!1}},"entry:projectAccess:inviteOrgMemberToProject":async(q,J)=>{let{getState:he,dispatch:V}=q,{payload:Y}=J;const{projectCid:H,userCid:w,roleName:C,userName:f}=Y;if(!H||!w)return;const y=he(),v=(0,te.IE)(y),S=(0,te.M0)(y),x=(0,te._B)(y);try{const I=await p(H,{user_cid:w,role:C}),{space:D}=await z(S,v);x.set(I.cid,I),await V({type:Z.X2.UpdateProjectListMap,payload:new Map(x)}),await V({type:Z.X2.UpdateSpace,payload:D}),window.top.postMessage({type:"updateApp",data:{newApp:(0,k.A)(I)}},"*"),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:T[C]}}},"*")}catch(I){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}},"entry:projectAccess:updateMemberProjectPermission":async(q,J)=>{let{getState:he,dispatch:V}=q,{payload:Y}=J;const{projectCid:H,userId:w,roleName:C,userName:f}=Y,y=he();try{const{permission:v}=await M(H,{target_role:C,target_user_id:w}),S=(0,te._B)(y),x=S.get(H),I=(x==null?void 0:x.permissions)||[],D=I.find(R=>Number(R.user_id)===Number(w));let l=[];D?l=I.map(R=>Number(R.user_id)===Number(w)?v:R):l=[...I,v];const L={...x,permissions:l};S.set(H,L),await V({type:Z.X2.UpdateProjectListMap,payload:new Map(S)}),window.top.postMessage({type:"updateApp",data:{newApp:(0,k.A)(L)}},"*"),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:T[C]}}},"*")}catch(v){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}},"entry:projectAccess:removeProjectMemberPermission":async(q,J)=>{let{getState:he,dispatch:V}=q,{payload:Y}=J;const{projectCid:H,userId:w,roleName:C,userName:f}=Y,y=he();try{await u(H,{target_role:C,target_user_id:w});const v=(0,te._B)(y),S=v.get(H),x=(S==null?void 0:S.permissions)||[];if(x.find(D=>Number(D.user_id)===Number(w))){const D=x.filter(L=>Number(L.user_id)!==Number(w)),l={...S,permissions:D};v.set(H,l),await V({type:Z.X2.UpdateProjectListMap,payload:new Map(v)}),window.top.postMessage({type:"successMsg",data:{msg:{name:f,other:"\u7684\u6743\u9650\u5DF2\u79FB\u9664"}}},"*"),window.top.postMessage({type:"updateApp",data:{newApp:(0,k.A)(l)}},"*")}}catch(v){window.top.postMessage({type:"errMsg",data:{msg:"\u6743\u9650\u66F4\u6539\u5931\u8D25"}},"*")}}}},50378:(Ne,de,n)=>{"use strict";n.d(de,{Ay:()=>u,X2:()=>p});const s={user:{id:0,user_id:"",user_cid:"",email:"",is_viewer:!1,name:"",mobile:null,avatar:""},org:{name:"",cid:"",logo:null,spaces:[],plan:"",trial_end_on:null,next_billing_on:"",settings:{},permissions:[],limitation:{is_export_watermark:!1,is_org_quit_transfer:!0,is_org_recent:!0,max_mkt_count:0,max_org_log_days:0,max_pic_bytes:0,max_project_count:0,max_project_share_count:0,max_project_template_count:0,max_project_version_count:0,max_recycler_days:0,max_screen_count:0,storage:0},members:[]},space:{cid:"",created_at:"",deleted:!1,name:"",org_cid:"",permissions:[],space_type:"",updated_at:"",user_id:0,is_disable_banned_viewer:!0},folderListMap:new Map,projectListMap:new Map,lang:"zh-CN"};let p=function(k){return k.UpdateState="reducer:update:user",k.UpdateUser="reducer:update:user",k.UpdateOrg="reducer:update:org",k.UpdateSpace="reducer:update:space",k.UpdateFolderListMap="reducer:update:folderListMap",k.UpdateProjectListMap="reducer:update:projectListMap",k}({});const u=function(k,te){k===void 0&&(k=s);const{type:Z,payload:z}=te;switch(Z){case p.UpdateState:return{...k,...z};case p.UpdateUser:return{...k,user:{...k.user,...z}};case p.UpdateOrg:return{...k,org:{...k.org,...z}};case p.UpdateSpace:return{...k,space:{...k.space,...z}};case p.UpdateFolderListMap:return{...k,folderListMap:z};case p.UpdateProjectListMap:return{...k,projectListMap:z};default:return k}}},61654:(Ne,de,n)=>{"use strict";n.d(de,{IE:()=>te,M0:()=>E,WR:()=>u,XW:()=>q,_B:()=>J,cb:()=>he,hG:()=>H,iF:()=>U,il:()=>T,kG:()=>z,oV:()=>Y,q7:()=>C,wA:()=>k,yZ:()=>V});var s=n(63686),p=n(59318),M=n(37673);const u=f=>{var y;return(y=f.projectAccess.user)==null?void 0:y.user_id},k=f=>f.projectAccess.org,te=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.cid)||""},Z=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.members)||[]},z=f=>{var y;return((y=f.projectAccess.org)==null?void 0:y.permissions)||[]},T=f=>f.projectAccess.space,E=f=>f.projectAccess.space.cid,U=f=>f.projectAccess.space.permissions||[],q=f=>f.projectAccess.folderListMap||new Map,J=f=>f.projectAccess.projectListMap||new Map,he=(0,s.Mz)(u,z,(f,y)=>(0,p.oE)({userId:f,orgPermissions:y})),V=(0,s.Mz)(u,U,(f,y)=>(0,p.sq)({userId:f,spacePermissions:y})),Y=(0,s.Mz)(U,f=>{const y=(0,p.Rc)(f);return f.reduce((v,S)=>{const{user_id:x}=S,I=(0,p.CF)({userId:x,permissionsMap:y,permissionScope:M.W.OrgSpace});return v.set(Number(x),I)},new Map)}),H=(0,s.Mz)(U,f=>f.filter(v=>v.role==="space_limiter").reduce((v,S)=>{const{user_id:x}=S;return v.set(Number(x),S)},new Map)),w=(0,s.Mz)(Z,f=>f.reduce((y,v)=>{const{user_id:S,user_cid:x,email:I,mobile:D}=v;return S&&x?y.set(Number(S)||S,{...v}):(I&&y.set(I,{...v}),D&&y.set(D,{...v}),y)},new Map)),C=(0,s.Mz)(z,w,(f,y)=>f.reduce((v,S)=>{const{user_id:x,unsign_remark:I}=S,D=x||I,l=y.get(D)||{},L=(0,p.Sc)(S);return v.set(D,{...l,permission:L})},new Map))},37673:(Ne,de,n)=>{"use strict";n.d(de,{W:()=>s,p:()=>p});let s=function(M){return M.Org="org",M.OrgSpace="space",M.Folder="folder",M.Project="project",M}({}),p=function(M){return M.OrgSpace="orgspace-permission-tip",M.OrgSpaceLimiter="orgspace-limiter-permission-tip",M.RootFolder="root-folder-permission-tip",M.SubFolder="sub-folder-permission-tip",M.Project="project-permission-tip",M}({})},15573:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>u,u:()=>M});var s=n(63924),p=n.n(s);const M=(k,te)=>{if(!(k!=null&&k.cid))return-1;let Z=0,z="",T=k;for(;T&&!T.root_project&&(Z=Z+1,z=T.parent_cid,!!z);)T=te.get(z);return Z};function u(k){return k&&Object.entries(k).reduce((te,Z)=>{let[z,T]=Z;return Object.assign(te,{[p()(z)]:T})},{})}},59318:(Ne,de,n)=>{"use strict";n.d(de,{CF:()=>H,CZ:()=>u,Cj:()=>E,Rc:()=>Y,Sc:()=>Z,oE:()=>U,ox:()=>te,pf:()=>V,sq:()=>q});var s=n(3895),p=n(37673);const M=function(w,C,f){return f===void 0&&(f=!1),{level:w,roleName:C,isInherited:f,isOwner:w>=s.zB.OWNER,isSuperManager:w>=s.zB.SUPERMANAGER,isManager:w>=s.zB.MANAGER,isMember:w>=s.zB.MEMBER,isViewer:w>=s.zB.VIEWER,isLimiter:w===s.zB.LIMITER,isBanned:w===s.zB.BANNED,isUnknown:w===s.zB.UNKNOWN}},u=M(s.zB.UNKNOWN),k=M(s.zB.UNJOINED),te=(w,C)=>{if(!C||C.length<=0)return null;const f=C.find(y=>{let{user_id:v,unsign_remark:S}=y;return Number(v)===Number(w)||S===w});return f||null},Z=w=>w?M(s.p_[w.role],w.role):M(s.zB.UNKNOWN),z=(w,C)=>{if(!w)return[];let f=[],y=w,v=C.get(y);for(;v&&!v.root_project;)f=f.concat(v.permissions),y=v.parent_cid,v=C.get(y);return f},T=(w,C,f)=>{let y=z(w,C);return y=y.concat(f).reverse(),y=y.filter(v=>v.role!=="space_limiter"),y.map(v=>{const S=folderInheritePermissionRoleFixMap[v.role];return S?{...v,role:S,inherited:!0}:{...v,inherited:!0}})},E=(w,C,f)=>{let y=z(w,C);return y=y.concat(f).reverse(),y=y.filter(v=>v.role!=="space_limiter"),y.map(v=>{const S=s.OO[v.role];return S?{...v,role:S,inherited:!0}:{...v,inherited:!0}})},U=w=>{const{orgPermissions:C,userId:f}=w;if(!f||!C||C.length<1)return M(s.zB.UNKNOWN);const y=te(f,C);return y?M(s.p_[y.role],y.role):M(s.zB.UNKNOWN)},q=w=>{const{spacePermissions:C,userId:f}=w;if(!f||!C||C.length<1)return M(s.zB.UNKNOWN);const y=te(f,C);return y?M(s.FU[y.role],y.role):M(s.zB.UNKNOWN)},J=w=>{const{userId:C,orgPermissions:f,spacePermissions:y,currentFolder:v,folderListMap:S}=w;if(!C||!v)return M(permissionLevels.UNKNOWN);const{org_cid:x,space_cid:I}=v;if(!x&&!I)return M(permissionLevels.OWNER,"team_owner");if(!y||y.length<1)return M(permissionLevels.UNKNOWN);const{isRoot:D,root_project:l}=v;if(D||l){const A=q({spacePermissions:y,userId:C});return A.roleName==="space_limiter"?{...U({orgPermissions:f,userId:C}),isLimiter:!0}:A}const L=v.permissions||[],R=T(v.parent_cid,S,y),se=Object.values(Object.fromEntries([...R,...L].map(A=>[A.user_id,A]))),g=te(C,se);return g?M(folderPermissionLevelsMap[g.role],g.role,g.inherited):M(permissionLevels.UNKNOWN)},he=w=>{const{userId:C,spacePermissions:f,currentProject:y,folderListMap:v}=w;if(!C||!y)return M(permissionLevels.UNKNOWN);const{space_cid:S}=y;if(!S)return M(permissionLevels.OWNER,"project_owner");if(!f||f.length<1)return M(permissionLevels.UNKNOWN);const x=y.permissions||[],I=E(y.team_cid,v,f),D=Object.values(Object.fromEntries([...I,...x].map(L=>[L.user_id,L]))),l=te(C,D);return l?M(projectPermissionLevelsMap[l.role],l.role,l.inherited):M(permissionLevels.UNKNOWN)},V=function(w,C,f){return f===void 0&&(f=!1),C?typeof C[w]<"u"?M(C[w],w,f):M(s.zB.UNJOINED):M(s.zB.UNKNOWN)},Y=w=>!w||w.length<1?new Map:w.reduce((C,f)=>C.set(Number(f.user_id),f),new Map),H=w=>{const{permissionsMap:C,userId:f,permissionScope:y}=w;if(!f||!C||C.size<1)return M(s.zB.UNKNOWN);const{role:v,inherited:S}=C.get(Number(f))||{};if(!v)return M(s.zB.UNKNOWN);let x=null;switch(y){case p.W.Org:x=s.p_;break;case p.W.OrgSpace:x=s.FU;break;case p.W.Folder:x=s.lY;break;case p.W.Project:x=s.au;break;default:break}return V(v,x,S)}},53159:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>w});var s=n(51044),p=n(21676),M=n(66748),u=n.n(M),k=n(98236),te=n.n(k),Z=n(28149);const z=p.Ay.label.withConfig({displayName:"styled__StyledLabel",componentId:"sc-1pwp3tx-0"})(["position:relative;display:inline-flex;align-items:baseline;line-height:1.5;cursor:pointer;color:#5b6b73;> input[type=radio]{position:absolute;opacity:0;}&.regular{font-size:14px;}&.small{font-size:12px;.Check-state{transform:translateY(2px);}}&.readonly{cursor:default;}&.is-disabled{cursor:not-allowed;&::after{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background-color:rgba(255,255,255,0.5);}}.Check-state{position:relative;flex:0 0 1em;margin-right:0.33333em;width:1em;height:1em;transform:translateY(1px);font-size:12px;background-color:#f6f7f8;border:1px solid #8d9ea7;border-radius:2px;color:#fff;.svg-icon.check{position:absolute;top:-1px;left:-1px;opacity:0;}}&.small .Check-state{transform:translateY(2px);}&.CoreRadio,&.Radio{&.is-checked{.Check-state{background-color:#298df8;border-color:transparent;color:#fff;.svg-icon.check{transform:scale(0.833);}}}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}&.CoreRadio{.Check-state{background-color:#fff;border-color:#bacdd6;}&.is-checked .Check-state{background-color:#eb5648;}}"]),T=(0,p.Ay)(z).withConfig({displayName:"styled__StyledRadio",componentId:"sc-1pwp3tx-1"})([".Check-state{padding:1px;border-radius:50%;&:after{content:'';position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);display:block;width:4px;height:4px;background-color:#fff;border-radius:50%;}}.is-checked .Check-state{position:relative;}"]),E=p.Ay.span.withConfig({displayName:"styled__StyledRadioGroup",componentId:"sc-1pwp3tx-2"})(["position:relative;display:flex;flex-wrap:wrap;&.is-disabled{cursor:not-allowed;}.Radio,.CoreRadio{margin-right:.5em;min-height:2em;}"]);var U=n(57464);function q(C,f,y){return(f=J(f))in C?Object.defineProperty(C,f,{value:y,enumerable:!0,configurable:!0,writable:!0}):C[f]=y,C}function J(C){var f=he(C,"string");return typeof f=="symbol"?f:f+""}function he(C,f){if(typeof C!="object"||!C)return C;var y=C[Symbol.toPrimitive];if(y!==void 0){var v=y.call(C,f||"default");if(typeof v!="object")return v;throw new TypeError("@@toPrimitive must return a primitive value.")}return(f==="string"?String:Number)(C)}class V extends s.PureComponent{constructor(){super(...arguments),q(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),q(this,"onToggle",()=>{const{name:f,value:y,label:v,onToggle:S,onChange:x}=this.props,{isChecked:I}=this.state,{canToggle:D}=this,l=D?!0:I;this.setState({isChecked:l}),S(l,f,y||v),x(f,y||v,l)})}static getDerivedStateFromProps(f,y){let{prevProps:v,isChecked:S}=y;return te()(v,f)?null:{prevProps:f,isChecked:f.isChecked}}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:f,readOnly:y}=this;return!f&&!y}render(){const{size:f,theme:y,className:v,label:S,name:x}=this.props,{isChecked:I}=this.state,{isDisabled:D,readOnly:l}=this;return(0,U.jsxs)(T,{className:(0,Z.Hn)([y==="core"?"CoreRadio":"Radio",f,v,I&&"is-checked",D&&"is-disabled",l&&"readonly"]),children:[(0,U.jsx)("input",{type:"radio",defaultChecked:I,disabled:D,name:x,onClick:this.onToggle}),(0,U.jsx)("span",{className:"Check-state"}),(0,U.jsx)("span",{className:"Check-label",children:S})]})}}q(V,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),className:u().string,label:u().any,name:u().string,value:u().any,isChecked:u().bool,isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired}),q(V,"defaultProps",{size:"regular",theme:"plain",isChecked:!1,label:"",className:"",onChange:()=>null,onToggle:()=>null});class Y extends s.PureComponent{constructor(){super(...arguments),q(this,"name",this.props.name||Math.random().toString(36).substring(2,15)),q(this,"state",{prevProps:this.props,value:this.props.value}),q(this,"createOnChangeHandler",(f,y,v)=>()=>{const{onToggle:S,onChange:x}=this.props,{value:I}=this.state,{canToggle:D}=this,l=D?y:I;this.setState({value:l}),S(l,f),x({name:f,value:l,idx:v})})}static getDerivedStateFromProps(f,y){let{prevProps:v,value:S}=y;return te()(v,f)?null:{prevProps:f,value:f.value}}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:f,readOnly:y}=this;return!f&&!y}render(){const{size:f,theme:y,className:v,optionList:S}=this.props,{value:x}=this.state,{name:I,isDisabled:D,readOnly:l}=this,L=(0,Z.Hn)([y==="core"?"CoreRadioGroup":"RadioGroup",f,v,D&&"is-disabled",l&&"readonly"]);return(0,U.jsx)(E,{className:L,children:S.map((R,se)=>R&&(0,U.jsx)(V,{name:I,size:f,theme:y,label:(0,Z.Oi)(R),type:"radio",isChecked:(0,Z.o3)(R,x),isDisabled:D||R.isDisabled,readOnly:l,onChange:D||R.isDisabled?void 0:this.createOnChangeHandler(I,(0,Z.nE)(R),se)},se))})}}q(Y,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),className:u().string,name:u().string,optionList:u().arrayOf(u().oneOfType([u().string,u().number,u().shape({label:u().any,value:u().any,isDisabled:u().bool})])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired}),q(Y,"defaultProps",{size:"regular",theme:"plain",className:"",optionList:[],onChange:()=>null,onToggle:()=>null});const H=(0,p.Ay)(Y).withConfig({displayName:"styles__StyledRadioGroup",componentId:"sc-1x96wso-0"})([".Radio{color:",';input[type="radio"]{cursor:pointer;}&.is-checked{color:',";.Check-state{background:",";&::after{background:#fff;}}}&:not(.is-checked){.Check-state{background:",";border:1px solid ",";&::after{background:",";}}}&.is-disabled::after{background-color:",";opacity:0.5;}}"],C=>C.theme.color_text_L2,C=>C.theme.color_text_L1,C=>C.theme.color_proto,C=>C.theme.color_bg_white,C=>C.theme.color_text_disabled01,C=>C.theme.color_bg_white,C=>C.theme.color_bg_white);class w extends s.PureComponent{render(){return(0,U.jsx)(H,{...this.props})}}},86778:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>V});var s=n(51044),p=n(21676),M=n(66748),u=n.n(M),k=n(98236),te=n.n(k),Z=n(28149);const z=p.Ay.label.withConfig({displayName:"styled__StyledSwitch",componentId:"sc-16h4tgy-0"})(["position:relative;display:inline-block;margin:0 10px;width:28px;height:10px;vertical-align:text-bottom;background:rgba(22,132,252,0.4);border-radius:100px;cursor:pointer;transform:matrix(-1,0,0,1,0,0);transition:all 0.2s ease-in;transition-delay:0.15s;&.readonly{cursor:default;}> button{position:absolute;top:-20%;left:0;display:flex;justify-content:center;align-items:center;width:14px;height:14px;font-size:14px;border-radius:50%;background:#1684FC;box-shadow:0px 1px 1px rgba(0,0,0,0.15),0px 0px 2px rgba(0,0,0,0.12);transform:matrix(-1,0,0,1,0,0);cursor:inherit;transition:all 0.2s cubic-bezier(0.55,0.06,0.68,0.19);}&.small{width:28px;height:10px;vertical-align:baseline;> button{width:14px;height:14px;border-width:1px;}}&.is-checked{background:#dbdbdb;> button{transform:translate(14px,0%);cursor:inherit;background-color:#fff;box-shadow:0px 1px 1px rgba(0,0,0,0.15),0px 0px 2px rgba(0,0,0,0.12);}&.small > button{transform:translate(14px,0);background-color:#999;}&.small{background-color:#59515c;}}&.is-disabled{background-color:#dedee4;cursor:not-allowed;&.is-checked{background-color:rgba(22,132,252,0.4);}button{background-color:#f6f7f8;}}&.Switch-IOS{background:#1684fc;transform:matrix(1,0,0,1,0,0);transition:all 0.2s ease-in;> button{top:1px;left:1px;background:#fff;transform:matrix(1,0,0,1,0,0);}&.small{width:24px;height:12px;border-radius:6px;> button{width:10px;height:10px;border-width:0;}}&.is-checked{&.small > button{transform:translate(12px,0);background-color:#fff;}&.small{background-color:#ccc;}}}&.Switch-IOS-NoTransform{background:#1684fc;transition:all 0.2s ease-in;> button{top:1px;left:1px;background:#fff;transform:matrix(1,0,0,1,0,0);}&.small{width:24px;height:12px;border-radius:6px;> button{width:10px;height:10px;border-width:0;}}&.is-checked{&.small > button{transform:translate(12px,0);background-color:#fff;}&.small{background-color:#ccc;}}}"]);var T=n(57464);function E(Y,H,w){return(H=U(H))in Y?Object.defineProperty(Y,H,{value:w,enumerable:!0,configurable:!0,writable:!0}):Y[H]=w,Y}function U(Y){var H=q(Y,"string");return typeof H=="symbol"?H:H+""}function q(Y,H){if(typeof Y!="object"||!Y)return Y;var w=Y[Symbol.toPrimitive];if(w!==void 0){var C=w.call(Y,H||"default");if(typeof C!="object")return C;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(Y)}class J extends s.PureComponent{constructor(){super(...arguments),E(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),E(this,"toggle",H=>{let{target:w}=H;const{onChange:C}=this.props,{isChecked:f}=this.state,{isDisabled:y,canToggle:v}=this,S=v?!f:f;return w.blur(),this.setState({isChecked:S}),!y&&C(S)})}static getDerivedStateFromProps(H,w){let{prevProps:C}=w;return te()(C,H)?null:{prevProps:H,isChecked:H.isChecked}}get isDisabled(){const{isDisabled:H,disabled:w}=this.props;return H||w}get readOnly(){return this.props.readOnly}get canToggle(){const{isDisabled:H,readOnly:w}=this;return!H&&!w}render(){const{size:H,readOnly:w,className:C,children:f,isIOS:y,isIOSReverse:v}=this.props,{isChecked:S}=this.state,{isDisabled:x}=this;return(0,T.jsxs)(z,{className:(0,Z.Hn)(["Switch",y&&"Switch-IOS",v&&"Switch-IOS-NoTransform",H,S?"is-checked":"isnt-checked",x&&"is-disabled",w&&"readonly",C]),children:[(0,T.jsx)("button",{type:"button",disabled:x,onClick:this.toggle}),f]})}}E(J,"propTypes",{className:u().string,size:u().oneOf(["regular","small"]),isChecked:u().bool,isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,isIOS:u().bool,isIOSReverse:u().bool,onChange:u().func,children:u().any}),E(J,"defaultProps",{className:"",size:"regular",isChecked:!1,isDisabled:!1,disabled:!1,readOnly:!1,isIOS:!1,isIOSReverse:!1,onChange:()=>null});const he=(0,p.Ay)(J).withConfig({displayName:"styles__StyledSwitch",componentId:"sc-1wltq4q-0"})(["&.Switch-IOS{background:",";transform:matrix(1,0,0,-1,0,0);margin:0;button{border:unset;background:",";}&.regular{width:30px;height:14px;button{height:12px;width:12px;}}&.is-checked{&.small,&.regular{background:",";button{background:",";}}&.regular  button{transform:translate(15px,0);}}}"],Y=>Y.theme.color_text_disabled01,Y=>Y.theme.color_text_btn,Y=>Y.theme.color_proto,Y=>Y.theme.color_text_btn);class V extends s.PureComponent{render(){return(0,T.jsx)(he,{...this.props})}}},7775:(Ne,de,n)=>{"use strict";n.d(de,{it:()=>k,V0:()=>E,mq:()=>T});var s=n(51044),p=n(10481),M=n(21676);const u=(0,M.Ay)(p.Ay).withConfig({displayName:"styled__StyledWorkspaceSelectProto",componentId:"sc-32xdp1-0"})(["&.WorkspaceSelect{padding:0px;border-radius:4px;button{width:auto;display:flex;padding:0 6px;align-items:center;color:",";}.caret{flex-shrink:0;width:26px;display:flex;justify-content:center;align-items:center;padding:0;color:",";svg{width:26px;height:26px;}}&:not(.is-disabled):not(.readonly):hover{border-color:",";button{width:calc(100% - 26px);}.caret{padding:0;color:",";}}&.is-open{border-color:transparent;}}"],U=>U.theme.color_text_L1,U=>U.theme.color_text_L3,U=>U.theme.color_bg_border_02,U=>U.theme.color_text_L1),k="select-menu-width-108",te=(0,M.DU)(["#IBOT_SELECT_MENU_ROOT{.SelectMenuPortal{.menu-animation-select{margin-left:-15px;}.WorkspaceSelectMenu{padding:8px;border-radius:8px;color:",";background:",";box-shadow:",";&.is-open{border:1px solid ",";border-radius:8px;width:176px;max-width:176px;padding:8px;background-color:",";box-shadow:",";.SelectOption{&:not(.empty-msg):not(.is-disabled):hover{background:",";}}}> .divider{border-bottom:1px solid ",";margin:5px;}.SelectOption{border-radius:4px;height:28px;line-height:28px;&.is-active{color:",";}&:not(.empty-msg):not(.is-disabled):not(.is-active):hover{background-color:",";color:",";}}}.menu-animation-select .SelectGroup > .title{color:",";}}.autoFillPanelSelectScreenMenu{margin-left:-138px;margin-top:26px;width:175px;}.bdr-width-input-menu{width:88px;}.arrow-setting-menu{width:78px;}.","{width:108px;}}"],U=>U.theme.color_text_L1,U=>U.theme.color_bg_white,U=>U.theme.shadow_m,U=>U.theme.color_bg_border_01,U=>U.theme.color_bg_white,U=>U.theme.shadow_m,U=>U.theme.color_btn_secondary_hover,U=>U.theme.color_bg_border_01,U=>U.theme.color_text_L1,U=>U.theme.color_btn_secondary_hover,U=>U.theme.color_text_L1,U=>U.theme.color_text_L3,k);var Z=n(7290),z=n(57464);class T extends s.PureComponent{render(){return(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(u,{...this.props,arrowSvg:(0,z.jsx)(Z.C,{className:"triangle-icon",name:"common/expand@26"})}),(0,z.jsx)(te,{})]})}}class E extends s.PureComponent{render(){return(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(p.eB,{...this.props}),(0,z.jsx)(te,{})]})}}},24660:(Ne,de,n)=>{"use strict";n.d(de,{l:()=>s});const s=function(p,M,u,k){u===void 0&&(u=""),k===void 0&&(k=!1);let te;return p?te={mode:"org",orgCid:p.cid,payEntrance:M,checkoutArea:"proto",checkoutPlace:u||"org_use_vip_mkt"}:te={mode:k?"org":"solo",isSelectOrg:!!k,payEntrance:M,checkoutArea:"proto",checkoutPlace:u||"solo_use_vip_mkt"},te}},64598:(Ne,de,n)=>{"use strict";n.d(de,{_:()=>M});var s=n(36820);const p=u=>{let{exposure_reason:k}=u;try{saTrack("trial_watermark_exposure",{exposure_reason:k})}catch(te){console.log(te.message)}},M=u=>{let{click_button:k}=u;try{(0,s.kH)("trial_watermark_click",{click_button:k})}catch(te){console.log(te.message)}}},10286:(Ne,de,n)=>{"use strict";n.d(de,{ER:()=>w,J_:()=>he,Or:()=>T,QC:()=>v,X0:()=>Y,Y5:()=>V,aX:()=>y,dJ:()=>q,g:()=>E,l1:()=>f,wi:()=>z,yq:()=>U});var s=n(26114),p=n(29815),M=n(72907),u=n(46795),k=n(29601),te=n(36521),Z=n(57464);const z=async S=>{let{projectCid:x}=S;return(0,u.DE)("/api/upper/web_v1/sharing/initial?cid="+x)},T=async S=>{let{projectCid:x,sharing:I}=S;return(await(0,u.zi)("/api/flat/web_v1/project_share/"+x,{...I})).project_share},E=async S=>{let{projectCid:x,sharingCid:I}=S;try{return await(0,u.OD)("/api/flat/web_v1/project_share/"+I+"?project_cid="+x)}catch(D){await(0,M.FY)(D)}},U=async S=>{let{sharing:x}=S;try{return await(0,u.SN)("/api/flat/web_v1/project_share/"+x.cid,{project_share:x})}catch(I){await(0,M.FY)(I)}},q=async S=>{try{return await(0,u.DE)("/api/flat/web_v1/preview/visit_count/"+S)}catch(x){console.error(x)}},J=()=>{const S=document.getElementById("workspace");(0,s.createRoot)(S).render((0,Z.jsx)(p.A,{locale:(0,k.w)(),errorType:404,customDes:I18N.ToolBar.ReEnter,isShowPrimaryBtn:!1}))},he=async S=>{try{if(!S.accessToken||S.project.access_token===S.accessToken)return V(S.project);{const{project:x,accessToken:I}=S,{project_share:D}=await(0,u.DE)("/api/flat/web_v1/project_share/"+x.cid+"?access_token="+I);return{sharing:{...D,type:"advanced",view_mode:D.device_model}}}}catch(x){await J()}},V=S=>{const{cid:x,access_token:I,access:D,building:l,comment_permission:L,password:R,visibility:se,wechat:g,is_first_canvas_open:A}=S,ee=new URLSearchParams(location.search),le=ee.get("screen"),re=ee.get("canvasId"),X=ee.get("view_mode")||(0,te.Yt)(x+"_default_sharing_view_mode","read_only",te.qW.String),we=ee.get("view_mode")||(0,te.Yt)(x+"_default_sharing_view_mode","read_only",te.qW.String),ze=!!ee.get("selection"),He=ze?ee.get("selection").split(","):[];return{sharing:{type:"default",project_cid:x,access_token:I,view_access:D,view_sticky:l,comment_permission:L,password:R,visibility:se,wechat:g,view_mode:X,screen_visible_switch:ze,screen_visible_list:He,page_begin:le,canvas_begin:re,device_model:we,is_first_canvas_open:A}}},Y=async S=>{let{projectCid:x}=S;try{const{project_shares:I}=await(0,u.DE)("/api/flat/web_v1/project_share/"+x+"/all");return I.sort((D,l)=>new Date(l.created_at).getTime()-new Date(D.created_at).getTime())}catch(I){return await(0,M.FY)(I),[]}},H=async S=>{try{const{result:x}=await fetchGetJSON("/api/accesses/v4/project/"+S+"/permission_check?permission=P:update");return x}catch(x){await apiErrorModals(x)}},w=async S=>{let x=null;try{x=await(0,u.DE)("/flpak/ww-p2meta/"+S)}catch(I){await(0,M.FY)(I)}return x},C={access_token:"",comment_permission:"",device_model:"",highlight:!0,password:"",project_cid:"",screen_visible_switch:!1,screen_visible_list:[],shell_type:"device",simulator_type:"device",sticky:!0,view_access:"private",view_count:0,view_prd:!1,wechat:!1,enable_version_record:!1,view_sticky:!0,is_first_canvas_open:!1},f=()=>({sharing:C}),y=async S=>{let x,I;try{x=await(0,u.DE)("/api/upper/web_v1/basics/"+S+"/get_details"),x&&JSON.stringify(x)!=="{}"&&(I=!0)}catch(D){x=D,I=!1}return{result:x,statusOk:I}},v=async S=>{let x,I;try{x=await(0,u.DE)("/api/dsh2/web_v1/permissions/can_edit_check?cid="+S),x&&JSON.stringify(x)!=="{}"&&(I=!0)}catch(D){x=D,I=!1}return{result:x,statusOk:I}}},77180:(Ne,de,n)=>{"use strict";n.d(de,{gE:()=>p,nM:()=>s,nQ:()=>M});const s="local_sharing_copy_url_v8_1",p=50,M={public:"share_anyone_view",restricted:"org_members_only"}},55134:(Ne,de,n)=>{"use strict";n.d(de,{n:()=>Ur});var s=n(51044),p=n(28055),M=n(96510),u=n(82928),k=n(50378),te=n(30939);const Z={cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"read_only",is_first_canvas_open:!1},z=null,T="123",E={projectBasicToken:"BcHc8Xdrrwlnj5pdDxPloA",projectShares:[{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"},{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"},{cid:"pslj5lho8lkbh1m4",project_cid:"pblj5lho3sfrgsvp",view_access:"restricted",access_token:"bNDBP6orwlnj5R57xaJ4y",password:"",view_count:0,view_prd:!0,screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,sticky:!0,view_sticky:!0,comment_permission:"org_member",link_name:"\u5206\u4EAB",is_default_link:!1,creator_id:2735130,expire_type:"forever",device_model:"bbb"}]},U={owner_id:10,owner_name:"d9",owner_email:"<EMAIL>",owner_avatar:"/images/avatar.png",id:71,limitation:{storage:5e3,exportable:["png","pngs","htmlzip"],encryptable:!0,inspectable:!0,slices:!0,projects:65535,screens:65535,commentable:!0},screens_count:0,cid:"pb2lje6hu9dax5akx",team_cid:"teuaroot",space_cid:null,name:"sharing",type:"proto2",attr:{},created_at:1687863747e3,updated_at:1688043155e3,timestamp:"1688043155",access:"public",access_token:"32vJ5gBXrwws03fxYV0OK2",version:"v3",icon:null,splash:null,width:390,height:844,device:"iphone",model:"iphone_13_pro",scale:100,archived:!1,parent_cid:null,source_upper_cid:null,clones:0,shell_type:"device",password:"",wechat:!1,highlight:!0,preview_option:1,expired:!1,deleted:!1,duplicating:!1,permissions:[{user_id:10,role:"project_owner"}],is_org_project:!1,is_sub_project:!1,runner_mode:"preview",comment_permission:"org_member",tabs:null,visibility:"open",building:"view_sticky"},q={owner_id:10,owner_name:"d9",owner_email:"<EMAIL>",owner_avatar:"/images/avatar.png",id:71,limitation:{storage:5e3,exportable:["png","pngs","htmlzip"],encryptable:!0,inspectable:!0,slices:!0,projects:65535,screens:65535,commentable:!0},screens_count:0,cid:"pb2lje6hu9dax5akx",team_cid:"teuaroot",space_cid:null,name:"sharing",type:"proto2",attr:{},created_at:1687863747e3,updated_at:1688043155e3,timestamp:"1688043155",access:"public",access_token:"32vJ5gBXrwws03fxYV0OK2",version:"v3",icon:null,splash:null,width:390,height:844,device:"iphone",model:"iphone_13_pro",scale:100,archived:!1,parent_cid:null,source_upper_cid:null,clones:0,shell_type:"device",password:"",wechat:!1,highlight:!0,preview_option:1,expired:!1,deleted:!1,duplicating:!1,permissions:[{user_id:10,role:"project_owner"}],is_org_project:!1,is_sub_project:!1,runner_mode:"preview",comment_permission:"org_member",tabs:null,visibility:"open",building:"view_sticky"};var J=n(10286),he=n(46664),V=n(60482),Y=n(36820),H=n(59318);let w=function(e){return e.acces="access",e.viewAccess="viewAccess",e.copy="copy",e.qrCode="qrCode",e.embed="Embed",e.setting="Setting",e.pwdProtection="PwdProtection",e.password="password",e.sticky="sticky",e.comment="comment",e.mobile="mobile",e}({});var C=function(e){return e.Edit="\u53EF\u7F16\u8F91",e.onlyView="\u4EC5\u67E5\u770B",e}(C||{}),f=function(e){return e.NoLogin="\u672A\u767B\u5F55\u7528\u6237",e.LoginButNotOrgMember="\u975E\u56E2\u961F\u767B\u5F55\u7528\u6237",e.ProjectMember="\u534F\u4F5C\u6210\u5458",e.TeamMember="\u56E2\u961F\u6210\u5458",e}(f||{}),y=function(e){return e.EditMode="\u7F16\u8F91\u6A21\u5F0F",e.ReadOnlyMode="\u53EA\u8BFB\u6A21\u5F0F",e.DeviceMode="\u771F\u673A\u6A21\u5F0F",e.InspectMode="\u6807\u6CE8\u6A21\u5F0F",e}(y||{});const v=e=>{let{userId:i,orgPermissions:c,spacePermissions:d,projectPermissions:_}=e;if(!i)return f.NoLogin;const b=(0,H.ox)(i,c),m=(0,H.ox)(i,d),N=(0,H.ox)(i,_);if(!b)return f.LoginButNotOrgMember;if(m||N)return f.ProjectMember;if(b)return f.TeamMember},S=e=>{let{userCanEdit:i,userId:c,orgPermissions:d,spacePermissions:_,projectPermissions:b,isEditMode:m,view_mode:N,isIframe:j}=e;const B=i?C.Edit:C.onlyView,G=v({userId:c,orgPermissions:d,spacePermissions:_,projectPermissions:b}),$=j?"\u5DE5\u4F5C\u53F0":m?y.EditMode:N==="read_only"?y.ReadOnlyMode:N==="device"?y.DeviceMode:N==="inspect"?y.InspectMode:"";return{user_rights:B,user_type:G,operation_from:$}},x=e=>{let{optionType:i,userCanEdit:c,userId:d,orgPermissions:_,spacePermissions:b,projectPermissions:m,isEditMode:N,view_mode:j,share_type:B,isIframe:G,project_name:$,project_cid:P}=e;const{user_rights:ae,user_type:oe,operation_from:O}=S({userCanEdit:c,userId:d,orgPermissions:_,spacePermissions:b,projectPermissions:m,isEditMode:N,view_mode:j,isIframe:G});(0,Y.kH)("share_function_click_V8",{operation_type:i,user_rights:ae,user_type:oe,operation_from:O,project_name:$,project_cid:P,share_type:B})},I=e=>{(0,Y.kH)("advanced_sharing_click",e)},l=(()=>{const e={project:void 0,flatKey:void 0,theme:"light",user:void 0,org:void 0,settingPageType:"create",subSettingPageType:"basic",currentSharing:Z,hostType:"proto",topPageIndex:"edit",advancedSharingList:[],canEditByUser:!1,mainPage:"share",tabIndex:0,projShareToEdit:"",members:[],isEditMode:location.pathname.includes("/design/"),screenMetaList:void 0,initData:{},loading:!0,hostSharingData:void 0,sharingToast:"",currentSelectSharing:void 0,isOnlyMemberManager:!1,hostCurrentScreen:void 0},i=function(b,m){b===void 0&&(b=e);let{type:N="sharing:state:update",payload:j=e}=m;switch(N){case"sharing:state:update":return{...b,...j};case"sharing:state:clear":return{...e};default:return b}},c={"sharing:init":(b,m)=>{let{dispatch:N}=b,{payload:j}=m;N({type:"sharing:state:update",payload:j})},"sharing:currentSharing:update":(b,m)=>{let{getState:N,dispatch:j}=b,{payload:{updatedKV:B}}=m;const G=N(),P={...d.getCurrentSharing(G),...B};j({type:"sharing:state:update",payload:{currentSharing:P}})},"sharing:remote:sharing:update":async(b,m)=>{let{getState:N,dispatch:j}=b,{payload:{sharingData:B,updatedKV:G}}=m;const $=N(),P=d.getOrg($),ae=d.getProject($);let oe;if(B.type==="default"){var O,ne;oe=async()=>(0,he.Yu)(ae.cid,{...G}),await(0,V.eH)({updateType:"update",updateFn:oe,org:P});const ie={...ae,...G};j({type:"sharing:state:update",payload:{project:ie}}),(O=MB)!=null&&O.action&&((ne=MB)==null||ne.action("current:update:state",{currentProject:ie}))}else{const ie=d.getAdvancedSharingList($),ge={...B,...G};oe=async()=>(0,J.yq)({sharing:ge});const{expired_at:pe}=await(0,V.eH)({updateType:"update",updateFn:oe,org:P,updatedKV:G});j({type:_["sharing:currentSharing:update"],payload:{updatedKV:{expired_at:pe}}});const fe=ie.map(W=>W.cid===ge.cid?{...ge,expired_at:pe}:W);j({type:_["sharing:advancedSharingList:update"],payload:{sharingList:fe}})}},"sharing:remote:sharing:delete":async(b,m)=>{let{getState:N,dispatch:j}=b,{payload:{sharingCid:B}}=m;const G=N(),$=d.getProject(G),P=d.getAdvancedSharingList(G);await(0,J.g)({projectCid:$.cid,sharingCid:B});const ae=P.filter(oe=>oe.cid!==B);j({type:_["sharing:advancedSharingList:update"],payload:{sharingList:ae}})},"sharing:settingPage:advanced:confirm":async(b,m)=>{let{getState:N,dispatch:j}=b,{payload:{updatedKV:B,isCustom:G}}=m;const $=N(),P=d.getSettingPageType($),ae=d.getCurrentSharing($),oe=d.getOrg($),O=d.getProject($);if(P==="create"){const ne=async()=>(0,J.Or)({projectCid:O.cid,sharing:ae});await(0,V.eH)({updateType:"create",updateFn:ne,org:oe}),j({type:_["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})}else{const ne=async()=>(0,J.yq)({sharing:ae}),{expired_at:ie}=await(0,V.eH)({updateType:"update",updateFn:ne,org:oe,updatedKV:B,isCustom:G});j({type:_["sharing:currentSharing:update"],payload:{updatedKV:{expired_at:ie}}})}j({type:"sharing:advance:click:track"})},"sharing:settingPage:default:confirm":async b=>{var m,N;let{getState:j,dispatch:B}=b;const G=j(),$=d.getCurrentSharing(G),P=d.getProject(G),ae=d.getOrg(G),oe=async()=>(0,he.Yu)(P.cid,Object.assign({},$,{building:$.view_sticky}));await(0,V.eH)({updateType:"update",updateFn:oe,org:ae});const O=Object.assign({},P,$,{building:$.view_sticky});B({type:"sharing:state:update",payload:{project:O}}),(m=MB)!=null&&m.action&&((N=MB)==null||N.action("current:update:state",{currentProject:O}))},"sharing:go-settingPage":(b,m)=>{let{dispatch:N}=b,{payload:{currentSharing:j,settingPageType:B,subSettingPageType:G=e.subSettingPageType}}=m;N({type:"sharing:state:update",payload:{currentSharing:j,settingPageType:B,topPageIndex:"setting",subSettingPageType:G}})},"sharing:topPageIndex:jump":(b,m)=>{let{dispatch:N}=b,{payload:{topPageIndex:j=e.topPageIndex}}=m;N({type:"sharing:state:update",payload:{topPageIndex:j}})},"sharing:advancedSharingList:update":(b,m)=>{let{dispatch:N}=b,{payload:{sharingList:j}}=m;N({type:"sharing:state:update",payload:{advancedSharingList:j}})},"sharing:projectAccess:init":async b=>{let{dispatch:m,getState:N}=b;const j=N(),B=d.getMembers(j),G=d.getProject(j),$=G==null?void 0:G.cid,P=G==null?void 0:G.is_org_project;if(!$||!P){setTimeout(()=>{m({type:l.entryKey["sharing:init"],payload:{loading:!1}})},100);return}const{result:ae,statusOk:oe}=await(0,J.aX)($);if(m({type:l.entryKey["sharing:init"],payload:{loading:!1}}),!!oe&&ae){var O,ne;const{project:ie,org:ge}=ae;await m({type:"entry:projectAccess:initProjectData",payload:{initData:ae,members:B}}),m({type:l.entryKey["sharing:init"],payload:{project:ie,org:ge,initData:ae}}),(O=MB)!=null&&O.action&&((ne=MB)==null||ne.action("current:update:state",{currentProject:ie}))}},"sharing:function:track":(b,m)=>{let{getState:N}=b,{payload:{operation:j,viewMode:B,isDefault:G}}=m;const $=N(),P=d.getProject($)||{},ae=d.getCanEditByUser($),oe=d.getOrg($)||{},O=d.getUser($),ne=d.getInitData($)||{},ie=(ne==null?void 0:ne.space)||{},ge=d.getIsEditMode($),pe=d.getHostType($),{cid:fe,name:W}=P,ye={userCanEdit:ae,userId:O==null?void 0:O.id,orgPermissions:oe==null?void 0:oe.permissions,spacePermissions:ie==null?void 0:ie.permissions,projectPermissions:P==null?void 0:P.permissions,isEditMode:ge,view_mode:B,optionType:j,share_type:G?"\u9ED8\u8BA4\u5206\u4EAB":"\u9AD8\u7EA7\u5206\u4EAB",isIframe:pe==="iframe",project_name:W,project_cid:fe};x(ye)},"sharing:advance:click:track":(b,m)=>{let{getState:N}=b,{}=m;const j=N(),B=d.getSettingPageType(j),G=d.getCurrentSharing(j),$=d.getProject(j),P=d.getAdvancedSharingList(j)||[],ae=d.getScreenMetaList(j),oe=d.getHostType(j),O=(0,V.RF)(ae.pageAttrMap),ne=(0,V.Mj)(!G.screen_visible_switch,G.screen_visible_list,O);let ie="\u5168\u90E8";G.screen_visible_switch&&(ie=""+ne);const ge=P.length;I({link_name:G.link_name,links_number:B==="create"?ge+1:ge,share_type:G.device_model==="device"?"\u6F14\u793A\u6A21\u5F0F":"\u753B\u5E03\u6A21\u5F0F",page_number:ie,operation_type:B==="create"?"\u65B0\u5EFA":"\u7F16\u8F91",source:oe==="iframe"?"\u5DE5\u4F5C\u53F0":"\u7F16\u8F91\u533A",project_name:$.name,project_cid:$.cid})}},d=te.B.genQuery(e,{getStateFn:b=>b.sharing}),_=te.B.genEntryKey(c);return{__initialState:e,__reducer:i,__entry:c,entryKey:_,query:d}})(),L=(0,p.HY)({sharing:l.__reducer,projectAccess:k.Ay}),R={...l.__entry,...M.s},se=()=>{const e=(0,u.RZ)(),i=(0,p.y$)(L,(0,p.Tw)(e.middleware));return e.setEntryMap(R),i};var g=n(72605),A=n(21676),ee=n(21952),le=n(57716),re=n(86634),X=n.n(re),we=n(77180);const ze=A.Ay.div.withConfig({displayName:"styles__StyledSharingToolBar",componentId:"sc-1k4rs1h-0"})(["display:flex;align-items:center;justify-content:space-between;height:","px;padding-right:12px;border-bottom:1px solid ",";&.tool-upgrade{padding-right:24px;}.sharing-title{margin-left:24px;color:",";font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:500;line-height:24px;}.upgrade-team-business{padding:0 10px;height:26px;border-radius:13px;display:flex;align-items:center;justify-content:center;background:",";font-size:14px;font-weight:500;position:relative;cursor:pointer;z-index:1;&:hover{background:",";}.team-business{background:linear-gradient(265.2deg,"," -21.19%,"," 105.08%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;text-fill-color:transparent;flex:none;order:0;flex-grow:0;}.content{font-size:12px;font-family:PingFang SC;font-style:normal;font-weight:400;line-height:18px;position:absolute;background:#454647;top:36px;right:0;width:300px;padding:12px 16px;border-radius:6px;.learn-text{margin-left:8px;color:#80BCFF;&:hover{color:#A6D2FF;}}&::before{content:'';position:absolute;top:0px;right:30px;transform:translateY(-100%);display:inline-block;width:0;height:0;border:6px solid transparent;border-bottom:6px solid #454647;}.top-bar{position:absolute;width:100%;height:10px;top:-10px;right:0;}span{color:",";}a{cursor:pointer;color:",";&:hover{color:",";}}}}"],we.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_background_AI_head_tag,e=>e.theme.color_background_AI_head_tag_hover,e=>e.theme.color_AI_switch_gradient_start,e=>e.theme.color_AI_switch_gradient_end,e=>e.theme.color_text_btn,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover),He=A.Ay.div.withConfig({displayName:"styles__StyledShareHeadMembers",componentId:"sc-1d78taz-0"})(["height:36px;border-radius:18px;display:flex;align-items:center;padding:0 4px;cursor:pointer;&:hover{background:",";}.avatar{height:24px;width:24px;border-radius:12px;box-sizing:border-box;border:1px solid ",";&:not(:first-child){margin-left:-6px;}&.avater-name{text-align:center;background:rgb(22,133,252);justify-content:center;color:white;font-size:12px;text-align:center;display:flex;align-items:center;}}.item-line{margin-left:6px;width:1px;height:16px;background:",";}.remaining{box-sizing:border-box;border-radius:12px;border:1px solid ",";background:rgb(22,133,252);justify-content:center;display:flex;align-items:center;margin-left:-6px;width:24px;height:24px;svg{width:24px;height:24px;}}.arror{margin-left:6px;width:14px;height:14px;}"],e=>e.theme.color_btn_secondary_active,e=>e.theme.color_share_member__border_color,e=>e.theme.color_bg_border_02,e=>e.theme.color_share_member__border_color);var je=n(37673),Ee=n(7290);const tt="__mb_delete_permission",Pe=[{value:"org_owner",label:"permissions_org_owner"},{value:"org_manager",label:"permissions_org_super_administrator"},{value:"org_admin",label:"permissions_org_administrator"},{value:"org_member",label:"permissions_org_collaboration_member"},{value:"org_viewer",label:"permissions_org_review_member"}],Q=[{value:"space_manager",label:"permissions_space_manager"},{value:"space_member",label:"permissions_space_collaboration_member"},{value:"space_viewer",label:"permissions_space_review_member"},{value:"space_limiter",label:"permissions_space_unregistered_member"}],K=[{value:"team_manager",label:"permissions_manager"},{value:"team_member",label:"permissions_can_edit"},{value:"team_viewer",label:"permissions_only_view"},{value:"team_ban_viewer",label:"permissions_ban_view"}],me=[{value:"project_manager",label:"permissions_manager"},{value:"project_member",label:"permissions_can_edit"},{value:"project_viewer",label:"permissions_only_view"},{value:"project_ban_viewer",label:"permissions_ban_view"}],Ce={space_owner:"project_manager",space_manager:"project_manager",space_member:"project_member",space_viewer:"project_viewer",team_owner:"project_manager",team_manager:"project_manager",team_member:"project_member",team_viewer:"project_viewer",team_ban_viewer:"project_ban_viewer",team_none:"project_viewer",project_owner:"project_manager"},ke={space_owner:"team_manager",space_manager:"team_manager",space_member:"team_member",space_viewer:"team_viewer",space_ban_viewer:"team_ban_viewer",team_none:"team_viewer",team_owner:"team_manager"},$e=Object.fromEntries([...Pe,...Q,...K,...me].map(e=>[e.value,e.label]));var Me=n(3895),Ie=n(61654),Je=n(15573);const st=e=>{const i=(0,g.d4)(Ie.XW),c=i.get(e);return(0,Je.u)(c,i)},yt=(e,i)=>{const c=(0,g.d4)(Ie.XW),d=(0,g.d4)(Ie._B),_=(0,g.d4)(Ie.kG),b=(0,g.d4)(Ie.iF),m=(0,g.d4)(Ie.yZ);let N=null,j=null;const B=e;let G=[],$=[],P=[];N=d.get(i),$=N.permissions,P=(0,H.Cj)(N.team_cid,c,b),j=je.W.Org,G=_,P=Object.values(Object.fromEntries(P.map(ie=>[ie.user_id,ie])));const ae=Object.values(Object.fromEntries([...P,...$].map(ie=>[ie.user_id,ie]))),oe=(0,s.useMemo)(()=>{const ie=(0,H.Rc)(G);return G.reduce((ge,pe)=>{const{user_id:fe}=pe,W=(0,H.CF)({userId:fe,permissionsMap:ie,permissionScope:j});return ge.set(Number(fe),W)},new Map)},[G]),O=(0,s.useMemo)(()=>{const ie=(0,H.Rc)(ae);return G.reduce((ge,pe)=>{const{user_id:fe}=pe,W=(0,H.CF)({userId:fe,permissionsMap:ie,permissionScope:B});return ge.set(Number(fe),W)},new Map)},[ae,G]),ne=(0,s.useMemo)(()=>{const ie=(0,H.Rc)(P);return P.reduce((ge,pe)=>{const{user_id:fe}=pe,W=(0,H.CF)({userId:fe,permissionsMap:ie,permissionScope:B});return ge.set(Number(fe),W)},new Map)},[P]);return{userCurrentSpacePermission:m,membersTopScopePermissonMap:oe,membersCurrentScopePermissonMap:O,membersInheritedCurrentScopePermissonMap:ne,topScopePermissionMembers:G,currentScopePermissionMembers:$,inheritedPermissionMembers:P,combinedCurrentPermissionMembers:ae}},xt=(e,i,c)=>{const d=(0,g.wA)();return(0,s.useCallback)(async(b,m)=>{const{name:N,email:j,mobile:B,permissionMap:G}=b,{memberCurrentScopePermisson:$,currentScopeRoleName:P}=G;if(!$)return;const ae=N||j||B;let oe=!1;m===tt&&(oe=!0),$.isUnknown?await d({type:"entry:projectAccess:inviteOrgMemberToProject",payload:{projectCid:i,userCid:b.user_cid,roleName:m,userName:ae}}):oe?await d({type:"entry:projectAccess:removeProjectMemberPermission",payload:{projectCid:i,userId:b.user_id,roleName:P,userName:ae}}):await d({type:"entry:projectAccess:updateMemberProjectPermission",payload:{projectCid:i,userId:b.user_id,roleName:m,userName:ae}})},[i,d])},bt=(e,i)=>{const c=(0,g.d4)(Ie.WR),d=(0,g.d4)(Ie.q7),_=(0,g.d4)(Ie.oV),b=(0,g.d4)(Ie.hG),{userCurrentSpacePermission:m,membersTopScopePermissonMap:N,membersCurrentScopePermissonMap:j,membersInheritedCurrentScopePermissonMap:B,topScopePermissionMembers:G,inheritedPermissionMembers:$,currentScopePermissionMembers:P,combinedCurrentPermissionMembers:ae}=yt(e,i),oe=j.get(Number(c))||H.CZ,O=!!b.get(Number(c)),ne=(0,s.useMemo)(()=>{const xe=new Map;return G.forEach(Te=>{let{user_id:Le,role:Ge}=Te;const Fe=N.get(Number(Le))||H.CZ,Ye=_.get(Number(Le))||H.CZ,rt=j.get(Number(Le))||H.CZ,it=B.get(Number(Le))||H.CZ,at=!!b.get(Number(Le)),Mt=d.get(Number(Le));if(!Mt)return;const kt=Mt.permission,Ot=rt.isUnknown?Ge:rt.roleName;xe.set(Number(Le),{...Mt,permissionMap:{currentScopeRoleName:Ot,userCurrentSpacePermission:m,userCurrentScopePermisson:oe,memberTopScopePermisson:Fe,memberCurrentScopePermisson:rt,memberInheritedPermisson:it,memberOrgPermisson:kt,isSelf:Number(Le)===Number(c),userIsSpaceLimiter:O,memberIsSpaceLimiter:at,memberIsSpaceManager:Ye.isManager}})}),xe},[G,N,j,B,b]),ie=(0,s.useMemo)(()=>P.sort((xe,Te)=>Me.Z8[Te.role]-Me.Z8[xe.role]).map(xe=>ne.get(Number(xe.user_id))).filter(xe=>xe),[P,ne]),ge=(0,s.useMemo)(()=>$.sort((xe,Te)=>Me.Z8[Te.role]-Me.Z8[xe.role]).map(xe=>{const{user_id:Te,role:Le}=xe,Ge=B.get(Number(Te))||H.CZ,Fe=ne.get(Number(xe.user_id));return Fe?{...Fe,permissionMap:{...Fe.permissionMap,currentScopeRoleName:Le,memberCurrentScopePermisson:Ge}}:null}).filter(xe=>xe),[$,ne,B]),pe=(0,s.useMemo)(()=>ae.sort((xe,Te)=>Me.Z8[Te.role]-Me.Z8[xe.role]).map(xe=>ne.get(Number(xe.user_id))).filter(xe=>xe),[ae,ne]),fe=(0,s.useMemo)(()=>{const xe=pe.map(Le=>Number(Le.user_id)),Te=[];for(const[Le,Ge]of ne.entries())xe.includes(Number(Le))||Ge&&Te.push(Ge);return Te},[ne,pe]),W=(0,s.useMemo)(()=>{const xe=ge.filter(Le=>{var Ge;return(Ge=Le.permissionMap)==null||(Ge=Ge.memberCurrentScopePermisson)==null?void 0:Ge.isManager});return Object.values(Object.fromEntries([...xe,...ie].map(Le=>[Le.user_id,Le])))},[ge,ie]),ye=(0,s.useMemo)(()=>{const xe=W.map(Le=>Number(Le.user_id)),Te=[];for(const[Le,Ge]of ne.entries())xe.includes(Number(Le))||Ge&&Te.push(Ge);return Te},[ne,W]);return{isOnlyOneManager:(0,s.useMemo)(()=>pe.filter(xe=>{var Te;return(Te=xe.permissionMap)==null||(Te=Te.memberCurrentScopePermisson)==null?void 0:Te.isManager}).length===1,[pe]),userCurrentScopePermisson:oe,userCurrentSpacePermission:m,currentMembers:ie,inheritedMembers:ge,joinedMembers:pe,unjoinedMembers:fe,assignedMembers:W,unassignedMembers:ye,currentOrgMembersMap:d}};var St=n(11777),r=n(57464);const ue=()=>{var e;const i=(0,g.d4)(l.query.getUser),c=(0,g.d4)(l.query.getProject),d=(0,g.d4)(l.query.getInitData),_=st(c==null?void 0:c.team_cid),b=c==null?void 0:c.cid,m=(0,g.wA)(),N=(0,s.useCallback)(O=>{O.stopPropagation(),m({type:l.entryKey["sharing:init"],payload:{mainPage:"access"}}),m({type:l.entryKey["sharing:function:track"],payload:{operation:"\u56E2\u961F\u534F\u4F5C"}})},[m]),{currentMembers:j,inheritedMembers:B,joinedMembers:G}=bt(je.W.Project,b);let $=j;_>=1?B.forEach(O=>{$.find(ie=>ie.id===O.id)||$.push(O)}):$=G;const ae=(e=$)==null?void 0:e.filter(O=>Number(O.user_id)!==Number(i==null?void 0:i.id));ae&&($=ae.slice(0,2));const oe=(0,s.useCallback)(O=>{var ne;const{avatar:ie,name:ge,id:pe}=O;return ie&&!ie.includes("/images/avatar.png")?(0,r.jsx)("img",{className:"avatar",src:O.avatar,alt:O.name},pe):(0,r.jsx)("div",{className:"avatar avater-name",children:ge==null||(ne=ge.slice(0,1))==null?void 0:ne.toUpperCase()},pe)},[]);return!d||JSON.stringify(d)==="{}"?null:(0,r.jsx)(St.A,{content:I18N.imockSharing.team_collaborator,direction:"down",distance:5,enterHoverTime:500,children:(0,r.jsxs)(He,{onClick:N,children:[oe(i),$.length>0&&$.map(O=>oe(O)),(0,r.jsx)("div",{className:"remaining",children:(0,r.jsx)(Ee.C,{name:"sharing/add_member",isColorPure:!0})}),(0,r.jsx)(Ee.C,{name:"sharing/member_arror",isColorPure:!0,className:"arror"})]})})},ve=(0,s.memo)(ue),Ae=A.Ay.div.withConfig({displayName:"styles__StyledSharingSliderBar",componentId:"sc-1garbfp-0"})(["display:flex;height:100%;align-items:center;position:relative;padding-left:24px;.tab-item{color:",";font-size:14px;font-family:PingFang SC;cursor:pointer;height:100%;display:flex;align-items:center;&:not(:first-child){margin-left:14px;}&.tab-sel{color:",";font-weight:500;border-top:2px solid rgba(0,0,0,0);border-bottom:2px solid ",";box-sizing:border-box;}}"],e=>e.theme.color_share_switch_sel_color,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1),Ve=e=>{let{tabs:i,handleChangeTabIndex:c,tabIndex:d,className:_}=e;return(0,r.jsx)(Ae,{className:_,children:i.map((b,m)=>(0,r.jsx)("div",{className:X()("tab-item",d===m&&"tab-sel"),id:"tab-item-"+m,onClick:()=>c(m),children:b},m))})},qe=(0,s.memo)(Ve);var ut=n(15336),jt=n(47755);const Ut=()=>{let e=null;const i=(0,g.wA)(),c=(0,g.d4)(l.query.getProject),d=(0,g.d4)(l.query.getCanEditByUser),_=(0,g.d4)(l.query.getHostType),b=(0,g.d4)(l.query.getIsEditMode),m=(0,g.d4)(l.query.getInitData),N=(0,g.d4)(l.query.getTabIndex),j=(0,g.d4)(l.query.getUser),[B,G]=(0,s.useState)(!1),$=(c==null?void 0:c.is_org_project)||!1,P=_==="iframe"||b,oe=new URLSearchParams(location.search).get("view_mode")||"read_only",O=P&&$&&m&&JSON.stringify(m)!=="{}",ne=d&&!$,ie=(0,s.useCallback)(()=>{const W=_==="iframe",ye=(W?"\u5DE5\u4F5C\u533A":"\u7F16\u8F91\u533A")+"-v8_\u4E2A\u4EBA_\u5206\u4EAB_\u56E2\u961F\u534F\u4F5C\u5347\u7EA7",be=(W?"dashboard":"workspace")+"-v8_solo_share_upgrade",xe={payEntrance:ye,checkoutPlace:be,checkoutArea:W?"dashboard":"proto",mode:"org",isSelectOrg:!0};if(W){if(!j||!j.solo_org)return;const Te="/workspace/"+j.solo_org.cid+"/admin/order?payment_param="+(0,jt._)(xe);(0,ut.JW)(Te,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(xe)},[]),ge=()=>{e=setTimeout(()=>{G(!0)},1e3)},pe=()=>{e&&clearTimeout(e),G(!1)},fe=(0,s.useCallback)(W=>{i({type:l.entryKey["sharing:init"],payload:{tabIndex:W}})},[i]);return(0,r.jsx)(ze,{className:X()(ne&&"tool-upgrade"),children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(qe,{tabIndex:N,tabs:[I18N.imockSharing.default_share,I18N.imockSharing.advanced_sharing],handleChangeTabIndex:fe}),ne&&(0,r.jsxs)("div",{className:"upgrade-team-business",onClick:ie,onMouseEnter:ge,onMouseLeave:pe,children:[(0,r.jsx)("span",{className:"team-business",children:I18N.imockSharing.team_collaboration}),B&&(0,r.jsxs)("div",{className:"content",onClick:W=>W.stopPropagation(),children:[(0,r.jsx)("div",{className:"top-bar"}),(0,r.jsx)("span",{children:I18N.imockSharing.upgrade_to_team_business_plan}),(0,r.jsx)("a",{className:"learn-text",href:I18N.link.link_upgrade_enterprise,target:"_blank",rel:"noopener noreferrer",children:I18N.imockSharing.learn_more})]})]}),O&&(0,r.jsx)(ve,{})]}):(0,r.jsx)("div",{className:"sharing-title",children:oe==="read_only"?I18N.pPreviewToolbar.share:I18N.pPreviewToolbar.share_preview})})},Zt=(0,s.memo)(Ut),$t=A.Ay.div.withConfig({displayName:"styles__StyledSharingAdvancedPage",componentId:"sc-1h5adoi-0"})(["width:100%;height:calc(100% - ","px);padding:16px 20px 5px;overflow-y:auto;.create-item{display:flex;align-items:center;padding:6px 12px;height:32px;color:",";text-align:center;font-size:14px;font-family:PingFang SC;width:fit-content;border-radius:6px;cursor:pointer;svg{width:12px;margin-right:4px;svg path{fill:",";}}&:not(.no-edit):hover{background:",";}&:not(.no-edit):active{background:",";}&.no-edit{opacity:0.3;pointer-events:none;}}"],we.gE,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_btn_secondary_active),Dn=A.Ay.div.withConfig({displayName:"styles__StyledSharingEmpty",componentId:"sc-1h5adoi-1"})(["width:100%;height:calc(100% - ","px);display:flex;justify-content:center;flex-direction:column;align-items:center;svg{width:254px;}.create-sharing{width:134px;margin-top:40px;}"],we.gE);var dn=n(15668),In=n(44057),un=n(48590);const jn=A.Ay.div.withConfig({displayName:"styles__StyledSharingNormalItem",componentId:"sc-11k1y99-0"})(["margin-top:12px;padding:0px 13px;height:134px;display:flex;flex-direction:column;justify-content:center;background:",";border-radius:8px;.share-icon{width:24px;height:24px;border-radius:4px;margin-left:6px;cursor:pointer;&.tempDisabled{pointer-events:none;}svg{width:100%;height:100%;}}"],e=>e.theme.color_bg_canvas),mn=A.Ay.div.withConfig({displayName:"styles__StyledCenterItem",componentId:"sc-11k1y99-1"})(["display:flex;min-height:36px;width:100%;margin-top:12px;margin-bottom:16px;&.default-link{margin-bottom:0px;}&.advance-item{margin-bottom:12px;}&.userNoEdit{.left.normal-hover{cursor:not-allowed;.view-access-select{svg,path{fill:",";}}}}&.notView{cursor:not-allowed;.url-copy-button{background:",";border:"," solid 1px;color:",";pointer-events:none;}}.left{flex-grow:1;background-color:",";display:flex;align-items:center;border:"," solid 1px;border-right:none;border-radius:6px 0 0 6px;&.can-not-edit{pointer-events:none;}&.normal-hover{cursor:pointer;&:hover{background-color:",";}}.chore{margin-right:21px;color:",";}.view-access-select{margin-left:10px;display:flex;align-items:center;.access-name{color:",";}&.is-expired{","}.svg-icon{color:",";transition:all 0.2s;transform :",";}}.single-preview{width:100%;padding:0 6px;color:",";}}.url-copy-button{flex-grow:0;width:97px;}"],e=>e.theme.color_share_access__button_disable,e=>e.theme.color_share_copy__button_disable_bg,e=>e.theme.color_bg_border_02,e=>e.theme.color_share_copy__button_disable,e=>e.isViewActiveDropdown?e.theme.color_btn_secondary_hover:e.theme.color_bg_white,e=>e.theme.color_bg_border_02,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.isViewActiveDropdown?"rotate(-180deg)":"",e=>e.theme.color_text_L1),En=(0,A.Ay)(In.Ay).withConfig({displayName:"styles__StyledMenuContent",componentId:"sc-11k1y99-2"})(["ul{width:270px;li > a{padding-left:10px;svg{position:absolute;right:8px;}}}",";&.advance_menu ul{width:244px;}&.misc ul{width:80px;min-width:auto;}"],un.ZJ),tn=A.Ay.button.withConfig({displayName:"styles__StyledCopyButton",componentId:"sc-11k1y99-3"})(["display:flex;position:relative;width:82px;font-size:12px;font-weight:500;border-top-right-radius:6px;border-bottom-right-radius:6px;background-color:",";&:hover{background-color:",";}&:active{background-color:",";}color:",";&.is-disabled{cursor:not-allowed;pointer-events:none;color:",";background-color:",";}.state{position:absolute;left:0;top:0;display:flex;justify-content:center;align-items:center;width:100%;height:100%;transition:all 0.15s ease-out;}.state-1{opacity:0;.copy-check{color:#ffffff;width:24px;}}&.is-state-1 .state-0{opacity:0;transform:translateY(50%);}&.is-state-1 .state-1{opacity:1;transform:none;}"],e=>e.theme.color_btn_primary_normal,e=>e.theme.color_btn_primary_hover,e=>e.theme.color_btn_primary_clicked,e=>e.theme.color_text_btn,e=>e.theme.color_text_disabled02,e=>e.theme.color_btn_primary_disabled),pn=A.Ay.div.withConfig({displayName:"styles__StyledSharingItemHead",componentId:"sc-1tpfzbt-0"})(["display:flex;align-items:center;justify-content:space-between;color:",";font-size:12px;font-family:PingFang SC;.head-left{display:flex;font-size:14px;font-weight:500;align-items:center;color:",";.public-access{margin-right:8px;}.link-form{width:176px;input{width:176px;}}.link-name{width:176px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;cursor:text;&.link-name-not-edit{pointer-events:none;}&:not(.link-name-not-edit):hover{border-bottom:1px solid ",";}position:relative;height:24px;line-height:24px;}input{height:24px;line-height:24px;color:",";width:240px;border-bottom:1px solid ",";}.edit-name{width:20px;height:20px;margin-left:5px;}}.head-right{display:flex;justify-content:center;align-items:center;border-radius:4px;padding-left:4px;&:hover{background-color:",";}}.foot-right{display:flex;align-items:center;}.view-mode-icon{width:16px;height:16px;margin-right:6px;[stroke]{stroke:currentColor;fill:none;}}.share-icon{margin-left:5px;}&.canNotEdit{label{pointer-events:none;opacity:0.3;cursor:not-allowed;}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_proto,e=>e.theme.color_text_L1,e=>e.theme.color_proto,e=>e.theme.color_btn_secondary_active);function Ys(e){const{fBlur:i,name:c}=e,[d,_]=(0,s.useState)(!1),[b,m]=(0,s.useState)(c),N=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(d){var P;(P=N.current)==null||P.focus()}},[d]);const j=()=>{i(),_(!1)};return{inputRef:N,isRenaming:d,setIsRenaming:_,inputName:b,setInputName:m,handleFocus:()=>{var P;(P=N.current)==null||P.select()},handleBlur:j,handleKeyDown:P=>{P.key==="Enter"&&j()},handleInput:P=>{let ae=P.target.value;ae.length>200&&(ae=ae.substring(0,200)),m(ae)}}}const Rt={embed:"embed",QRcode:"QRcode",setting:"setting",mkt:"mkt_icon",showPage:"showPage"},Qs=A.Ay.div.withConfig({displayName:"styles__StyledIconBox",componentId:"sc-1juwfby-0"})(["display:flex;justify-content:center;align-items:center;width:20px;height:20px;color:",";cursor:pointer;border-radius:4px;position:relative;transition:background-color 0.2s ease-in-out;.Tooltip{display:flex;}&.tempDisabled{opacity:0.3;}&.isBorder{border-radius:50%;border:1px solid ",";}&.disabled{cursor:not-allowed;color:",";}&:not(.disabled):hover{background:",";}&:not(.disabled):active{background:",";}"],e=>e.theme.color_text_L2,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_disabled01,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active);function _n(e){const{className:i,size:c=20,name:d,onClick:_,onMouseEnter:b,onMouseLeave:m,children:N,tempDisabled:j,direction:B,toolTipName:G,type:$,isColorPure:P,isBorder:ae}=e,oe=O=>{j||_(O,$)};return(0,r.jsx)(St.A,{content:G,direction:B||"up",distance:5,enterHoverTime:500,children:(0,r.jsxs)(Qs,{className:X()(i,j&&"tempDisabled",ae&&"isBorder"),onClick:oe,onMouseEnter:b,onMouseLeave:m,children:[(0,r.jsx)(Ee.C,{size:c,name:d,isColorPure:P}),N]})})}const bs=e=>{let{sharingData:i,handleItemAction:c}=e;const{link_name:d}=i,_=(0,g.wA)(),b=i.device_model,[m,N]=(0,s.useState)(d),j=(0,g.d4)(l.query.getCanEditByUser),B=(0,g.d4)(l.query.getIsEditMode),G=(0,g.d4)(l.query.getHostType),$=(0,g.d4)(l.query.getScreenMetaList),P=(0,g.d4)(l.query.getAdvancedSharingList),ae=G==="iframe"||B,oe=ae&&j,O=ae&&j,ne=()=>{const Ye=be.current.value;if(Ye==="")Te(m),_({type:l.entryKey["sharing:init"],payload:{sharingToast:I18N.Common.name_cannot_be_empty}});else if((0,V.it)({value:Ye,currentSharing:i,sharingList:P}))Te(m),_({type:l.entryKey["sharing:init"],payload:{sharingToast:I18N.imockSharing.sharing_name_repeate_wran}});else{N(Ye);const rt={link_name:Ye};_({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:i,updatedKV:rt}})}},ie=(0,s.useMemo)(()=>i.type==="advanced"?(0,V.Sy)($,i):null,[i,$]),{isRenaming:ge,setIsRenaming:pe,handleKeyDown:fe,handleBlur:W,handleFocus:ye,inputRef:be,handleInput:xe,setInputName:Te,inputName:Le}=Ys({name:m||"",fBlur:ne}),Ge=(0,s.useCallback)(()=>{pe(!0)},[pe]),Fe=(0,s.useMemo)(()=>[{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Rt.embed,tempDisabled:!1},ENV.IS_ON_PREMISES?null:{name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Rt.QRcode,tempDisabled:!1},{name:"sharing/more",toolTipName:I18N.LeftSidePanel.menu,className:Rt.setting,tempDisabled:!O}].filter(Boolean),[O]);return(0,r.jsxs)(pn,{className:X()(!oe&&"canNotEdit"),children:[(0,r.jsxs)("div",{className:"head-left",children:[(0,r.jsx)(Ee.C,{className:"view-mode-icon",name:b==="device"?"sharing/mode_device":"sharing/mode_canvas"}),ge?(0,r.jsx)("form",{className:"link-form",children:(0,r.jsx)("input",{value:Le,onFocus:ye,onChange:xe,ref:be,onBlur:W,onKeyPress:fe})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:X()("link-name",!O&&"link-name-not-edit"),onClick:Ge,children:m})})]}),(0,r.jsx)(St.A,{content:I18N.imockSharing.tab_screen,direction:"up",distance:5,enterHoverTime:500,children:(0,r.jsxs)("div",{className:"head-right",children:[(0,r.jsx)("div",{children:ie}),(0,r.jsx)(_n,{className:"share-icon",name:"sharing/page_visible",tempDisabled:!O,size:20,onClick:Ye=>c(Ye,Rt.showPage)})]})}),(0,r.jsx)("div",{className:"foot-right",children:Fe.map((Ye,rt)=>Ye&&(0,r.jsx)(_n,{className:X()("share-icon",Ye.className),name:Ye.name,type:Ye.className,toolTipName:Ye.toolTipName,onClick:c,tempDisabled:Ye.tempDisabled,isColorPure:Ye.isColorPure,size:20},rt))})]})},Js=(0,s.memo)(bs),Xs=A.Ay.div.withConfig({displayName:"styles__StyledSharingItemFoot",componentId:"sc-1b50v3w-0"})(["display:flex;align-items:center;justify-content:space-between;color:",";font-size:12px;font-family:PingFang SC;.visit_count{color:",";.svg-icon{path{fill:rgba(22,132,252,1);}path:last-of-type{fill:#fff;}}}.expiration-time{display:flex;align-items:center;color:",";.expire-text{margin:0 4px;cursor:pointer;.expired-desc{color:",";}.expired-no{color:",";}}.expire-icon{width:24px;height:24px;}&.is-expired{color:",";}svg{color:",";cursor:pointer;}.self-transform{svg{transition:all 0.4s;transform:rotate(360deg);}}}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1),Rn=(0,A.DU)([".visit-tips{padding:6px;.tip-updated{display:flex;cursor:pointer;align-items:center;font-size:12px;p{margin:0;display:flex;align-items:center;}.icon-updated{display:inline-flex;align-items:center;margin-left:10px;line-height:14px;color:rgba(112,188,246,1);&:hover{color:rgba(149,209,248,1);}.svg-icon{margin-right:2px;path{fill:rgba(22,132,252,1);}path:last-of-type{fill:#fff;}}}}}"]);var Bn=n(76713),es=n.n(Bn);function kn(e,i,c){const d=(0,s.useMemo)(()=>{const m=es()(),N=es()(e.expired_at).subtract(2,"s");return N.isBefore(m)?0:N.diff(m,"days")+1},[e]),_=d<=0;let b;return i?b="\u5206\u4EAB\u8D85\u9650\uFF0C\u5347\u7EA7\u89E3\u9501":d>0&&d<=9999?b=d+" \u5929\u540E\u5931\u6548":d>9999?b=c===1?"\u6C38\u4E45\uFF08\u9650\u65F6\u514D\u8D39\uFF09":"\u6C38\u4E45\u6709\u6548":b="\u91CD\u65B0\u751F\u6548",{isExpired:_,remainTimeText:b}}var an=n(89604),Tn=n(59305);const qs=e=>{let{sharingData:i,handleUpdateSharing:c}=e;const d=(0,g.wA)(),[_,b]=(0,s.useState)(null),[m,N]=(0,s.useState)(!1),{remainTimeText:j,isExpired:B}=kn(i,!1,999),G=i.device_model,$=i.expire_type==="forever",P=(0,g.d4)(l.query.getUser),ae=(0,g.d4)(l.query.getOrg),oe=(0,g.d4)(l.query.getProject),O=(0,g.d4)(Ie.cb),ne=oe==null?void 0:oe.is_org_project;let ie=!1,ge=!1;if(ne){const{plan:be,trial:xe}=(0,Tn.rM)(ae);ge=xe,ie=be===an.Sj.OrgFull&&!ge}const pe=ge||ie,fe=O.roleName&&["org_owner","org_admin","org_manager"].includes(O.roleName),W=pe?fe?"enterpriseAdmin":"enterpriseUser":"nonEnterprise",ye=(0,s.useCallback)(()=>{c({expire_type:i.expire_type}),N(!0),setTimeout(()=>N(!1),500),d({type:l.entryKey["sharing:function:track"],payload:{operation:"\u91CD\u7F6E\u6709\u6548\u671F",viewMode:G,isDefault:!1}})},[d,c,i.expire_type,G]);return(0,s.useEffect)(()=>{(async()=>{if(i.access_token)try{const xe=await(0,J.dJ)(i.access_token);b(xe.count)}catch(xe){console.error("Error fetching visit count:",xe)}})()},[i.access_token]),(0,r.jsxs)(Xs,{children:[(0,r.jsxs)("div",{className:"visit_count",children:[I18N.imockSharing.visit_count," ",_]}),(0,r.jsxs)("div",{className:X()("expiration-time",{"is-expired":B}),children:[(0,r.jsx)("div",{className:"expire-text",children:(0,r.jsx)("span",{className:B?"expired-desc":"expired-no",children:j})}),!$&&(0,r.jsx)(_n,{direction:"down",toolTipName:I18N.imockSharing.resetLink,onClick:ye,className:X()("expire-icon",{"self-transform":m}),size:12,name:"sharing/refresh_24"})]}),(0,r.jsx)(Rn,{})]})},eo=(0,s.memo)(qs);var pt=n(36521);const ts=e=>{const{project:i,sharing:c,user:d}=e,[_,b]=(0,s.useState)(!1),[m,N]=(0,s.useState)(void 0);return{isURLCopied:_,handleCopyURL:()=>{(0,V.kv)({project:i,action:c.password!==""?"\u590D\u5236\u94FE\u63A5\u548C\u5BC6\u7801":"\u590D\u5236\u94FE\u63A5",linkName:c.link_name}),b(!0),clearTimeout(m);const B=setTimeout(()=>b(!1),3e3);N(B);const G=es()().format("YYYY-MM-DD HH:mm:ss"),$=(0,V.EL)(d)&&!(0,pt.kV)(we.nM);(0,pt.cH)(we.nM,G),$&&(0,Y.Sx)({prototype_activation_time:G})}}};var wn=n(93869);function Ln(e){const{password:i}=e;return{isHavePassword:(0,s.useMemo)(()=>i!==null&&i!=="",[i])}}var ns=n(36114);const nn=e=>{var i;let{sharingData:c,setShowDelConfirmModal:d,fromDefault:_}=e;const b=(0,g.wA)(),m=(0,s.useRef)(null),N=c.view_access,j=c.device_model,B=(0,g.d4)(l.query.getProject),G=(0,g.d4)(l.query.getCanEditByUser),$=(0,g.d4)(l.query.getIsEditMode),P=(0,g.d4)(l.query.getHostType),ae=(0,g.d4)(l.query.getUser),oe=(0,g.d4)(l.query.getHostCurrentScreen),O=oe==null?void 0:oe.cid,ne=(B==null?void 0:B.is_org_project)||!1,ie=P==="iframe"||$,ge=ie&&G,[pe,fe]=(0,s.useState)({top:0,left:0}),[W,ye]=(0,s.useState)(!1),{isURLCopied:be,handleCopyURL:xe}=ts({project:B,sharing:c,user:ae}),{isHavePassword:Te}=Ln(c),Le=(0,s.useCallback)(()=>{const gt=(0,V.O8)(B,c,O);(0,wn.$)(gt),xe(),b({type:l.entryKey["sharing:function:track"],payload:{operation:"\u9AD8\u7EA7\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:j,isDefault:!1}}),ie&&ns.U.protoEditorShareTrack(ae==null?void 0:ae.id,P==="iframe"?"\u5DE5\u4F5C\u53F0\u53F3\u952E\u9AD8\u7EA7\u5206\u4EAB":"\u9AD8\u7EA7\u5206\u4EAB")},[b,c,O,xe,B,j]),Ge=(0,s.useCallback)(gt=>{const Tt={view_access:gt};b({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:c,updatedKV:Tt}}),b({type:l.entryKey["sharing:function:track"],payload:{operation:gt==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:j,isDefault:!1}})},[c,b,j]),Fe=(0,s.useCallback)(()=>{d({isOpened:!0,item:c})},[d,c]),Ye=(0,s.useCallback)(()=>{b({type:l.entryKey["sharing:go-settingPage"],payload:{currentSharing:c,settingPageType:"edit"}}),b({type:l.entryKey["sharing:function:track"],payload:{operation:"\u8BBE\u7F6E",viewMode:j,isDefault:!1}})},[b,c,j]),rt=(0,s.useCallback)(gt=>{b({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:c,updatedKV:gt}})},[b,c]),[it,at]=(0,s.useState)(void 0),Mt=(0,s.useMemo)(()=>{if(it==="viewAccess"){const gt=(0,r.jsx)(Ee.C,{name:"toolbar/selected",className:"selected-icon"});if(ne){const Tt=N==="public";return[{label:I18N.imockSharing.share_anyone_view,onclick:()=>Ge("public"),icon:Tt?gt:(0,r.jsx)("div",{style:{width:0}})},{label:I18N.imockSharing.org_members_only,onclick:()=>Ge("restricted"),icon:Tt?(0,r.jsx)("div",{style:{width:0}}):gt}]}else return[]}else return[{label:I18N.imockSharing.setting,onclick:Ye},{label:I18N.imockSharing.delete,onclick:Fe}]},[it,Ge,Fe,Ye,N,ne]),kt=(0,s.useCallback)(gt=>{if(!ne||!ge)return;at("viewAccess");const Tt=gt.currentTarget.getBoundingClientRect();fe({top:Tt.y+Tt.height+3,left:Tt.x-1}),ye(!W)},[ne,ge,W,at,fe]),Ot=(0,s.useCallback)((gt,Tt)=>{if(gt!=null&&gt.currentTarget)switch(Tt){case Rt.QRcode:gt.stopPropagation(),b({type:l.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:c}}),b({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:j,isDefault:!1}});break;case Rt.embed:b({type:l.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:c}}),b({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:j,isDefault:!1}});break;case Rt.mkt:(0,ut.JW)("/com24/upload?project_cid="+(B==null?void 0:B.cid),"_blank","noreferrer");break;case Rt.setting:{at("misc");const Pt=gt.currentTarget.getBoundingClientRect(),on=document.getElementById("v8-share-page").getBoundingClientRect().bottom-Pt.bottom<120?Pt.y-75:Pt.bottom+5;fe({top:on,left:P==="iframe"?Pt.x+20:Pt.x-60}),ye(!W)}break;case Rt.showPage:b({type:l.entryKey["sharing:go-settingPage"],payload:{currentSharing:c,settingPageType:"edit",subSettingPageType:"visible"}});break;default:break}},[W,at,fe,ye,b,B,c,j,P]),Dt=(0,s.useCallback)(()=>{var gt;fe({...pe,left:((gt=m.current)==null?void 0:gt.getBoundingClientRect().x)-1})},[pe]);(0,s.useEffect)(()=>(window.addEventListener("resize",Dt),()=>window.removeEventListener("resize",Dt)));const{isExpired:Gt}=kn(c,!1,999);return(0,r.jsxs)(jn,{className:X()(_&&"from-default"),children:[(0,r.jsx)(Js,{sharingData:c,handleItemAction:Ot}),(0,r.jsxs)(mn,{className:X()("item-center",!ge&&"userNoEdit","advance-item"),isViewActiveDropdown:W&&it==="viewAccess",children:[(0,r.jsx)("div",{ref:m,className:X()("left","sharingV2-click-visible",{"normal-hover":!0},!ne&&"can-not-edit"),onClick:kt,children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(i=I18N.imockSharing[we.nQ[N]])!=null?i:"missing data"}),ne&&(0,r.jsx)(Ee.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:X()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(be),{"is-disabled":Gt}),onClick:Le,children:[(0,r.jsx)("span",{className:"state state-0",children:Gt?I18N.dModule.link_expired_err_title:Te?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Ee.C,{name:"new_replace/check",className:"copy-check"})})]})]}),(0,r.jsx)(eo,{sharingData:c,handleUpdateSharing:rt}),W&&(0,r.jsx)(En,{className:X()("design-avoid",it,"advance_menu"),position:pe,onClose:()=>{ye(!1)},children:Mt.map((gt,Tt)=>(0,r.jsx)(In.Dr,{text:gt.label,onClick:gt.onclick,icon:gt.icon,disabled:gt.disabled,canClick:gt.tempDisabled},Tt))})]})},to=(0,s.memo)(nn),vs=A.Ay.div.withConfig({displayName:"styles__StyledDeletingConfirmModal",componentId:"sc-1ws30dd-0"})(["display:flex;position:absolute;top:0;bottom:0;left:0;width:100%;height:100%;justify-content:center;align-items:center;background:rgba(0,0,0,0.4);.confirm-content{display:flex;height:fit-content;border-radius:10px;background:",";width:calc(100% - 54px);color:",";font-family:PingFang SC;font-style:normal;flex-direction:column;padding-bottom:18px;border:1px solid ",";box-shadow:0px 9px 28px 0px rgba(0,0,0,0.09),0px 6px 16px 0px rgba(0,0,0,0.02),0px 3px 6px 0px rgba(0,0,0,0.02);.top{width:100%;height:","px;display:flex;align-items:center;justify-content:center;justify-content:space-between;padding:0 24px;font-size:16px;font-weight:500;line-height:normal;border-bottom:1px solid ",";.close{width:24px;height:24px;svg{width:24px;height:24px;}}}.center{font-size:14px;line-height:22px;width:100%;padding:20px 24px;}.bottom{width:100%;padding-right:24px;display:flex;align-items:center;justify-content:flex-end;.delete{margin-left:12px;}}}"],e=>e.theme.color_bg_white,e=>e.theme.color_text_L1,e=>e.theme.color_share_del_confirm_border_color,we.gE,e=>e.theme.color_bg_border_02);function Sn(e){let{handleConfirmAction:i}=e;return(0,r.jsx)(vs,{children:(0,r.jsxs)("div",{className:"confirm-content",children:[(0,r.jsxs)("div",{className:"top",children:[(0,r.jsx)("div",{children:I18N.imockSharing.delete_share}),(0,r.jsx)(_n,{name:"sharing/close",onClick:()=>i(!1),className:"close"})]}),(0,r.jsx)("div",{className:"center",children:I18N.imockSharing.deletion_shared_links_warning}),(0,r.jsxs)("div",{className:"bottom",children:[(0,r.jsx)(dn.tA,{onClick:()=>i(!1),children:I18N.imockSharing.cancel}),(0,r.jsx)(dn.Qi,{className:"delete",onClick:()=>i(!0),children:I18N.imockSharing.delete})]})]})})}const Fn=s.memo(Sn),An=()=>{const e=(0,g.d4)(l.query.getUser),i=(0,g.d4)(l.query.getOrg),{cid:c,name:d,is_org_project:_}=(0,g.d4)(l.query.getProject),b=(0,g.d4)(l.query.getHostType),m=(0,g.d4)(l.query.getAdvancedSharingList),N=(0,g.d4)(l.query.getCanEditByUser),j=(0,g.d4)(l.query.getProjShareToEdit),B=(0,g.d4)(l.query.getTheme),[G,$]=(0,s.useState)({isOpened:!1,item:null}),[P,ae]=(0,s.useState)(!1),oe=(0,g.wA)(),O=m.length===0,ne=(0,s.useCallback)(async ge=>{ge&&oe({type:l.entryKey["sharing:remote:sharing:delete"],payload:{sharingCid:G.item.cid}}),$({isOpened:!1,item:null})},[G.item,$,oe]);(0,s.useEffect)(()=>{(async()=>{const fe=(await(0,J.X0)({projectCid:c})).map(W=>({...W,type:"advanced"})).sort((W,ye)=>new Date(ye.created_at).getTime()-new Date(W.created_at).getTime());if(j){const W=fe.find(ye=>ye.access_token===j);W&&(oe({type:l.entryKey["sharing:go-settingPage"],payload:{currentSharing:W,settingPageType:"edit"}}),oe({type:l.entryKey["sharing:init"],payload:{projShareToEdit:""}}))}oe({type:l.entryKey["sharing:advancedSharingList:update"],payload:{sharingList:fe}}),ae(!0)})()},[c,oe]);const ie=async()=>{const ge=(0,V.jc)(e,i),pe=()=>{(0,Y.kH)("create_advanced_sharing_click",{project_name:d,project_cid:c,source:b==="iframe"?"\u5DE5\u4F5C\u53F0":"\u7F16\u8F91\u533A"})};if(m.length>=ge&&!ENV.IS_ON_PREMISES){const W=(i==null?void 0:i.plan)==="org_full"?"MaxOrgSize":"NormalOrgSize";window.SharingEventEmitter.emit("sharing:count",{whichModal:W}),window.top.postMessage(JSON.stringify({sharingMessage:"sharing:count",payload:{whichModal:W}}),"*"),pe();return}const fe=(0,V.zM)(c,m,{view_access:_?"restricted":"public"});oe({type:l.entryKey["sharing:go-settingPage"],payload:{currentSharing:fe,settingPageType:"create"}}),pe()};return P?O?(0,r.jsxs)(Dn,{children:[(0,r.jsx)(Ee.C,{name:B==="dark"?"sharing/empty_dark":"sharing/empty",isColorPure:!0}),(0,r.jsx)(dn.jn,{onClick:ie,className:"create-sharing",children:I18N.imockSharing.new_share})]}):(0,r.jsxs)($t,{children:[(0,r.jsxs)("div",{className:"create-item "+(N?"":"no-edit"),onClick:ie,children:[(0,r.jsx)(Ee.C,{name:"sharing/add_new"}),I18N.imockSharing.new_share]}),m.map((ge,pe)=>(0,r.jsx)(to,{setShowDelConfirmModal:$,sharingData:ge},ge.cid+"-"+pe)),G.isOpened&&(0,r.jsx)(Fn,{handleConfirmAction:ne})]}):null},ss=(0,s.memo)(An),os=A.Ay.div.withConfig({displayName:"styles__StyledSharingDefaultMainPage",componentId:"sc-zm7bo-0"})(["width:100%;height:calc(100% - ","px);.default-top{height:calc(100% - ","px);display:flex;flex-direction:column;justify-content:center;padding:0 24px;.switch-content{height:24px;display:flex;align-items:center;color:",";font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:500;line-height:normal;justify-content:space-between;span{margin-right:10px;}.visit-count{font-weight:400;font-size:12px;color:",";}}.wm-tip{height:25px;margin-top:12px;display:flex;span{height:25px;font-size:12px;font-weight:400;line-height:25px;color:",";}.divider{width:1px;height:13px;margin:6px 10px;background:",";}.btn{display:inline-block;padding:4px 6px;width:60px;height:25px;line-height:17px;font-size:12px;font-weight:500;border-radius:4px;background:",";color:",";&:hover{background:",";}}}}.default-bottom{height:","px;display:flex;align-items:center;justify-content:space-between;padding:0 24px;color:",";background:",";.bottom-left{display:flex;align-items:center;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:500;line-height:normal;}.share-icon{width:28px;height:28px;margin-left:14px;&:hover,&:active{background:",";}}svg{width:24px;height:24px;}.default-setting{display:flex;align-items:center;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:400;line-height:14px;padding:6px 12px 6px 6px;height:28px;border-radius:6px;cursor:pointer;svg{margin-right:2px;}&:not(.no-edit):hover,&:not(.no-edit):active{background:",";}&.no-edit{opacity:0.3;pointer-events:none;}}}&.canNotEdit{label{pointer-events:none;opacity:0.3;cursor:not-allowed;}}"],we.gE,we.gE,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_info_bg,e=>e.theme.color_text_link_normal,e=>e.theme.color_grid,we.gE,e=>e.theme.color_text_L1,e=>e.theme.color_bg_canvas,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active);var Ct=n(86778),sn=n(53955),_s=n(42781),rs=n(63508),is=n(64598),zn=n(15915);const Un=e=>{var i;let{defaultShare:c}=e;const d=(0,g.wA)(),_=(0,s.useRef)(null),b=(0,g.d4)(l.query.getProject),m=c,[N,j]=(0,s.useState)(null);b&&(m.visibility=b.visibility);const B=(0,g.d4)(l.query.getHostType),G=(0,g.d4)(l.query.getIsEditMode),$=(0,g.d4)(l.query.getUser),P=B==="iframe"||G,ae=(0,g.d4)(l.query.getCanEditByUser),oe=(0,g.d4)(l.query.getHostCurrentScreen),O=oe==null?void 0:oe.cid,ne=(0,g.d4)(l.query.getUser),ie=(0,g.d4)(l.query.getOrg),ge=(0,g.d4)(Ie.cb),pe=(b==null?void 0:b.is_org_project)||!1,fe=!pe&&ae,[W,ye]=(0,s.useState)({top:0,left:0}),[be,xe]=(0,s.useState)(!1),[Te,Le]=(0,s.useState)(m==null?void 0:m.visibility);let Ge=!1,Fe=!1;if(pe){const{plan:nt,trial:It}=(0,Tn.rM)(ie);Fe=It,Ge=nt===an.Sj.OrgFull&&!Fe}const Ye=Fe||Ge,rt=ge.roleName&&["org_owner","org_admin","org_manager"].includes(ge.roleName),it=Ye?rt?"enterpriseAdmin":"enterpriseUser":"nonEnterprise",at=P&&ae&&Te==="open",Mt=P&&ae,{isURLCopied:kt,handleCopyURL:Ot}=ts({project:b,sharing:m,user:$}),[Dt,Gt]=(0,s.useState)(!1),[gt,Tt]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const nt=(0,sn.fV)();if(nt){const{mdWMMktList:It,mtWMMktList:Jt,noWMMktList:$s}=nt;(It.size||Jt.size)&&(0,zn._5)()&&Tt(!1);const qo=[...It,...Jt,...$s];qo.length&&(0,_s.q)(qo).then(er=>{if(er.mdWMMktList.length>0||er.mtWMMktList.length>0){if(!(0,zn._5)())return;Gt(!0),ns.U.watermarkExposureTrack("share")}})}},[]);const Pt=()=>{const{isSoloMdMember:nt}=(0,rs.s)(ne,MB.currentOrg),It=!!MB.currentOrg,Jt={mode:"mat",payEntrance:"\u7F16\u8F91\u533A-v8-"+(It?"\u56E2\u961F":"\u4E2A\u4EBA")+"-\u4ED8\u8D39\u7D20\u6750\u8BD5\u7528-\u5206\u4EAB\u884C\u4E3A\u63D0\u793A-"+(nt?"\u5347\u7EA7\u7D20\u6750\u4F1A\u5458":"\u5347\u7EA7\u53CC\u4F1A\u5458"),checkoutPlace:"workspace_v8_"+(It?"org":"solo")+"_watermark_share_"+(nt?"template":"vip-template"),checkoutArea:"proto"};MB.global.popupHelper.chargeAsync({...Jt}),(0,is._)({click_button:"\u5206\u4EAB-"+(nt?"\u5347\u7EA7\u7D20\u6750\u4F1A\u5458":"\u5347\u7EA7\u53CC\u4F1A\u5458")})},Qt=(0,s.useCallback)(nt=>{const It={access:nt};d({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:m,updatedKV:It}}),d({type:l.entryKey["sharing:function:track"],payload:{operation:nt==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:m.view_mode,isDefault:!0}})},[m,d]),ft=(0,s.useMemo)(()=>{const nt=(0,r.jsx)(Ee.C,{name:"toolbar/selected",className:"selected-icon"});if(pe){const It=(m==null?void 0:m.view_access)==="public";return[{label:I18N.imockSharing.share_anyone_view,onclick:()=>Qt("public"),icon:It?nt:(0,r.jsx)("div",{style:{width:0}})},{label:I18N.imockSharing.org_members_only,onclick:()=>Qt("restricted"),icon:It?(0,r.jsx)("div",{style:{width:0}}):nt}]}else return[]},[Qt,pe,m]),vt=(0,s.useMemo)(()=>{const nt=[{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Rt.embed,tempDisabled:Te==="close",isBorder:!0}];return ENV.IS_ON_PREMISES||nt.unshift({name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Rt.QRcode,tempDisabled:Te==="close",isBorder:!0}),fe&&nt.push({name:"sharing/mkt_icon",toolTipName:gt?I18N.imockSharing.publish_to_community:"\u542B\u4ED8\u8D39\u7D20\u6750\uFF0C\u4E0D\u652F\u6301\u4E0A\u4F20",className:Rt.mkt,tempDisabled:Te==="close"||!gt,isColorPure:!0,isBorder:!0}),nt},[Te,fe,gt]),on=(0,s.useCallback)(()=>{const nt=Te==="close"?"open":"close",It={visibility:nt};Le(nt),d({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:m,updatedKV:It}}),d({type:l.entryKey["sharing:function:track"],payload:{operation:nt==="open"?"\u6253\u5F00\u8BBF\u95EE":"\u5173\u95ED\u8BBF\u95EE",viewMode:m.view_mode,isDefault:!0}})},[Te,d,m,Le]),Xn=(0,s.useCallback)(nt=>{if(!pe||!at)return;const It=nt.currentTarget.getBoundingClientRect();let Jt=It.y+It.height+3;B==="iframe"&&(Jt=Jt+56),ye({top:Jt,left:It.x-1}),xe(!be)},[pe,at,be,ye,B]),qn=(0,s.useCallback)(()=>{const nt=(0,V.O8)(b,m,O);(0,wn.$)(nt),Ot(),d({type:l.entryKey["sharing:function:track"],payload:{operation:"\u9ED8\u8BA4\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:m.view_mode,isDefault:!0}}),P&&ns.U.protoEditorShareTrack(ne==null?void 0:ne.id,B==="iframe"?"\u5DE5\u4F5C\u53F0\u53F3\u952E\u666E\u901A\u5206\u4EAB":"\u666E\u901A\u5206\u4EAB")},[b,m,O,Ot,d]),vn=(0,s.useCallback)(()=>{const nt=m,{view_mode:It}=m;d({type:l.entryKey["sharing:go-settingPage"],payload:{currentSharing:nt,settingPageType:"edit"}}),d({type:l.entryKey["sharing:function:track"],payload:{operation:"\u8BBE\u7F6E",viewMode:It,isDefault:!0}})},[m,d]),rn=(0,s.useCallback)((nt,It)=>{if(!(nt!=null&&nt.currentTarget))return;const Jt=m,{view_mode:$s}=m;switch(It){case Rt.QRcode:nt.stopPropagation(),d({type:l.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:Jt}}),d({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:$s,isDefault:!1}});break;case Rt.embed:d({type:l.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:Jt}}),d({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:$s,isDefault:!1}});break;case Rt.mkt:(0,ut.JW)("/com24/upload?project_cid="+(b==null?void 0:b.cid),"_blank","noreferrer");break;default:break}},[d,b,m]),Kt=(0,s.useCallback)(()=>{var nt;ye({...W,left:((nt=_.current)==null?void 0:nt.getBoundingClientRect().x)-1})},[W]);return(0,s.useEffect)(()=>(window.addEventListener("resize",Kt),()=>window.removeEventListener("resize",Kt))),(0,s.useEffect)(()=>{(async()=>{const It=m.access_token;if(It)try{const Jt=await(0,J.dJ)(It);j(Jt.count)}catch(Jt){console.error("Error fetching visit count:",Jt)}})()},[m.access_token]),m?(0,r.jsxs)(os,{className:X()(!Mt&&"canNotEdit"),children:[(0,r.jsxs)("div",{className:"default-top",children:[P?(0,r.jsxs)("div",{className:"switch-content",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:I18N.imockSharing.public_access}),(0,r.jsx)(Ct.A,{size:"small",readOnly:!0,isIOS:!0,isChecked:Te==="open",onChange:on})]}),(0,r.jsx)("div",{className:"visit-count",children:N!==null&&N!==0&&(0,r.jsxs)(r.Fragment,{children:[I18N.imockSharing.visit_count," ",N]})})]}):(0,r.jsxs)("div",{className:"switch-content",children:[I18N.imockSharing.default_share,N!==null&&N!==0&&(0,r.jsxs)("div",{className:"visit-count",children:[I18N.imockSharing.visit_count," ",N]})]}),(0,r.jsxs)(mn,{className:X()("item-center","default-link",Te==="close"&&"notView",!at&&"userNoEdit"),isViewActiveDropdown:be,children:[(0,r.jsx)("div",{ref:_,className:X()("left","sharingV2-click-visible",{"normal-hover":!0},!pe&&"can-not-edit"),onClick:Xn,children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(i=I18N.imockSharing[we.nQ[m.view_access]])!=null?i:"missing data"}),pe&&(0,r.jsx)(Ee.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:X()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(kt)),onClick:qn,children:[(0,r.jsx)("span",{className:"state state-0",children:m.password&&m.password.length>0?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Ee.C,{name:"new_replace/check",className:"copy-check"})})]})]}),Dt&&(0,r.jsxs)("div",{className:"wm-tip",children:[(0,r.jsx)("span",{children:I18N.WaterMark.mkt.share.tip}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsx)("div",{className:"btn",onClick:Pt,children:I18N.WaterMark.mkt.remove_wm})]})]}),(0,r.jsxs)("div",{className:"default-bottom",children:[(0,r.jsxs)("div",{className:"bottom-left",children:[(0,r.jsx)("div",{children:I18N.imockSharing.share_mode}),vt.map((nt,It)=>(0,r.jsx)(_n,{className:X()("share-icon",nt.className),name:nt.name,type:nt.className,toolTipName:nt.toolTipName,onClick:rn,tempDisabled:nt.tempDisabled,isColorPure:nt.isColorPure,isBorder:nt.isBorder,size:24},It))]}),P&&(0,r.jsxs)("div",{className:X()("default-setting",!at&&"no-edit"),onClick:vn,children:[(0,r.jsx)(Ee.C,{name:"sharing/default_setting"}),I18N.imockSharing.setting]})]}),be&&(0,r.jsx)(En,{className:X()("design-avoid","viewAccess"),position:W,onClose:()=>{xe(!1)},children:ft.map((nt,It)=>(0,r.jsx)(In.Dr,{text:nt.label,onClick:nt.onclick,icon:nt.icon,disabled:nt.disabled,canClick:nt.tempDisabled},It))}),(0,r.jsx)(Rn,{})]}):null},no=(0,s.memo)(Un),so=A.Ay.div.withConfig({displayName:"styles__StyledSharingMainPage",componentId:"sc-7p2gxy-0"})(["width:100%;position:relative;height:100%;"]),oo=A.Ay.div.withConfig({displayName:"styles__StyledSharingEmbedded",componentId:"sc-xvloju-0"})(["width:100%;padding:20px 32px 0 32px;.desc{line-height:17px;font-size:12px;font-family:PingFang SC;color:",";margin-bottom:20px;}.copy-wrapper{display:flex;justify-content:space-between;height:112px;.demo-img{width:192px;height:100%;object-fit:cover;flex-shrink:0;}.copy-box{display:flex;flex-direction:column;width:148px;height:100%;border:1px solid ",";border-radius:2px;overflow:hidden;.sharing-embedded-url{flex-grow:1;height:0;padding:6px 8px;white-space:normal;word-wrap:break-word;color:",";font-size:12px;font-weight:500;}.copy-button{width:100%;height:28px;font-size:12px;background:",";border-top:1px solid ",";color:",";&:hover{background:",";}}}}.open-setting{justify-content:flex-start;.opener-wrapper{display:flex;align-items:center;span{color:#1684fc;margin-right:3px;cursor:pointer;}.svg-icon{width:6px;height:4px;cursor:pointer;transition:transform 0.2s ease-in-out;color:",";}&.is-open{.svg-icon{transform:rotate(-180deg);}}}}.open-setting.line-item.embedded-padding{padding:0;margin-top:14px;}.hidden{height:0;overflow:hidden;transition:height 0.2s;}.hidden.is-open{height:156px;}"],e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L2,e=>e.theme.color_bg_card,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L3);var fn=n(95230);const Yt=A.Ay.div.withConfig({displayName:"styles__StyledSharingTitleBar",componentId:"sc-174zu8p-0"})(["width:100%;height:","px;display:flex;align-items:center;border-bottom:1px solid ",";color:",";font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:500;line-height:24px;.title{cursor:pointer;display:flex;align-items:center;svg{width:32px;height:32px;margin-left:10px;}}"],we.gE,e=>e.theme.color_border_state,e=>e.theme.color_text_L1),Cn=e=>{let{title:i,handleClick:c}=e;return(0,r.jsx)(Yt,{children:(0,r.jsxs)("div",{className:"title",onClick:c,children:[(0,r.jsx)(Ee.C,{name:"sharing/nav_back"}),i]})})},Bt=()=>{let e="read_only";const i=(0,g.wA)(),c=(0,g.d4)(l.query.getCurrentSelectSharing),d=(0,g.d4)(l.query.getProject),_=(0,g.d4)(l.query.getTheme),b=(0,g.d4)(l.query.getHostCurrentScreen),m=(0,g.d4)(l.query.getHostType),N=b==null?void 0:b.cid,{screen_visible_list:j,access_token:B,is_first_canvas_open:G}=c||{};if(c.type==="default"){const{view_mode:pe}=c;e=pe}else{const{device_model:pe}=c;e=pe}let $="";const P=(0,g.d4)(l.query.getIsEditMode);c.type==="default"?$=P||e==="read_only"?N:new URLSearchParams(location.search).get("screen")||"":$=N;let ae="";const oe=(0,V.a2)(B);m==="iframe"?ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+'" allowTransparency="true" frameborder="0"></iframe>':j&&j.length!==0?ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+"&selection="+j[0]+(G?"":"&screen="+$)+'" allowTransparency="true" frameborder="0"></iframe>':ae='<iframe src="'+oe+"/embed/v2/sharing?view_mode="+e+(G?"":"&screen="+$)+'" allowTransparency="true" frameborder="0"></iframe>';let O=null;const ne=(0,s.useCallback)(pe=>{const fe=pe.currentTarget;fe.innerHTML=I18N.imockSharing.successfully_copied,clearTimeout(O),O=setTimeout(()=>{fe.innerHTML=I18N.imockSharing.successfully_copied},3e3),(0,V.kv)({action:"\u590D\u5236\u4EE3\u7801",project:d||{},linkName:c.link_name});try{MB.notice({text:"\u590D\u5236\u6210\u529F"})}catch(W){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice",payload:{type:"embedCopy"}}),"*")}},[c,O,d]),ie=(0,s.useCallback)(pe=>{(0,wn.$)(ae),ne(pe)},[ne,ae]),ge=(0,s.useCallback)(()=>{i({type:l.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})},[i]);return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsx)(Cn,{title:I18N.imockSharing.insert,handleClick:ge}),(0,r.jsxs)(oo,{children:[(0,r.jsx)("p",{className:"desc",children:I18N.imockSharing.zoom_insertion_desc}),(0,r.jsxs)("div",{className:"copy-wrapper",children:[(0,r.jsx)("img",{src:_===fn.Sx.DARK?"/mb-workspace/images/sharing/share_embedded_dark.png":"/mb-workspace/images/sharing/share_embedded_light.png",className:"demo-img"}),(0,r.jsxs)("div",{className:"copy-box",children:[(0,r.jsx)("p",{className:"sharing-embedded-url",children:ae}),(0,r.jsx)("button",{className:"copy-button copy","data-clipboard-text":ae,onClick:ie,children:I18N.imockSharing.copy_code})]})]})]})]})},Et=(0,s.memo)(Bt);var Wt=n(54909);const yn=(0,A.AH)([".vip-title{display:inline-block;color:",";background-color:",";font-weight:500;font-size:12px;padding:0 4px;height:20px;text-align:center;border-radius:4px;line-height:20px;}"],e=>e.isIframe?Wt.f.color_text_link_normal.value_dark:e.theme.color_text_link_normal,e=>e.isIframe?Wt.f.color_info_bg.value_dark:e.theme.color_info_bg),On=A.Ay.div.withConfig({displayName:"styles__StyledNewSharePage",componentId:"sc-v150qt-0"})(["width:100%;height:100%;background-color:",";.basic-content{width:100%;height:calc(100% - ","px);:first-child{margin-bottom:16px;}}.header{height:","px;display:flex;align-items:center;border-bottom:1px solid ",";span{margin-left:24px;font-size:14px;font-weight:500;color:",";}}.share-link-title{margin-top:20px;margin-bottom:10px;display:flex;flex-direction:column;justify-content:center;margin-left:24px;font-size:12px;.title-text{color:",";}.name-input{display:flex;align-items:center;height:30px;font-size:14px;font-weight:500;color:",";span{max-width:212px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;position:relative;height:24px;line-height:24px;&:hover:after{box-sizing:border-box;content:'';position:absolute;top:0;left:0;width:100%;height:100%;border-bottom:1px solid rgba(22,133,252,1);}}.input-icon{margin-left:8px;}input{width:100%;margin-right:32px;height:24px;line-height:24px;color:",";border-bottom:1px solid rgba(22,133,252,1);}}}.tabs{margin:8px 24px 18px 24px;border-bottom:1px solid ",";.sharing-nav{width:90px;height:25px;min-height:25px;padding:0px;}.nav-item{flex:0;padding:0 24px 0 0;}.nav-label{height:24px;}.active .nav-label{color:",";font-weight:600;}.active.basic{.nav-label{&::after{content:'';position:absolute;bottom:-1px;width:30px;height:2px;background:",";}}}.active.visible{.nav-label{&::after{content:'';position:absolute;bottom:-1px;width:50px;height:2px;background:",";}}}}.line-item{display:flex;padding:0 24px;height:34px;align-items:center;justify-content:space-between;flex-grow:0;.item-title{color:",";}.item-expired{display:flex;.WorkspaceSelect{border-radius:6px;}.general-input{width:65px;height:28px;border-radius:6px;margin-right:12px;}}.RadioGroup label{min-height:unset;margin-right:unset;font-size:12px;&:not(:last-child){margin-right:16px;}}.switch-group{display:flex;.view-sticky{display:flex;align-items:center;margin-right:17px;label{margin-right:4px;}}.view-prd{display:flex;align-items:center;.question-action{display:flex;align-items:center;margin:0 8px;color:",";&:hover{color:",";}}}label.checked{color:",";}}}"],e=>e.theme.color_bg_white,we.gE,we.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_background_icon_hover2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1),ws=A.Ay.div.withConfig({displayName:"styles__StyledBasic",componentId:"sc-v150qt-1"})(["overflow-y:overlay;.divider{height:1px;background:",";margin:4px 24px;}&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&::-webkit-scrollbar-corner{background-color:transparent;}.expiration,.menu-list-type{cursor:pointer;display:flex;align-items:center;color:",";.misc{display:flex;align-items:center;}.drop-down-icon{margin-left:6px;}}.expiration{.drop-down-icon .svg-icon{transition:all 0.2s;transform :",";}}.menu-list-type{.drop-down-icon .svg-icon{transition:all 0.2s;transform :",";}}.password-input-item{display:flex;align-items:center;> :last-child{margin-left:10px;}}.last{margin-bottom:-8px;}.line-item.version-control{"," .vip-title{cursor:pointer;margin-left:4px;}}"],e=>e.theme.color_background_icon_hover2,e=>e.theme.color_text_L1,e=>e.isActiveContextMenu&&e.menuListType==="expiration"?"rotate(-180deg)":"rotate(0deg)",e=>e.isActiveContextMenu&&e.menuListType==="viewMode"?"rotate(-180deg)":"rotate(0deg)",yn),Mn=A.Ay.div.withConfig({displayName:"styles__StyledVisible",componentId:"sc-v150qt-2"})(["height:328px;.visible-check{height:32px;}"]),Ss=(0,A.DU)([".custom-title{display:flex;justify-content:space-between;align-items:baseline;","}}#IBOT_SELECT_MENU_ROOT .SelectMenuPortal .WorkspaceSelectMenu .SelectOption{height:28px;&:hover{.vip-title{background-color:",";}}}#IBOT_SELECT_MENU_ROOT .SelectMenuPortal .WorkspaceSelectMenu.is-open{right:-14px;}"],yn,e=>e.isIframe?Wt.f.color_bg_white.value_dark:e.theme.color_bg_white);var Wn=n(53159);const Cs=A.Ay.div.withConfig({displayName:"styles__StyledPasswordTextInput",componentId:"sc-k8kmsm-0"})(["display:flex;background-color:",";height:28px;&.is-warning{.password-input{border:1px solid #ff6161;border-radius:4px 0 0 4px;&:focus{border:1px solid #ff6161;}}}.divider{height:100%;width:1px;background-color:",";}.password-input-wrapper{position:relative;display:flex;align-items:center;width:100px;.password-input{position:relative;width:100%;font-size:12px;padding-right:22px;padding-left:8px;overflow:hidden;white-space:nowrap;text-overflow:clip;color:",";border:1px solid ",";border-radius:6px;height:100%;&:focus{border:1px solid ",";outline:1px solid ",";outline-offset:-2px;}&:hover{border:1px solid ",";}}.reset-pwd{width:20px;height:20px;position:absolute;border-radius:4px;right:4px;color:",";cursor:pointer;&:hover{path{fill:",";}color:",";background-color:",";}&:active{path{fill:",";}color:",";background-color:",";}}}.password-button{width:72px;padding:8px 12px;color:",";font-size:12px;line-height:16px;white-space:nowrap;border:1px solid ",";border-radius:0 4px 4px 0;border-left:none;background-color:",";&.saved{background-color:",";color:#999;pointer-events:none;}}"],e=>e.theme.color_bg_white,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02,e=>e.theme.color_proto,e=>e.theme.color_proto,e=>e.theme.color_proto,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_canvas,e=>e.theme.color_bg_canvas);function Ms(e){return Math.random().toString(36).slice(-e)}function ro(e){const{onSubmit:i,value:c}=e,[d,_]=(0,s.useState)(e.value),b=(0,s.useRef)(null),m=G=>{_(G.target.value)},N=()=>{const G=Ms(6);_(G),i(G)},j=()=>{if(!i(d)){_(c);try{MB.notice({text:"\u5BC6\u7801\u683C\u5F0F\u9519\u8BEF\uFF0C\u4E0D\u5F97\u4E3A\u7A7A\u6216\u7279\u6B8A\u5B57\u7B26",type:"warning"})}catch($){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice"}),"*")}}},B=G=>{G.key==="Enter"&&(j(),b.current.blur())};return(0,r.jsx)(Cs,{children:(0,r.jsxs)("div",{className:"password-input-wrapper",children:[(0,r.jsx)("input",{ref:b,type:"text",className:"password-input",value:d,onChange:m,onBlur:j,onKeyPress:B}),(0,r.jsx)(St.A,{content:I18N.imockSharing.gen_pwd_randomly,direction:"down",children:(0,r.jsx)(Ee.C,{className:"reset-pwd",name:"common/spinner",onClick:N})})]})})}function Ns(e){const{isOrgProject:i,sharingData:c,updateSharing:d}=e,_=c.view_mode,{isHavePassword:b}=Ln(c),m=(0,g.wA)(),j=(0,g.d4)(l.query.getHostType)==="iframe",{comment_permission:B,wechat:G,view_sticky:$,is_first_canvas_open:P}=c,ae=(0,s.useMemo)(()=>(0,V.R0)(i),[i]),oe=()=>{if(b)d({password:""}),m({type:l.entryKey["sharing:settingPage:default:confirm"]});else{const ye=Ms(6);O(ye)}m({type:l.entryKey["sharing:function:track"],payload:{operation:b?"\u5173\u95ED\u5BC6\u7801\u4FDD\u62A4":"\u6253\u5F00\u5BC6\u7801\u4FDD\u62A4",viewMode:_}})},O=ye=>/^\s*$/.test(ye)||!/^\w+$/.test(ye)?!1:(d({password:ye}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:function:track"],payload:{operation:"\u5237\u65B0\u5BC6\u7801",viewMode:_}}),!0),ne=()=>{d({wechat:!G}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:function:track"],payload:{operation:G?"\u5173\u95ED\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875":"\u6253\u5F00\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875",viewMode:_}})},ie=()=>{d({is_first_canvas_open:!P}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:function:track"],payload:{operation:P?"\u5173\u95ED\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03":"\u6253\u5F00\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03",viewMode:_}})},ge=(0,s.useMemo)(()=>B==="org_member",[B]),pe=()=>{d({view_sticky:$==="view_sticky"?"no_sticky":"view_sticky"}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:function:track"],payload:{operation:$?"\u5173\u95ED\u6279\u6CE8\u53EF\u89C1":"\u6253\u5F00\u6279\u6CE8\u53EF\u89C1",viewMode:_}})},fe=()=>{d({comment_permission:B==="org_member"?"off":"org_member"}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:function:track"],payload:{operation:ge?"\u5173\u95ED\u8BC4\u8BBA":"\u6253\u5F00\u8BC4\u8BBA",viewMode:_}})},W=ye=>{let{value:be}=ye;d({view_access:be}),m({type:l.entryKey["sharing:settingPage:default:confirm"]}),m({type:l.entryKey["sharing:remote:sharing:update"],payload:{sharingData:c,updatedKV:{access:be}}}),m({type:l.entryKey["sharing:function:track"],payload:{operation:be==="public"?"\u6240\u6709\u4EBA":"\u5207\u6362\u56E2\u961F",viewMode:_,isDefault:!1}})};return(0,r.jsxs)(ws,{children:[i&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_access}),(0,r.jsx)(Wn.A,{name:"access",optionList:ae,value:c.view_access,onChange:W})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_password_protect}),(0,r.jsxs)("div",{className:"password-input-item",children:[b&&(0,r.jsx)(ro,{value:c.password,onSubmit:O}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:b,onChange:oe})]})]}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.note_display}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:$==="view_sticky",onChange:pe})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.comment_allowed}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ge,onChange:fe})]}),(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.share_skip_install_run_inwechat}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:G,onChange:ne})]}),!j&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:"\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03"}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:P,onChange:ie})]})]})}var io=n(60185);const Is=()=>[{key:"read_only",label:I18N.imockSharing.read_only_mode,icon:"sharing/mode_canvas"},{key:"device",label:I18N.imockSharing.device_mode,icon:"sharing/mode_device"},{key:"inspect",label:I18N.imockSharing.inspect_mode,icon:"sharing/mode_inspect"}],js=e=>{let{sharingData:i,updateSharing:c}=e;const d=P=>()=>{c({device_model:P})},[_,b]=(0,s.useState)(!1),m=(0,s.createRef)(),N=(0,g.d4)(l.query.getTheme),[j,B]=(0,s.useState)({left:0,top:0}),G=()=>{if(!(m!=null&&m.current))return;const{left:P,top:ae,height:oe}=m.current.getBoundingClientRect();B({left:P-58,top:ae+oe+6}),setTimeout(()=>{b(!0)},0)},$=()=>{b(!1)};return(0,r.jsx)(ao,{className:X()({dark:N==="dark"}),children:(0,r.jsxs)("div",{className:"device-model",children:[(0,r.jsxs)("div",{className:"device-model-header",children:[(0,r.jsx)("div",{className:"device-model-title",children:I18N.imockSharing.default_mode}),(0,r.jsxs)("div",{className:"modal-container",ref:m,children:[(0,r.jsx)(Ee.C,{onMouseEnter:G,onMouseLeave:$,className:"tipsIcon",name:"sharing/question",size:14}),_&&(0,r.jsx)(Hn,{theme:N,position:j})]})]}),(0,r.jsx)("div",{className:"device-model-type-container",children:Is().map((P,ae)=>{let{key:oe,label:O,icon:ne}=P;const ie="device-model-"+oe+"   ";return(0,r.jsxs)("div",{className:X()("device-model-type-item",ie,{"is-active":oe===i.device_model,dark:N==="dark"}),onClick:d(oe),children:[(0,r.jsx)(Ee.C,{name:ne,size:16}),(0,r.jsx)("span",{children:O})]},ae)})})]})})},Hn=(0,s.memo)(e=>{const{theme:i,position:c}=e;return(0,io.createPortal)((0,r.jsxs)(Es,{className:X()("device-model-modal",{light:i==="light",dark:i==="dark"}),style:{left:c.left,top:c.top},children:[(0,r.jsx)("div",{className:"icon-triangle"}),(0,r.jsxs)("div",{className:X()("modal-content"),children:[(0,r.jsxs)("div",{className:"content-item read_only",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_read_only_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.read_only_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.read_only_mode_intro})]})]}),(0,r.jsxs)("div",{className:"content-item device",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_device_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.device_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.device_mode_intro})]})]}),(0,r.jsxs)("div",{className:"content-item inspect",children:[(0,r.jsx)("img",{src:"/mb-proto2/images/sharing/intro_inspect_"+i+"_2.png"}),(0,r.jsxs)("div",{className:"content-item-text",children:[(0,r.jsx)("div",{className:"intro-title",children:I18N.imockSharing.inspect_mode}),(0,r.jsx)("div",{className:"intro-content",children:I18N.imockSharing.inspect_mode_intro})]})]})]})]}),document.body)}),ao=A.Ay.div.withConfig({displayName:"ViewModeModel__StyledDeviceModel",componentId:"sc-spi606-0"})(["position:relative;margin-bottom:14px;margin-top:20px;.device-model{padding:0 24px;.device-model-header{display:flex;align-items:center;margin-bottom:10px;color:",";.device-model-title{margin-right:4px;line-height:14px;}}.modal-container{display:flex;.tipsIcon{color:",";&:hover{color:",";}}}.device-model-type-container{display:flex;justify-content:space-between;.svg-icon > *{fill:currentColor;}.device-model-type-item{width:111px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:1px solid rgba(219,219,219,1);border-radius:6px;color:",";&.dark{border:1px solid #4f5052;}span{margin-left:4px;font-size:14px;}&:hover{border:1px solid  ",";}.svg-icon{[stroke]{stroke:currentColor;fill:none;}}&.is-active{border:1px solid  ",";color:",";.svg-icon{color:",";}}}}}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_normal),Es=A.Ay.div.withConfig({displayName:"ViewModeModel__StyledDeviceModelModal",componentId:"sc-spi606-1"})(["&.device-model-modal{z-index:100;position:absolute;border-radius:8px;&.dark{.modal-content{outline:1px solid ",";}}&.light{box-shadow:0px 2px 10px 0px #27364E14;box-shadow:4px 12px 40px 0px #27364E1A;}.icon-triangle{z-index:1;position:absolute;width:10px;height:10px;top:-4px;left:58px;background:",";transform:rotate(45deg);border-top-left-radius:1px;}&.dark .icon-triangle{width:9px;height:9px;top:-5px;left:59px;border-top:1px solid ",";border-left:1px solid ",";}.modal-content{display:flex;flex-direction:column;padding:20px;width:374px;height:366px;border-radius:8px;background:",";&:lang(en){height:250px;}img{width:160px;height:96px;}.content-item:not(:last-child){margin-bottom:16px;}.content-item-text{display:flex;flex-direction:column;justify-content:center;height:100%;margin-left:16px;}.device,.read_only,.inspect{width:100%;height:96px;display:flex;flex-direction:row;justify-content:space-between;align-items:center;.intro-title{width:56px;height:22px;color:",";font-size:14px;font-weight:600;margin-bottom:6px;&:lang(en){width:max-content;}}.intro-content{color:",";}}}}"],e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_white,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_border_02,e=>e.theme.color_bg_white,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2);function lo(){const e=(0,g.d4)(l.query.getCurrentSharing),i=(0,g.d4)(l.query.getProject),c=(0,g.wA)(),d=(0,s.useCallback)(m=>{c({type:l.entryKey["sharing:currentSharing:update"],payload:{updatedKV:m}})},[c]),_=m=>{c({type:l.entryKey["sharing:currentSharing:update"],payload:{updatedKV:m}}),(0,pt.a0)(i.cid+"_default_sharing_view_mode",m.device_model,pt.qW.String),c({type:l.entryKey["sharing:settingPage:default:confirm"]})},b=()=>{c({type:l.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})};return(0,r.jsxs)(On,{children:[(0,r.jsx)(Cn,{title:I18N.imockSharing.sharing_setting_title,handleClick:b}),(0,r.jsxs)("div",{className:"basic-content",children:[(0,r.jsx)(js,{sharingData:e,updateSharing:_}),(0,r.jsx)(Ns,{isOrgProject:i.is_org_project,sharingData:e,updateSharing:d})]})]})}var as=n(54608);const ks=()=>(0,r.jsx)(Ts,{}),Ts=A.Ay.div.withConfig({displayName:"Divider__StyledDivider",componentId:"sc-x06h85-0"})(["height:1px;background:",";margin:8px 24px;"],e=>e.theme.color_background_icon_hover2);var co=n(7775),uo=n(60081),Ls=n(24660),ls=n(39547);function po(e){const{isOrgProject:i,sharingData:c,updateSharing:d,settingPageType:_}=e,b=c.device_model,m=(0,s.useMemo)(()=>(0,V.R0)(i),[i]),{isHavePassword:N}=Ln(c),j=(0,g.d4)(l.query.getUser),B=(0,g.d4)(l.query.getOrg),$=(0,g.d4)(l.query.getHostType)==="iframe",P=(0,g.wA)(),ae="v8-\u5206\u4EAB\u5F39\u7A97_\u81EA\u5B9A\u4E49\u94FE\u63A5\u6709\u6548\u671F";let oe=!1,O=!1;if(i){const{plan:ft}=(0,Tn.rM)(B);oe=[an.Sj.OrgExpired,an.Sj.OrgFree].includes(ft),O=ft!==an.Sj.OrgFull}else{const ft=ls.t.InitialUser(j),vt=ft.planSdk.prototypePlan.getUserPlan(),on=ft.planSdk.prototypePlan.getUserStatus();oe=![an.L1.Lifetime,an.L1.PremiumLifetime].includes(vt)&&on!==an.pZ.Trial,O=![an.L1.Lifetime,an.L1.PremiumLifetime].includes(vt)}const{view_sticky:ne,comment_permission:ie,wechat:ge,enable_version_record:pe,is_first_canvas_open:fe}=c,W=ft=>{let{value:vt}=ft;d({view_access:vt})},ye=()=>{if(N)d({password:""});else{const ft=Ms(6);be(ft)}P({type:l.entryKey["sharing:function:track"],payload:{operation:N?"\u5173\u95ED\u5BC6\u7801\u4FDD\u62A4":"\u6253\u5F00\u5BC6\u7801\u4FDD\u62A4",viewMode:b,isDefault:!0}})},be=ft=>/^\s*$/.test(ft)||!/^\w+$/.test(ft)?!1:(d({password:ft}),P({type:l.entryKey["sharing:function:track"],payload:{operation:"\u5237\u65B0\u5BC6\u7801",viewMode:b,isDefault:!0}}),!0),xe=()=>{d({wechat:!ge}),P({type:l.entryKey["sharing:function:track"],payload:{operation:ge?"\u5173\u95ED\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875":"\u6253\u5F00\u8DF3\u8FC7\u79FB\u52A8\u7AEF\u63D0\u793A\u9875",viewMode:b,isDefault:!0}})},Te=()=>{d({is_first_canvas_open:!fe}),P({type:l.entryKey["sharing:function:track"],payload:{operation:fe?"\u6253\u5F00\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03":"\u5173\u95ED\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03",viewMode:b,isDefault:!0}})},Le=(0,s.useMemo)(()=>ie==="org_member",[ie]),Ge=()=>{d({view_sticky:!ne,sticky:!ne}),P({type:l.entryKey["sharing:function:track"],payload:{operation:ne?"\u5173\u95ED\u6279\u6CE8\u53EF\u89C1":"\u6253\u5F00\u6279\u6CE8\u53EF\u89C1",viewMode:b,isDefault:!0}})},Fe=()=>{d({comment_permission:ie==="org_member"?"off":"org_member"}),P({type:l.entryKey["sharing:function:track"],payload:{operation:Le?"\u5173\u95ED\u8BC4\u8BBA":"\u6253\u5F00\u8BC4\u8BBA",viewMode:b,isDefault:!0}})},[Ye,rt]=(0,s.useState)("1"),[it,at]=(0,s.useState)("forever"),{remainTimeText:Mt}=kn(c,!1,999);(0,s.useEffect)(()=>{c&&c.expire_type==="forever"?at(c.expire_type):(rt(c.expire_type),at(Mt))},[c,Mt]);const kt=(0,s.useRef)(null),Ot=()=>{if(oe){const ft=(0,Ls.l)(B,ae);if(B||(ft.mode="org",ft.isSelectOrg=!0),$){const vt="/workspace?payment_param="+(0,jt._)(ft);(0,ut.JW)(vt,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(ft);return!0}return!1},Dt=ft=>{if(ft==="forever")d({expire_type:ft});else if(Ot()){d({expire_type:"forever"},{isCustom:!0});return}P({type:l.entryKey["sharing:function:track"],payload:{operation:ft==="forever"?"\u6709\u6548\u671F\u6C38\u4E45":"\u6709\u6548\u671F\u81EA\u5B9A\u4E49",viewMode:b,isDefault:!1}}),at(ft)},Gt=[{label:"\u6C38\u4E45\u6709\u6548",value:"forever"},{label:oe?(0,r.jsxs)("div",{className:"custom-title",children:["\u81EA\u5B9A\u4E49",(0,r.jsx)("span",{className:"vip-title",children:"\u56E2\u961F\u7248"})]}):"\u81EA\u5B9A\u4E49",value:"custom"}],gt=ft=>/^([1-9]\d{0,3}|9999)$/.test(ft),Tt=ft=>{const vt=ft.target.value;if(!gt(vt))try{MB.notice({text:"\u8F93\u5165\u503C\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165",type:"warning"});return}catch(Xn){window.top.postMessage(JSON.stringify({sharingMessage:"sharing:notice"}),"*")}d({expire_type:vt}),rt(vt)},Pt=()=>{const ft=(0,Ls.l)(B,"\u7F16\u8F91\u533A_v8\u539F\u578B\u5206\u4EAB_\u7248\u672C\u8BB0\u5F55\u5F00\u5173","v8proto-share-set-version",!0);if(ft.immediateUse="org_full",$){const vt="/workspace?payment_param="+(0,jt._)(ft);(0,ut.JW)(vt,"_blank","noreferrer")}else MB.global.popupHelper.chargeAsync(ft)},Qt=()=>{O?Pt():(d({enable_version_record:!pe}),P({type:l.entryKey["sharing:function:track"],payload:{operation:pe?"\u5173\u95ED\u67E5\u770B\u7248\u672C\u8BB0\u5F55":"\u6253\u5F00\u67E5\u770B\u7248\u672C\u8BB0\u5F55",viewMode:b,isDefault:!1}}))};return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsxs)(ws,{children:[c.type==="advanced"&&i&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_access}),(0,r.jsx)(Wn.A,{name:"access",optionList:m,value:c.view_access,onChange:W})]}),(0,r.jsxs)("div",{className:"line-item "+(i?"mt":""),children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_password_protect}),(0,r.jsxs)("div",{className:"password-input-item",children:[N&&(0,r.jsx)(ro,{value:c.password,onSubmit:be}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:N,onChange:ye})]})]}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.permission_link_expired}),(0,r.jsxs)("div",{className:"item-expired",children:[it==="custom"&&(0,r.jsx)(uo.A,{className:"general-input",attr:"width",value:Ye,cursorDirection:!1,min:1,max:9999,rightTitle:"\u5929",actionButton:!1,ref:kt,isSelect:!0,onBlur:Tt,disableKeyboardEvents:!0}),(0,r.jsx)(co.mq,{value:it,attr:"expire_type",placeholder:_==="create"?Ye+"\u5929\u540E\u5931\u6548":it,optionList:Gt,onChange:ft=>Dt(ft)})]})]}),(0,r.jsx)(ks,{}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.note_display}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ne,onChange:Ge})]}),(0,r.jsxs)("div",{className:"line-item mt",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.comment_allowed}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:Le,onChange:Fe})]}),(0,r.jsxs)("div",{className:"line-item version-control mt",children:[(0,r.jsxs)("label",{className:"item-title",children:[I18N.imockSharing.version_control,O&&(0,r.jsx)("span",{className:"vip-title",onClick:Pt,children:I18N.Common.business_plan})]}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:pe,readOnly:O,onChange:Qt})]}),(0,r.jsx)(ks,{}),(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:I18N.imockSharing.share_skip_install_run_inwechat}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:ge,onChange:xe})]}),!$&&(0,r.jsxs)("div",{className:"line-item",children:[(0,r.jsx)("label",{className:"item-title",children:"\u9ED8\u8BA4\u6253\u5F00\u7B2C\u4E00\u4E2A\u753B\u5E03"}),(0,r.jsx)(Ct.A,{size:"small",isIOS:!0,isChecked:fe,onChange:Te})]})]}),(0,r.jsx)(Ss,{isIframe:$})]})}var ln=n(74059),ho=n(66748),_t=n.n(ho);const As=A.Ay.div.withConfig({displayName:"styles__StyledCheck",componentId:"sc-1wxzhto-0"})(["position:relative;display:flex;align-items:center;cursor:pointer;color:",";&.readonly{cursor:default;}&.is-disabled{cursor:not-allowed;&::after{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background-color:#f2f2f2;}}> input[type=radio],> input[type=checkbox]{position:absolute;opacity:0;width:12px;height:12px;}.Check-state{position:relative;width:12px;height:12px;font-size:12px;display:flex;align-items:center;border:1px solid ",";border-radius:",";transition:all 0.2s ease-in-out;background-color:",";.icon{transition:all 0.2s ease-in-out;position:absolute;top:-1px;left:-1px;}}.Check-label{margin-left:0.33333em;color:",";}&.is-checked .Check-state{background-color:",";border:1px solid ",";color:#fff;.icon{transform:scale(0.833);color:#f2f4f5;}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}"],e=>e.theme.color_text_L2,e=>e.theme.color_text_disabled01,e=>e.isCircle?"50%":"2px",e=>e.theme.color_bg_white,e=>e.theme.color_text_L2,e=>e.theme.color_text_L3,e=>e.theme.color_text_L2);class Vn extends s.PureComponent{constructor(){super(...arguments),(0,ln.A)(this,"onToggle",i=>{const{onChange:c}=this.props;c(i)})}render(){const{className:i,isDisabled:c,readOnly:d,isChecked:_,isCircle:b}=this.props,m=X()(i,{"is-checked":_,"is-disabled":c,readonly:d});return(0,r.jsx)(As,{className:m,onClick:this.onToggle,isCircle:b,children:(0,r.jsx)("span",{className:"Check-state",children:(0,r.jsx)(Ee.C,{name:"new_replace/box_check"})})})}}(0,ln.A)(Vn,"propTypes",{isChecked:_t().bool,isDisabled:_t().bool,readOnly:_t().bool,onChange:_t().func,label:_t().any,name:_t().string,value:_t().any,className:_t().string,isCircle:_t().bool}),(0,ln.A)(Vn,"defaultProps",{isChecked:!1,label:"",className:"",isCircle:!1,onChange:()=>null,onToggle:()=>null});var cs=n(67234),ds=n(89099),Os=n(44622);const mo=A.Ay.div.withConfig({displayName:"styles__StyledSharingScreenHiddenList",componentId:"sc-1bmii7a-0"})(["padding:0 17px;.sm-hidden-check{display:flex;height:40px;align-items:center;justify-content:space-between;.item-title{color:",";}}.sm-check{.Check-state{background-color:transparent;border:1px solid ",";}.Check-label{color:",";}&.is-checked .Check-state{background-color:#1684fc;border:1px solid #1684fc;.icon{color:#fff;}}&.is-checked.is-disabled .Check-state{background-color:",";border:1px solid ",";span.icon{color:",";}}&.is-disabled .Check-state{background-color:",";border:1px solid ",";}&.is-disabled::after{background-color:unset;}}.screen-config-header{display:flex;padding-left:8px;flex-direction:row;height:36px;&.is-disabled{cursor:not-allowed;color:#ccc;.screen-name{color:",";}> label{&.is-disabled::after{background-color:unset;}}span.Check-label{color:",";}.content-wrapper{span{color:",";}}}span.Check-label{color:",";margin-left:10px;}.content-wrapper{display:flex;align-items:center;span{&.divider{margin-left:10px;margin-right:11px;}color:",";}}}.screen-list-content{height:210px;overflow-y:overlay;background:",";margin:0 8px;&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&.dark{&::-webkit-scrollbar-thumb{background-color:#666;border-radius:4px;&:hover{background-color:#cccccc;}}}&::-webkit-scrollbar-corner{background-color:transparent;}&::-webkit-scrollbar{width:6px;}.screen-name{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}&.is-disabled{cursor:not-allowed;color:#ccc;.screen-name{color:",";}}ol,li{list-style:none;}.sm-list{height:auto;overflow-y:overlay;&::-webkit-scrollbar-track{background-color:transparent;}&::-webkit-scrollbar{width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:#dbdbdb;border-radius:4px;&:hover{background-color:#cccccc;}}&::-webkit-scrollbar-corner{background-color:transparent;}margin-left:8px;padding-left:15px;.sm-box{display:flex;align-items:center;height:36px;&.is-disabled{cursor:not-allowed;.sm-item{cursor:not-allowed;.expander{svg{color:",";cursor:not-allowed;}}.screen-name{color:",";}}}}.sm-item{display:flex;height:100%;width:100%;align-items:center;cursor:pointer;.sm-icon{min-width:20px;min-height:20px;margin-right:4px;color:",";}.expander{display:flex;align-items:center;justify-content:center;margin-left:-15px;margin-right:1px;width:14px;height:14px;.arrow-icon{margin-right:0;color:#999;width:8px;&:not(.is-expand){transform:rotate(-90deg);}}}.screen-icon{margin-right:4px;}.screen-name{color:",";}}}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_disabled01,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_btn_secondary_active,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L2,e=>e.theme.color_text_L2,e=>e.theme.color_bg_card,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_disabled01,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1);class Ps extends s.PureComponent{constructor(i){super(i),(0,ln.A)(this,"handleCheck",m=>{const{onUpdateScreenVisibleList:N,screenVisibleList:j}=this.props,B=new Set(j),G=(0,Os.YU)(m),$=B.has(m.cid);G.forEach(ae=>{let{cid:oe}=ae;$?B.delete(oe):B.add(oe)});const P=[];for(const ae of B){const oe=this.screenDataMap[ae];oe&&!(0,Os.Mo)(oe)&&P.push(oe)}N(Array.from(B))}),(0,ln.A)(this,"onSelectAll",m=>{const{onUpdateScreenVisibleList:N}=this.props;N(m?this.pageKeyList:[])});const{screenMetaList:c}=i,{treeData:d,pageAttrMap:_,pageKeyList:b}=c;this.screenDataMap=_,this.screenTreeData=d,this.pageKeyList=b,this.pageExceptFolderList=(0,V.RF)(_)}componentDidMount(){const i=(0,pt.Yt)("currentJumpingSharingScreenMetaCid",void 0,pt.qW.String);(0,pt.G5)("currentJumpingSharingScreenMetaCid"),i!==void 0&&setTimeout(()=>document.getElementById("app-sharing").querySelector('.content-item[data-cid="'+i+'"]').scrollIntoView(),300)}render(){const{theme:i,screenVisibleList:c,isDisplayScreenAll:d}=this.props,_=(c==null?void 0:c.length)===this.pageKeyList.length,b=(0,V.Mj)(d,c,this.pageExceptFolderList);return(0,r.jsxs)(mo,{children:[(0,r.jsxs)("div",{className:X()("screen-config-header",{"is-disabled":d}),children:[(0,r.jsx)(cs.A,{className:"sm-check",label:I18N.imockSharing.select_all,onChange:this.onSelectAll,isDisabled:d,isChecked:_}),(0,r.jsxs)("div",{className:"content-wrapper",children:[(0,r.jsx)("span",{className:"divider",children:"|"}),(0,r.jsx)("span",{children:I18N.imockSharing.select_n_screens.replace("%1",b).replace("%2",this.pageExceptFolderList.length)})]})]}),(0,r.jsx)("div",{className:X()("screen-list-content",{"is-disabled":d,dark:i==="dark"}),children:(0,r.jsx)("div",{className:"sm-list",children:(0,r.jsx)(Ds,{theme:i,isDisplayScreenAll:d,screenTreeData:this.screenTreeData,screenVisibleList:c,onCheck:this.handleCheck})})})]})}}(0,ln.A)(Ps,"propTypes",{theme:_t().string,screenMetaList:_t().object,screenVisibleList:_t().array,onUpdateScreenVisibleList:_t().func,isDisplayScreenAll:_t().bool});const Ds=e=>{let{theme:i,isDisplayScreenAll:c,screenTreeData:d,screenVisibleList:_,onCheck:b}=e;return(0,r.jsx)(r.Fragment,{children:d.children.map(m=>(0,r.jsx)(Gn,{theme:i,treeNode:m,root:!0,depth:0,screenVisibleList:_,isDisplayScreenAll:c,onCheck:b},m.cid))})};Ds.propTypes={theme:_t().string,isDisplayScreenAll:_t().bool,screenTreeData:_t().object,screenVisibleList:_t().array,onCheck:_t().func};class Gn extends s.PureComponent{constructor(i){super(i),(0,ln.A)(this,"handleCheck",c=>{c.stopPropagation();const{isDisplayScreenAll:d}=this.props;if(d)return;const{treeNode:_,onCheck:b}=this.props;b(_)}),(0,ln.A)(this,"handleExpand",c=>{const{isDisplayScreenAll:d}=this.props;d||(c.stopPropagation(),this.setState({expand:!this.state.expand}))}),this.state={expand:!0}}render(){const{isDisplayScreenAll:i,treeNode:c,root:d,depth:_,screenVisibleList:b,onCheck:m,theme:N}=this.props,{children:j,data:B}=c,G=j.length!==0,{expand:$}=this.state,P=b&&b.includes(c.cid);return(0,r.jsxs)("li",{className:X()("content-item"),"data-cid":c.cid,children:[(0,r.jsxs)("div",{className:X()("sm-box",{"is-display":P,"is-disabled":i}),children:[(0,r.jsx)(Vn,{isDisabled:i,className:"sm-check",isChecked:P,onChange:this.handleCheck}),(0,r.jsx)(Rs,{isDisabled:i,theme:N,root:d,depth:_,showExpander:G,data:B,expand:$,onClick:this.handleCheck,onExpand:this.handleExpand})]}),j.length>0&&$&&(0,r.jsx)("ol",{className:"sm-child",children:j.map(ae=>(0,r.jsx)(Gn,{theme:N,treeNode:ae,depth:_+1,screenVisibleList:b,isDisplayScreenAll:i,onCheck:m},ae.cid))})]})}}(0,ln.A)(Gn,"propTypes",{theme:_t().string,isDisplayScreenAll:_t().bool,treeNode:_t().object,screenVisibleList:_t().array,depth:_t().number,root:_t().bool,onCheck:_t().func});class Rs extends s.PureComponent{render(){const{depth:i,showExpander:c,data:d,expand:_,onExpand:b,onClick:m,theme:N,isDisabled:j}=this.props,B=19*(i+1);return(0,r.jsxs)("div",{className:X()("sm-item"),style:{paddingLeft:B},onClick:m,children:[c&&(0,r.jsx)("a",{className:"expander",onClick:b,children:(0,r.jsx)(Ee.C,{className:X()("arrow-icon",{"is-expand":_}),name:"common/expand"})}),(0,r.jsx)(ds.k,{icon:d.icon,theme:N,disabled:j,isExpand:_}),(0,r.jsx)("span",{className:"screen-name",children:d.name})]})}}(0,ln.A)(Rs,"propTypes",{theme:_t().string,isDisabled:_t().bool,showExpander:_t().bool,data:_t().object,depth:_t().number,expand:_t().bool,onExpand:_t().func,onClick:_t().func});function go(e){const i=[{value:1,label:I18N.imockSharing.all_pages},{value:2,label:I18N.imockSharing.partial_pages}],{sharingData:c,updateSharing:d}=e,[_,b]=(0,s.useState)(c.screen_visible_switch?2:1),m=(0,g.d4)(l.query.getTheme),N=(0,g.d4)(l.query.getScreenMetaList),j=G=>{d({screen_visible_list:G})},B=G=>{let{value:$}=G;d({screen_visible_switch:$==="2",screen_visible_list:[]}),b($)};return(0,r.jsxs)(Mn,{children:[(0,r.jsx)("div",{className:"line-item visible-check",children:(0,r.jsx)(Wn.A,{name:"access",optionList:i,value:_,onChange:B})}),N&&(0,r.jsx)(Ps,{theme:m,screenMetaList:N,screenVisibleList:c.screen_visible_list,isDisplayScreenAll:!c.screen_visible_switch,onUpdateScreenVisibleList:j})]})}var Bs=n(19722);const fo=e=>{let{handleConfirm:i,handleCancel:c}=e;const d=(0,g.d4)(l.query.getCurrentSharing),_=(0,g.d4)(l.query.getTheme),b=(0,g.d4)(l.query.getScreenMetaList),m=d.type==="default"?!1:d.screen_visible_switch&&(0,V.Mb)(b,d).selectedSize===0,N=()=>{m||i()};return(0,r.jsxs)(Fs,{className:"confirm-bar",children:[(0,r.jsx)(dn.jn,{className:X()("confirm",{dark:_==="dark"}),disabled:m,onClick:N,children:I18N.SettingPanel.confirm}),(0,r.jsx)(dn.tA,{className:"cancel",onClick:c,children:I18N.imockSharing.cancel})]})},Fs=A.Ay.div.withConfig({displayName:"ConfirmBar__StyledConfirmBar",componentId:"sc-1m75upe-0"})(["&.confirm-bar{display:inline-flex;flex-direction:row-reverse;padding:20px 24px;transition:all 0.2s ease-in-out 0s;position:absolute;bottom:0px;right:0px;.confirm{margin-left:12px;}}"]),zs=()=>[{key:0,name:"basic",label:I18N.imockSharing.sharing_basic},{key:1,name:"visible",label:I18N.imockSharing.sharing_visible}];function us(){const e=(0,g.d4)(l.query.getProject),i=(0,g.d4)(l.query.getSettingPageType),c=(0,g.d4)(l.query.getSubSettingPageType),d=(0,g.d4)(l.query.getCurrentSharing),_=(0,g.d4)(l.query.getAdvancedSharingList),b="recommend",m=(0,g.wA)(),[N,j]=(0,s.useState)(c==="basic"?0:1),[B,G]=(0,s.useState)(d.link_name),[$,P]=(0,s.useState)(!1),ae=Fe=>{j(Fe)},oe=(0,s.useCallback)(async(Fe,Ye)=>{const{isCustom:rt}=Ye||{};m({type:l.entryKey["sharing:settingPage:advanced:confirm"],payload:{updatedKV:Fe,...rt!==void 0&&{isCustom:rt}}})},[m]),O=(0,s.useCallback)(()=>{m({type:l.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}}),i==="edit"&&$&&m({type:"sharing:advance:click:track"})},[m,i,$]),ne=(0,s.useCallback)((Fe,Ye)=>{m({type:l.entryKey["sharing:currentSharing:update"],payload:{updatedKV:Fe}}),i==="edit"&&(P(!0),oe(Fe,Ye))},[m,i,oe,P]),ie=()=>{const Fe=be.current.value;Fe===""?(m({type:l.entryKey["sharing:init"],payload:{sharingToast:I18N.Common.name_cannot_be_empty}}),Le(B)):(0,V.it)({value:Fe,currentSharing:d,sharingList:_})?(Le(B),m({type:l.entryKey["sharing:init"],payload:{sharingToast:I18N.imockSharing.sharing_name_repeate_wran}})):(G(Fe),ne({link_name:Fe}))},{isRenaming:ge,setIsRenaming:pe,handleKeyDown:fe,handleBlur:W,handleFocus:ye,inputRef:be,handleInput:xe,inputName:Te,setInputName:Le}=Ys({name:d.link_name,fBlur:ie}),Ge=()=>{pe(!0)};return(0,r.jsxs)(On,{children:[i==="create"?(0,r.jsx)("div",{className:"header",children:(0,r.jsx)("span",{children:I18N.imockSharing.sharing_create_title})}):(0,r.jsx)(Cn,{title:I18N.imockSharing.sharing_setting_title,handleClick:O}),(0,r.jsxs)("div",{className:"share-link-title",children:[(0,r.jsx)("div",{className:"title-text",children:I18N.SmartFill.title}),(0,r.jsx)("div",{className:X()("name-input",{"is-renaming":ge}),children:ge?(0,r.jsx)("form",{children:(0,r.jsx)("input",{value:Te,onFocus:ye,onChange:xe,ref:be,onBlur:W,onKeyPress:fe})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{onClick:Ge,children:B}),(0,r.jsx)(Bs.A,{size:20,name:"sharing/pen_20",className:"input-icon",onClick:Ge})]})})]}),(0,r.jsx)(js,{sharingData:d,updateSharing:ne}),(0,r.jsxs)("div",{className:"tabs",children:[(0,r.jsx)(as.A,{className:"sharing-nav",activeIndex:N,onTabChange:ae,layoutV9:b,children:zs().map(Fe=>{let{key:Ye,name:rt,...it}=Fe;return(0,r.jsx)(as.n,{...it,active:N===Ye,className:X()({visible:rt==="visible",basic:rt==="basic"})},Ye)})}),(0,r.jsx)("div",{})]}),N===0?(0,r.jsx)(po,{isOrgProject:e.is_org_project,sharingData:d,updateSharing:ne,settingPageType:i}):(0,r.jsx)(go,{sharingData:d,updateSharing:ne}),i==="create"&&(0,r.jsx)(fo,{handleConfirm:oe,handleCancel:O})]})}function yo(){return(0,g.d4)(l.query.getCurrentSharing).type==="default"?(0,r.jsx)(lo,{}):(0,r.jsx)(us,{})}const Xt=A.Ay.div.withConfig({displayName:"styles__StyledSharingToast",componentId:"sc-lv8qo5-0"})(["z-index:1;border-radius:4px;position:absolute;display:flex;align-items:center;padding:6px 10px;color:#FFF;font-size:14px;font-family:PingFang SC;font-style:normal;font-weight:400;line-height:24px;background:#454647;top:72px;left:50%;transform:translateX(-50%);svg{width:21px;height:21px;margin-right:4px;}"]),xo=(0,s.memo)(function(){const i=(0,g.d4)(l.query.getSharingToast),c=(0,g.wA)();return(0,s.useEffect)(()=>{setTimeout(()=>{c({type:l.entryKey["sharing:init"],payload:{sharingToast:""}})},1500)},[c]),i?(0,r.jsxs)(Xt,{children:[(0,r.jsx)(Ee.C,{name:"sharing/toast_waring",isColorPure:!0}),i]}):null}),bo=A.Ay.div.withConfig({displayName:"styles__StyledSharingQrCode",componentId:"sc-17am38q-0"})(["margin-top:70px;display:flex;align-items:center;justify-content:flex-start;flex-direction:column;height:calc(100% - ","px);p{margin-top:16px;color:","}"],we.gE,e=>e.theme.color_text_L3);var Zn=n(80582);const vo=A.Ay.div.withConfig({displayName:"styles__StyledQRcodeBox",componentId:"sc-1xfoi8f-0"})(["display:flex;justify-content:center;align-items:center;background-color:",";border-radius:8px;width:130px;height:130px;filter:",";border:1px solid ",";.qrcode{width:110px;height:110px;}&.qrcode-and-icon{.p-icon-box{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:24px;height:24px;overflow:hidden;background-color:#f2f2f3;border-radius:2px;img{display:block;width:100%;height:100%;}}}"],e=>e.theme.color_sharing_qrcode_bg,e=>e.theme.color_sharing_qrcode_filter,e=>e.theme.color_share_del_confirm_border_color);function Us(e){const{isQRcodePanel:i,text:c,project:d}=e,_=d.icon||null;return i?(0,r.jsxs)(vo,{className:"qrcode-and-icon",children:[(0,r.jsx)(Zn.A,{className:"qrcode",text:c,width:110,height:110}),_&&(0,r.jsx)("div",{className:"p-icon-box",children:(0,r.jsx)("img",{src:_})})]}):null}const _o=()=>{const e=(0,g.wA)(),i=(0,g.d4)(l.query.getCurrentSelectSharing),c=(0,g.d4)(l.query.getProject),d=(0,g.d4)(l.query.getHostCurrentScreen),_=d==null?void 0:d.cid,b=(0,s.useMemo)(()=>(0,V.O8)(c,i,_),[i,c,_]),m=(0,s.useCallback)(()=>{e({type:l.entryKey["sharing:topPageIndex:jump"],payload:{topPageIndex:"edit"}})},[e]);return(0,r.jsxs)(s.Fragment,{children:[(0,r.jsx)(Cn,{title:I18N.imockSharing.qr_code,handleClick:m}),(0,r.jsxs)(bo,{children:[(0,r.jsx)(Us,{isQRcodePanel:!0,project:c,text:b}),(0,r.jsx)("p",{children:I18N.imockSharing.qr_tips})]})]})},wo=(0,s.memo)(_o),ps=()=>{var e;const i=(0,g.wA)(),c=(0,g.d4)(l.query.getHostSharingData),d=(0,g.d4)(l.query.getProject),_=d==null?void 0:d.is_org_project,{link_name:b,view_access:m}=c,N=(0,g.d4)(l.query.getUser),j=(0,g.d4)(l.query.getHostType),B=(0,g.d4)(l.query.getIsEditMode),G=j==="iframe"||B,$=(0,g.d4)(l.query.getCanEditByUser),P=(0,g.d4)(l.query.getHostCurrentScreen),ae=P==null?void 0:P.cid,{isURLCopied:oe,handleCopyURL:O}=ts({project:d,sharing:c,user:N}),ne=G&&$,ie=(0,s.useMemo)(()=>[{name:"sharing/QRcode_icon",toolTipName:I18N.imockSharing.qr_code,className:Rt.QRcode,tempDisabled:!1,isBorder:!0},{name:"sharing/embed_icon",toolTipName:I18N.imockSharing.insert,className:Rt.embed,tempDisabled:!1,isBorder:!0}],[]),ge=(0,s.useCallback)(()=>{const fe=(0,V.O8)(d,c,ae);(0,wn.$)(fe),O(),i({type:l.entryKey["sharing:function:track"],payload:{operation:"\u9AD8\u7EA7\u5206\u4EAB\u590D\u5236\u94FE\u63A5",viewMode:c.device_model,isDefault:!1}})},[i,c,O,ae,d]),pe=(0,s.useCallback)((fe,W)=>{if(!(fe!=null&&fe.currentTarget))return;const ye=c,{device_model:be}=c;switch(W){case Rt.QRcode:fe.stopPropagation(),i({type:l.entryKey["sharing:init"],payload:{topPageIndex:"qrCode",currentSelectSharing:ye}}),i({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u626B\u7801",viewMode:be,isDefault:!1}});break;case Rt.embed:i({type:l.entryKey["sharing:init"],payload:{topPageIndex:"embed",currentSelectSharing:ye}}),i({type:l.entryKey["sharing:function:track"],payload:{operation:"\u70B9\u51FB\u5D4C\u5165\u4E09\u65B9",viewMode:be,isDefault:!1}});break;default:break}},[i,c]);return(0,r.jsxs)(os,{children:[(0,r.jsxs)("div",{className:"default-top",children:[(0,r.jsx)("div",{className:"switch-content",children:b}),(0,r.jsxs)(mn,{className:X()("item-center","default-link",!ne&&"userNoEdit"),children:[(0,r.jsx)("div",{className:X()("left","sharingV2-click-visible",{"normal-hover":!0},!_&&"can-not-edit"),children:(0,r.jsxs)("div",{className:"view-access-select",children:[(0,r.jsx)("div",{className:"chore",children:I18N.imockSharing.permission_access}),(0,r.jsx)("div",{className:"access-name",children:(e=I18N.imockSharing[we.nQ[m]])!=null?e:"missing data"}),_&&(0,r.jsx)(Ee.C,{size:24,name:"sharing/dropdown_24"})]})}),(0,r.jsxs)(tn,{type:"button",className:X()("copy","sharingV2-click-visible","url-copy-button","is-state-"+Number(oe)),onClick:ge,children:[(0,r.jsx)("span",{className:"state state-0",children:c.password&&c.password.length>0?I18N.imockSharing.copy_url_pwd:I18N.imockSharing.copy_url}),(0,r.jsx)("span",{className:"state state-1",children:(0,r.jsx)(Ee.C,{name:"new_replace/check",className:"copy-check"})})]})]})]}),(0,r.jsx)("div",{className:"default-bottom",children:(0,r.jsxs)("div",{className:"bottom-left",children:[(0,r.jsx)("div",{children:I18N.imockSharing.share_mode}),ie.map((fe,W)=>(0,r.jsx)(_n,{className:X()("share-icon",fe.className),name:fe.name,type:fe.className,toolTipName:fe.toolTipName,onClick:pe,tempDisabled:fe.tempDisabled,isColorPure:fe.isColorPure,isBorder:fe.isBorder,size:24},W))]})})]})},So=(0,s.memo)(ps),Co=()=>{const e=(0,g.d4)(l.query.getTopPageIndex),i=(0,g.d4)(l.query.getTabIndex),c=(0,g.d4)(l.query.getSharingToast),d=(0,g.d4)(l.query.getHostSharingData),_=!d||d.type==="default",b=(0,g.d4)(l.query.getProject),m=(0,g.d4)(l.query.getIsEditMode),j=(0,g.d4)(l.query.getHostType)==="iframe"||m,[B,G]=(0,s.useState)(d);return(0,s.useEffect)(()=>{const{sharing:$}=(0,J.Y5)(b);G($)},[d,b]),(0,r.jsxs)(so,{children:[e==="edit"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Zt,{}),i===0?!j&&!_?(0,r.jsx)(So,{}):B?(0,r.jsx)(no,{defaultShare:B}):(0,r.jsx)(r.Fragment,{}):(0,r.jsx)(ss,{})]}),e==="embed"&&(0,r.jsx)(Et,{}),e==="setting"&&(0,r.jsx)(yo,{}),e==="qrCode"&&(0,r.jsx)(wo,{}),c&&(0,r.jsx)(xo,{})]})},xn=(0,s.memo)(Co),Kn=/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z0-9-]{2,63}$/i,Pn=/^(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[0-35-9]\d{2}|4(?:0\d|1[0-2]|9\d))|9[0-35-9]\d{2}|6[2567]\d{2}|4[579]\d{2})\d{6}$/;var hs=n(46795);const ms=async(e,i)=>{try{let d=null;return e?d=await(0,hs.DE)("/api/dashboard/v4/users/search?email="+e):i&&(d=await(0,hs.DE)("/api/dashboard/v4/users/search?mobile="+i)),d}catch(d){var c;return d!=null&&(c=d.message)!=null&&c.includes("failed with 404")?{}:null}};var gs=n(29584);const Mo=(0,A.Ay)(gs.A).withConfig({displayName:"styles__StyledPermissionTipDropdown",componentId:"sc-1ofnwys-0"})(["display:flex;align-items:center;justify-content:center;line-height:14px;button{.permisison-tip-icon{margin:5px 6px 0 6px;&:hover{path{fill:","}}}}"],e=>e.theme.color_project_access_tip_hover),fs=(0,A.DU)(['#IBOT_DROPDOWN_MENU_ROOT{.DropdownMenuBase{.permission-tip{.content{min-height:fit-content;padding:0;background-color:#333;border:1px solid #4f5052;.multi-line{padding:14px 14px 20px 14px;p,ul{padding:0;padding-left:3px;}&::before{content:"";width:0;height:0;border:11px solid transparent;border-bottom-color:#4F5052;position:absolute;top:-1px;left:',';margin-top:-18px;border-bottom-width:9px;}&::after{content:"";width:0;height:0;border:9px solid transparent;border-bottom-color:#333;position:absolute;top:3px;left:',";margin-top:-18px;border-bottom-width:7px;}}.single-line{padding:4px 12px;p{margin:0;padding:0;}}h4{font-weight:500;font-size:14px;line-height:16px;margin:0;margin-bottom:10px;}ul{text-align:justify;list-style:none;li{list-style:none;margin-bottom:8px;&:last-child{margin-bottom:0px;}}}.desc{font-weight:400;font-size:12px;line-height:18px;white-space:nowrap;.strong{font-weight:500;}.link{margin-left:4px;text-align:left;color:#1684fc;cursor:pointer;&:hover{text-decoration:underline;}}}.extra{white-space:nowrap;margin:12px 0 8px;font-weight:400;font-size:12px;color:rgba(255,255,255,0.5);}}}}}"],e=>e.isOnlyMemberManager?"66px":"88px",e=>e.isOnlyMemberManager?"68px":"90px");let gn=function(e){return e.OrgSpace="orgspace-permission-tip",e.OrgSpaceLimiter="orgspace-limiter-permission-tip",e.RootFolder="root-folder-permission-tip",e.SubFolder="sub-folder-permission-tip",e.Project="project-permission-tip",e}({});const No=e=>{const{tipType:i,position:c="center",className:d,isOnlyMemberManager:_=!1}=e,[b,m]=(0,s.useState)({}),N=(0,g.d4)(Ie.il);(0,s.useEffect)(()=>{const B=document.querySelector(".sharingBoxV2 ");if(B){const{left:G,top:$}=B.getBoundingClientRect();m({left:Number(G)+13,top:Number($)+29})}},[]);const j=()=>{switch(i){case gn.Project:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsxs)("h4",{children:["\u{1F537} ",I18N.imockSharing.permissions]}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_manager_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_can_edit,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_can_edit_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_only_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_only_view_file]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_ban_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_ban_view_file]})]}),(0,r.jsx)("p",{className:"extra",dangerouslySetInnerHTML:{__html:I18N.imockSharing.permissions_automatically_sync_desc}})]});case gn.SubFolder:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("p",{className:"extra",children:["\u6839\u6587\u4EF6\u5939\u6210\u5458\u53CA\u6743\u9650\u81EA\u52A8\u540C\u6B65\u81F3\u7EC4\u5185\uFF0C\u82E5\u6709\u8C03\u6574\uFF0C",(0,r.jsx)("br",{}),"\u8BF7\u524D\u5F80\u6839\u6587\u4EF6\u5939\u8FDB\u884C\u6210\u5458\u53CA\u6743\u9650\u8C03\u6574"]})]});case gn.RootFolder:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_manager_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_can_edit,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_can_edit_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_only_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_only_view_folder]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_ban_view,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_ban_view_folder]})]})]});case gn.OrgSpace:return(0,r.jsxs)("section",{className:"multi-line",children:[(0,r.jsx)("h4",{children:I18N.imockSharing.permissions}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_manager,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_manager_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_collaboration_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_collaboration_member_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_review_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_review_member_desc]}),(0,r.jsxs)("li",{className:"desc",children:[(0,r.jsxs)("strong",{children:[I18N.imockSharing.permissions_space_unregistered_member,I18N.imockSharing.common_colon]}),I18N.imockSharing.permissions_space_unregistered_member_desc]})]})]});case gn.OrgSpaceLimiter:return(0,r.jsx)("section",{className:"single-line",children:(0,r.jsxs)("p",{className:"desc",children:["\u8BE5\u6210\u5458\u53EA\u5BF9\u56E2\u961F\u5185\u6307\u5B9A\u6587\u4EF6/\u6587\u4EF6\u5939\u53EF\u67E5\u770B/\u7F16\u8F91 ",(0,r.jsx)("a",{href:"/hc/articles/222",className:"link",children:" \u67E5\u770B\u8BE6\u60C5"})]})});default:break}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Mo,{opener:(0,r.jsx)("svg",{className:"permisison-tip-icon",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.33341 7.00008C2.33341 4.42275 4.42275 2.33341 7.00008 2.33341C9.57741 2.33341 11.6667 4.42275 11.6667 7.00008C11.6667 9.57741 9.57741 11.6667 7.00008 11.6667C4.42275 11.6667 2.33341 9.57741 2.33341 7.00008ZM7.00008 1.16675C3.77842 1.16675 1.16675 3.77842 1.16675 7.00008C1.16675 10.2217 3.77842 12.8334 7.00008 12.8334C10.2217 12.8334 12.8334 10.2217 12.8334 7.00008C12.8334 3.77842 10.2217 1.16675 7.00008 1.16675ZM6.00842 5.82717V5.69267C6.00912 5.53262 6.06636 5.35509 6.19406 5.22162C6.31201 5.09835 6.54189 4.95841 6.99673 4.95841C7.42386 4.95841 7.72087 5.16234 7.87234 5.39386C8.0341 5.6411 8.00223 5.84856 7.92545 5.9522C7.82971 6.08146 7.71083 6.20085 7.56715 6.33019C7.52345 6.36954 7.46957 6.41642 7.4118 6.46669L7.41179 6.4667C7.30849 6.55659 7.19276 6.6573 7.10047 6.74511C6.77279 7.05691 6.41686 7.49208 6.41686 8.16675L6.41875 8.46216L7.5854 8.45467L7.58354 8.16515C7.58397 7.96563 7.66576 7.81765 7.90469 7.5903C7.985 7.51388 8.0572 7.45142 8.1398 7.37995L8.13984 7.37992C8.20093 7.32707 8.26773 7.26928 8.34772 7.19727C8.51407 7.04752 8.69919 6.8677 8.86295 6.64662C9.30517 6.0496 9.20176 5.29489 8.84863 4.75513C8.48521 4.19965 7.82399 3.79175 6.99673 3.79175C6.28084 3.79175 5.72583 4.02342 5.35109 4.41508C4.98633 4.79631 4.84308 5.27667 4.84175 5.68971V5.82717H6.00842ZM6.41874 9.04175V10.2107H7.58541V9.04175H6.41874Z",fill:"#999999"})}),mode:"dark",menuX:c,shouldOpenOnHover:!0,hoverDelay:100,menuClassName:X()("permission-tip",d&&d),menu:j(),menuBaseStyle:b}),(0,r.jsx)(fs,{isOnlyMemberManager:_})]})};var Ws=n(99739);const Io=A.Ay.div.withConfig({displayName:"styles__StyledReadOnlyOption",componentId:"sc-1j0j1bo-0"})(["min-width:65px;text-align:start;padding-left:10px;cursor:default;&:lang(en){width:auto;}&.disabled{cursor:not-allowed;}.label{color:#9EA9BC;font-size:12px;}"]),Hs=(0,A.Ay)(Ws.Ay).withConfig({displayName:"styles__StyledPermissionSelector",componentId:"sc-1j0j1bo-1"})(["min-width:65px;&:lang(en){width:auto;}&.is-disabled{opacity:1;.caret{display:none;}button{span{color:#9EA9BC;}}}&.is-open .caret{transform:rotate(180deg);svg{transform:translateY(-3px) rotate(-45deg);}}button{text-align:right;span{color:#35445D;font-size:12px;}}.caret{margin:0 0 0 6px;transition:transform .2s;svg{width:6px;height:6px;margin:1px;border:solid 1px #35445D;border-top-color:transparent;border-right-color:transparent;transform:translateY(0) rotate(-45deg);path{opacity:0;}}}"]),jo=(0,A.DU)([".permission-select.CheckSelectMenu{padding:8px;border-radius:8px;border:1px solid ",";color:",";background:",";box-shadow:",";&:lang(en){min-width:136px;}&:not(.is-empty){padding:8px;}.SelectOption{height:24px;margin-bottom:2px;padding:0;color:",";border-radius:4px;line-height:24px;&:last-child{margin-bottom:0;}&.is-active,&:not(.empty-msg):not(.is-disabled):hover{background-color:",";color:",";}&:hover{color:",";}> span.Ellipsis{min-width:80px;padding:0 10px;margin-left:20px;&:lang(en){width:calc(100% - 22px);}}> .svg-icon{position:absolute;margin:0 0 0 10px;path{fill:",";}}&[data-value=\"__mb_delete_permission\"]{position:relative;margin-top:9px;&::before{position:absolute;top:-5px;right:0;left:0;border-top:solid 1px rgba(255,255,255,.1);content:'';}}}}"],e=>e.theme.color_bg_border_01,e=>e.theme.color_text_L1,e=>e.theme.color_bg_white,e=>e.theme.shadow_m,e=>e.theme.color_text_L1,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1);var Vs=n(23485);const Gs=e=>{let i=[],c={};switch(e){case je.W.OrgSpace:i=Q,c=Me.FU;break;case je.W.Folder:i=K,c=Me.lY;break;case je.W.Project:i=me,c=Me.au;break;default:break}return i.map(d=>({...d,permission:(0,H.pf)(d.value,c)}))},Eo=(e,i)=>{let c={};switch(e){case je.W.OrgSpace:return i;case je.W.Folder:c=ke;break;case je.W.Project:c=Ce;break;default:return i}return c[i]||i},ko=e=>{switch(e){case je.W.OrgSpace:return I18N.imockSharing.permission_no_right_project;case je.W.Folder:return I18N.imockSharing.permission_no_right_folder;case je.W.Project:return I18N.imockSharing.permission_no_right_project;default:return I18N.imockSharing.permission_no_right}},To=e=>{const{value:i,optionList:c,handleChange:d}=e,_=(0,s.useRef)(null),[b,m]=(0,s.useState)(!0),N=c.filter(B=>B.value===i),j=(0,s.useCallback)(()=>{_.current&&_.current.close()},[]);return(0,s.useEffect)(()=>(document.body.addEventListener("wheel",j),()=>{document.body.removeEventListener("wheel",j)}),[j]),(0,s.useEffect)(()=>{const B=setTimeout(()=>{m(!1)},1e3);return()=>{clearTimeout(B)}},[]),b?(0,r.jsx)(Hs,{unstyled:!0,ref:_,placeholder:I18N.imockSharing.submit,menuTheme:"check",value:i,menuX:"right",onChange:d,optionList:N,menuClassName:"permission-select"}):(0,r.jsx)(Hs,{unstyled:!0,ref:_,placeholder:I18N.imockSharing.submit,menuTheme:"check",value:i,menuX:"right",onChange:d,optionList:c,menuClassName:"permission-select"})},Lo=(0,s.memo)(To);var ys=n(67029);const Zs=e=>{const i={pt_total_seats:10,pt_bindings:[],ptlt_total_seats:0,ptlt_bindings:[],bx_total_seats:10,bx_bindings:[]},{pt_total_seats:c,pt_bindings:d,ptlt_total_seats:_,ptlt_bindings:b,bx_total_seats:m,bx_bindings:N}=e||i;return{plTotalSeats:_,pTotalSeats:c,pBindings:d,plBindings:b,bTotalSeats:m,bBindings:N}},xs=(e,i)=>{const{pBindings:c,plBindings:d,bBindings:_}=Zs(e),b=d.includes(i),m=c.includes(i),N=_.includes(i);return{isPLSeat:b,isPSeat:m,isBSeat:N}},$n=(e,i)=>{if(!e)return{protoSeat:!1,bomxSeat:!1};if(e.otype==="personal")return{protoSeat:!0,bomxSeat:!0};const c=ls.t.InitialOrg(e),d=c.planSdk.getIsDefaultFreeOrg(),_=c.planSdk.getOrgStatus()===ys._E.Trial;if(d||_)return{protoSeat:!0,bomxSeat:!0};const{pTotalSeats:b,bTotalSeats:m,plTotalSeats:N}=Zs(e),{isPLSeat:j,isPSeat:B,isBSeat:G}=xs(e,i);return{protoSeat:b>0||N>0?j||B:!0,bomxSeat:m>0?G:!0}},Ao=(e,i)=>{const{isSelf:c,userCurrentSpacePermission:d,userCurrentScopePermisson:_,memberCurrentScopePermisson:b,memberTopScopePermisson:m,memberOrgPermisson:N,memberIsSpaceManager:j}=i;return e.map(G=>{let{value:$,label:P,permission:ae}=G,oe=!1;const O=ae.roleName.includes("ban_viewer");return d.isManager&&c?oe=O:_.isMember?_.level<b.level||_.level<ae.level?oe=!0:(!m.isMember&&m.isViewer&&!N.isMember&&ae.isMember&&(oe=!0),O&&(oe=!_.isManager||c||j)):oe=!0,Object.assign({},{value:$,label:I18N.imockSharing[P]||P},{isDisabled:oe})})},Oo=e=>{const{readOnlyTooltip:i,isReset:c=!1,member:d,isOnlyOneManager:_,permissionScope:b,onPermissionChange:m,currentOrg:N}=e;let{readOnly:j}=e,B=j,G=i;const $=Gs(b),{permissionMap:P}=d,{memberCurrentScopePermisson:ae,userCurrentScopePermisson:oe,userCurrentSpacePermission:O,currentScopeRoleName:ne,isSelf:ie,userIsSpaceLimiter:ge,memberIsSpaceLimiter:pe}=P;let fe=Ao($,P);b===je.W.OrgSpace&&!pe&&(fe=fe.filter(be=>be.value!=="space_limiter")),(b===je.W.OrgSpace||pe)&&!ae.isUnknown&&!ae.isInherited&&!c&&(ie&&!ae.isUnknown||oe.isManager&&fe.push({value:tt,label:I18N.imockSharing.remove,isDisabled:!1})),!B&&b===je.W.OrgSpace&&ge&&!c&&(j=!0,B=!0),!B&&([je.W.Project,je.W.Folder].includes(b)||ae.isViewer)&&!c&&(oe.isMember?oe.level<ae.level?(B=!0,G="\u65E0\u6743\u66F4\u6539\u6743\u9650\u9AD8\u7684\u6210\u5458\u6743\u9650"):ae.isManager&&_?(B=!0,G=ko(b)):(b===je.W.OrgSpace&&!oe.isManager||[je.W.Project,je.W.Folder].includes(b)&&!oe.isManager&&ae.isBanned)&&(B=!0):O!=null&&O.isManager&&ie||(B=!0));let W=Eo(b,ne);c&&(W="");const ye=be=>{B||m(d,be)};if(N){const{protoSeat:be}=$n(N,Number(d.user_id)),Te=be?"":"(\u65E0\u5E2D\u4F4D)";fe.map(Le=>{(Le.value==="project_manager"||Le.value==="project_member")&&(Le.label+=Te)})}if(B||j){const be=fe.find(xe=>xe.value===W);return be?(0,r.jsx)(Io,{className:X()("read-only-option",!j&&B&&"disabled"),children:(0,r.jsx)(Vs.A,{content:G,position:"bottom",children:(0,r.jsx)("span",{className:"label",children:be.label})})}):null}return(0,r.jsx)(Lo,{value:W,handleChange:ye,optionList:fe})},a=(0,s.memo)(Oo),t=(0,A.DU)(["#IBOT_DROPDOWN_MENU_ROOT{.add-outer-member-tip{.content{min-height:fit-content;padding:2px 8px;background-color:#333;.desc{white-space:nowrap;.copy-text{text-decoration:underline;cursor:pointer;}}}}}"]),o=A.Ay.section.withConfig({displayName:"styles__StyledMemberList",componentId:"sc-188anjy-0"})(['margin-bottom:10px;width:406px;&.sharing{.members > .enterTip > span > div{display:flex;flex-direction:column;align-items:center;justify-content:center;}}&[data-expanded="false"]{.members{display:none;}.caption{margin-bottom:15px;}}&+.member-list{margin-top:10px;}.caption{position:relative;display:flex;align-items:center;justify-content:flex-start;padding:0;font-weight:400;font-size:14px;line-height:20px;color:',";span{color:",";}.invite-item{display:flex;align-items:center;height:32px;text-align:center;font-family:PingFang SC;width:fit-content;border-radius:6px;font-weight:500;font-size:14px;color:",";cursor:pointer;position:absolute;right:20px;z-index:1;&:hover{color:",";}&:active{color:",";}.svg-icon{width:32px;color:inherit;& > *{fill:currentColor;}}}.btn-add-collaborator{position:absolute;right:0;display:flex;align-items:center;color:#1883FB;.svg-icon{margin-right:4px;width:12px;height:12px;}}.btn-toggle-expand{margin-left:10px;user-select:none;color:#1883FB;cursor:pointer;&.with-red-point{position:relative;&::after{content:'';background:rgb(228,33,33);position:absolute;width:6px;height:6px;top:-2px;left:100%;border-radius:50%;}}}}.members{.item{display:flex;align-items:center;justify-content:space-between;padding:10px 20px;&:hover{background:",";border-radius:4px;}.title,.account,.Select{button{font-weight:500;font-size:14px;line-height:20px;letter-spacing:1.2px;color:#545F6F;.Ellipsis{max-width:100px;&:lang(en){max-width:none;}}}}.title{flex:1;display:flex;justify-content:flex-start;align-items:center;.avatar{width:30px;height:30px;overflow:hidden;border-radius:50%;margin-right:12px;border:1px solid ",";&.avater-name{text-align:center;background:rgb(22,133,252);justify-content:center;color:white;font-size:14px;text-align:center;display:flex;align-items:center;}}.name{width:120px;overflow:hidden;color:#1f292e;white-space:nowrap;text-overflow:ellipsis;}}.account{flex:1;width:160px;color:#999;}.register{display:inline-block;text-align:center;height:22px;line-height:22px;border:1px solid rgb(41,141,248);box-sizing:border-box;border-radius:4px;width:48px;color:rgb(41,141,248);font-size:12px;font-weight:400;margin-left:8px;position:relative;color:red;&:lang(en){display:block;background:#F1F8FF;border:none;color:#1684FC;padding:3px 4px;width:max-content;margin-left:10px;font-weight:500;font-size:12px;line-height:12px;height:auto;}}}}.invite{.item{.title{.name{width:150px;&:lang(en){width:max-content;flex:1;display:flex;align-items:center;}}}.account{width:150px;display:inline-block;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;word-break:break-all;&:lang(en){margin:10px;}}}.addOuter{color:#999999;width:108px;text-align:right;.add{font-size:12px;margin-right:2px;}button{color:rgb(91,107,115);position:relative;top:2px;left:4px;}}.enterTip{font-size:12px;color:",";}}.inviteJoin{border-radius:4px;background:#298DF8;color:#fff;font-size:12px;font-weight:400;padding:7px 11px;cursor:pointer;word-break:keep-all;&:lang(en){padding:5px 11px;}}&.dark{.members .item .register{&:lang(en){background:rgba(22,132,252,0.1);}}}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover,e=>e.theme.color_text_link_hover,e=>e.theme.color_btn_secondary_hover,e=>e.theme.color_share_member__border_color,e=>e.theme.color_AI_Text_Auto_fill_path_hover);var h=n(18824);function F(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var ce=n(7043),Oe=Number.isNaN||function(i){return typeof i=="number"&&i!==i};function De(e,i){return!!(e===i||Oe(e)&&Oe(i))}function Ue(e,i){if(e.length!==i.length)return!1;for(var c=0;c<e.length;c++)if(!De(e[c],i[c]))return!1;return!0}function Ke(e,i){i===void 0&&(i=Ue);var c,d=[],_,b=!1;function m(){for(var N=[],j=0;j<arguments.length;j++)N[j]=arguments[j];return b&&c===this&&i(N,d)||(_=e.apply(this,N),b=!0,c=this,d=N),_}return m}const Re=Ke;var Xe=typeof performance=="object"&&typeof performance.now=="function",_e=Xe?function(){return performance.now()}:function(){return Date.now()};function Se(e){cancelAnimationFrame(e.id)}function We(e,i){var c=_e();function d(){_e()-c>=i?e.call(null):_.id=requestAnimationFrame(d)}var _={id:requestAnimationFrame(d)};return _}var Qe=-1;function lt(e){if(e===void 0&&(e=!1),Qe===-1||e){var i=document.createElement("div"),c=i.style;c.width="50px",c.height="50px",c.overflow="scroll",document.body.appendChild(i),Qe=i.offsetWidth-i.clientWidth,document.body.removeChild(i)}return Qe}var Nt=null;function wt(e){if(e===void 0&&(e=!1),Nt===null||e){var i=document.createElement("div"),c=i.style;c.width="50px",c.height="50px",c.overflow="scroll",c.direction="rtl";var d=document.createElement("div"),_=d.style;return _.width="100px",_.height="100px",i.appendChild(d),document.body.appendChild(i),i.scrollLeft>0?Nt="positive-descending":(i.scrollLeft=1,i.scrollLeft===0?Nt="negative":Nt="positive-ascending"),document.body.removeChild(i),Nt}return Nt}var Ht=150,zt=function(i){var c=i.columnIndex,d=i.data,_=i.rowIndex;return _+":"+c},qt=null,ct=null,Ze=null;function ot(e){var i,c=e.getColumnOffset,d=e.getColumnStartIndexForOffset,_=e.getColumnStopIndexForStartIndex,b=e.getColumnWidth,m=e.getEstimatedTotalHeight,N=e.getEstimatedTotalWidth,j=e.getOffsetForColumnAndAlignment,B=e.getOffsetForRowAndAlignment,G=e.getRowHeight,$=e.getRowOffset,P=e.getRowStartIndexForOffset,ae=e.getRowStopIndexForStartIndex,oe=e.initInstanceProps,O=e.shouldResetStyleCacheOnItemSizeChange,ne=e.validateProps;return i=function(ie){(0,ce.A)(ge,ie);function ge(fe){var W;return W=ie.call(this,fe)||this,W._instanceProps=oe(W.props,F(W)),W._resetIsScrollingTimeoutId=null,W._outerRef=void 0,W.state={instance:F(W),isScrolling:!1,horizontalScrollDirection:"forward",scrollLeft:typeof W.props.initialScrollLeft=="number"?W.props.initialScrollLeft:0,scrollTop:typeof W.props.initialScrollTop=="number"?W.props.initialScrollTop:0,scrollUpdateWasRequested:!1,verticalScrollDirection:"forward"},W._callOnItemsRendered=void 0,W._callOnItemsRendered=Re(function(ye,be,xe,Te,Le,Ge,Fe,Ye){return W.props.onItemsRendered({overscanColumnStartIndex:ye,overscanColumnStopIndex:be,overscanRowStartIndex:xe,overscanRowStopIndex:Te,visibleColumnStartIndex:Le,visibleColumnStopIndex:Ge,visibleRowStartIndex:Fe,visibleRowStopIndex:Ye})}),W._callOnScroll=void 0,W._callOnScroll=Re(function(ye,be,xe,Te,Le){return W.props.onScroll({horizontalScrollDirection:xe,scrollLeft:ye,scrollTop:be,verticalScrollDirection:Te,scrollUpdateWasRequested:Le})}),W._getItemStyle=void 0,W._getItemStyle=function(ye,be){var xe=W.props,Te=xe.columnWidth,Le=xe.direction,Ge=xe.rowHeight,Fe=W._getItemStyleCache(O&&Te,O&&Le,O&&Ge),Ye=ye+":"+be,rt;if(Fe.hasOwnProperty(Ye))rt=Fe[Ye];else{var it=c(W.props,be,W._instanceProps),at=Le==="rtl";Fe[Ye]=rt={position:"absolute",left:at?void 0:it,right:at?it:void 0,top:$(W.props,ye,W._instanceProps),height:G(W.props,ye,W._instanceProps),width:b(W.props,be,W._instanceProps)}}return rt},W._getItemStyleCache=void 0,W._getItemStyleCache=Re(function(ye,be,xe){return{}}),W._onScroll=function(ye){var be=ye.currentTarget,xe=be.clientHeight,Te=be.clientWidth,Le=be.scrollLeft,Ge=be.scrollTop,Fe=be.scrollHeight,Ye=be.scrollWidth;W.setState(function(rt){if(rt.scrollLeft===Le&&rt.scrollTop===Ge)return null;var it=W.props.direction,at=Le;if(it==="rtl")switch(wt()){case"negative":at=-Le;break;case"positive-descending":at=Ye-Te-Le;break}at=Math.max(0,Math.min(at,Ye-Te));var Mt=Math.max(0,Math.min(Ge,Fe-xe));return{isScrolling:!0,horizontalScrollDirection:rt.scrollLeft<Le?"forward":"backward",scrollLeft:at,scrollTop:Mt,verticalScrollDirection:rt.scrollTop<Ge?"forward":"backward",scrollUpdateWasRequested:!1}},W._resetIsScrollingDebounced)},W._outerRefSetter=function(ye){var be=W.props.outerRef;W._outerRef=ye,typeof be=="function"?be(ye):be!=null&&typeof be=="object"&&be.hasOwnProperty("current")&&(be.current=ye)},W._resetIsScrollingDebounced=function(){W._resetIsScrollingTimeoutId!==null&&Se(W._resetIsScrollingTimeoutId),W._resetIsScrollingTimeoutId=We(W._resetIsScrolling,Ht)},W._resetIsScrolling=function(){W._resetIsScrollingTimeoutId=null,W.setState({isScrolling:!1},function(){W._getItemStyleCache(-1)})},W}ge.getDerivedStateFromProps=function(W,ye){return et(W,ye),ne(W),null};var pe=ge.prototype;return pe.scrollTo=function(W){var ye=W.scrollLeft,be=W.scrollTop;ye!==void 0&&(ye=Math.max(0,ye)),be!==void 0&&(be=Math.max(0,be)),this.setState(function(xe){return ye===void 0&&(ye=xe.scrollLeft),be===void 0&&(be=xe.scrollTop),xe.scrollLeft===ye&&xe.scrollTop===be?null:{horizontalScrollDirection:xe.scrollLeft<ye?"forward":"backward",scrollLeft:ye,scrollTop:be,scrollUpdateWasRequested:!0,verticalScrollDirection:xe.scrollTop<be?"forward":"backward"}},this._resetIsScrollingDebounced)},pe.scrollToItem=function(W){var ye=W.align,be=ye===void 0?"auto":ye,xe=W.columnIndex,Te=W.rowIndex,Le=this.props,Ge=Le.columnCount,Fe=Le.height,Ye=Le.rowCount,rt=Le.width,it=this.state,at=it.scrollLeft,Mt=it.scrollTop,kt=lt();xe!==void 0&&(xe=Math.max(0,Math.min(xe,Ge-1))),Te!==void 0&&(Te=Math.max(0,Math.min(Te,Ye-1)));var Ot=m(this.props,this._instanceProps),Dt=N(this.props,this._instanceProps),Gt=Dt>rt?kt:0,gt=Ot>Fe?kt:0;this.scrollTo({scrollLeft:xe!==void 0?j(this.props,xe,be,at,this._instanceProps,gt):at,scrollTop:Te!==void 0?B(this.props,Te,be,Mt,this._instanceProps,Gt):Mt})},pe.componentDidMount=function(){var W=this.props,ye=W.initialScrollLeft,be=W.initialScrollTop;if(this._outerRef!=null){var xe=this._outerRef;typeof ye=="number"&&(xe.scrollLeft=ye),typeof be=="number"&&(xe.scrollTop=be)}this._callPropsCallbacks()},pe.componentDidUpdate=function(){var W=this.props.direction,ye=this.state,be=ye.scrollLeft,xe=ye.scrollTop,Te=ye.scrollUpdateWasRequested;if(Te&&this._outerRef!=null){var Le=this._outerRef;if(W==="rtl")switch(wt()){case"negative":Le.scrollLeft=-be;break;case"positive-ascending":Le.scrollLeft=be;break;default:var Ge=Le.clientWidth,Fe=Le.scrollWidth;Le.scrollLeft=Fe-Ge-be;break}else Le.scrollLeft=Math.max(0,be);Le.scrollTop=Math.max(0,xe)}this._callPropsCallbacks()},pe.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&Se(this._resetIsScrollingTimeoutId)},pe.render=function(){var W=this.props,ye=W.children,be=W.className,xe=W.columnCount,Te=W.direction,Le=W.height,Ge=W.innerRef,Fe=W.innerElementType,Ye=W.innerTagName,rt=W.itemData,it=W.itemKey,at=it===void 0?zt:it,Mt=W.outerElementType,kt=W.outerTagName,Ot=W.rowCount,Dt=W.style,Gt=W.useIsScrolling,gt=W.width,Tt=this.state.isScrolling,Pt=this._getHorizontalRangeToRender(),Qt=Pt[0],ft=Pt[1],vt=this._getVerticalRangeToRender(),on=vt[0],Xn=vt[1],qn=[];if(xe>0&&Ot)for(var vn=on;vn<=Xn;vn++)for(var rn=Qt;rn<=ft;rn++)qn.push((0,s.createElement)(ye,{columnIndex:rn,data:rt,isScrolling:Gt?Tt:void 0,key:at({columnIndex:rn,data:rt,rowIndex:vn}),rowIndex:vn,style:this._getItemStyle(vn,rn)}));var Kt=m(this.props,this._instanceProps),nt=N(this.props,this._instanceProps);return(0,s.createElement)(Mt||kt||"div",{className:be,onScroll:this._onScroll,ref:this._outerRefSetter,style:(0,h.A)({position:"relative",height:Le,width:gt,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:Te},Dt)},(0,s.createElement)(Fe||Ye||"div",{children:qn,ref:Ge,style:{height:Kt,pointerEvents:Tt?"none":void 0,width:nt}}))},pe._callPropsCallbacks=function(){var W=this.props,ye=W.columnCount,be=W.onItemsRendered,xe=W.onScroll,Te=W.rowCount;if(typeof be=="function"&&ye>0&&Te>0){var Le=this._getHorizontalRangeToRender(),Ge=Le[0],Fe=Le[1],Ye=Le[2],rt=Le[3],it=this._getVerticalRangeToRender(),at=it[0],Mt=it[1],kt=it[2],Ot=it[3];this._callOnItemsRendered(Ge,Fe,at,Mt,Ye,rt,kt,Ot)}if(typeof xe=="function"){var Dt=this.state,Gt=Dt.horizontalScrollDirection,gt=Dt.scrollLeft,Tt=Dt.scrollTop,Pt=Dt.scrollUpdateWasRequested,Qt=Dt.verticalScrollDirection;this._callOnScroll(gt,Tt,Gt,Qt,Pt)}},pe._getHorizontalRangeToRender=function(){var W=this.props,ye=W.columnCount,be=W.overscanColumnCount,xe=W.overscanColumnsCount,Te=W.overscanCount,Le=W.rowCount,Ge=this.state,Fe=Ge.horizontalScrollDirection,Ye=Ge.isScrolling,rt=Ge.scrollLeft,it=be||xe||Te||1;if(ye===0||Le===0)return[0,0,0,0];var at=d(this.props,rt,this._instanceProps),Mt=_(this.props,at,rt,this._instanceProps),kt=!Ye||Fe==="backward"?Math.max(1,it):1,Ot=!Ye||Fe==="forward"?Math.max(1,it):1;return[Math.max(0,at-kt),Math.max(0,Math.min(ye-1,Mt+Ot)),at,Mt]},pe._getVerticalRangeToRender=function(){var W=this.props,ye=W.columnCount,be=W.overscanCount,xe=W.overscanRowCount,Te=W.overscanRowsCount,Le=W.rowCount,Ge=this.state,Fe=Ge.isScrolling,Ye=Ge.verticalScrollDirection,rt=Ge.scrollTop,it=xe||Te||be||1;if(ye===0||Le===0)return[0,0,0,0];var at=P(this.props,rt,this._instanceProps),Mt=ae(this.props,at,rt,this._instanceProps),kt=!Fe||Ye==="backward"?Math.max(1,it):1,Ot=!Fe||Ye==="forward"?Math.max(1,it):1;return[Math.max(0,at-kt),Math.max(0,Math.min(Le-1,Mt+Ot)),at,Mt]},ge}(s.PureComponent),i.defaultProps={direction:"ltr",itemData:void 0,useIsScrolling:!1},i}var et=function(i,c){var d=i.children,_=i.direction,b=i.height,m=i.innerTagName,N=i.outerTagName,j=i.overscanColumnsCount,B=i.overscanCount,G=i.overscanRowsCount,$=i.width,P=c.instance},ht=50,mt=function(i,c){var d=i.rowCount,_=c.rowMetadataMap,b=c.estimatedRowHeight,m=c.lastMeasuredRowIndex,N=0;if(m>=d&&(m=d-1),m>=0){var j=_[m];N=j.offset+j.size}var B=d-m-1,G=B*b;return N+G},Ft=function(i,c){var d=i.columnCount,_=c.columnMetadataMap,b=c.estimatedColumnWidth,m=c.lastMeasuredColumnIndex,N=0;if(m>=d&&(m=d-1),m>=0){var j=_[m];N=j.offset+j.size}var B=d-m-1,G=B*b;return N+G},Lt=function(i,c,d,_){var b,m,N;if(i==="column"?(b=_.columnMetadataMap,m=c.columnWidth,N=_.lastMeasuredColumnIndex):(b=_.rowMetadataMap,m=c.rowHeight,N=_.lastMeasuredRowIndex),d>N){var j=0;if(N>=0){var B=b[N];j=B.offset+B.size}for(var G=N+1;G<=d;G++){var $=m(G);b[G]={offset:j,size:$},j+=$}i==="column"?_.lastMeasuredColumnIndex=d:_.lastMeasuredRowIndex=d}return b[d]},At=function(i,c,d,_){var b,m;i==="column"?(b=d.columnMetadataMap,m=d.lastMeasuredColumnIndex):(b=d.rowMetadataMap,m=d.lastMeasuredRowIndex);var N=m>0?b[m].offset:0;return N>=_?Be(i,c,d,m,0,_):dt(i,c,d,Math.max(0,m),_)},Be=function(i,c,d,_,b,m){for(;b<=_;){var N=b+Math.floor((_-b)/2),j=Lt(i,c,N,d).offset;if(j===m)return N;j<m?b=N+1:j>m&&(_=N-1)}return b>0?b-1:0},dt=function(i,c,d,_,b){for(var m=i==="column"?c.columnCount:c.rowCount,N=1;_<m&&Lt(i,c,_,d).offset<b;)_+=N,N*=2;return Be(i,c,d,Math.min(_,m-1),Math.floor(_/2),b)},Vt=function(i,c,d,_,b,m,N){var j=i==="column"?c.width:c.height,B=Lt(i,c,d,m),G=i==="column"?Ft(c,m):mt(c,m),$=Math.max(0,Math.min(G-j,B.offset)),P=Math.max(0,B.offset-j+N+B.size);switch(_==="smart"&&(b>=P-j&&b<=$+j?_="auto":_="center"),_){case"start":return $;case"end":return P;case"center":return Math.round(P+($-P)/2);case"auto":default:return b>=P&&b<=$?b:P>$||b<P?P:$}},en=ot({getColumnOffset:function(i,c,d){return Lt("column",i,c,d).offset},getColumnStartIndexForOffset:function(i,c,d){return At("column",i,d,c)},getColumnStopIndexForStartIndex:function(i,c,d,_){for(var b=i.columnCount,m=i.width,N=Lt("column",i,c,_),j=d+m,B=N.offset+N.size,G=c;G<b-1&&B<j;)G++,B+=Lt("column",i,G,_).size;return G},getColumnWidth:function(i,c,d){return d.columnMetadataMap[c].size},getEstimatedTotalHeight:mt,getEstimatedTotalWidth:Ft,getOffsetForColumnAndAlignment:function(i,c,d,_,b,m){return Vt("column",i,c,d,_,b,m)},getOffsetForRowAndAlignment:function(i,c,d,_,b,m){return Vt("row",i,c,d,_,b,m)},getRowOffset:function(i,c,d){return Lt("row",i,c,d).offset},getRowHeight:function(i,c,d){return d.rowMetadataMap[c].size},getRowStartIndexForOffset:function(i,c,d){return At("row",i,d,c)},getRowStopIndexForStartIndex:function(i,c,d,_){for(var b=i.rowCount,m=i.height,N=Lt("row",i,c,_),j=d+m,B=N.offset+N.size,G=c;G<b-1&&B<j;)G++,B+=Lt("row",i,G,_).size;return G},initInstanceProps:function(i,c){var d=i,_=d.estimatedColumnWidth,b=d.estimatedRowHeight,m={columnMetadataMap:{},estimatedColumnWidth:_||ht,estimatedRowHeight:b||ht,lastMeasuredColumnIndex:-1,lastMeasuredRowIndex:-1,rowMetadataMap:{}};return c.resetAfterColumnIndex=function(N,j){j===void 0&&(j=!0),c.resetAfterIndices({columnIndex:N,shouldForceUpdate:j})},c.resetAfterRowIndex=function(N,j){j===void 0&&(j=!0),c.resetAfterIndices({rowIndex:N,shouldForceUpdate:j})},c.resetAfterIndices=function(N){var j=N.columnIndex,B=N.rowIndex,G=N.shouldForceUpdate,$=G===void 0?!0:G;typeof j=="number"&&(m.lastMeasuredColumnIndex=Math.min(m.lastMeasuredColumnIndex,j-1)),typeof B=="number"&&(m.lastMeasuredRowIndex=Math.min(m.lastMeasuredRowIndex,B-1)),c._getItemStyleCache(-1),$&&c.forceUpdate()},m},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(i){var c=i.columnWidth,d=i.rowHeight}}),Nn=150,bn=function(i,c){return i},hn=null,Yn=null;function Wo(e){var i,c=e.getItemOffset,d=e.getEstimatedTotalSize,_=e.getItemSize,b=e.getOffsetForIndexAndAlignment,m=e.getStartIndexForOffset,N=e.getStopIndexForStartIndex,j=e.initInstanceProps,B=e.shouldResetStyleCacheOnItemSizeChange,G=e.validateProps;return i=function($){(0,ce.A)(P,$);function P(oe){var O;return O=$.call(this,oe)||this,O._instanceProps=j(O.props,F(O)),O._outerRef=void 0,O._resetIsScrollingTimeoutId=null,O.state={instance:F(O),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof O.props.initialScrollOffset=="number"?O.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},O._callOnItemsRendered=void 0,O._callOnItemsRendered=Re(function(ne,ie,ge,pe){return O.props.onItemsRendered({overscanStartIndex:ne,overscanStopIndex:ie,visibleStartIndex:ge,visibleStopIndex:pe})}),O._callOnScroll=void 0,O._callOnScroll=Re(function(ne,ie,ge){return O.props.onScroll({scrollDirection:ne,scrollOffset:ie,scrollUpdateWasRequested:ge})}),O._getItemStyle=void 0,O._getItemStyle=function(ne){var ie=O.props,ge=ie.direction,pe=ie.itemSize,fe=ie.layout,W=O._getItemStyleCache(B&&pe,B&&fe,B&&ge),ye;if(W.hasOwnProperty(ne))ye=W[ne];else{var be=c(O.props,ne,O._instanceProps),xe=_(O.props,ne,O._instanceProps),Te=ge==="horizontal"||fe==="horizontal",Le=ge==="rtl",Ge=Te?be:0;W[ne]=ye={position:"absolute",left:Le?void 0:Ge,right:Le?Ge:void 0,top:Te?0:be,height:Te?"100%":xe,width:Te?xe:"100%"}}return ye},O._getItemStyleCache=void 0,O._getItemStyleCache=Re(function(ne,ie,ge){return{}}),O._onScrollHorizontal=function(ne){var ie=ne.currentTarget,ge=ie.clientWidth,pe=ie.scrollLeft,fe=ie.scrollWidth;O.setState(function(W){if(W.scrollOffset===pe)return null;var ye=O.props.direction,be=pe;if(ye==="rtl")switch(wt()){case"negative":be=-pe;break;case"positive-descending":be=fe-ge-pe;break}return be=Math.max(0,Math.min(be,fe-ge)),{isScrolling:!0,scrollDirection:W.scrollOffset<be?"forward":"backward",scrollOffset:be,scrollUpdateWasRequested:!1}},O._resetIsScrollingDebounced)},O._onScrollVertical=function(ne){var ie=ne.currentTarget,ge=ie.clientHeight,pe=ie.scrollHeight,fe=ie.scrollTop;O.setState(function(W){if(W.scrollOffset===fe)return null;var ye=Math.max(0,Math.min(fe,pe-ge));return{isScrolling:!0,scrollDirection:W.scrollOffset<ye?"forward":"backward",scrollOffset:ye,scrollUpdateWasRequested:!1}},O._resetIsScrollingDebounced)},O._outerRefSetter=function(ne){var ie=O.props.outerRef;O._outerRef=ne,typeof ie=="function"?ie(ne):ie!=null&&typeof ie=="object"&&ie.hasOwnProperty("current")&&(ie.current=ne)},O._resetIsScrollingDebounced=function(){O._resetIsScrollingTimeoutId!==null&&Se(O._resetIsScrollingTimeoutId),O._resetIsScrollingTimeoutId=We(O._resetIsScrolling,Nn)},O._resetIsScrolling=function(){O._resetIsScrollingTimeoutId=null,O.setState({isScrolling:!1},function(){O._getItemStyleCache(-1,null)})},O}P.getDerivedStateFromProps=function(O,ne){return tr(O,ne),G(O),null};var ae=P.prototype;return ae.scrollTo=function(O){O=Math.max(0,O),this.setState(function(ne){return ne.scrollOffset===O?null:{scrollDirection:ne.scrollOffset<O?"forward":"backward",scrollOffset:O,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},ae.scrollToItem=function(O,ne){ne===void 0&&(ne="auto");var ie=this.props,ge=ie.itemCount,pe=ie.layout,fe=this.state.scrollOffset;O=Math.max(0,Math.min(O,ge-1));var W=0;if(this._outerRef){var ye=this._outerRef;pe==="vertical"?W=ye.scrollWidth>ye.clientWidth?lt():0:W=ye.scrollHeight>ye.clientHeight?lt():0}this.scrollTo(b(this.props,O,ne,fe,this._instanceProps,W))},ae.componentDidMount=function(){var O=this.props,ne=O.direction,ie=O.initialScrollOffset,ge=O.layout;if(typeof ie=="number"&&this._outerRef!=null){var pe=this._outerRef;ne==="horizontal"||ge==="horizontal"?pe.scrollLeft=ie:pe.scrollTop=ie}this._callPropsCallbacks()},ae.componentDidUpdate=function(){var O=this.props,ne=O.direction,ie=O.layout,ge=this.state,pe=ge.scrollOffset,fe=ge.scrollUpdateWasRequested;if(fe&&this._outerRef!=null){var W=this._outerRef;if(ne==="horizontal"||ie==="horizontal")if(ne==="rtl")switch(wt()){case"negative":W.scrollLeft=-pe;break;case"positive-ascending":W.scrollLeft=pe;break;default:var ye=W.clientWidth,be=W.scrollWidth;W.scrollLeft=be-ye-pe;break}else W.scrollLeft=pe;else W.scrollTop=pe}this._callPropsCallbacks()},ae.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&Se(this._resetIsScrollingTimeoutId)},ae.render=function(){var O=this.props,ne=O.children,ie=O.className,ge=O.direction,pe=O.height,fe=O.innerRef,W=O.innerElementType,ye=O.innerTagName,be=O.itemCount,xe=O.itemData,Te=O.itemKey,Le=Te===void 0?bn:Te,Ge=O.layout,Fe=O.outerElementType,Ye=O.outerTagName,rt=O.style,it=O.useIsScrolling,at=O.width,Mt=this.state.isScrolling,kt=ge==="horizontal"||Ge==="horizontal",Ot=kt?this._onScrollHorizontal:this._onScrollVertical,Dt=this._getRangeToRender(),Gt=Dt[0],gt=Dt[1],Tt=[];if(be>0)for(var Pt=Gt;Pt<=gt;Pt++)Tt.push((0,s.createElement)(ne,{data:xe,key:Le(Pt,xe),index:Pt,isScrolling:it?Mt:void 0,style:this._getItemStyle(Pt)}));var Qt=d(this.props,this._instanceProps);return(0,s.createElement)(Fe||Ye||"div",{className:ie,onScroll:Ot,ref:this._outerRefSetter,style:(0,h.A)({position:"relative",height:pe,width:at,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:ge},rt)},(0,s.createElement)(W||ye||"div",{children:Tt,ref:fe,style:{height:kt?"100%":Qt,pointerEvents:Mt?"none":void 0,width:kt?Qt:"100%"}}))},ae._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var O=this.props.itemCount;if(O>0){var ne=this._getRangeToRender(),ie=ne[0],ge=ne[1],pe=ne[2],fe=ne[3];this._callOnItemsRendered(ie,ge,pe,fe)}}if(typeof this.props.onScroll=="function"){var W=this.state,ye=W.scrollDirection,be=W.scrollOffset,xe=W.scrollUpdateWasRequested;this._callOnScroll(ye,be,xe)}},ae._getRangeToRender=function(){var O=this.props,ne=O.itemCount,ie=O.overscanCount,ge=this.state,pe=ge.isScrolling,fe=ge.scrollDirection,W=ge.scrollOffset;if(ne===0)return[0,0,0,0];var ye=m(this.props,W,this._instanceProps),be=N(this.props,ye,W,this._instanceProps),xe=!pe||fe==="backward"?Math.max(1,ie):1,Te=!pe||fe==="forward"?Math.max(1,ie):1;return[Math.max(0,ye-xe),Math.max(0,Math.min(ne-1,be+Te)),ye,be]},P}(s.PureComponent),i.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},i}var tr=function(i,c){var d=i.children,_=i.direction,b=i.height,m=i.layout,N=i.innerTagName,j=i.outerTagName,B=i.width,G=c.instance;if(0)var $},nr=50,Qn=function(i,c,d){var _=i,b=_.itemSize,m=d.itemMetadataMap,N=d.lastMeasuredIndex;if(c>N){var j=0;if(N>=0){var B=m[N];j=B.offset+B.size}for(var G=N+1;G<=c;G++){var $=b(G);m[G]={offset:j,size:$},j+=$}d.lastMeasuredIndex=c}return m[c]},sr=function(i,c,d){var _=c.itemMetadataMap,b=c.lastMeasuredIndex,m=b>0?_[b].offset:0;return m>=d?Ho(i,c,b,0,d):or(i,c,Math.max(0,b),d)},Ho=function(i,c,d,_,b){for(;_<=d;){var m=_+Math.floor((d-_)/2),N=Qn(i,m,c).offset;if(N===b)return m;N<b?_=m+1:N>b&&(d=m-1)}return _>0?_-1:0},or=function(i,c,d,_){for(var b=i.itemCount,m=1;d<b&&Qn(i,d,c).offset<_;)d+=m,m*=2;return Ho(i,c,Math.min(d,b-1),Math.floor(d/2),_)},Vo=function(i,c){var d=i.itemCount,_=c.itemMetadataMap,b=c.estimatedItemSize,m=c.lastMeasuredIndex,N=0;if(m>=d&&(m=d-1),m>=0){var j=_[m];N=j.offset+j.size}var B=d-m-1,G=B*b;return N+G},Wr=Wo({getItemOffset:function(i,c,d){return Qn(i,c,d).offset},getItemSize:function(i,c,d){return d.itemMetadataMap[c].size},getEstimatedTotalSize:Vo,getOffsetForIndexAndAlignment:function(i,c,d,_,b,m){var N=i.direction,j=i.height,B=i.layout,G=i.width,$=N==="horizontal"||B==="horizontal",P=$?G:j,ae=Qn(i,c,b),oe=Vo(i,b),O=Math.max(0,Math.min(oe-P,ae.offset)),ne=Math.max(0,ae.offset-P+ae.size+m);switch(d==="smart"&&(_>=ne-P&&_<=O+P?d="auto":d="center"),d){case"start":return O;case"end":return ne;case"center":return Math.round(ne+(O-ne)/2);case"auto":default:return _>=ne&&_<=O?_:_<ne?ne:O}},getStartIndexForOffset:function(i,c,d){return sr(i,d,c)},getStopIndexForStartIndex:function(i,c,d,_){for(var b=i.direction,m=i.height,N=i.itemCount,j=i.layout,B=i.width,G=b==="horizontal"||j==="horizontal",$=G?B:m,P=Qn(i,c,_),ae=d+$,oe=P.offset+P.size,O=c;O<N-1&&oe<ae;)O++,oe+=Qn(i,O,_).size;return O},initInstanceProps:function(i,c){var d=i,_=d.estimatedItemSize,b={itemMetadataMap:{},estimatedItemSize:_||nr,lastMeasuredIndex:-1};return c.resetAfterIndex=function(m,N){N===void 0&&(N=!0),b.lastMeasuredIndex=Math.min(b.lastMeasuredIndex,m-1),c._getItemStyleCache(-1),N&&c.forceUpdate()},b},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(i){var c=i.itemSize}}),Hr=ot({getColumnOffset:function(i,c){var d=i.columnWidth;return c*d},getColumnWidth:function(i,c){var d=i.columnWidth;return d},getRowOffset:function(i,c){var d=i.rowHeight;return c*d},getRowHeight:function(i,c){var d=i.rowHeight;return d},getEstimatedTotalHeight:function(i){var c=i.rowCount,d=i.rowHeight;return d*c},getEstimatedTotalWidth:function(i){var c=i.columnCount,d=i.columnWidth;return d*c},getOffsetForColumnAndAlignment:function(i,c,d,_,b,m){var N=i.columnCount,j=i.columnWidth,B=i.width,G=Math.max(0,N*j-B),$=Math.min(G,c*j),P=Math.max(0,c*j-B+m+j);switch(d==="smart"&&(_>=P-B&&_<=$+B?d="auto":d="center"),d){case"start":return $;case"end":return P;case"center":var ae=Math.round(P+($-P)/2);return ae<Math.ceil(B/2)?0:ae>G+Math.floor(B/2)?G:ae;case"auto":default:return _>=P&&_<=$?_:P>$||_<P?P:$}},getOffsetForRowAndAlignment:function(i,c,d,_,b,m){var N=i.rowHeight,j=i.height,B=i.rowCount,G=Math.max(0,B*N-j),$=Math.min(G,c*N),P=Math.max(0,c*N-j+m+N);switch(d==="smart"&&(_>=P-j&&_<=$+j?d="auto":d="center"),d){case"start":return $;case"end":return P;case"center":var ae=Math.round(P+($-P)/2);return ae<Math.ceil(j/2)?0:ae>G+Math.floor(j/2)?G:ae;case"auto":default:return _>=P&&_<=$?_:P>$||_<P?P:$}},getColumnStartIndexForOffset:function(i,c){var d=i.columnWidth,_=i.columnCount;return Math.max(0,Math.min(_-1,Math.floor(c/d)))},getColumnStopIndexForStartIndex:function(i,c,d){var _=i.columnWidth,b=i.columnCount,m=i.width,N=c*_,j=Math.ceil((m+d-N)/_);return Math.max(0,Math.min(b-1,c+j-1))},getRowStartIndexForOffset:function(i,c){var d=i.rowHeight,_=i.rowCount;return Math.max(0,Math.min(_-1,Math.floor(c/d)))},getRowStopIndexForStartIndex:function(i,c,d){var _=i.rowHeight,b=i.rowCount,m=i.height,N=c*_,j=Math.ceil((m+d-N)/_);return Math.max(0,Math.min(b-1,c+j-1))},initInstanceProps:function(i){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(i){var c=i.columnWidth,d=i.rowHeight}}),rr=Wo({getItemOffset:function(i,c){var d=i.itemSize;return c*d},getItemSize:function(i,c){var d=i.itemSize;return d},getEstimatedTotalSize:function(i){var c=i.itemCount,d=i.itemSize;return d*c},getOffsetForIndexAndAlignment:function(i,c,d,_,b,m){var N=i.direction,j=i.height,B=i.itemCount,G=i.itemSize,$=i.layout,P=i.width,ae=N==="horizontal"||$==="horizontal",oe=ae?P:j,O=Math.max(0,B*G-oe),ne=Math.min(O,c*G),ie=Math.max(0,c*G-oe+G+m);switch(d==="smart"&&(_>=ie-oe&&_<=ne+oe?d="auto":d="center"),d){case"start":return ne;case"end":return ie;case"center":{var ge=Math.round(ie+(ne-ie)/2);return ge<Math.ceil(oe/2)?0:ge>O+Math.floor(oe/2)?O:ge}case"auto":default:return _>=ie&&_<=ne?_:_<ie?ie:ne}},getStartIndexForOffset:function(i,c){var d=i.itemCount,_=i.itemSize;return Math.max(0,Math.min(d-1,Math.floor(c/_)))},getStopIndexForStartIndex:function(i,c,d){var _=i.direction,b=i.height,m=i.itemCount,N=i.itemSize,j=i.layout,B=i.width,G=_==="horizontal"||j==="horizontal",$=c*N,P=G?B:b,ae=Math.ceil((P+d-$)/N);return Math.max(0,Math.min(m-1,c+ae-1))},initInstanceProps:function(i){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(i){var c=i.itemSize}});function Po(e,i){for(var c in e)if(!(c in i))return!0;for(var d in i)if(e[d]!==i[d])return!0;return!1}var ir=null,ar=null;function lr(e,i){var c=e.style,d=_objectWithoutPropertiesLoose(e,ir),_=i.style,b=_objectWithoutPropertiesLoose(i,ar);return!Po(c,_)&&!Po(d,b)}function Vr(e,i){return!lr(this.props,e)||Po(this.state,i)}let cn;typeof window<"u"?cn=window:typeof self<"u"?cn=self:cn=n.g;let Do=null,Ro=null;const Go=20,Bo=cn.clearTimeout,Zo=cn.setTimeout,Fo=cn.cancelAnimationFrame||cn.mozCancelAnimationFrame||cn.webkitCancelAnimationFrame,Ko=cn.requestAnimationFrame||cn.mozRequestAnimationFrame||cn.webkitRequestAnimationFrame;Fo==null||Ko==null?(Do=Bo,Ro=function(i){return Zo(i,Go)}):(Do=function(i){let[c,d]=i;Fo(c),Bo(d)},Ro=function(i){const c=Ko(function(){Bo(d),i()}),d=Zo(function(){Fo(c),i()},Go);return[c,d]});function cr(e){let i,c,d,_,b,m,N;const j=typeof document<"u"&&document.attachEvent;if(!j){m=function(ie){const ge=ie.__resizeTriggers__,pe=ge.firstElementChild,fe=ge.lastElementChild,W=pe.firstElementChild;fe.scrollLeft=fe.scrollWidth,fe.scrollTop=fe.scrollHeight,W.style.width=pe.offsetWidth+1+"px",W.style.height=pe.offsetHeight+1+"px",pe.scrollLeft=pe.scrollWidth,pe.scrollTop=pe.scrollHeight},b=function(ie){return ie.offsetWidth!==ie.__resizeLast__.width||ie.offsetHeight!==ie.__resizeLast__.height},N=function(ie){if(ie.target.className&&typeof ie.target.className.indexOf=="function"&&ie.target.className.indexOf("contract-trigger")<0&&ie.target.className.indexOf("expand-trigger")<0)return;const ge=this;m(this),this.__resizeRAF__&&Do(this.__resizeRAF__),this.__resizeRAF__=Ro(function(){b(ge)&&(ge.__resizeLast__.width=ge.offsetWidth,ge.__resizeLast__.height=ge.offsetHeight,ge.__resizeListeners__.forEach(function(W){W.call(ge,ie)}))})};let P=!1,ae="";d="animationstart";const oe="Webkit Moz O ms".split(" ");let O="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),ne="";{const ie=document.createElement("fakeelement");if(ie.style.animationName!==void 0&&(P=!0),P===!1){for(let ge=0;ge<oe.length;ge++)if(ie.style[oe[ge]+"AnimationName"]!==void 0){ne=oe[ge],ae="-"+ne.toLowerCase()+"-",d=O[ge],P=!0;break}}}c="resizeanim",i="@"+ae+"keyframes "+c+" { from { opacity: 0; } to { opacity: 0; } } ",_=ae+"animation: 1ms "+c+"; "}const B=function(P){if(!P.getElementById("detectElementResize")){const ae=(i||"")+".resize-triggers { "+(_||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',oe=P.head||P.getElementsByTagName("head")[0],O=P.createElement("style");O.id="detectElementResize",O.type="text/css",e!=null&&O.setAttribute("nonce",e),O.styleSheet?O.styleSheet.cssText=ae:O.appendChild(P.createTextNode(ae)),oe.appendChild(O)}};return{addResizeListener:function(P,ae){if(j)P.attachEvent("onresize",ae);else{if(!P.__resizeTriggers__){const oe=P.ownerDocument,O=cn.getComputedStyle(P);O&&O.position==="static"&&(P.style.position="relative"),B(oe),P.__resizeLast__={},P.__resizeListeners__=[],(P.__resizeTriggers__=oe.createElement("div")).className="resize-triggers";const ne=oe.createElement("div");ne.className="expand-trigger",ne.appendChild(oe.createElement("div"));const ie=oe.createElement("div");ie.className="contract-trigger",P.__resizeTriggers__.appendChild(ne),P.__resizeTriggers__.appendChild(ie),P.appendChild(P.__resizeTriggers__),m(P),P.addEventListener("scroll",N,!0),d&&(P.__resizeTriggers__.__animationListener__=function(pe){pe.animationName===c&&m(P)},P.__resizeTriggers__.addEventListener(d,P.__resizeTriggers__.__animationListener__))}P.__resizeListeners__.push(ae)}},removeResizeListener:function(P,ae){if(j)P.detachEvent("onresize",ae);else if(P.__resizeListeners__.splice(P.__resizeListeners__.indexOf(ae),1),!P.__resizeListeners__.length){P.removeEventListener("scroll",N,!0),P.__resizeTriggers__.__animationListener__&&(P.__resizeTriggers__.removeEventListener(d,P.__resizeTriggers__.__animationListener__),P.__resizeTriggers__.__animationListener__=null);try{P.__resizeTriggers__=!P.removeChild(P.__resizeTriggers__)}catch(oe){}}}}}class dr extends s.Component{constructor(){super(...arguments),this.state={height:this.props.defaultHeight||0,scaledHeight:this.props.defaultHeight||0,scaledWidth:this.props.defaultWidth||0,width:this.props.defaultWidth||0},this._autoSizer=null,this._detectElementResize=null,this._parentNode=null,this._resizeObserver=null,this._timeoutId=null,this._onResize=()=>{this._timeoutId=null;const{disableHeight:i,disableWidth:c,onResize:d}=this.props;if(this._parentNode){const _=window.getComputedStyle(this._parentNode)||{},b=parseFloat(_.paddingLeft||"0"),m=parseFloat(_.paddingRight||"0"),N=parseFloat(_.paddingTop||"0"),j=parseFloat(_.paddingBottom||"0"),B=this._parentNode.getBoundingClientRect(),G=B.height-N-j,$=B.width-b-m,P=this._parentNode.offsetHeight-N-j,ae=this._parentNode.offsetWidth-b-m;(!i&&(this.state.height!==P||this.state.scaledHeight!==G)||!c&&(this.state.width!==ae||this.state.scaledWidth!==$))&&(this.setState({height:P,width:ae,scaledHeight:G,scaledWidth:$}),typeof d=="function"&&d({height:P,scaledHeight:G,scaledWidth:$,width:ae}))}},this._setRef=i=>{this._autoSizer=i}}componentDidMount(){const{nonce:i}=this.props;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._parentNode!=null&&(typeof ResizeObserver<"u"?(this._resizeObserver=new ResizeObserver(()=>{this._timeoutId=setTimeout(this._onResize,0)}),this._resizeObserver.observe(this._parentNode)):(this._detectElementResize=cr(i),this._detectElementResize.addResizeListener(this._parentNode,this._onResize)),this._onResize()))}componentWillUnmount(){this._parentNode&&(this._detectElementResize&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize),this._timeoutId!==null&&clearTimeout(this._timeoutId),this._resizeObserver&&(this._resizeObserver.observe(this._parentNode),this._resizeObserver.disconnect()))}render(){const{children:i,defaultHeight:c,defaultWidth:d,disableHeight:_=!1,disableWidth:b=!1,nonce:m,onResize:N,style:j={},tagName:B="div",...G}=this.props,{height:$,scaledHeight:P,scaledWidth:ae,width:oe}=this.state,O={overflow:"visible"},ne={};let ie=!1;return _||($===0&&(ie=!0),O.height=0,ne.height=$,ne.scaledHeight=P),b||(oe===0&&(ie=!0),O.width=0,ne.width=oe,ne.scaledWidth=ae),(0,s.createElement)(B,{ref:this._setRef,style:{...O,...j},...G},!ie&&i(ne))}}function Gr(e){return e&&e.disableHeight!==!0&&e.disableWidth!==!0}function Zr(e){return e&&e.disableHeight!==!0&&e.disableWidth===!0}function Kr(e){return e&&e.disableHeight===!0&&e.disableWidth!==!0}const ur=(0,s.memo)(e=>{let{className:i,itemCount:c,itemSize:d,itemData:_,renderItem:b,onItemCountChange:m,onListRef:N}=e;const j=s.createRef();return(0,s.useEffect)(()=>{j.current&&m&&m(j)},[c,j,m]),(0,s.useEffect)(()=>{j.current&&N&&N(j)},[j,N]),(0,r.jsx)(dr,{children:B=>{let{width:G,height:$=0}=B;return(0,r.jsx)(rr,{ref:j,className:i,itemCount:c,itemSize:d,itemData:_,height:$,width:G,children:b})}})}),pr=e=>{const{theme:i,caption:c,captionExtra:d,className:_,permissionScope:b,memberList:m,isOnlyOneManager:N,readOnly:j,readOnlyTooltip:B,expandable:G,defaultExpanded:$=!0,isResetPermission:P=!1,children:ae,onPermissionChange:oe,onChangeHeight:O,isMemberList:ne,onlyTitle:ie,isUseVirtualizedRender:ge=!1,onClickInviteButton:pe,isShowInviteButton:fe=!1,currentOrg:W}=e,[ye,be]=(0,s.useState)($),xe=(0,s.useCallback)(()=>{be(Fe=>!Fe),O&&O()},[O]),Te=(0,s.useCallback)(Fe=>{var Ye;const{avatar:rt,name:it}=Fe;return rt&&!rt.includes("/images/avatar.png")?(0,r.jsx)("img",{className:"avatar",src:Fe.avatar,alt:Fe.name}):(0,r.jsx)("div",{className:"avatar avater-name",children:it==null||(Ye=it.slice(0,1))==null?void 0:Ye.toUpperCase()})},[]),Le=(Fe,Ye)=>(0,r.jsxs)("div",{className:"item",style:Ye,children:[(0,r.jsxs)("span",{className:"title",children:[Te(Fe),(0,r.jsx)("span",{className:"name",children:Fe.name})]}),(0,r.jsx)("span",{className:"account",children:Fe.email||Fe.mobile}),(0,r.jsx)(a,{readOnly:j,readOnlyTooltip:B,member:Fe,isReset:P,isOnlyOneManager:N,permissionScope:b,currentOrg:W,onPermissionChange:oe})]},Fe.user_cid),Ge=Fe=>{let{data:Ye,index:rt,style:it}=Fe;const{memberList:at}=Ye;return Le(at[rt],it)};return(0,r.jsxs)(o,{className:X()("member-list",i,_&&_,ge&&"use-virtualized-list"),"data-expanded":ye,children:[G&&(0,r.jsxs)("h5",{className:"caption",children:[(0,r.jsx)("span",{children:c}),fe&&!(!ie&&G&&!ne)&&(0,r.jsxs)("div",{className:"invite-item",onClick:pe,children:[(0,r.jsx)(Ee.C,{name:"sharing/invite_member"}),I18N.imockSharing.add_collaborator]}),!ie&&G&&!ne&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:m.length>0&&"\xB7"+m.length}),(0,r.jsx)("span",{className:X()("btn-toggle-expand"),onClick:xe,children:ye?I18N.BasicWidgetsNav.share_collapse:I18N.BasicWidgetsNav.share_view})]}),d]}),ae,!!m.length&&(0,r.jsx)("div",{className:"members",children:ge?(0,r.jsx)(ur,{className:"virtualized-list",itemSize:48,itemCount:m.length,itemData:{memberList:m},renderItem:Ge}):m.map(Fe=>Le(Fe))})]})},zo=(0,s.memo)(pr),hr=A.Ay.div.withConfig({displayName:"style__StyledSearchBox",componentId:"sc-f6syk1-0"})(["flex:1;position:relative;.icon-search{position:absolute;top:12px;left:12px;width:16px;height:17px;color:#666;}.autoCompleteInput{position:absolute;width:1px;height:1px;top:-2000px;}input{width:100%;height:36px;padding:0 12px 0 12px;border-radius:4px;outline:none;color:",";border:1px solid ",";font-size:14px;&:hover,&:focus{border-color:#298df8;border:1px solid #1883FB;}}"],e=>e.theme.color_text_L1,e=>e.theme.color_bg_border_02),mr=e=>{const{value:i,onChange:c,onKeyPress:d}=e,_=m=>{c(m.currentTarget.value)},b=m=>{m.key==="Enter"&&d(m.currentTarget.value)};return(0,r.jsx)(hr,{className:"search-box",children:(0,r.jsx)("input",{className:"search-input",placeholder:I18N.imockSharing.search_member,type:"text",value:i,onChange:_,onKeyPress:b})})},gr=(0,s.memo)(mr),Jn={OWNER:1e4,SUPERMANAGER:9999,MANAGER:999,MEMBER:99,LIMITER:10,VIEWER:9,BANNED:1,UNKNOWN:0,UNJOINED:-1},$r={space_owner:16,space_manager:15,space_member:14,space_viewer:13,space_limiter:12,team_owner:11,team_manager:10,team_member:9,team_viewer:8,project_owner:6,project_manager:5,project_member:4,project_viewer:3,project_ban_viewer:2,project_team_owner:1,team_ban_viewer:1},fr={org_owner:Jn.OWNER,org_manager:Jn.SUPERMANAGER,org_admin:Jn.MANAGER,org_member:Jn.MEMBER,org_viewer:Jn.VIEWER},yr=e=>{const i={pt_total_seats:10,pt_bindings:[],ptlt_total_seats:0,ptlt_bindings:[],bx_total_seats:10,bx_bindings:[],total_viewer_seats:100,viewer_seats_taken:0},{pt_total_seats:c,pt_bindings:d,ptlt_total_seats:_,ptlt_bindings:b,bx_total_seats:m,bx_bindings:N,total_viewer_seats:j,viewer_seats_taken:B}=e||i;return{plTotalSeats:_,pTotalSeats:c,pBindings:d||[],plBindings:b||[],bTotalSeats:m,bBindings:N||[],totalViewerSeats:j,viewerSeatsTaken:B}},xr=e=>{if(!e)return null;const{permissions:i,total_viewer_seats:c,viewer_seats_taken:d}=e,{bBindings:_,pTotalSeats:b,pBindings:m,bTotalSeats:N,plBindings:j,plTotalSeats:B}=yr(e);let G=N;const $=ls.t.InitialOrg(e),P=$.planSdk.getIsDefaultFreeOrg(),ae=$.planSdk.getOrgPlan()===ys.Sj.OrgExpired,oe=$.planSdk.getOrgStatus()===ys._E.Trial;let O=m.length,ne=_.length;if(oe||P||ae){const Fe=i.filter(Ye=>fr[Ye.role]>Jn.VIEWER);ne=(Fe==null?void 0:Fe.length)||10,O=(Fe==null?void 0:Fe.length)||10,G=b}const ie=j.length>=B,ge=O>=b&&ie,pe=ne>=G,fe=pe&&ge,W=O>=b&&ne>=G,ye=d>=c,be=O+j.length,xe=ne,Te=b+B,Le=Te+G,Ge=be+xe;return{isNormalSeatFull:W,isLifetimeFull:ie,isPtMemberFull:ge,isBxMemberFull:pe,isMemberFull:fe,isViewerFull:ye,ptSeatsTaken:be,bxSeatsTaken:xe,ptTotalSeats:Te,bxTotalSeats:G,viewerSeatsTaken:d,totalViewerSeats:c,totalSeats:Le,seatsTaken:Ge}};var $o=n(90503);function br(e){const{theme:i,caption:c,outerMembers:d,canInvite:_,isSharing:b,handleClickInviteMember:m}=e,N=()=>{const G="\u54C8\u55BD\uFF0C\u7BA1\u7406\u5458\u60A8\u597D\uFF0C\u7533\u8BF7\u5C06"+(d[0].mobile||d[0].email)+"\u52A0\u5165\u300C\u58A8\u5200\u5E73\u53F0-"+c+"\u300D\uFF0C\u52A0\u5165\u540E\u6211\u5C31\u53EF\u4EE5\u548CTA\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~";(0,wn.$)(G)},j=!d||d.length<1;return b?(0,r.jsxs)(o,{className:"member-list sharing "+i,children:[(0,r.jsxs)("div",{className:"members invite",children:[!j&&d.map(B=>(0,r.jsxs)("div",{className:"item",children:[(0,r.jsxs)("span",{className:"title",children:[(0,r.jsx)("img",{className:"avatar",src:B.avatar,alt:B.name}),(0,r.jsxs)("span",{className:"name",children:[B.name,(0,r.jsx)("span",{className:"register",children:B.id?I18N.imockSharing.external:I18N.imockSharing.unregistered})]})]}),(0,r.jsx)("span",{className:"account",children:B.email||B.mobile}),!n.g.ENV.IS_ON_PREMISES&&(_?(0,r.jsx)("span",{className:"inviteJoin",onClick:m,children:I18N.imockSharing.invite_h}):(0,r.jsxs)("div",{className:"addOuter",children:[(0,r.jsx)("span",{className:"add",children:I18N.imockSharing.submit}),(0,r.jsx)(gs.A,{opener:(0,r.jsx)($o.A,{name:"question"}),mode:"dark",menuX:"right",shouldOpenOnHover:!0,hoverDelay:100,menuClassName:"add-outer-member-tip",menu:(0,r.jsxs)("p",{className:"desc",children:["\u590D\u5236\u5E10\u53F7\uFF0C\u63D0\u9192\u7BA1\u7406\u5458\u628ATA\u52A0\u5165\u4F01\u4E1A\uFF0C\u5C31\u53EF\u4EE5\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~",(0,r.jsx)("a",{className:"copy-text",onClick:N,children:I18N.imockSharing.copy})]})})]}))]},B.cid)),j&&(0,r.jsx)("div",{className:"enterTip",children:(0,r.jsx)("span",{children:n.g.ENV.IS_ON_PREMISES?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:"\u60A8\u627E\u7684\u6210\u5458\u4E0D\u5728\u4F01\u4E1A\u4E2D\uFF0C"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"\u8BF7\u786E\u8BA4\u300C\u90AE\u7BB1/\u624B\u673A\u53F7\u300D\u91CD\u65B0\u641C\u7D22~"})]}):(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:I18N.imockSharing.member_search_not_in_the_team}})})})]}),(0,r.jsx)(t,{})]}):(r.Fragment,(0,r.jsxs)(o,{className:"member-list "+i,children:[(0,r.jsxs)("div",{className:"members invite",children:[!j&&d.map(B=>(0,r.jsxs)("div",{className:"item",children:[(0,r.jsxs)("span",{className:"title",children:[(0,r.jsx)("img",{className:"avatar",src:B.avatar,alt:B.name}),(0,r.jsxs)("span",{className:"name",children:[B.name,(0,r.jsx)("span",{className:"register",children:B.id?I18N.imockSharing.external:I18N.imockSharing.unregistered})]})]}),(0,r.jsx)("span",{className:"account",children:B.email||B.mobile}),!n.g.ENV.IS_ON_PREMISES&&(_?(0,r.jsx)("span",{className:"inviteJoin",onClick:m,children:I18N.imockSharing.invite_h}):(0,r.jsxs)("div",{className:"addOuter",children:[(0,r.jsx)("span",{className:"add",children:I18N.imockSharing.submit}),(0,r.jsx)(gs.A,{opener:(0,r.jsx)($o.A,{name:"question"}),mode:"dark",menuX:"right",shouldOpenOnHover:!0,hoverDelay:100,menuClassName:"add-outer-member-tip",menu:(0,r.jsxs)("p",{className:"desc",children:["\u590D\u5236\u5E10\u53F7\uFF0C\u63D0\u9192\u7BA1\u7406\u5458\u628ATA\u52A0\u5165\u4F01\u4E1A\uFF0C\u5C31\u53EF\u4EE5\u4E00\u8D77\u534F\u4F5C\u4E86\u54E6~",(0,r.jsx)("a",{className:"copy-text",onClick:N,children:I18N.imockSharing.copy})]})})]}))]},B.cid)),j&&(0,r.jsx)("div",{className:"enterTip",children:(0,r.jsx)("span",{children:n.g.ENV.IS_ON_PREMISES?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{children:"\u60A8\u627E\u7684\u6210\u5458\u4E0D\u5728\u4F01\u4E1A\u4E2D\uFF0C"}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"\u8BF7\u786E\u8BA4\u300C\u90AE\u7BB1/\u624B\u673A\u53F7\u300D\u91CD\u65B0\u641C\u7D22~"})]}):(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:I18N.imockSharing.member_search_not_in_the_team}})})})]}),(0,r.jsx)(t,{})]}))}const vr=(0,s.memo)(br),Yo=()=>!!Number(sessionStorage.getItem("inClowdz"));function _r(e){let i="file_editarea-proto-v8-add",c="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0";return i=e?"file_editarea-proto-v8-input-add":"file_editarea-proto-v8-add-direct",c=e?"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u6DFB\u52A0":"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0\u534F\u4F5C\u8005-\u6DFB\u52A0",{trackSourceId:i,trackSource:c}}function wr(e,i){i===void 0&&(i=!1);let c="file_editarea-proto-v8-invite",d="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u9080\u8BF7\u6210\u5458";return i?(c="file_editarea-proto-v8-input-inviteTA",d="\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u9080\u8BF7TA"):(c=e?"file_editarea-proto-v8-input-invite":"file_editarea-proto-v8-add-invite",d=e?"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u8F93\u5165\u6846-\u9080\u8BF7\u6210\u5458":"\u7F16\u8F91\u533A_V8-\u539F\u578B-\u6210\u5458\u7BA1\u7406-\u6DFB\u52A0\u534F\u4F5C\u8005-\u9080\u8BF7\u6210\u5458"),{trackSourceId:c,trackSource:d}}function Qo(e){if(!e)return"";let i="";return e==="org_owner"?i="\u4F01\u4E1A\u6240\u6709\u8005":e==="org_manager"?i="\u8D85\u7EA7\u7BA1\u7406\u5458":e==="org_admin"||e!=null&&e.includes("manager")?i="\u7BA1\u7406\u5458":e!=null&&e.includes("member")?i="\u534F\u4F5C\u6210\u5458":e!=null&&e.includes("viewer")&&(i="\u5BA1\u9605\u6210\u5458"),i}const Jo=(e,i,c)=>{try{const{source_id:d,source:_,step:b,option:m=null}=c;if(!i||!e||ENV.IS_ON_PREMISES||ENV.IS_MO)return;if((!m||(m==null?void 0:m.results)!==!1)&&d)try{(0,hs.Ds)("/api/dashboard/v5/org/invitation_records",{invitation_record:{user_id:e,org_cid:i,source:d,step:b}})}catch(N){console.log(N.message)}if(b===0)(0,Y.kH)("invite_members_entrance",{source:_});else if(b===2&&m){const N=Qo(m==null?void 0:m.invitor_role),j=Qo(m==null?void 0:m.invitee_role);(0,Y.kH)("invite_members_via_add",{source:_,join_space_num:1,has_department:!1,invitor_role:N,invitee_role:j,results:(m==null?void 0:m.results)||!0,error_type:(m==null?void 0:m.error_type)||""})}}catch(d){console.log(d.message)}},Ks=function(e,i,c,d,_){_===void 0&&(_=null);const{trackSourceId:b,trackSource:m}=_r(c);Jo(e,i,{source_id:b,source:m,step:d,option:_})},Sr=function(e,i,c,d){d===void 0&&(d=!1);const{trackSourceId:_,trackSource:b}=wr(c,d),m={trackSourceId:_,trackSource:b},N="/workspace/"+i+"/admin/member?openInviteByLinkModal=true&invite_track="+(0,jt._)(m);if(Jo(e,i,{source_id:_,source:b,step:0}),Yo()){location.pathname=N;return}(0,ut.JW)(N)},Cr=(e,i,c)=>{const[d]=(0,s.useState)(c),_=(0,s.useRef)(0);return(0,s.useEffect)(()=>{d&&_.current>=1||_.current>=2||(Ks(e,i,c,0),_.current+=1)},[!!c]),d||c},Mr=A.Ay.div.withConfig({displayName:"styles__StyledInviteMemberOverlay",componentId:"sc-six6tj-0"})(["z-index:3;height:100%;width:100%;display:flex;flex-direction:column;align-items:stretch;.overlay-content{flex:1;padding:0 4px;overflow:auto;position:relative;height:calc(100% - ","px);overflow-x:hidden;&::-webkit-scrollbar{display:block;width:4px;height:4px;}.member-list.use-virtualized-list{height:calc(100% - 10px);width:410px;.members{height:100%;.virtualized-list{&::-webkit-scrollbar{display:block;width:4px;height:4px;}}}}}.overlay-content.no-members{.member-list{height:90%;.invite{height:90%;.enterTip{height:100%;display:flex;align-items:center;justify-content:center;&:lang(en){span{display:flex;justify-content:center;div{width:80%;text-align:center;line-height:20px;}}}}}}}.collaboration-remind{position:absolute;bottom:57px;left:0;width:100%;height:29px;color:#FFFFFF;background:#298DF8;padding:0 23px;font-size:12px;line-height:17px;display:flex;justify-content:space-between;align-items:center;.collaboration-remind-close{width:16px;height:16px;cursor:pointer;path{fill:#FFFFFF;}}}.overlay-footer{display:flex;align-items:center;justify-content:space-between;height:","px;padding:0 24px;border-top:1px solid ",";.seats{color:",";font-size:12px;}}"],we.gE,we.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1),Nr=e=>{const{theme:i,keyword:c,source:d,caption:_,permissionScope:b,assignedMembers:m,unassignedMembers:N,outerMember:j,onPermissionChange:B,hostType:G,handleClose:$}=e,P=(0,g.d4)(Ie.wA),ae=(0,g.d4)(Ie.cb),oe=(0,g.d4)(Ie.WR),{settings:O,cid:ne}=P,{seatsTaken:ie,totalSeats:ge}=xr(P)||{},pe=Cr(oe,ne,c);let fe=!1;(O.invite_permission&&O.invite_permission[0]==="all"||ae.isManager)&&(fe=!0);const W=()=>{const Ge={mode:"org",orgCid:P.cid,payEntrance:"\u7F16\u8F91\u533A-v8_\u7248\u672C\u7BA1\u7406_\u4E2A\u4EBA\u5347\u4F01\u4E1A",checkoutPlace:"workspace-v8_org_mem_limit",checkoutArea:"proto"};if(G==="proto")MB.global.popupHelper.chargeAsync(Ge),$&&$();else{const Fe="/workspace/"+P.cid+"/admin/order?payment_param="+(0,jt._)(Ge);if(Yo()){location.pathname=Fe;return}(0,ut.JW)(Fe)}},ye=(0,s.useMemo)(()=>N.filter(Ge=>Ge.name&&Ge.name.includes(c)||Ge.mobile&&Ge.mobile.includes(c)||Ge.email&&Ge.email.includes(c)),[c,N]),be=()=>{fe&&Sr(oe,ne,pe,!0)},xe=(Ge,Fe)=>{B(Ge,Fe),Ks(oe,ne,pe,1),j&&Ks(oe,ne,pe,2,{invitor_role:ae==null?void 0:ae.roleName,invitee_role:Fe}),Ks(oe,ne,pe,3)},Te=!ye||ye.length<1,Le=j?[j]:[];return(0,r.jsxs)(Mr,{className:"overlay-adding-member sharing",children:[(0,r.jsxs)("div",{className:X()("overlay-content",!j&&Te&&"no-members"),children:[!Te&&(0,r.jsx)(zo,{theme:i,caption:c?null:I18N.imockSharing.unjoined,expandable:!1,className:X()(c&&"invite"),permissionScope:b,currentOrg:P,memberList:ye,isResetPermission:!0,onPermissionChange:xe,isUseVirtualizedRender:!0},"filtered-unassigned-members"),Te&&(0,r.jsx)(vr,{theme:i,caption:_,outerMembers:Le,canInvite:fe,isSharing:!0,handleClickInviteMember:be})]}),(0,r.jsxs)("footer",{className:"overlay-footer",children:[(0,r.jsxs)("span",{className:"seats",children:["\u5DF2\u5360\u7528\u5E2D\u4F4D/\u4F01\u4E1A\u603B\u5E2D\u4F4D\u6570\uFF1A",ie,"/",ge]}),!n.g.ENV.IS_ON_PREMISES&&(0,r.jsx)(dn.Oc,{type:"primary",size:"tiny",corner:"soft",onClick:W,children:I18N.imockSharing.add_team_seat})]})]})},Ir=(0,s.memo)(Nr),jr=A.Ay.div.withConfig({displayName:"styles__StyledProjectCollaborators",componentId:"sc-th5siy-0"})(["position:relative;display:flex;flex-direction:column;width:500px;height:100%;border-radius:8px;&.sharing{width:100%;&.dark{background:#252626;&.isInviteOverlayOpen{.overlay-adding-member{background:#252626;.overlay-header{.btn-back > svg > path{stroke:rgba(255,255,255,0.9);}.caption{color:rgba(255,255,255,0.9);}}.overlay-content{.member-list > .members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}}}}.nav-header > .title-left{label > button > svg > path{fill:#B8BCBF;}}.modal-content{.collaborators{.member-list > .members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}.inherited-members{.caption > span{color:",";&:last-child{color:#1684FC;}}.members > .item{.title{.name{color:rgba(255,255,255,0.9);}}.account{color:rgba(255,255,255,0.9);}label{button > span{color:rgba(255,255,255,0.9);}.caret svg{border-color:transparent transparent rgba(255,255,255,0.9) rgba(255,255,255,0.9);}}.read-only-option > span > span{color:rgba(255,255,255,0.9);}}}}}}&.isInviteOverlayOpen{.overlay-adding-member{.overlay-header{height:50px;.btn-back{left:20px;}.caption{font-size:14px;font-weight:500;color:",";left:82px;&:lang(en){left:100px;}}}}}& > .blank{height:86px;&.noSearch{height:18px;}}.modal-content{.blank{height:55px;}.collaborators{overflow-y:overlay;.member-list{.caption{padding:10px 20px;& > span{font-weight:500;font-size:12px;}.btn-toggle-expand{color:",";&:hover{color:",";}}.btn-add-collaborator{right:20px;}}.members{margin-top:0;.item{padding:7px 20px;height:48px;.title{flex:none;width:150px;.avatar{width:28px;height:28px;}.name{width:105px;font-weight:400;font-size:14px;line-height:initial;color:#333;}}.account{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}}}}}}}.nav-header{display:flex;padding-left:10px;font-weight:500;font-size:16px;color:",";height:","px;align-items:center;border-bottom:1px solid ",";justify-content:space-between;.title-left{display:flex;align-items:center;.title-left-back{cursor:pointer;display:flex;align-items:center;}.member-manager{cursor:default;margin-left:10px;}svg{margin-top:2px;}.nav-title{color:"," !important;font-size:14px;margin-top:1px;}}.title-close{margin-right:20px;cursor:pointer;.svg-icon{color:",";&:hover{color:",";}&:active{color:",";}}}}.modal-content{display:flex;flex:1;flex-direction:column;align-items:stretch;height:calc(100% - ","px);padding-top:10px;&.modal-content-show-search{height:calc(100% - 116px);}&.in-invite-overlay{padding-top:20px;}.collaborators{padding:0 4px 20px 4px;height:100%;overflow:hidden auto;&::-webkit-scrollbar{display:block;width:4px;height:4px;}}}.search{display:flex;align-items:center;position:relative;margin:0px 20px;padding-top:20px;}"],e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,e=>e.theme.color_text_link_normal,e=>e.theme.color_text_link_hover,e=>e.theme.color_text_L1,we.gE,e=>e.theme.color_bg_border_02,e=>e.theme.color_text_L1,e=>e.theme.color_text_L2,e=>e.theme.color_text_L1,e=>e.theme.color_text_L1,we.gE),Er=e=>{const{theme:i,from:c,caption:d,targetCid:_,permissionScope:b,captionTipType:m,isFolder:N=!1,isAllowSetCollaborators:j,isShowInheritedCollaborators:B=!1,handleBackSharingHandel:G,onChangeMembersAccess:$,handleClose:P,hostType:ae,isOnlyMemberManager:oe=!1,currentOrg:O}=e,[ne,ie]=(0,s.useState)(""),[ge,pe]=(0,s.useState)(null),[fe,W]=(0,s.useState)(!1),{currentMembers:ye,inheritedMembers:be,joinedMembers:xe,unjoinedMembers:Te,assignedMembers:Le,unassignedMembers:Ge,userCurrentScopePermisson:Fe,isOnlyOneManager:Ye}=bt(b,_),rt=xt(b,_,c),it=async(vt,on)=>{await rt(vt,on),$&&$(vt,on)};let at=ye,Mt=Le,kt=Ge;const Ot=Fe.isMember,Dt=j&&Ot,Gt=!fe&&Ot;j&&!B&&(at=xe,Mt=xe,kt=Te);const gt=(0,s.useCallback)(()=>{W(!1),ie(""),pe(null)},[]),Tt=(0,s.useCallback)(vt=>{ie(vt),pe(null),vt&&W(!0)},[]),Pt=(0,s.useCallback)(async vt=>{if(!n.g.ENV.IS_ON_PREMISES&&(ie(vt),vt)){const on=kt.find(Kt=>Kt.email===vt.trim()||Kt.mobile===vt.trim()),Xn=Mt.find(Kt=>Kt.email===vt.trim()||Kt.mobile===vt.trim());if(on||Xn)return;const qn=Kn.test(vt.trim()),vn=Pn.test(vt.trim());let rn=null;if(qn?rn=await ms(vt.trim()):vn&&(rn=await ms(null,vt.trim())),rn){let Kt=null;rn.user?(Kt=rn.user,Kt.name=I18N.imockSharing.to_be_invited,Kt.cid="outer"):Kt={avatar:"/images/avatar.png",cid:vt,email:vt,isNotRegister:!0,name:I18N.imockSharing.to_be_invited},pe(Kt)}}},[]),Qt=(0,s.useCallback)(()=>{if(!(oe&&!fe)){if(ie(""),pe(null),fe){W(!1);return}G()}},[fe,W,G]),ft=(0,s.useCallback)(()=>{W(!0)},[W]);return(0,r.jsxs)(jr,{className:X()("ProjectCollaborators sharing",""+i,{isInviteOverlayOpen:!0}),children:[(0,r.jsxs)("div",{className:"nav-header",children:[(0,r.jsxs)("div",{className:"title-left",children:[(0,r.jsxs)("div",{className:X()("title-left-back",{"member-manager":oe&&!fe}),onClick:Qt,children:[oe?fe&&(0,r.jsx)(Ee.C,{name:"sharing/nav_back",size:32}):(0,r.jsx)(Ee.C,{name:"sharing/nav_back",size:32}),(0,r.jsx)("div",{className:"nav-title",children:fe?I18N.imockSharing.add_collaborator:I18N.imockSharing.team_collaborator})]}),!fe&&(0,r.jsx)(No,{tipType:m,position:"left",isOnlyMemberManager:oe})]}),oe&&(0,r.jsx)("div",{className:"title-close",onClick:P,children:(0,r.jsx)(Ee.C,{size:24,name:"sharing/close"})})]}),Dt&&(0,r.jsx)("div",{className:X()("search"),children:(0,r.jsx)(gr,{value:ne,onChange:Tt,onKeyPress:Pt})}),(0,r.jsx)("div",{className:X()("modal-content",Dt&&"modal-content-show-search"),children:fe?(0,r.jsx)(Ir,{theme:i,permissionScope:b,currentOrg:O,unassignedMembers:kt,assignedMembers:Mt,outerMember:ge,keyword:ne,caption:d,onClose:gt,onPermissionChange:it,handleClose:P,hostType:ae}):(0,r.jsxs)("div",{className:X()("collaborators",fe&&"in-invite-overlay"),children:[j&&(0,r.jsx)(zo,{theme:i,caption:I18N.imockSharing.project_collaborators,permissionScope:b,currentOrg:O,memberList:at,isOnlyOneManager:Ye,isMemberList:!1,expandable:!0,onlyTitle:!0,onPermissionChange:it,onClickInviteButton:ft,isShowInviteButton:Gt},"current-collaborators"),B&&(0,r.jsx)(zo,{theme:i,caption:I18N.imockSharing.parent_folder_collaborator,expandable:!0,defaultExpanded:N,readOnly:!0,permissionScope:b,currentOrg:O,memberList:be,onPermissionChange:it,className:"inherited-members",onClickInviteButton:ft,isShowInviteButton:Gt},"inherited-collaborators")]})}),(0,r.jsx)(jo,{})]})},kr=(0,s.memo)(Er);function Tr(e){const{theme:i,targetCid:c,isSharing:d,handleBackSharingHandel:_,onChangeMembersAccess:b,handleClose:m,hostType:N,isOnlyMemberManager:j,currentOrg:B}=e,$=(0,g.d4)(Ie._B).get(c),P=st($==null?void 0:$.team_cid);return!$||P<0?null:(0,r.jsx)(kr,{theme:i,targetCid:c,caption:$.name,isAllowSetCollaborators:!0,isShowInheritedCollaborators:P>=1,captionTipType:je.p.Project,currentOrg:B,permissionScope:je.W.Project,isSharing:d,handleBackSharingHandel:_,onChangeMembersAccess:b,handleClose:m,hostType:N,isOnlyMemberManager:j})}const Lr=(0,s.memo)(Tr);var Ar=n(63686);const Or=e=>{const{user:i,initData:c,currentProject:d,members:_,dispatch:b}=e,m=d==null?void 0:d.cid;return(0,s.useEffect)(()=>{(async j=>{b({type:"entry:projectAccess:initProjectData",payload:{initData:c,members:_}})})(m)},[b,c,_,m,i]),i!=null&&i.id?(0,r.jsx)(Lr,{targetCid:m,isSharing:!0,...e}):null},Pr=(0,Ar.Mz)([],()=>({})),Dr=(0,g.Ng)(e=>Pr(e))(Or);var Rr=n(11645),Xo=n(87537),Br=n(33520);function Fr(e){const{getIframeContentHeight:i,handleChangeMembersAccess:c,handleClose:d}=e,_=(0,g.wA)(),b=(0,g.d4)(l.query.getProject),m=(0,g.d4)(l.query.getMembers),N=(0,g.d4)(l.query.getTheme),j=(0,g.d4)(l.query.getTopPageIndex),B=(0,g.d4)(l.query.getHostType),G=(0,g.d4)(l.query.getTabIndex),$=(0,g.d4)(l.query.getMainPage),P=(0,g.d4)(l.query.getLoading),ae=(0,g.d4)(l.query.getInitData),oe=(0,g.d4)(l.query.getOrg),O=(0,g.d4)(l.query.getUser),ne=(0,g.d4)(l.query.getIsEditMode),ie=(0,g.d4)(l.query.getIsOnlyMemberManager),[ge,pe]=(0,s.useState)(!1);(0,s.useEffect)(()=>{const xe=(0,sn.fV)();if(xe){const{mdWMMktList:Te,mtWMMktList:Le,noWMMktList:Ge}=xe,Fe=[...Te,...Le,...Ge];Fe.length&&(0,_s.q)(Fe).then(Ye=>{if(Ye.mdWMMktList.length>0||Ye.mtWMMktList.length>0){if(!(0,zn._5)())return;pe(!0)}})}},[]);const[fe,W]=(0,s.useState)(282);(0,s.useEffect)(()=>{let xe=619;$!=="access"&&(G===0&&j==="edit"?xe=212:(j==="embed"||j==="qrCode"||G===0&&j==="setting")&&(xe=380)),ge&&(xe+=37),W(xe),B!=="proto"&&i(xe)},[B,G,j,$,i,ne,ge]);const ye=(0,s.useCallback)(()=>{_({type:l.entryKey["sharing:init"],payload:{mainPage:"share"}})},[_]);(0,s.useEffect)(()=>{_({type:l.entryKey["sharing:projectAccess:init"]})},[$,_]);const be=(0,s.useCallback)(async(xe,Te)=>{c&&c(xe,Te)},[c]);return P?(0,r.jsx)(Xo.A,{className:X()("sharingBoxV2",""+N,"loading"),children:(0,r.jsx)(Rr.A,{})}):(0,r.jsxs)(Xo.A,{className:X()("sharingBoxV2",""+N,B==="iframe"&&"in-iframe"),style:{height:fe+"px"},id:"v8-share-page",children:[$==="share"?(0,r.jsx)(xn,{}):(0,r.jsx)(Dr,{theme:N,user:O,currentProject:b,members:m,initData:ae,currentOrg:oe,handleBackSharingHandel:ye,onChangeMembersAccess:be,hostType:B,handleClose:d,isOnlyMemberManager:ie}),(0,r.jsx)(Br.r,{})]})}var zr=n(40778);const Ur=e=>{let{theme:i,org:c=null,user:d,project:_,flatKey:b,members:m=[],hostType:N="proto",screenMetaList:j,hostSharingData:B,projShareToEdit:G="",handleClose:$,isOnlyMemberManager:P=!1,hostCurrentScreen:ae}=e;const oe=se();let O=!0;N!=="iframe"&&(O=(0,zr.OB)());const ne=G?1:0;oe.dispatch({type:l.entryKey["sharing:init"],payload:{project:_,theme:i,user:d,org:c,flatKey:b,hostType:N,members:m,screenMetaList:j,hostSharingData:B,isEditMode:O,tabIndex:ne,projShareToEdit:G,hostCurrentScreen:ae}});const ie=(0,s.useCallback)(pe=>{window.top.postMessage(JSON.stringify({sharingV2Message:"sharing:height",payload:{height:pe}}),"*")},[]),ge=(0,s.useCallback)((pe,fe)=>{window.top.postMessage(JSON.stringify({sharingV2Message:"sharing:changeMembersAccess",payload:{member:pe,value:fe}}),"*")},[]);return N==="iframe"&&P&&(oe.dispatch({type:l.entryKey["sharing:init"],payload:{mainPage:"access",isOnlyMemberManager:!0}}),ie(585)),(0,s.useEffect)(()=>{if(N==="iframe"&&!P&&ie(212),!_)return;const{cid:pe}=_;(async()=>{if(!_)return;const{result:{result:W},statusOk:ye}=await(0,J.QC)(pe);ye&&oe.dispatch({type:l.entryKey["sharing:init"],payload:{canEditByUser:W}})})()},[N,ie,_,oe]),(0,r.jsx)(g.Kq,{store:oe,children:(0,r.jsx)(A.NP,{theme:le.A[i],children:(0,r.jsx)(ee.$,{children:(0,r.jsx)(Fr,{getIframeContentHeight:ie,handleChangeMembersAccess:ge,handleClose:$})})})})}},87537:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>p});var s=n(21676);const p=s.Ay.div.withConfig({displayName:"styles__StyledMainPage",componentId:"sc-8fjrks-0"})(["width:414px;background-color:",";color:",";position:relative;height:212px;&.loading{height:212px;}&.dark{.personal-project-access{.title{span{color:rgba(255,255,255,0.9);}}.content{span{color:rgba(255,255,255,0.9);}}}.access-operation{.open-access-switch > span{color:rgba(255,255,255,0.9);}.item-center > button{background-color:#252626;&:hover{background-color:#666666;}span{color:rgba(255,255,255,0.9);}}}.sharing-footer{background-color:#151515;.sharing-type{span{color:rgba(255,255,255,0.9);}.toolbar-icon-item > svg > path{color:rgba(255,255,255,0.9);}}.sharingV2-click-visible{&:hover{background-color:#666666;}span{color:rgba(255,255,255,0.9);}}}}.access-operation{margin:20px 20px 14px;.open-access-switch{display:flex;align-items:center;margin-bottom:20px;span{font-weight:500;font-size:14px;line-height:20px;color:#333333;margin-right:6px;}}}"],M=>M.theme.color_bg_white,M=>M.theme.color_text_L2)},60482:(Ne,de,n)=>{"use strict";n.d(de,{CV:()=>U,EL:()=>Y,Mb:()=>f,Mj:()=>w,O8:()=>I,R0:()=>E,RF:()=>H,R_:()=>J,Sy:()=>C,Uh:()=>q,a2:()=>he,eH:()=>S,it:()=>x,jc:()=>D,kv:()=>V,vt:()=>y,zM:()=>v});var s=n(51691),p=n(88223),M=n(76713),u=n.n(M),k=n(41449),te=n(72907),Z=n(44864),z=n(36521),T=n(39547);const E=l=>l?[{value:"public",label:I18N.imockSharing.share_anyone_view},{value:"restricted",label:I18N.imockSharing.org_members_only}]:[{value:"public",label:I18N.imockSharing.share_anyone_view}],U=l=>{let{accessToken:L,targetKey:R,pageKey:se,view_mode:g,selection:A,page_begin:ee,canvas_begin:le}=l;const re=(we,ze)=>ze?"&"+we+"="+ze:"";let X=""+location.origin+k.o$+"/"+L+"/sharing?view_mode="+(g||"read_only");return g==="read_only"?X=""+X+re("screen",se)+re("canvasId",R)+re("selection",A):g==="device"?X=""+X+re("screen",ee!=null?ee:se)+re("canvasId",le!=null?le:R)+re("selection",A):X=""+X+re("screen",se)+re("selection",A),X},q=l=>l&&l.length>0?l[0]:"",J=(l,L,R,se)=>{const g=se?"  \u5BC6\u7801\uFF1A"+se:"";return l+" #"+(0,p.qk)(L)+"-"+(0,p.qk)(R||"\u5206\u4EAB")+g},he=l=>""+(["v6.modao.cc"].includes(location.host)?"https://modao.cc":location.origin)+k.o$+"/"+l,V=l=>{const{project:L,action:R,linkName:se,isFromScreenList:g}=l;s.Y4.trackShareProjectAction({project_type:"\u539F\u578B V8",source:g?"\u9875\u9762\u5217\u8868\u83DC\u5355":(0,s.IQ)(),project_name:L.name,project_cid:L.cid,share_link_action:R,share_link_name:se})},Y=l=>{if(!l)return!1;const{created_at:L}=l;return u()(L).isBetween(u()().subtract(3,"days"),u()())},H=l=>Object.entries(l).filter(R=>{let[se,g]=R;return!g.hotAttr.asFolder&&se!==Z.$k}).map(R=>R[0]),w=(l,L,R)=>{if(l)return R.length;let se=0;return L==null||L.forEach(g=>{R.find(ee=>ee===g)&&(se+=1)}),se},C=(l,L)=>{if(L.screen_visible_switch){const{selectedSize:R,pageSize:se}=f(l,L);return R+"/"+se}else return I18N.imockSharing.all},f=(l,L)=>{const R=H(l.pageAttrMap),se=w(!L.screen_visible_switch,L.screen_visible_list,R);return{pageSize:R.length,selectedSize:se}},y=l=>{const L={},R=[],se=new Map;for(const[ee,le,re]of l)if(re.type==="rResBunch"){ee!==Z.$k&&R.push(ee),L[ee]={hotAttr:re},se.get(ee)||se.set(ee,[]);const X=se.get(le);Array.isArray(X)&&se.set(le,[...X,ee])}const g=ee=>{let le=[];for(const X of se.get(ee))le.push(g(X));le=le.sort((X,we)=>X.data.zIndex-we.data.zIndex);const{hotAttr:re}=L[ee];return{cid:ee,data:{cid:ee,...re},children:le}},A=g("B@main");return{pageAttrMap:L,pageKeyList:R,treeData:A}};function v(l,L,R){let se=I18N.imockSharing.share;return L&&(se=se+" "+(L.length+1)),{project_cid:l,view_access:"public",screen_visible_switch:!1,screen_visible_list:[],wechat:!1,highlight:!0,view_sticky:!0,comment_permission:"org_member",is_default_link:!1,password:"",device_model:"read_only",view_prd:!1,expire_type:"forever",sticky:!1,...R,link_name:se,type:"advanced"}}const S=async l=>{let{updateType:L,org:R,updateFn:se,updatedKV:g,isCustom:A}=l;const ee=()=>{const re=(R==null?void 0:R.plan)==="org_full"?"MaxOrgSize":"NormalOrgSize";window.SharingEventEmitter.emit("sharing:count",{whichModal:re}),window.top&&window.top.postMessage(JSON.stringify({sharingMessage:"sharing:count",payload:{whichModal:re}}),"*")},le=()=>{if(!A&&L==="update"){const re={sharingMessage:"sharing:notice",payload:{type:"settingSuccess"}};try{g!=null&&g.expire_type?MB.notice({text:I18N.imockSharing.sharing_hasRest,type:"success"}):MB.notice({text:I18N.imockSharing.setting_valid})}catch(X){window.top&&window.top.postMessage(JSON.stringify(re),"*")}}};try{const re=await se();if(re)return le(),re}catch(re){await(0,te.m0)(re,ee)}},x=l=>{let{value:L,currentSharing:R,sharingList:se}=l,g=!1;for(const A of se)if(A.cid!==R.cid&&L===A.link_name){g=!0;break}return g},I=(l,L,R)=>{let se;if(L.type==="default"){const A=new URLSearchParams(location.search),ee=A.get("view_mode")||(0,z.Yt)(l.cid+"_default_sharing_view_mode","read_only",z.qW.String),le=A.get("selection"),re=A.get("screen"),X=A.get("canvasId");se=U({accessToken:L.access_token,view_mode:ee,page_begin:L.is_first_canvas_open?"":re,canvas_begin:L.is_first_canvas_open?"":X,selection:le,pageKey:L.is_first_canvas_open?"":R})}else L.screen_visible_switch?R&&L.screen_visible_list.includes(R)?se=U({accessToken:L.access_token,view_mode:L.device_model,pageKey:L.is_first_canvas_open?"":R}):se=U({accessToken:L.access_token,view_mode:L.device_model}):se=U({accessToken:L.access_token,view_mode:L.device_model,pageKey:L.is_first_canvas_open?"":R});return J(se,l==null?void 0:l.name,L.link_name||"",L.password)},D=(l,L)=>L&&(L==null?void 0:L.otype)!=="personal"?T.t.InitialOrg(L).limitationSdk.prototypeLimit.pLimitation.max_project_share_count:l?T.t.InitialUser(l).limitationSdk.prototypeLimit.pLimitation.max_project_share_count:1},72907:(Ne,de,n)=>{"use strict";n.d(de,{$r:()=>T,AG:()=>k,FY:()=>u,K7:()=>z,ai:()=>te,m0:()=>Z});var s=n(46795),p=n(33386),M=n(39719);const u=async(E,U)=>{if((0,p.p)("[alertAsyncBlocked]",E,{url:E.url,status:E.status}),E.status===403||/403/.test(E.message)){var q;(q=MB)==null||(q=q.messageBucket)==null||q.send("mobileLoadProjectError",{errMsg:I18N.dPages.cant_edit_desc}),await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.cant_edit,confirmText:I18N.dModule.exit_editing,desc:I18N.dModule.cant_edit_desc}).then(()=>U?U():MB.global.onBackButtonClick())}else if(E.status===401||/401/.test(E.message))z(),await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.err_info_modal.INVALID_COOKIE.title,desc:I18N.dModule.err_info_modal.INVALID_COOKIE.desc,confirmText:I18N.dModule.confirm}).then(()=>U?U():k());else{var J;const he=String(E.status||E.message||"UNKNOWN").replace(/\s/g,"").slice(0,16);(J=MB)==null||(J=J.messageBucket)==null||J.send("mobileLoadProjectError",{errMsg:window.I18N.dModule.unknown_error.replace(/%s/i,he)}),await MB.global.popupHelper.alertAsyncBlocked({desc:window.I18N.dModule.unknown_error.replace(/%s/i,he),isHTML:!0})}},k=async()=>{try{await(0,s.DE)("/api/web/v3/initial"),location.reload()}catch(E){const U=location.origin+"/signin?next="+location.pathname;location.href=U}},te=E=>{E.status===404&&E.error_type==="MT_NOT_FOUND"&&MB.notice({text:I18N.dModals.template_no_available,type:"error"})},Z=async(E,U)=>{E.status===403||/403/.test(E.message)?U():(E.status===401||/401/.test(E.message))&&await MB.global.popupHelper.alertAsyncBlocked({title:I18N.dModule.err_info_modal.INVALID_COOKIE.title,desc:I18N.dModule.err_info_modal.INVALID_COOKIE.desc,confirmText:I18N.dModule.confirm}).then(()=>k())},z=()=>{window.top.postMessage(JSON.stringify({sharingMessage:"sharing:loginInvalid"}),"*")},T=function(E){let{onClick:U}=E===void 0?{}:E;U&&!ENV.IS_MO?MB.notice({text:window.I18N.dModule.network_api_error_1,CustomChildComponent:(0,M.DV)({onClick:U}),type:"error",duration:5e3}):MB.notice({text:window.I18N.dModule.network_api_error,type:"error"})}},42226:(Ne,de,n)=>{"use strict";n.d(de,{JQ:()=>q,kZ:()=>V,tg:()=>Y,zW:()=>J});var s=n(76713),p=n.n(s),M=n(35736),u=n.n(M),k=n(54466),te=n.n(k);if(n.j!=15)var Z=n(29601);function z(w){return w<10?"0"+w:w}function T(w,C,f){const y=new Date(w),v=y.getFullYear(),S=z(y.getMonth()+1),x=z(y.getDate()),I=z(y.getHours()),D=z(y.getMinutes());return C==="time"?I+":"+D:C==="datetime"?""+S+f+x+" "+I+":"+D:""+v+f+S+f+x+" "+I+":"+D}const E=n.j!=15?["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Spt","Oct","Nov","Dec"]:null;function U(w,C){const f=new Date(w),y=f.getFullYear(),v=E[f.getMonth()],S=z(f.getDate()),x=z(f.getHours()),I=z(f.getMinutes());return C==="time"?x+":"+I:C==="datetime"?v+" "+S+" "+x+":"+I:v+" "+S+", "+y+" "+x+":"+I}function q(w,C,f){if(C===void 0&&(C="datetime"),f===void 0&&(f="."),!w)return"";let y=w;typeof w!="number"&&(y=parseInt(w,10)),y.length===10&&(y=y*1e3);let v="";const S=(0,Z.w)();return S==="zh-CN"?v=T(y,C,f):(S==="en-US"||S==="en")&&(v=U(y,C,f)),v}function J(w,C){C===void 0&&(C=he());const f=C-w;let y="";if(f<=3*1e3)y=I18N.Common.just_now||"\u521A\u521A";else if(f>3*1e3&&f<60*1e3)y=Math.floor(f/1e3)+I18N.Common.num_seconds_ago||"\u79D2\u524D";else if(f>=60*1e3&&f<60*60*1e3)y=Math.floor(f/(60*1e3))+I18N.Common.num_minutes_ago||"\u5206\u949F\u524D";else{y=q(w,"full");const v=new Date(new Date().setHours(0,0,0,0)).getTime(),S=new Date(new Date().getFullYear(),0,1).getTime();w>S&&(y=q(w,"datetime")),w>v&&(y=q(w,"time"))}return y}function he(){return new Date().getTime()}const V=w=>{p().locale(w==="zh-CN"?"zh-cn":w),p().extend(u()),p().extend(te())},Y=()=>p()().isBefore("2024-10-06"),H=w=>new Promise(C=>setTimeout(C,w))},98794:(Ne,de,n)=>{"use strict";n.d(de,{qk:()=>Y,I2:()=>J,ZI:()=>he});const s={set:function(H,w,C,f){f===void 0&&(f="/");let y="";if(y+=H+"="+encodeURIComponent(w),f&&(y+=";path="+f),C){const v=new Date;v&&(v.setTime(v.getTime()+C*1e3),y+=";expires="+v.toUTCString())}document.cookie=y},get:function(H){const w=document.cookie.split(";");let C="";for(let f=0;f<w.length;f++){const y=w[f].indexOf(H);if(y!==-1){C=w[f].substring(y+H.length+1,w[f].length);break}}return C}},p=function(H,w){if(H!=="visit")return;const C="visit_id",f=60*24*3600;return s.set(C,w,f,"/"),w},M=function(){const H=s.get("visit_id");if(H&&H!="")return H;const w=new Date,C=w.getFullYear().toString().substr(2,2),f=w.getMonth()+1,y=w.getDate(),v=w.getHours(),S=w.getMinutes(),x=w.getSeconds(),I=w.getMilliseconds();let D=Math.round(Math.random()*Math.random()*1e4).toString();const l=D.length;l===1?D=D+"000":l===2?D=D+"00":l===3&&(D=D+"0");let L="2_"+C;return L+=f<10?"0"+f:f,L+=y<10?"0"+y:y,L+=v<10?"0"+v:v,L+=S<10?"0"+S:S,L+=x<10?"0"+x:x,I<10?L+="00"+I:I<100?L+="0"+I:L+=I,L+="_"+D,p("visit",L),L};var u=function(H){return H.WWW="www",H.WWW_TEST="www-test",H.OVERSEA="oversea",H.OVERSEA_TEST="oversea-test",H}(u||{});const k={www:"https://sc.bosyun.cn","www-test":"https://sc.bosyun.cn",oversea:"https://sc.bosyun.net","oversea-test":"https://sc.bosyun.net"},te="MD_",Z="modao",z=()=>{let H=u.WWW;return/^([a-z0-9]+\.)*mockitt\.com$/.test(location.host)&&(H=u.OVERSEA),H},T=()=>{const H=window.sensorsDataAnalytic201505;if(!H)return;const w=z();H.init({name:"sensors",server_url:k[w]+"/sa.gif?project=production",is_track_single_page:!1,use_client_time:!0,show_log:!1,send_type:"beacon",heatmap:{clickmap:"not_collect",scroll_notice_map:"not_collect"}}),H.identify(M(),!0),H.registerPage({product:Z}),window.sensors=H},E=function(H){var w;if(window.sensors){H=""+te+H;for(var C=arguments.length,f=new Array(C>1?C-1:0),y=1;y<C;y++)f[y-1]=arguments[y];(w=window.sensors)==null||w.track(H,...f)}},U=function(){return window.sensors&&window.sensors.setOnceProfile(...arguments)},q=function(){return window.sensors&&window.sensors.setProfile(...arguments)},J=ENV.NO_TRACK?()=>{}:T,he=ENV.NO_TRACK?()=>{}:E,V=ENV.NO_TRACK?()=>{}:U,Y=ENV.IS_MO?H=>{if(!window.gtag)return console.error("gtag is not a function");window.gtag("config","UA-4839360-64",{user_id:H,transport_type:"beacon"})}:()=>{}},36114:(Ne,de,n)=>{"use strict";n.d(de,{U:()=>z});var s=n(98794),p=n(27374),M=n(97788),u=n(98195);const k={[p.SR.PageV9]:"\u751F\u6210\u539F\u578B\u9875\u9762",[p.SR.Component]:"\u751F\u6210\u7EC4\u4EF6",[p.SR.Flow]:"\u751F\u6210\u6D41\u7A0B\u56FE",[p.SR.Mind]:"\u751F\u6210\u601D\u7EF4\u5BFC\u56FE",[p.SR.Table]:"\u751F\u6210\u8868\u683C",[p.SR.Chart]:"\u751F\u6210\u56FE\u8868",[p.SR.Semantic]:"AI\u6307\u4EE4",[p.SR.Chat]:"AI\u5BF9\u8BDD",[p.SR.AutoFill]:"\u667A\u80FD\u586B\u5145"},te={[u.lS.Directory]:"\u76EE\u5F55\u9762\u677F",[u.lS.Builtin]:"\u7EC4\u4EF6\u9762\u677F",[u.lS.Icon]:"\u56FE\u6807\u9762\u677F",[u.lS.Page]:"\u9875\u9762\u9762\u677F",[u.lS.Asset]:"\u56FE\u7247\u9762\u677F",[u.lS.Template]:"\u6BCD\u7248\u9762\u677F",[u.lS.AI]:"AI\u9762\u677F"},Z={color:"\u989C\u8272\u6837\u5F0F",appear:"\u5916\u89C2\u6837\u5F0F",text:"\u6587\u672C\u6837\u5F0F"};let z;(function(T){const E=T.AIOpenSourceTrack=l=>{const L=k[l];L&&(0,s.ZI)("proto_ai_entrance",{ai_func_name:L})},U=T.AIUseTrack=(l,L)=>{const R=k[l];!R||!L||(0,s.ZI)("proto_ai_use",{ai_func:R,ai_input:L})},q=T.addShapeToCanvasTrackSourceMap={quickCreate:"\u5FEB\u6377\u952E",topToolbar:"\u9876\u90E8\u64CD\u4F5C\u680F"},J=T.addShapeToCanvasTrack=(l,L)=>{l&&L&&(0,s.ZI)("proto_shape_add",{name:l,source:L})},he=T.addDrawIOToCanvasTrack=l=>{l&&(0,s.ZI)("proto_draw_add",{type:l})},V=T.addAnimationTrack=l=>{l&&I18N.dConst.ani[l]&&(0,s.ZI)("proto_animation_add",{type:I18N.dConst.ani[l]})},Y=T.updateInteractionTrack=l=>{var L;const{type:R,interactionTrigger:se,interactionType:g,value:A=""}=l;if(!se||!g)return;const ee=(0,M.Oz)(se);if(!ee)return;const re=(L=(0,M.gu)().find(X=>X.value===g))==null?void 0:L.label;re&&(0,s.ZI)("proto_interaction_set",{type:R,trigger:ee,action:re,value:A})},H=T.searchResultTrack=(l,L,R)=>{const se=te[L];!l||!se||(0,s.ZI)("proto_search_result",{keyword:l,source:se,count:R})},w=T.watermarkExposureTrack=l=>{if(!l)return;const L=!!MB.currentOrg;(0,s.ZI)("proto_watermark_show",{watermark_type:l,workspace_type:L?"org":"solo"})},C=T.protoEditorLoadSuccessTrack=l=>{l&&(0,s.ZI)("proto_load_success",{uid:l})},f=T.protoEditorShareTrack=(l,L)=>{l&&(0,s.ZI)("proto_share",{uid:l,source:L})},y=T.protoEditorPresentTrack=(l,L)=>{l&&(0,s.ZI)("proto_present",{uid:l,source:L})},v=T.protoDownloadTrack=(l,L,R)=>{!l||!L||!R||(0,s.ZI)("proto_download",{uid:l,type:L,source:R})},S=T.protoStyleTrack=(l,L,R)=>{!l||!L||!R||(0,s.ZI)("proto_style_set",{uid:l,type:L,source:Z[R]})},x=T.protoMasterClickTrack=l=>{l&&(0,s.ZI)("proto_master_click",{uid:l})},I=T.protoMasterPublishTrack=(l,L)=>{!l||!L||(0,s.ZI)("proto_master_publish",{uid:l,type:L})},D=T.protoMasterUseTrack=(l,L,R)=>{!l||!L||!R||(0,s.ZI)("proto_master_use",{uid:l,source:L,type:R})}})(z||(z={}))},47755:(Ne,de,n)=>{"use strict";n.d(de,{_:()=>s});const s=M=>encodeURIComponent(JSON.stringify(M)),p=M=>JSON.parse(decodeURIComponent(M))},67234:(Ne,de,n)=>{"use strict";n.d(de,{p:()=>U,A:()=>he});var s=n(74059),p=n(51044),M=n(66748),u=n.n(M),k=n(98236),te=n.n(k),Z=n(7290),z=n(86634),T=n.n(z),E=n(21676);const U="checkbox-has-box",q=E.Ay.label.withConfig({displayName:"styles__StyledCheck",componentId:"sc-gd8gcf-0"})(["position:relative;display:flex;align-items:center;cursor:pointer;color:",";> input[type=radio],> input[type=checkbox]{position:absolute;opacity:0;width:12px;height:12px;}.Check-state{position:relative;width:12px;height:12px;font-size:12px;display:flex;align-items:center;border:1px solid ",";border-radius:2px;transition:all 0.2s ease-in-out;background-color:",";flex-shrink:0;.icon{position:absolute;top:-1px;left:-1px;}}.Check-label{margin-left:0.33333em;color:",";}&.is-checked .Check-state{background-color:",";border:1px solid ",";color:#fff;.icon{transform:scale(0.833);color:#fff;}}&:not(.is-checked) .Check-state .icon{speak:none;opacity:0;}&.readonly,&.is-disabled{cursor:default;.Check-state{background:",";border:1px solid ",";.icon{color:",";}}}&.is-disabled{cursor:not-allowed;}&.","{input,.Check-state{margin:6px;transition:none;}.Check-label{margin-left:4px;}}"],V=>V.theme.color_text_L2,V=>V.theme.color_text_disabled01,V=>V.theme.color_bg_white,V=>V.theme.color_text_L2,V=>V.theme.color_proto,V=>V.theme.color_proto,V=>V.theme.color_btn_secondary_active,V=>V.theme.color_btn_secondary_active,V=>V.theme.color_text_disabled01,U);var J=n(57464);class he extends p.PureComponent{constructor(){super(...arguments),(0,s.A)(this,"state",{prevProps:this.props,isChecked:this.props.isChecked}),(0,s.A)(this,"onToggle",Y=>{const{name:H,value:w,label:C,onChange:f,onToggle:y,isDisabled:v,readOnly:S,attr:x}=this.props,{isChecked:I}=this.state,D=!v&&!S,l=D?I==="mixed"?!0:!I:I;this.setState({isChecked:l}),y(l,H,w||C,Y),f(l,x,Y)})}static getDerivedStateFromProps(Y,H){let{prevProps:w}=H;return te()(w,Y)?null:{prevProps:Y,isChecked:Y.isChecked}}render(){const{className:Y,label:H,name:w,isDisabled:C,readOnly:f}=this.props,{isChecked:y}=this.state,v=T()(Y,{"is-checked":!!y,"is-disabled":C,readonly:f});return(0,J.jsxs)(q,{className:v,children:[(0,J.jsx)("input",{type:"checkbox",defaultChecked:!!y,disabled:C,name:w,onChange:this.onToggle}),(0,J.jsx)("span",{className:"Check-state",children:(0,J.jsx)(Z.C,{name:y==="mixed"?"new_replace/box_check_mixed":"new_replace/box_check"})}),H&&(0,J.jsx)("span",{className:"Check-label",children:H})]})}}(0,s.A)(he,"propTypes",{isChecked:u().bool||"mixed",isDisabled:u().bool,readOnly:u().bool,onChange:u().func.isRequired,onToggle:u().func.isRequired,label:u().any,name:u().string,attr:u().string,value:u().any,className:u().string}),(0,s.A)(he,"defaultProps",{isChecked:!1,label:"",className:"",onChange:()=>null,onToggle:()=>null})},80582:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>z});var s=n(74059),p=n(51044),M=n(21183),u=n(66748),k=n.n(u),te=n(57464);class Z extends p.PureComponent{constructor(E){super(E),(0,s.A)(this,"freshCanvas",()=>{const U=this.canvasWrapperRef.current,{height:q,width:J,text:he,foreground:V,background:Y}=this.props,H=(0,M.j)({height:q,width:J,text:he,foreground:V,background:Y});U.hasChildNodes()?U.replaceChild(H,U.firstChild):U.appendChild(H)}),this.canvasWrapperRef=(0,p.createRef)()}componentDidMount(){this.freshCanvas()}componentDidUpdate(){this.freshCanvas()}render(){const{className:E}=this.props;return(0,te.jsx)("div",{className:E,ref:this.canvasWrapperRef})}}Z.propTypes={width:k().number,height:k().number,text:k().string.isRequired,foreground:k().string,background:k().string,className:k().string},Z.defaultProps={width:150,height:150};const z=Z},29739:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>u});var s=n(46479),p=n(21676);const u=(0,p.Ay)(s.ft).withConfig({displayName:"ThemeLoading",componentId:"sc-1f7bkn3-0"})(["svg g g:first-child g path:last-child{stroke:",";}svg g g:last-child g path:last-child{stroke:",";}"],k=>k.theme.color_btn_secondary_active,k=>k.theme.color_text_disabled01)},11645:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>Z});var s=n(51044),p=n(29739),M=n(21676);const u=M.Ay.div.withConfig({displayName:"styles__StyledSharingLoading",componentId:"sc-iidm8l-0"})(["position:absolute;top:0;left:0;right:0;bottom:0;display:flex;align-items:center;justify-content:center;.loading{width:50px;height:50px;}"]);var k=n(57464);const Z=(0,s.memo)(()=>(0,k.jsx)(u,{className:"sharing-loading",children:(0,k.jsx)(p.A,{className:"loading"})}))},13005:()=>{"use strict";const Ne={},de={},n={},s={}},33520:(Ne,de,n)=>{"use strict";n.d(de,{l:()=>M,r:()=>p});var s=n(21676);const p=(0,s.DU)(["::-webkit-scrollbar-track{background-color:transparent;}::-webkit-scrollbar{width:4px;height:4px;border-radius:4px;}::-webkit-scrollbar-thumb{background-color:",";border-radius:4px;&:hover{background-color:",";}}::-webkit-scrollbar-corner{background-color:transparent;}.widget.panel{::-webkit-scrollbar-thumb{background-color:#d2d2d8;&:hover{background-color:",";}}}"],u=>u.theme.color_bg_border_02,u=>u.theme.color_background_split_hover,u=>u.theme.color_background_split_hover),M=(0,s.DU)([".ModalPortal,.CoreModalPortal,.OverlayPortal{min-width:initial !important;}"])},57716:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>H});var s=n(35524),p=n(54909);const M="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQwIDc5LjE2MDQ1MSwgMjAxNy8wNS8wNi0wMTowODoyMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDkzNzZFN0RDODhFMTFFOEExOUJFNkQ0MTNFM0Q2OTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDkzNzZFN0VDODhFMTFFOEExOUJFNkQ0MTNFM0Q2OTYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpEOTM3NkU3QkM4OEUxMUU4QTE5QkU2RDQxM0UzRDY5NiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpEOTM3NkU3Q0M4OEUxMUU4QTE5QkU2RDQxM0UzRDY5NiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PktroGEAAAAoSURBVHjaYvz06RMDDPDy8sLZTAw4AOkSjP///4dzPn/+TAs7AAIMAG56COJosoI3AAAAAElFTkSuQmCC",u="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAAGzCI4dAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAACKADAAQAAAABAAAACAAAAACVhHtSAAAAVklEQVQYGYWOMQ6AMAwD04ot7wJWyv+/kblgpIsqZcBDGtu1W9uPc9o17tk0IsKaJHe3rpGKvZAgbCxyhS+jBaOLrCiRciM7iNEFLwkMznwDge/BfxsegvIceOfO91wAAAAASUVORK5CYII=",k=Object.entries(p.f).reduce((w,C)=>{const f=C[0];return{...w,[f]:[p.f[f].value_light,p.f[f].value_dark||p.f[f].value_light]}},{}),te=Object.entries(p.$).reduce((w,C)=>{const f=C[0];return{...w,[f]:[p.$[f].value_light,p.$[f].value_dark||p.$[f].value_light]}},{}),Z=Object.entries(s.af).reduce((w,C)=>{var f;const y=C[0];return{...w,[y]:[s.af[y].value,((f=s.qY[y])==null?void 0:f.value)||s.af[y].value]}},{}),z=w=>{let C;const[f,y,v]=w,S=v!=null?v:f;return y==="light"?C=s.af[S].value:C=s.qY[S].value,{[f]:[C,C]}},E=[["color_tips_black","dark","color_ruler_shadow_color"]].reduce((C,f)=>{const y=z(f);return{...C,...y}},{}),V={...Z,...E,...{loading_styles_bg:["linear-gradient(130deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%)","linear-gradient(118.3deg, #474848 1.71%, #363738 100%)"],float_nav_box_shadow:["0 4px 8px 0 rgba(39, 54, 78, 0.08), 0 4px 12px 0 rgba(39, 54, 78, 0.06)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],menu_content_shadow:["0 6px 16px -8px rgba(0,0,0,0.08), 0 9px 28px 0 rgba(0,0,0,0.05)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],shadow_1:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 6px 0 rgba(0,0,0,0.30), 0 10px 30px 0 rgba(0,0,0,0.15), inset 0 1px 0 0 #252626"],shadow_2:["0 2px 10px 0 rgba(39, 54, 78, 0.8), 0 12px 40px 0 rgba(39, 54, 78, 0.1)","0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20)"],shadow_3:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 7px 0 rgba(0, 0, 0, 0.26), 0 12px 36px 0 rgba(0, 0, 0, 0.1)"],shadow_4:["0 2px 10px 0 rgba(0, 0, 0, 0.12)","0 11px 15 px rgba(0, 0, 0, 0.2), 0 9 px 46px 0 rgba(0, 0, 0, 0.12)"],shadow_5:["0 2px 10px 0 rgba(0, 0, 0, 0.12)","0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12), 0 11px 15px -7px rgba(0, 0, 0, 0.2)"],shadow_hover:["0 2px 4px rgba(0, 0, 0, 0.14)","0 2px 4px rgba(0, 0, 0, 0.46), 0 8px 16px rgba(0, 0, 0, 0.40)"],miss_font_box_shadow:["0 3px 10px rgba(0, 0, 0, 0.1)","0px 24px 38px rgba(0, 0, 0, 0.14), 0px 9px 46px rgba(0, 0, 0, 0.12), 0px 11px 15px rgba(0, 0, 0, 0.2)"],export_bar_box_shadow:["0 -4px 8px rgba(219, 219, 219, 0.4)","0 -4px 8px rgba(0, 0, 0, 0.35)"],sidebar_box_shadow:["0 2px 10px 0 rgba(39, 54, 78, 0.08), 0 12px 40px 0 rgba(39, 54, 78, 0.1)",""],interation_active_box_shadow:["0 0 4px 0 rgba(34, 162, 237, 0.66)"," 0 0 4px 0 rgba(41,141,248,0.50)"],modal_bg1:["linear-gradient(to bottom,#fafafa,#f5f5f5)","#4f5052"],modal_shadow:["0 2px 10px 0 rgba(0, 0, 0, 0.1), 0 12px 40px 0 rgba(0, 0, 0, 0.1)",""],comment_layer_box_shadow:["0 -2px 10px rgba(26, 58, 109, 0.12)","0 -24px 38px 3px rgba(0,0,0,0.14), 0 -9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.20)"],workspace_dropdown_menu_shadow:["0 2px 8px 0 rgba(0, 0, 0, 0.1)","0 2px 7px 0 rgba(0, 0, 0, 0.26), 0 12px 36px 0 rgba(0, 0, 0, 0.1)"],drag_sort_box_shadow:["0 1px 4px 0 rgba(0, 0, 0, 0.15)","0px 2px 4px rgba(0, 0, 0, 0.15)"],annotate_btn_shadow:["0 2px 6px rgba(39, 54, 78, 0.08)"," 0 6px 10px rgba(0, 0, 0, 0.22), 0px 13px 30px rgba(0, 0, 0, 0.146744)"],color_var_bind_point:["#DEDFE0","#4B4B4B"],bg_base64:[M,u],img_wrap_border_width:[1,2],fontPosition:["73px",0],disabled_opacity:[1,.5],footer_disabled_opacity:[1,.6]},...k,...te,...{color_literal_number:["#015CC5","#218BFF"],color_literal_string:["#002155","#80CCFF"],color_literal_boolean:["#8250DF","#A475F9"],color_literal_operator:["#F23DD1","#E85AAD"]},...{expr_editor_string:["#002155","#80CCFF"],expr_editor_number:["#015CC5","#218BFF"],expr_editor_boolean:["#8250DF","#A475F9"],expr_editor_func:["#23863A","#2DA44E"],expr_editor_builtinVar:["#8250DF","#A475F9"]}},Y=(w,C)=>{const f={};return Object.entries(w).forEach(y=>{let[v,S]=y;Array.isArray(S)?f[v]=S[C]:typeof S=="object"?f[v]=Y(S,C):f[v]=S}),f},H={light:Y(V,0),dark:Y(V,1)}},48590:(Ne,de,n)=>{"use strict";n.d(de,{Aw:()=>u,L9:()=>p,ZJ:()=>T,eP:()=>Z,n9:()=>te,nw:()=>z});var s=n(21676);const p=(0,s.AH)(["white-space:nowrap;overflow:hidden;text-overflow:ellipsis;"]),M=(0,s.AH)(['position:relative;pointer-events:none;&::after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;cursor:not-allowed;pointer-events:auto;}']),u=(0,s.AH)(["position:relative;height:32px;padding-left:14px;padding-right:4px;display:flex;align-items:center;border:1px solid transparent;color:",";font-size:12px;cursor:pointer;.expander{width:12px;margin-left:-12px;color:",";}.editable-span,.editable-name{margin-right:14px;line-height:24px;border-radius:2px;&.is-editing{border-bottom-color:transparent;}}.actions{display:none;height:100%;padding-left:6px;.Dropdown > button{display:flex;padding:8px 10px 8px 0;}.action{margin-right:6px;font-size:12px;color:",";&:hover{color:",";}}&.is-active{display:flex;align-items:center;color:",";}}&:hover{.actions{display:flex;align-items:center;}}&:hover,&.hover{color:",";background:",";}&.active,&.is-active{color:",";background:",";}"],E=>E.theme.color_text_L2,E=>E.theme.color_text_disabled01,E=>E.theme.color_text_L2,E=>E.theme.color_text_L2,E=>E.theme.color_text_L1,E=>E.theme.color_text_L1,E=>E.theme.color_btn_secondary_hover,E=>E.theme.color_text_L1,E=>E.theme.color_btn_secondary_active),k=s.Ay.div.withConfig({displayName:"variables__StyledCustomTooltipInner",componentId:"sc-1k65wxr-0"})(["display:flex;align-items:center;height:22px;margin:-3px -7px;.tipsLeft{padding:0 8px;}.tipsRight{width:22px;line-height:22px;background-color:",";border-radius:0 2px 2px 0;color:#f2f4f5;}"],E=>E.theme.color_primary_brand01),te=(0,s.AH)(["width:24px;height:24px;display:flex;justify-content:center;align-items:center;color:",";cursor:pointer;.fore{fill:",";}&:hover{color:",";.fore{fill:",";}}.svg-icon{width:24px;height:24px;}"],E=>E.theme.color_text_L2,E=>E.theme.color_text_L3,E=>E.theme.color_proto,E=>E.theme.color_proto),Z=(0,s.AH)(["ul{.active,.select,.hover,li:hover,.rn-list-item:hover{& + li > .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}+ ul > li:first-child > .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}.rn-list-item:has(+ ul > li.active:first-child,+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}li{&:has(+ li.active,+ li.select,+ li.hover,+ li:hover){&.active,&.select,&.hover,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}.rn-list-item{border-radius:6px;&.context-menu-select{border-radius:6px;}}}li.active,li.select,li.dummy-select,li:hover{&:has(> ul > li.dummy-select){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}&:has(+ li.active,+ li.select,+li:hover){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}}ul{li.dummy-select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}&:has(+ li.dummy-select){> .rn-list-item{border-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-radius:0;}}}}}&:last-child,&:first-child{&:has(> ul){> .rn-list-item{border-radius:0;}}}}}}li.active,li.select,li.dummy-select,li:hover{& + li{&.active,&.select,&.dummy-select,&:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li{> .rn-list-item:hover{&:has(+ ul > li.active:first-child,+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}& + ul > li.active:first-child,& + ul > li.select:first-child,& + ul > li.hover:first-child,& + ul > li:first-child:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li:has(+ li:hover,+ li.select,+ li.active){ul > li:last-child{&.active,&.select,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}li:has(ul > li:last-child.active,ul > li:last-child.select,ul > li:last-child:hover){& + li:hover,& + li.select,& + li.active{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}"]),z=(0,s.AH)(["ul{li{.rn-list-item{border-radius:6px;&.context-menu-select{border-radius:6px;}}}li.select,li.dummy-select,li:hover{&:has(> ul > li.dummy-select){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}&:has(+ li.select,+li:hover){> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}}ul{li.dummy-select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}&:has(+ li.dummy-select){> .rn-list-item{border-radius:0;}ul{li.dummy-select{&:last-child{> .rn-list-item{border-radius:0;}}}}}&:last-child,&:first-child{&:has(> ul){> .rn-list-item{border-radius:0;}}}}}}li.select,li.dummy-select,li:hover{& + li{&.select,&.dummy-select,&:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li{> .rn-list-item:hover{&:has(+ ul > li.select:first-child,+ ul > li.hover:first-child,+ ul > li:first-child:hover){border-bottom-left-radius:0;border-bottom-right-radius:0;}& + ul > li.select:first-child,& + ul > li.hover:first-child,& + ul > li:first-child:hover{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}li:has(+ li:hover,+ li.select){ul > li:last-child{&.select,&:hover{> .rn-list-item{border-bottom-left-radius:0;border-bottom-right-radius:0;}}}}li:has(ul > li:last-child.select,ul > li:last-child:hover){& + li:hover,& + li.select{> .rn-list-item{border-top-left-radius:0;border-top-right-radius:0;}}}}"]),T=(0,s.AH)(["ul{padding:8px;border-radius:8px;border:1px solid ",";color:",";background:",";box-shadow:",";li.MenuItem{a{height:28px;border-radius:4px;background:",";color:",";svg:not(.pure-svg-icon) path{fill:",";}.right-arrow svg > path{fill:",";}.shortfont kbd{color:",";}}&:not(.disabled).active{a{background:",";}}&.disabled{a{color:",";svg path{fill:",";}.shortfont kbd{color:",";}}}}}"],E=>E.theme.color_bg_border_01,E=>E.theme.color_text_L1,E=>E.theme.color_bg_white,E=>E.theme.shadow_m,E=>E.theme.color_bg_white,E=>E.theme.color_text_L1,E=>E.theme.color_text_L1,E=>E.theme.color_text_L3,E=>E.theme.color_text_L3,E=>E.theme.color_btn_secondary_hover,E=>E.theme.color_text_disabled01,E=>E.theme.color_text_disabled01,E=>E.theme.color_text_disabled01)},44057:(Ne,de,n)=>{"use strict";n.d(de,{m:()=>ze,cG:()=>tt,Dr:()=>He,aM:()=>je,Ay:()=>we});var s=n(66748),p=n.n(s),M=n(51044),u=n(60185),k=n(21676),te=n(86634),Z=n.n(te),z=n(90503);class T{constructor(Q,K){Q===void 0&&(Q=0),K===void 0&&(K=0),this.x=Q,this.y=K}inside(Q){const{left:K,right:me,top:Ce,bottom:ke}=Q;return this.x>=K&&this.x<=me&&this.y>=Ce&&this.y<=ke}}const E=(0,M.createContext)(""),U=Pe=>{if(!Pe)return;const Q=Pe.getBoundingClientRect(),K=Pe.classList.contains("sub-menu"),me=1/.8,Ce=1/(.8*.9);if(K){const $e=document.getElementById("root-menu").getBoundingClientRect();$e.top+(Q.top-$e.top)*me+Q.height*Ce>window.innerHeight-28&&Pe.classList.add("top"),$e.left+(Q.left-$e.left)*me+Q.width*Ce>window.innerWidth&&Pe.classList.add("left");return}if(Q.left+Q.width*me>window.innerWidth-28&&Pe.classList.add("left"),Q.height>window.innerHeight){Pe.style.height=window.innerHeight+"px",Pe.style.overflow="auto",Pe.style.overflowX="hidden",Pe.style.marginTop=-Q.top+"px";return}Q.top+Q.height<window.innerHeight||(Pe.style.marginTop=window.innerHeight-(Q.top+Q.height)+"px")},q=function(Pe,Q,K,me,Ce,ke){let{subMenuLeftOffset:$e,subMenuTopOffset:Me}=Ce;if(ke===void 0&&(ke=!1),!Pe||!Q)return;const Ie=Q.getBoundingClientRect(),Je=Q.getBoundingClientRect(),st=1/.9,{left:yt,top:xt}=K;Ie.top+Ie.height*st>window.innerHeight-28?Ie.top-Ie.height*st>0?(Pe.style.bottom="-4px",Pe.style.top="unset"):Pe.style.marginTop=window.innerHeight-28-(Ie.top+Ie.height*st)+"px":Pe.style.top=Ie.top-xt-11+"px",Ie.left+Ie.width*st>window.innerWidth?(Pe.style.right=Ie.left+"px",Pe.style.left="unset"):Pe.style.left=Ie.right-yt-1+12*ke+"px",me==="left"&&(Pe.style.left=-2*Ie.width-12-22*ke+"px"),$e&&(Pe.style.left=parseInt(Pe.style.left)+$e+4*ke+"px"),Me&&(Pe.style.top=parseInt(Pe.style.top)+Me+"px"),Pe.style.maxHeight=window.innerHeight-Je.top+"px"},J=function(Pe,Q,K,me,Ce,ke,$e){let{subMenuLeftOffset:Me,subMenuTopOffset:Ie}=Ce;if(ke===void 0&&(ke=!1),!Pe||!Q)return;const Je=$e.getBoundingClientRect(),st=Q.getBoundingClientRect(),yt=Q.getBoundingClientRect(),{left:xt,top:bt}=K,{top:St}=Je;Pe.style.top=st.top-St-11+"px",Pe.style.left=st.right-xt-1+12*ke+"px",me==="left"&&(Pe.style.left=-2*st.width-12-22*ke+"px"),Me&&(Pe.style.left=parseInt(Pe.style.left)+Me+4*ke+"px"),Pe.style.maxHeight=window.innerHeight-yt.top+"px"},he=(Pe,Q)=>{let{x:K,y:me}=Pe,Ce=!1;for(let ke=0,$e=Q.length-1;ke<Q.length;$e=ke++){let{x:Me,y:Ie}=Q[ke],{x:Je,y:st}=Q[$e];Ie>me!=st>me&&K<(Je-Me)*(me-Ie)/(st-Ie)+Me&&(Ce=!Ce)}return Ce};var V=n(54909);const Y=(0,k.AH)(["background:#333;box-shadow:0 2px 8px rgba(0,0,0,0.3);border-radius:4px;color:#fff;border:1px solid #454647;"]),H=k.Ay.div.withConfig({displayName:"styles__StyledMask",componentId:"sc-h83js-0"})(["position:fixed;top:0;left:0;right:0;width:100vw;height:100vh;z-index:199;"]),w=k.Ay.div.withConfig({displayName:"styles__StyledContextMenu",componentId:"sc-h83js-1"})(["position:fixed;z-index:200;&.size-small > ul{min-width:120px;}ul{list-style:none;}& > ul{position:absolute;top:0;left:0;padding:8px 0;min-width:200px;margin:0;",";&.top{top:initial !important;bottom:-8px;}&.left{left:initial;right:100%;}&.top.left{}&::-webkit-scrollbar{display:block;width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,0.25);border-radius:4px;&:hover{background-color:rgba(255,255,255,0.30);}}}"],Y),C=k.Ay.ul.withConfig({displayName:"styles__StyledSubMenu",componentId:"sc-h83js-2"})(["position:absolute;min-width:200px;max-height:500px;padding:4px 0;",";opacity:0;pointer-events:none;overflow-x:hidden;margin:0;&:lang(ja){min-width:260px;}&.is-show{opacity:1;pointer-events:auto;}&.inner-sub{overflow-x:unset;}&.is-normal{li.MenuItem{& > a{padding-left:32px;}&.hasIcon{> a{padding-left:15px;}}}}&.is-airy{li.MenuItem{& > a{padding-left:29px;}&.hasIcon{> a{padding-left:12px;}}}}"],Y),f=k.Ay.li.withConfig({displayName:"styles__StyledMenuItem",componentId:"sc-h83js-3"})(["position:relative;a{height:28px;padding-left:16px;display:flex;align-items:center;color:#fff;padding-right:10px;& > span{margin-right:0;display:flex;justifyContent:flex-end;flex:1;alignItems:center;.text{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;line-height:20px;}}& > span{white-space:nowrap;margin-right:16px;.invisible{width:20px;height:20px;display:inline-block;color:",";margin-top:-3px;}}.selected-icon{width:8px;margin-right:9px;}.right-arrow{margin-left:auto;margin-right:-4px;display:flex;align-items:center;}img{width:14px;height:14px;opacity:0.8;margin-right:7px;}.fa-caret-right,.shortcut{margin-left:auto;width:12px;text-align:center;margin-right:-2px;}.shortcut{display:flex;align-items:center;color:#999;.mac{font-size:16px;margin-right:2px;}}.shortfont{display:flex;margin-left:auto;kbd{display:inline-block;text-align:center;min-width:12px;color:rgba(255,255,255,0.7);font-family:Inter;&.key-cmd{width:13px;text-align:right;}&:first-child{margin-left:0;}&:last-child{margin-right:0;}}}}&.hasDivider{a{margin-bottom:4px;}}&:not(.disabled).active{a{background:#666;}}&.disabled{*{cursor:not-allowed;}a{color:rgba(255,255,255,0.22);.right-arrow svg > path{fill:rgba(255,255,255,0.22);}.shortfont kbd{color:rgba(255,255,255,0.22);}}}"],V.f.color_text_L2.value_dark),y=k.Ay.li.withConfig({displayName:"styles__StyledMenuItemFont",componentId:"sc-h83js-4"})(["position:relative;a{height:28px;display:flex;align-items:center;color:#fff;& > span{margin-right:0;display:flex;justifyContent:flex-end;flex:1;alignItems:center;&.text{flex:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;line-height:20px;}&.is-svg{height:14px;}}& > span{white-space:nowrap;margin-left:20px;}.selected-icon{width:16px;position:absolute;}.right-arrow{margin-right:8px;display:flex;align-items:center;}img{width:14px;height:14px;opacity:0.8;margin-right:7px;}.fa-caret-right,.shortcut{margin-left:auto;width:12px;text-align:center;margin-right:-2px;}}&:not(.disabled).active{a{background:#666;}}"]);var v=n(57464);function S(Pe,Q,K){return(Q=x(Q))in Pe?Object.defineProperty(Pe,Q,{value:K,enumerable:!0,configurable:!0,writable:!0}):Pe[Q]=K,Pe}function x(Pe){var Q=I(Pe,"string");return typeof Q=="symbol"?Q:Q+""}function I(Pe,Q){if(typeof Pe!="object"||!Pe)return Pe;var K=Pe[Symbol.toPrimitive];if(K!==void 0){var me=K.call(Pe,Q||"default");if(typeof me!="object")return me;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Q==="string"?String:Number)(Pe)}const D=(0,v.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,v.jsx)("path",{d:"M5.5 5.04031L5.5 10.9597C5.5 11.3789 5.98497 11.612 6.31235 11.3501L10.012 8.39043C10.2622 8.19027 10.2622 7.80973 10.012 7.60957L6.31235 4.64988C5.98497 4.38797 5.5 4.62106 5.5 5.04031Z",fill:"white",fillOpacity:"0.9"})}),l="M1016 416L1016 -1856L328 -1856L328 416ZM512 -320L832 -320L832 -64L512 -64ZM512 -704L576 -704L576 -512L768 -512L768 -640L704 -640L704 -576L640 -576L640 -704L832 -704L832 -448L512 -448ZM512 192L728 64L512 64L512 0L832 0L832 65L617 192L832 192L832 256L512 256ZM512 -1408L704 -1408L704 -1248L832 -1248L832 -1184L512 -1184ZM512 -1536L624 -1536L624 -1632L512 -1632L512 -1696L832 -1696L832 -1632L688 -1632L688 -1536L832 -1536L832 -1472L512 -1472ZM512 -960L640 -960L640 -1056L512 -1056L512 -1120L832 -1120L832 -1056L704 -1056L704 -896L512 -896ZM512 -832L768 -832L768 -992L832 -992L832 -768L512 -768ZM768 -128L768 -256L576 -256L576 -128ZM640 -1248L640 -1344L576 -1344L576 -1248Z",L="M800 -800L800 -1240L1240 -1240L1240 -800Z",R="M860 416L172 416L548 -1856L1236 -1856ZM478 -320L436 -64L756 -64L798 -320ZM542 -704L499 -448L819 -448L862 -704L670 -704L648 -576L712 -576L723 -640L787 -640L766 -512L574 -512L606 -704ZM393 192L383 256L703 256L713 192L498 192L734 65L745 0L425 0L414 64L630 64ZM658 -1408L621 -1184L941 -1184L952 -1248L824 -1248L850 -1408ZM679 -1536L669 -1472L989 -1472L999 -1536L855 -1536L871 -1632L1015 -1632L1026 -1696L706 -1696L695 -1632L807 -1632L791 -1536ZM584 -960L573 -896L765 -896L792 -1056L920 -1056L930 -1120L610 -1120L600 -1056L728 -1056L712 -960ZM563 -832L552 -768L872 -768L909 -992L845 -992L819 -832ZM702 -128L510 -128L531 -256L723 -256ZM760 -1248L696 -1248L711 -1344L775 -1344Z",se="M1016 416L328 416L328 -1856L1016 -1856ZM512 -320L512 -64L832 -64L832 -320ZM512 -704L512 -448L832 -448L832 -704L640 -704L640 -576L704 -576L704 -640L768 -640L768 -512L576 -512L576 -704ZM512 192L512 256L832 256L832 192L617 192L832 65L832 0L512 0L512 64L728 64ZM512 -1408L512 -1184L832 -1184L832 -1248L704 -1248L704 -1408ZM512 -1536L512 -1472L832 -1472L832 -1536L688 -1536L688 -1632L832 -1632L832 -1696L512 -1696L512 -1632L624 -1632L624 -1536ZM512 -960L512 -896L704 -896L704 -1056L832 -1056L832 -1120L512 -1120L512 -1056L640 -1056L640 -960ZM512 -832L512 -768L832 -768L832 -992L768 -992L768 -832ZM768 -128L576 -128L576 -256L768 -256ZM640 -1248L576 -1248L576 -1344L640 -1344Z",g=3;let A=[],ee=!1;const le=new Map,re=Pe=>{A.push(Pe),A.length>g&&A.shift()},X=Pe=>{Pe.ctrlKey===!0&&Pe.preventDefault()};class we extends M.PureComponent{constructor(Q){super(Q),S(this,"setMenuRef",K=>this.$menu=K),S(this,"createContextMenuRef",K=>this.$mountRoot=K),S(this,"mousewheelHandle",K=>{const me=K.deltaX||K.wheelDeltaX;if(!me||!this.$elem)return;const Ce=this.$elem.scrollLeft+this.$elem.offsetWidth===this.$elem.scrollWidth&&me>0,ke=this.$elem.scrollLeft===0&&me<0;(Ce||ke)&&K.preventDefault()}),S(this,"handleClose",K=>{const{onClose:me}=this.props;me&&me(K)}),S(this,"handleCloseMouse",K=>{K.button===2&&this.handleClose(K)}),S(this,"handleClickMenu",K=>{const{alwaysOpen:me}=this.props;me||this.handleClose(K)}),S(this,"handleUpdateState",K=>this.setState({...K})),this.$elem=document.createElement("div"),this.state={activePath:[],showSubMenu:!1}}componentDidMount(){document.body.appendChild(this.$elem),!this.props.style&&U(this.$menu),this.$elem&&this.$elem.addEventListener("mousewheel",this.mousewheelHandle),this.$elem&&this.$elem.addEventListener("wheel",X)}componentWillUnmount(){this.$elem&&this.$elem.removeEventListener("mousewheel",this.mousewheelHandle,{capture:!0}),this.$elem&&this.$elem.removeEventListener("wheel",X,{capture:!0}),A=[],ee=!1,this.$elem&&document.body.contains(this.$elem)&&document.body.removeChild(this.$elem)}preventDefault(Q){Q.preventDefault()}render(){const{position:Q,children:K,className:me,style:Ce={}}=this.props,{top:ke,left:$e}=Q,{activePath:Me,showSubMenu:Ie}=this.state;return(0,u.createPortal)((0,v.jsx)(H,{className:"context-menu-mask",onMouseDown:this.handleClose,onClick:this.handleClickMenu,children:(0,v.jsx)(w,{ref:this.createContextMenuRef,style:{top:ke+2,left:$e+2,...Ce},className:me,onContextMenu:this.preventDefault,children:(0,v.jsx)("ul",{ref:this.setMenuRef,children:(0,v.jsx)(E.Provider,{value:{mountRoot:this.$mountRoot,contextMenuPosition:Q,activePath:Me,showSubMenu:Ie,onUpdateState:this.handleUpdateState},children:K})})})}),this.$elem)}}S(we,"propTypes",{className:p().string,position:p().object,children:p().oneOfType([p().element,p().array]),style:p().object,onClose:p().func,alwaysOpen:p().bool});class ze extends M.PureComponent{constructor(Q){super(Q),S(this,"setMenuRef",K=>this.$menu=K),S(this,"createContextMenuRef",K=>this.$mountRoot=K),S(this,"mousewheelHandle",K=>{const me=K.deltaX||K.wheelDeltaX;if(!me||!this.$elem)return;const Ce=this.$elem.scrollLeft+this.$elem.offsetWidth===this.$elem.scrollWidth&&me>0,ke=this.$elem.scrollLeft===0&&me<0;(Ce||ke)&&K.preventDefault()}),S(this,"handleClose",K=>{const{onClose:me}=this.props;me&&me(K)}),S(this,"handleCloseMouse",K=>{K.button===2&&this.handleClose(K)}),S(this,"handleClickMenu",K=>{const{alwaysOpen:me}=this.props;me||this.handleClose(K)}),S(this,"handleUpdateState",K=>this.setState({...K})),this.$elem=document.createElement("div"),this.state={activePath:[],showSubMenu:!1}}componentDidMount(){document.body.appendChild(this.$elem),this.$elem&&this.$elem.addEventListener("mousewheel",this.mousewheelHandle)}componentWillUnmount(){this.$elem&&this.$elem.removeEventListener("mousewheel",this.mousewheelHandle,{capture:!0}),A=[],ee=!1,this.$elem&&document.body.contains(this.$elem)&&document.body.removeChild(this.$elem)}preventDefault(Q){Q.preventDefault()}render(){const{position:Q,children:K,className:me,style:Ce={}}=this.props,{top:ke,left:$e}=Q,{activePath:Me,showSubMenu:Ie}=this.state;return(0,v.jsx)(w,{ref:this.createContextMenuRef,style:{top:ke+2,left:$e+2,...Ce},className:me,onContextMenu:this.preventDefault,children:(0,v.jsx)("ul",{ref:this.setMenuRef,children:(0,v.jsx)(E.Provider,{value:{mountRoot:this.$mountRoot,contextMenuPosition:Q,activePath:Me,showSubMenu:Ie,onUpdateState:this.handleUpdateState,containerRef:this.$elem,isEmbed:!0},children:K})})})}}S(ze,"propTypes",{className:p().string,position:p().object,children:p().oneOfType([p().element,p().array]),style:p().object,onClose:p().func,alwaysOpen:p().bool});class He extends M.PureComponent{constructor(){super(...arguments),S(this,"createMenuItemRef",Q=>this.$menuItemRef=Q),S(this,"createSubMenuRef",Q=>this.$subMenuRef=Q),S(this,"handleActiveItem",(Q,K)=>{const{children:me}=this.props;K({activePath:Q,showSubMenu:!!me})}),S(this,"handleMouseEnter",(Q,K,me)=>{const{onMouseEnter:Ce}=this.props;Ce&&Ce(Q),ee?setTimeout(()=>{if(this.$menuItemRef&&A.length>0){const ke=this.$menuItemRef.getBoundingClientRect();A.slice(-1)[0].inside(ke)&&this.handleActiveItem(K,me)}},200):this.handleActiveItem(K,me)}),S(this,"handleMouseLeave",async(Q,K,me,Ce)=>{const{onMouseLeave:ke}=this.props;if(ke&&ke(Q),me&&this.$subMenuRef){const $e=this.$subMenuRef.getBoundingClientRect();if($e&&A.length>0){const{left:Me,top:Ie,bottom:Je}=$e,st=new T(Me,Ie),yt=new T(Me,Je),xt=[A[0],st,yt],bt=new T(Q.clientX,Q.clientY);if(he(bt,xt)){ee=!0;return}}}if(K){Ce({activePath:K.dataset.index.split(","),showSubMenu:!0}),ee=!1;return}Ce({activePath:[],showSubMenu:!1}),ee=!1}),S(this,"handleMouseMove",Q=>{const K=new T(Q.clientX,Q.clientY);re(K)}),S(this,"handleMouseDown",Q=>Q.stopPropagation()),S(this,"handleGetCanActiveSubMenu",(Q,K,me)=>{let Ce=!1;return K&&(Q&&K.join("")===me.join("")||K.slice(0,me.length).join("")===me.join(""))&&(Ce=!0),Ce}),S(this,"handleGetPath",Q=>{const{dataIndex:K,text:me}=this.props,Ce=K!==void 0?K:me;return Q?Q.dataset.index.split(",").concat(Ce):[Ce]}),S(this,"handleClick",Q=>{const{disabled:K,onClick:me,canClick:Ce,children:ke}=this.props;(Ce||!K)&&me&&me(Q),(ke||K)&&Q.stopPropagation()})}render(){const{icon:Q,text:K,children:me,disabled:Ce,hotKeyText:ke,className:$e,dataType:Me,subMenuClassName:Ie,subMenuDirection:Je,subMenuLeftOffset:st,subMenuTopOffset:yt,isVisible:xt,isAiry:bt=!1}=this.props;return(0,v.jsx)(E.Consumer,{children:St=>{const{contextMenuPosition:r,mountRoot:ue,activePath:ve,showSubMenu:Ae,parentRef:Ve,onUpdateState:qe,isEmbed:ut=!1}=St,jt=this.handleGetPath(Ve),Ut=this.handleGetCanActiveSubMenu(Ae,ve,jt);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(f,{ref:this.createMenuItemRef,className:Z()("MenuItem",{disabled:Ce,hasIcon:Q},$e,{active:Ut}),"data-type":Me,"data-index":jt,onMouseEnter:Zt=>this.handleMouseEnter(Zt,jt,qe),onMouseLeave:Zt=>this.handleMouseLeave(Zt,Ve,Ae,qe),onMouseMove:this.handleMouseMove,onMouseDown:this.handleMouseDown,onClick:this.handleClick,children:(0,v.jsxs)("a",{children:[Q,(0,v.jsxs)("span",{children:[(0,v.jsx)("span",{className:"text",children:K}),xt?(0,v.jsx)(z.A,{className:"invisible",name:"eyeinvisible"}):null]}),me&&(0,v.jsx)("div",{className:"right-arrow",children:D}),ke&&ke.length>0&&(0,v.jsx)("div",{className:"shortfont",children:ke.map((Zt,$t)=>(0,v.jsx)("kbd",{children:Zt},$t))})]})}),me&&Ut&&!Ce&&(0,u.createPortal)((0,v.jsx)(Ee,{parentRef:this.$menuItemRef,setRef:this.createSubMenuRef,contextMenuPosition:r,subMenuDirection:Je,subMenuLeftOffset:st,subMenuTopOffset:yt,className:Ie,mountRoot:ue,isEmbed:ut,isAiry:bt,children:(0,v.jsx)(E.Provider,{value:{parentRef:this.$menuItemRef,mountRoot:ue,contextMenuPosition:r,activePath:ve,showSubMenu:Ae,onUpdateState:qe},children:me})}),ue)]})}})}}S(He,"propTypes",{icon:p().object,text:p().string.isRequired,className:p().string,disabled:p().bool,canClick:p().bool,children:p().oneOfType([p().element,p().array]),hotKeyText:p().array,onClick:p().func,onMouseEnter:p().func,onMouseLeave:p().func,dataType:p().number,dataIndex:p().oneOfType([p().number,p().string]),subMenuClassName:p().string,subMenuDirection:p().string,subMenuLeftOffset:p().number,subMenuTopOffset:p().number,isVisible:p().bool,isAiry:p().bool}),S(He,"defaultProps",{subMenuClassName:""});class je extends M.PureComponent{constructor(Q){super(Q),S(this,"createMenuItemRef",Me=>this.$menuItemRef=Me),S(this,"createSubMenuRef",Me=>this.$subMenuRef=Me),S(this,"spanRef",Me=>this.$spanRef=Me),S(this,"handleActiveItem",(Me,Ie)=>{const{children:Je}=this.props;Ie({activePath:Me,showSubMenu:!!Je})}),S(this,"handleMouseEnter",(Me,Ie,Je)=>{const{onMouseEnter:st}=this.props;st&&st(Me),ee?setTimeout(()=>{if(this.$menuItemRef&&A.length>0){const yt=this.$menuItemRef.getBoundingClientRect();A.slice(-1)[0].inside(yt)&&this.handleActiveItem(Ie,Je)}},200):this.handleActiveItem(Ie,Je)}),S(this,"handleMouseLeave",async(Me,Ie,Je,st)=>{const{onMouseLeave:yt}=this.props;if(yt&&yt(Me),Je&&this.$subMenuRef){const xt=this.$subMenuRef.getBoundingClientRect();if(xt&&A.length>0){const{left:bt,top:St,bottom:r}=xt,ue=new T(bt,St),ve=new T(bt,r),Ae=[A[0],ue,ve],Ve=new T(Me.clientX,Me.clientY);if(he(Ve,Ae)){ee=!0;return}}}if(Ie){st({activePath:Ie.dataset.index.split(","),showSubMenu:!0}),ee=!1;return}st({activePath:[],showSubMenu:!1}),ee=!1}),S(this,"handleMouseMove",Me=>{const Ie=new T(Me.clientX,Me.clientY);re(Ie)}),S(this,"handleMouseDown",Me=>Me.stopPropagation()),S(this,"handleGetCanActiveSubMenu",(Me,Ie,Je)=>{let st=!1;return Ie&&(Me&&Ie.join("")===Je.join("")||Ie.slice(0,Je.length).join("")===Je.join(""))&&(st=!0),st}),S(this,"handleGetPath",Me=>{const{dataIndex:Ie,text:Je}=this.props,st=Ie!==void 0?Ie:Je;return Me?Me.dataset.index.split(",").concat(st):[st]}),S(this,"handleClick",Me=>{const{disabled:Ie,onClick:Je,canClick:st,children:yt}=this.props;(st||!Ie)&&Je&&Je(Me),(yt||Ie)&&Me.stopPropagation()});const{text:K,fontUrl:me,usePlainText:Ce}=this.props,ke="U="+me+"&T="+K,$e=le.get(ke);Ce?this.state={renderContent:Q.text,contentType:"text"}:this.state={renderContent:($e==null?void 0:$e.content)||Q.text,contentType:($e==null?void 0:$e.type)||"text"}}async componentDidMount(){const{text:Q,fontUrl:K,usePlainText:me}=this.props;if(me)return;const Ce="U="+K+"&T="+Q;if(!le.get(Ce)){if(!K){le.set(Ce,{type:"text",content:Q});return}try{const ke=await(await fetch("/flatkiq/fontsvg/sync.svg?"+Ce)).text();if(ke!==""){const $e=[...new DOMParser().parseFromString(ke,"text/html").querySelectorAll("path")].map(Me=>Me.getAttribute("d"));if($e.find(Me=>Me===l||Me===L||Me===R||Me===se)){le.set(Ce,{type:"text",content:Q});return}$e.filter(Boolean).length!==Q.split("").filter(Me=>Me!==" ").length?le.set(Ce,{type:"text",content:Q}):(le.set(Ce,{type:"svg",content:ke}),this.setState({renderContent:ke,contentType:"svg"}))}else le.set(Ce,{type:"text",content:Q})}catch(ke){console.error(ke),le.set(Ce,{type:"text",content:Q})}}}render(){const{icon:Q,children:K,disabled:me,className:Ce,dataType:ke,subMenuClassName:$e,subMenuDirection:Me,subMenuLeftOffset:Ie,subMenuTopOffset:Je,tooltipComponent:st=null,tooltipWrapper:yt=null}=this.props,{renderContent:xt,contentType:bt}=this.state;return(0,v.jsx)(E.Consumer,{children:St=>{const{contextMenuPosition:r,mountRoot:ue,activePath:ve,showSubMenu:Ae,parentRef:Ve,onUpdateState:qe,isEmbed:ut=!1}=St,jt=this.handleGetPath(Ve),Ut=this.handleGetCanActiveSubMenu(Ae,ve,jt),Zt=(0,v.jsxs)("a",{children:[Q,(0,v.jsx)("span",{dangerouslySetInnerHTML:{__html:xt},className:Z()("text",{"is-svg":bt==="svg"})}),st,K&&(0,v.jsx)("div",{className:"right-arrow",children:D})]});return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(y,{ref:this.createMenuItemRef,className:Z()("MenuItem",{disabled:me,hasIcon:Q},Ce,{active:Ut}),"data-type":ke,"data-index":jt,onMouseEnter:$t=>this.handleMouseEnter($t,jt,qe),onMouseLeave:$t=>this.handleMouseLeave($t,Ve,Ae,qe),onMouseMove:this.handleMouseMove,onMouseDown:this.handleMouseDown,onClick:this.handleClick,children:yt?yt(Zt):Zt}),K&&Ut&&!me&&(0,u.createPortal)((0,v.jsx)(Ee,{parentRef:this.$menuItemRef,setRef:this.createSubMenuRef,contextMenuPosition:r,subMenuDirection:Me,subMenuLeftOffset:Ie,subMenuTopOffset:Je,className:$e,mountRoot:ue,isEmbed:ut,isNoraml:!1,isAiry:!1,children:(0,v.jsx)(E.Provider,{value:{parentRef:this.$menuItemRef,mountRoot:ue,contextMenuPosition:r,activePath:ve,showSubMenu:Ae,onUpdateState:qe},children:K})}),ue)]})}})}}S(je,"defaultProps",{subMenuClassName:""});class Ee extends M.PureComponent{constructor(Q){super(Q),S(this,"createRef",K=>{const{setRef:me}=this.props;me(K),this.subMenu=K}),this.state={isTransform:!1}}componentDidMount(){this.setState({isTransform:!0});const{parentRef:Q,contextMenuPosition:K,subMenuDirection:me,subMenuLeftOffset:Ce,subMenuTopOffset:ke,isAiry:$e,isEmbed:Me,mountRoot:Ie}=this.props;Me?J(this.subMenu,Q,K,me,{subMenuLeftOffset:Ce,subMenuTopOffset:ke},$e,Ie):q(this.subMenu,Q,K,me,{subMenuLeftOffset:Ce,subMenuTopOffset:ke},$e)}render(){const{children:Q,isAiry:K,className:me,isNoraml:Ce=!0}=this.props,{isTransform:ke}=this.state;return(0,v.jsx)(C,{className:Z()("SubMenu",{"is-show":ke},{"is-airy":K},{"is-normal":Ce},me),ref:this.createRef,children:Q})}}S(Ee,"propTypes",{children:p().oneOfType([p().element,p().array]),className:p().string,parentRef:p().node,contextMenuPosition:p().object,setRef:p().func,subMenuDirection:p().string,subMenuLeftOffset:p().number,subMenuTopOffset:p().number,isAiry:p().bool});const tt=k.Ay.div.withConfig({displayName:"ContextMenu__Divider",componentId:"sc-1802crt-0"})(["margin:8px 0;border-top:1px solid #454546;"])},29815:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>S});var s=n(66748),p=n.n(s),M=n(51044),u=n(21676),k=n(54909);const te=u.Ay.div.withConfig({displayName:"styled__StyledCrashedPage",componentId:"sc-8goufi-0"})(["position:fixed;top:50%;left:50%;transform:translateX(-50%) translateY(-55%);text-align:center;color:#666;.logo{margin:0 auto 40px;height:320px;.logo-clickable{cursor:pointer;}.svg-icon{width:600px;fill:none;}}.mobile &{.logo{width:100%;transform:scale(0.8);}.content{.title{font-size:20px;line-height:24px;}.proposal{width:290px;font-size:14px;line-height:20px;}}}p{margin:0;}.content{margin:40px auto;.title{font-weight:600;font-size:24px;line-height:24px;color:#1f292e;}.proposal{width:max-content;margin:12px auto 40px;font-style:normal;font-weight:normal;font-size:16px;line-height:20px;color:#415058;.count-detail{color:#1684fc;}}.text-left{text-align:left;margin-bottom:-12px;}.text-list{margin:0;padding-left:24px;}}.btn-list{display:grid;grid-auto-flow:column;gap:20px;justify-content:center;.btn{width:200px;height:48px;display:inline-block;border-radius:6px;font-size:16px;font-weight:500;line-height:46px;cursor:pointer;transition:all 0.2s ease-out;}.primary{color:",";background:",";border:1px solid transparent;&:hover{background:",";border:1px solid transparent;}&:active{background:",";border:1px solid transparent;}}.regular{color:",";background:",";border:1px solid ",";&:hover{background:",";border:1px solid ",";}&:active{background:",";border:1px solid ",";}}}"],k.f.color_text_btn.value_light,k.f.color_btn_primary_normal.value_light,k.f.color_btn_primary_hover.value_light,k.f.color_btn_primary_clicked.value_light,k.f.color_text_L1.value_light,k.f.color_bg_white.value_light,k.f.color_bg_border_02.value_light,k.f.color_btn_secondary_hover.value_light,k.f.color_bg_border_02.value_light,k.f.color_btn_secondary_active.value_light,k.f.color_bg_border_02.value_light);var Z=n(57464);const z="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M200 158.5c-54.4-5.2-59.3 54.2-55 84.5h308c22.8-46-17.7-69.2-44-73.5 49.3-15.5 19.5-88-28.5-116C258.2-17.8 268 165 200 158.5Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-width='2' d='M145 243h66a2 2 0 0 0 2-2V122.6a2 2 0 0 1 1.6-2L333 94l-101.6 32.6a2 2 0 0 0-1.4 1.9v139a2 2 0 0 0 2.5 2l54-13'/%3E%3Cpath fill='%231F292E' d='M213 121.5v122l17 26V127l100.5-32.5-117.5 27Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M455 243h-38'/%3E%3Ccircle cx='251' cy='198' r='4' fill='%231F292E'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M250.4 182.2Zm2.3-3.2a24.2 24.2 0 0 1 2.7-1c.9-.3 3 .2 4.4 1.1.6.6-.5.2-2 .4l-1.9.4a22.7 22.7 0 0 0-5 2.4 5.2 5.2 0 0 1-.7.3 1.8 1.8 0 0 1-.3.1 1.2 1.2 0 0 1-.3 0 1 1 0 0 1-.6-.2 1 1 0 0 1-.4-.8 1 1 0 0 1 0-.4l.3-.4.5-.5 1-1a2.2 2.2 0 0 0 .2-.3.7.7 0 0 0 0-.1.9.9 0 0 0-.1-.1c-.4-.2-1-.4-2.3-.6a3.2 3.2 0 0 1-.3 0 1.6 1.6 0 0 1-.5-.2 1 1 0 0 1-.5-.9c0-.4.2-.7.3-.8l.6-.4.6-.3.5-.2a13 13 0 0 0 2.7-1.2l.9-.7v-.4c-.1-.2-.4-.6-1.3-1-.8-.6-2-1.1-3.7-1.7a2.6 2.6 0 0 1-.8-.5 1 1 0 0 1-.5-.9c-.1-.5.2-1 .4-1.1.3-.2.6-.4.9-.4 1.6-.4 4-1 5.9-2.8 1.7-1.5 3-4.2 1.8-9.2a2.3 2.3 0 0 1 0-1.2l.2-.4a1 1 0 0 1 1.6-.2c.*******.3.4l.5 1c.8 2 3.5 4.9 6.7 6.7 1.2.7 1.2.8 0 .4-2.2-.6-5.6-2-7.3-4.6 1.3 4.3-.8 7-2.4 8.6a13.1 13.1 0 0 1-5.3 2.8c1.2.4 2 .9 2.8 1.3a5 5 0 0 1 2 2c.4.8.3 1.6-.1 2.2-.4.6-1 1-1.6 1.4l-*******.2c.7.4 1.2 1 1.2 1.9Zm-2 0Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='M292 163.5c61.2 5.2 103.5 55.5 117 80l-115 14-4-12.5 2-8-3-11 3-10 3.5-4v-13l7.5-7c-25-2-35-4-36.5-14.5.5-3.6 3.1-15.9 25.5-14Z'/%3E%3Cpath fill='%231F292E' fill-rule='evenodd' d='M363 191.1a219 219 0 0 1 46.8 51.6 1 1 0 1 1-1.6 1 217 217 0 0 0-46.5-51c-21.7-17-48.6-29.9-78.3-27.4-10.1.8-15 6.3-15.4 11.4-.4 5 3.6 10.6 12.8 11.8l5.2.6c5.4.7 11.2 1.4 16.5 2.4a70 70 0 0 1 12.1 3.2c3.5 1.3 6.5 3 8.5 5.4a1 1 0 0 1-1.6 1.3c-1.6-2-4.2-3.5-7.6-4.8a66.7 66.7 0 0 0-11.3-3c-3.5 2.3-7 6.7-7 12.9-.1 6.3 3.7 14.7 16 24.7 4.7 3.7 9.5 5 13.4 4.5a1 1 0 0 1 1.3-.3 11.1 11.1 0 0 0 1.2-.3c2-.7 3.6-2 4.6-3.7.8-1.4 1-3.2.7-5.3-4.2.6-7.4.3-9.7-.7-2-.8-3.2-2-4-3.6a8.5 8.5 0 0 1-.6-5.2c.3-1.5.8-3 1.6-4.5a26.7 26.7 0 0 1-2.2-2.8 7 7 0 0 1-1.2-2c-.4-.8-.6-1.5-.6-2.2a1 1 0 1 1 2 0c0 .******* *******.6 1.2 1 1.8a24.1 24.1 0 0 0 1.7 2.1 29.4 29.4 0 0 1 19.1-11.4 1 1 0 0 1 .4 2c-5.7.8-10.5 3.2-14 6.3a22.1 22.1 0 0 0-4.3 5 1 1 0 0 1-.4.6 13 13 0 0 0-1.5 4.1c-.3 1.6-.1 3 .4 4 .5 1.1 1.5 2 3 2.7 3 1.3 8.2 1.3 16.3-1.3 3.7-1.5 6.9-2.2 9.3-2.1 2.5 0 4.5.8 5.6 2.1a5 5 0 0 1 .7 5.1 12 12 0 0 1-4.3 5.3c-8 6-7.5 11.8-6.6 13.5a1 1 0 1 1-1.8 1c-1.4-3-1.4-9.6 7.2-16 2-1.5 3.1-3.1 3.6-4.4.5-1.4.3-2.4-.3-3.2-.7-.7-2-1.4-4.1-1.4-2.1 0-5 .5-8.6 2l-6.1 1.6c.4 2.5 0 4.7-1 6.6a10.2 10.2 0 0 1-5.6 4.6 12.3 12.3 0 0 1-1.1.3c.6 2.9.4 6-.7 8.6a9.4 9.4 0 0 1-6.1 5.4c5 4.5 6.4 10 5.1 14.4a8.7 8.7 0 0 1-3 4.6c3.2 2.3 9 5 15.8 5a1 1 0 0 1 1 0 28 28 0 0 0 12.1-3.5 1 1 0 1 1 1 1.8 30 30 0 0 1-11.8 3.6 51.8 51.8 0 0 1 3.2 8c1.2 3.9 2 8.2 1.3 12a1 1 0 1 1-2-.4c.6-3.3 0-7.3-1.2-11-1-3.4-2.4-6.5-3.5-8.5-8 0-14.4-3.3-17.8-6-2 .8-4.6.8-7.2-.3-13.4-5.2-19.9-12-22.4-18.3-2.4-6-1.2-11.6.7-15.1a21.2 21.2 0 0 1-2.8-15.1c.8-4.5 3-8.3 5.6-10.2A17.5 17.5 0 0 1 300 193l-14.1-1.9a765.9 765.9 0 0 1-5.2-.7c-10-1.2-15-7.4-14.6-13.9.5-6.4 6.5-12.3 17.2-13.2 30.4-2.5 57.8 10.7 79.7 27.8Zm-68 23.2c2.3 5.5 7 11.7 15.3 18.4 5 4 10.3 5.5 14.7 5 .5 2.5.4 5.2-.5 7.4a7.7 7.7 0 0 1-6.7 4.6c-3 .3-7.1-.5-12.5-3a28.2 28.2 0 0 1-11.7-9.7 19.2 19.2 0 0 1-3-14.2 15 15 0 0 1 4.4-8.5Zm-2.2 25.2c-1.4 3-2.1 7.6-.1 12.5 2.2 5.7 8.2 12 21.2 17.2 2.3.9 4.3.8 5.8.2a1 1 0 0 1 .7-.3 6.5 6.5 0 0 0 3-4c1-3.7-.3-9.1-5.8-13.4-3.5.3-7.8-.7-13.2-3.3-5.2-2.5-9-5.6-11.6-9Zm75.4 17.1a1 1 0 0 1-.5 1.4 7.7 7.7 0 0 0-3.8 4.5 1 1 0 1 1-2-.5 9.7 9.7 0 0 1 5-5.8 1 1 0 0 1 1.3.4Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='426' x2='259' y1='125' y2='289' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E",T="data:image/svg+xml;base64,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",E="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cg clip-path='url(%23a)'%3E%3Cpath fill='url(%23b)' d='M200 158.5c-54.4-5.2-59.3 54.2-55 84.5h308c22.8-46-17.7-69.2-44-73.5 49.3-15.5 19.5-88-28.5-116C258.2-17.8 268 165 200 158.5Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M145 242h310'/%3E%3Cpath fill='%23fff' d='M195.3 213c-7.5-17.5-7.5-20-7.5-20l38.8-39.8c15.9 23.6 45.6 71.5 37.6 74.6a46 46 0 0 0-17.5 13.4h-63.1s19.2-10.5 11.7-28.1Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='10' d='M378 221.6c-29.5-66-56.2-32.1-109.5-78.5-52.2-34.8-99.8-36-117.2-16-38.6 44.2 90.6 76.6 34.4 112.9'/%3E%3Cpath fill='%23fff' d='M306 149.4c-12.4 6.4-15.5 16.6-15.5 21 0 16.7 14 13.4 21 9.6l14.1 15.5a67.3 67.3 0 0 1-37.1 22.6 97.9 97.9 0 0 0-42.2 23l-26-23 12.2-35.2-26-9.2c15.5-15 27.6-23.9 40.2-54.9 2.9-11.3 10.7-33 20-32.6 9.4.3 5.3 20.5 2.1 30.5 10.5-4.1 13.4 4.7 15.5 3.8 7-3 13.9 2.5 15.8 5.9 5-3 14.8-4.3 18.8 0 10.4 11 11.2 29.8 3.8 35.2 0 0-13.7-5.8-14.6-6.7-.4-.4-1.1-.4-2.1-5.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M306 149c1.5 3.3 4.5 10.5 8.6 13 5.3 3.4 17.6-1.5 12.1-22.5-5.5-21-22.7-23.4-49.4 4.5'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M300.4 125.4c-2.7-2.3-6.8-7.8-16-5m-23.2 17.2c10.2-10.7 17.7-15.5 23.3-17.2m0 0c-2.3-3.2-7.7-6.4-15.7-3.9m-20 15.3c7.8-9 14.5-13.5 20-15.3m0 0c2.8-7 8.3-26-2.3-30.4-15.1-6.3-13.2 35.3-36 62.4a561.6 561.6 0 0 1-43.1 45'/%3E%3Cpath fill='%23fff' d='m330.3 165.8-5.9.5-5.8 6-7.5 7.4-11.5 6.9-11.5 6.9 28.7 11.6 11-11.5 6.8-13.5-.2-10.7-4-3.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M310.8 146.3c-7.5 3.2-24.2 14.2-19 29 3.8 10.8 16.5 10.6 26.5-3.1 10-13.8 20.8-3.9 15.5 10.2-12 31.4-47 34.1-60 40.4-9.7 4.7-16.6 8.7-27.8 18.7'/%3E%3Ccircle cx='383.5' cy='234.5' r='17.5' fill='%23fff' stroke='%231F292E' stroke-width='2'/%3E%3Ccircle cx='381.5' cy='229.5' r='18.5' fill='%231684FC'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linejoin='round' stroke-width='2' d='M329.7 261.3a1 1 0 0 1 .7-.3h139a1 1 0 0 1 .7 1.7l-17.8 16a1 1 0 0 1-.7.3h-139a1 1 0 0 1-.7-1.7l17.8-16Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linejoin='round' stroke-width='2' d='M313.7 285.5a1 1 0 0 0 .9.5h139.7a1 1 0 0 0 .8-1.5l-2.8-5a1 1 0 0 0-.9-.5H311.7a1 1 0 0 0-.8 1.5l2.8 5ZM472 269.1a1 1 0 0 1-.3.7L457 285a1 1 0 0 1-1.6-.2l-2.9-5.1a1 1 0 0 1 .2-1.2l17.7-16a1 1 0 0 1 1.7.7v5.9Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='square' stroke-linejoin='round' stroke-width='4' d='M466 277.5c16.3 6.3 39.2 21.6 0 32-39.2 10.4-45 32.7-43 42.5'/%3E%3Crect width='2' height='6' x='342' y='266.9' fill='%231F292E' rx='1' transform='rotate(-27 342 267)'/%3E%3Crect width='2' height='6' x='392' y='266.9' fill='%231F292E' rx='1' transform='rotate(-27 392 267)'/%3E%3Crect width='2' height='6' x='429.9' y='267.1' fill='%231F292E' rx='1' transform='rotate(-31 429.9 267)'/%3E%3Crect width='2' height='6' x='351.9' y='266.1' fill='%231F292E' rx='1' transform='rotate(31 352 266.1)'/%3E%3Crect width='2' height='6' x='402.2' y='266.2' fill='%231F292E' rx='1' transform='rotate(36 402.2 266.2)'/%3E%3Crect width='2' height='6' x='440.6' y='266.3' fill='%231F292E' rx='1' transform='rotate(43 440.6 266.3)'/%3E%3Crect width='4' height='10' x='374' y='225' fill='%231F292E' rx='2'/%3E%3Crect width='4' height='10' x='385' y='225' fill='%231F292E' rx='2'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M406 190.4Zm1.6 3.5a24 24 0 0 1-.5 2.9c-.2.9-1.7 2.5-3.2 3.2-.8.3 0-.5.6-1.8.2-.5.5-1.1.6-1.9a22.7 22.7 0 0 0 .7-5.6 5 5 0 0 1 0-1l.2-.3c0-.1.2-.3.5-.4.3-.2.6 0 .9 0l.3.3.2.4.2.7a8.4 8.4 0 0 0 .5 1.8 1 1 0 0 0 .3 0 9 9 0 0 0 1.6-1.7 3.3 3.3 0 0 1 .2-.3l.4-.3a1 1 0 0 1 1 0c.*******.6.7v.7l-.1.7v.5c-.2 1-.4 2-.4 3l.2 1c0 .*******.2.2 0 .7 0 1.6-.5.8-.4 2-1.2 3.3-2.4l.8-.4c.2-.1.6-.2 1 0 .*******.8 1l-.1 1c-.5 1.5-1.2 4-.7 6.4.5 2.3 2.2 4.7 7 *******.9.4 1.1.7l.2.4c0 .1.2.4 0 .8a1 1 0 0 1-.6.6h-.5a12.7 12.7 0 0 1-1.1 0c-2.1-.3-6 .6-9.2 2.4-1.2.6-1.3.6-.3-.4 1.6-1.5 4.5-3.7 7.6-3.8a7.9 7.9 0 0 1-6.1-6.5c-.5-2.2-.1-4.3.3-5.9l-2.6 1.7a5 5 0 0 1-2.7.7c-.9 0-1.5-.5-1.9-1.2-.3-.6-.4-1.4-.4-2l.1-2a4 4 0 0 1-.4.4c-.8.4-1.6.5-2.3 0Zm1-1.7Z' clip-rule='evenodd'/%3E%3C/g%3E%3Cdefs%3E%3ClinearGradient id='b' x1='426' x2='259' y1='125' y2='289' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3CclipPath id='a'%3E%3Cpath fill='%23fff' d='M0 0h600v320H0z'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E",U="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M332.3 78.2a71 71 0 0 0-110-27.1c-35.6 37.9-27 57.2-37.5 90.3-13 41.3-45.8 28-45.8 101 0 57.5 111.8 79 162.3 27.2 31-31.7 72.1-13.8 118-27.2 66.1-19 52.2-130-19.4-130-53.9 0-61.5-17-67.6-34.2Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M80 242h440'/%3E%3Cpath fill='%231F292E' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M156.8 260.3V134h140.5l18.5 120.7-159 5.6Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m139 134.4 17.8-.4v126.3L124 241l15-106.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m150.7 134.9-2.6 115m-3.8-115.5-4.8 65.1m-3 20.7-3 25.9m23.3-96.6 142.2-.8'/%3E%3Ccircle cx='165' cy='141.3' r='2.2' fill='%23FF6161'/%3E%3Ccircle cx='172.7' cy='141.3' r='2.2' fill='%23FFD361'/%3E%3Ccircle cx='180.5' cy='141.3' r='2.2' fill='%237BECB6'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m301.8 244.2-107 25.4a2 2 0 0 1-1.7-.3c-38.9-29-36-119.8-36-119.8l142.4-.8s7 37.9 23.5 49.4c1 .7.5 3.8-.6 3.9l-5 .4a1 1 0 0 0-.8 1.5l3.2 5.9a1 1 0 0 1-1.2 1.4l-11.7-4a1 1 0 0 0-1.3 1.4l1.8 3.6a1 1 0 0 1-1 1.4l-19-1.3c-1 0-1.5 1.2-.7 1.8l10.7 7.8a1 1 0 0 1 0 1.7l-3.4 2a1 1 0 0 0-.3 1.5l3.8 4.7a1 1 0 0 1-.6 1.7l-7.2 1.2a1 1 0 0 0-.2 2l12.5 5.6a1 1 0 0 1-.2 1.9Z'/%3E%3Cpath fill='%23F33' d='m226.1 198.2-1-13.2a.3.3 0 0 1 .2-.3l7-.5a.3.3 0 0 1 .3.2l1 13.2a.3.3 0 0 1-.2.3l-7 .6a.3.3 0 0 1-.3-.3Zm12.3-1-1-13.2a.3.3 0 0 1 .2-.3l7-.5a.3.3 0 0 1 .3.2l1 13.2a.3.3 0 0 1-.2.3l-7 .5a.3.3 0 0 1-.3-.2Zm19.7-15.2a.3.3 0 0 1 .3.3l.9 10.4v3a22.2 22.2 0 0 1-11.2 18.1.3.3 0 0 1-.4-.1l-3.4-6.1a.3.3 0 0 1 .2-.4c4.7-2.8 7.7-8 7.2-13.9l-.8-10.4a.3.3 0 0 1 .2-.3l7-.6Z'/%3E%3Cpath fill='%23fff' d='m378.8 208.7-6.5-1.8c-6.5-3.2-19.5-8.3-19.5-3.5 0 6.1.9 24 12.6 40a121.7 121.7 0 0 1 16.9 35.1l79-28.2v-33.8s17.7-46 17.7-53.5-14-5-20.5 4.5-7 13-7 13c-5.5-9-11.5-8.2-15-6.6-3.8-5.2-13.1-8.2-17.3-9-4.2-9.8-12.2-11-15.6-10.5l-15.7 3-4.3 30.9 17.4-7.9c1.7 5.4 3.9 18-1.3 25.2-5.2 7.3-11.2 8.3-13.5 7.8l-7.4-4.7Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M400.7 181.2c-3.5 1.6-10.7 5.1-15.7 4.3-6.3-1-12.7-13.1 5.9-25.9s33.7-2.9 35.7 37.2'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M421 166.5c3.7 0 10.7-1.5 16 6.8m6.5 29.4c-.7-15.3-3.3-24.2-6.5-29.4m0 0c4-1 10.3.2 14.9 7.6m5.3 25.7c0-12.5-2.2-20.6-5.3-25.7m0 0c2.6-7.4 11.2-26 22.4-22.2 16.2 5.3-13.7 36.6-14.1 73.3a571 571 0 0 0 4.4 73.8'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m383.9 206-51.3-27.4c-.9-.5-.6-1.9.5-2l18.9-.6a1 1 0 0 0 .7-1.6l-6.9-9.9a1 1 0 0 1 1-1.5l8.3 2a1 1 0 0 0 1.2-1.1l-1.4-7.6a1 1 0 0 1 1.6-1l10.6 8.4a1 1 0 0 0 1.6-1l-5.1-18c-.3-1.1 1.1-1.8 1.8-1l8 11a1 1 0 0 0 1.8-.4l3.8-12a1 1 0 0 1 1.7-.4l5 4.9a1 1 0 0 0 1.4-.1l9.4-11.2c.6-.8 2-.2 1.7.8l-12.9 69a1 1 0 0 1-1.4.8Z'/%3E%3Cpath fill='%23fff' d='m370.1 177.8 4.4 4.4.4 8.7.9 10.8L380 215l4.4 13.2-30.4-10.4-.9-16.5 4-15.2 7.3-8.3h5.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M398.7 175.8c3.7 7.6 9.4 27.5-4.9 35.6-10.3 6-20-2.9-18.6-20.5 1.5-17.6-13.6-17-19-2.4-12 32.7 13.7 58.6 19.5 72.3 5.9 13.7 8.8 23 10.8 51.3'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='M410 106.7Zm.9 3.8a24.1 24.1 0 0 1-1 2.7c-.3.8-2 2.2-3.7 2.7-.8 0 .2-.5 1-1.7l.9-1.8a22.7 22.7 0 0 0 1.5-5.4 5.4 5.4 0 0 1 .3-1l.2-.3.6-.3c.3 0 .6 0 .8.2.*******.3.4l.1.4v.7a8.3 8.3 0 0 0 .3 1.8 1 1 0 0 0 .2 0c.4-.1 1-.5 2-1.3l.2-.3.4-.2a1 1 0 0 1 1 .2c.*******.5.7l-.1.8a10.7 10.7 0 0 1-.4 1.1c-.3.9-.7 2-.8 2.8v1.2l.2.2 1.7-.2c.9-.3 2.1-.9 3.7-1.8.3-.2.5-.3.8-.3.3 0 .7-.1 1 .*******.7.6 1 0 .4 0 .7-.2 1-.7 1.5-1.9 3.8-1.8 6.2.1 2.4 1.4 5 5.9 *******.7.6 1 1v.3a1 1 0 0 1-.8 1.3 1.4 1.4 0 0 1-.5 0l-1.1-.3c-2-.6-6-.4-9.4.8-1.3.5-1.4.4-.3-.4 1.9-1.2 5-2.9 8.2-2.5a7.9 7.9 0 0 1-5-7.4c0-2.2.6-4.3 1.3-5.8-1 .6-2 1-2.8 1.3a5 5 0 0 1-2.8.2 2 2 0 0 1-1.6-1.5c-.2-.7-.2-1.4 0-2 0-.7.2-1.3.4-2a4.3 4.3 0 0 1-.5.3c-.8.3-1.6.3-2.3-.3Zm1.3-1.5Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='427.5' x2='219.7' y1='147.8' y2='310.6' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E",q="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='320' fill='none'%3E%3Cpath fill='url(%23a)' d='M332.3 78.2a71 71 0 0 0-110-27.1c-35.6 37.9-27 57.2-37.5 90.3-13 41.3-45.8 28-45.8 101 0 57.5 111.8 79 162.3 27.2 31-31.7 72.1-13.8 118-27.2 66.1-19 52.2-130-19.4-130-53.9 0-61.5-17-67.6-34.2Z' opacity='.6'/%3E%3Cpath stroke='%231F292E' stroke-width='2' d='M80 242h440'/%3E%3Cpath fill='%231F292E' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M156.8 260.3V134h140.5l18.5 120.7-159 5.6Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m139 134.4 17.8-.4v126.3L124 241l15-106.5Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m150.7 134.9-2.6 115m-3.8-115.5-4.8 65.1m-3 20.7-3 25.9m23.3-96.6 142.2-.8'/%3E%3Cpath fill='%23FF6161' d='M165 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%23FFD361' d='M172.7 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%237BECB6' d='M180.5 143.5a2.2 2.2 0 1 0 0-4.3 2.2 2.2 0 0 0 0 4.3Z'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m301.6 241.5-107 25.4a2 2 0 0 1-1.6-.3c-39-29-36-119.7-36-119.7l142.3-.9s7 37.9 23.6 49.5c1 .6.5 3.7-.7 3.8l-5 .5a1 1 0 0 0-.9 1l.1.4 3.2 6a1 1 0 0 1-1.2 1.3l-11.7-4a1 1 0 0 0-1.2 1.4l1.7 3.7a1 1 0 0 1-1 1.4l-19-1.4a1 1 0 0 0-.6 1.8l10.7 7.9a1 1 0 0 1-.1 1.6l-3.4 2.1a1 1 0 0 0-.4.7 1 1 0 0 0 .2.8l3.8 4.7a1 1 0 0 1-.6 1.6l-7.2 1.3a1 1 0 0 0-.3 1.9l12.5 5.6a1 1 0 0 1-.2 2Z'/%3E%3Cpath fill='%23fff' d='m378.8 208.7-6.5-1.8c-6.5-3.2-19.5-8.3-19.5-3.5 0 6.1.9 24 12.6 40a121.7 121.7 0 0 1 16.9 35.1l79-28.2v-33.8s17.7-46 17.7-53.5-14-5-20.5 4.5-7 13-7 13c-5.5-9-11.5-8.2-15-6.6-3.8-5.2-13.1-8.2-17.3-9-4.2-9.8-12.2-11-15.6-10.5l-15.7 3-4.3 30.9 17.4-7.9c1.7 5.4 3.9 18-1.3 25.2-5.2 7.3-11.2 8.3-13.5 7.8l-7.4-4.7Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M400.7 181.2c-3.5 1.6-10.7 5.1-15.7 4.3-6.3-1-12.7-13.1 5.9-25.9 18.5-12.7 33.7-2.9 35.7 37.2'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M421 166.5c3.7 0 10.7-1.5 16 6.8m0 0c3.2 5.2 5.8 14 6.5 29.4m-6.5-29.4c4-1 10.3.2 14.9 7.6m0 0a49.3 49.3 0 0 1 5.3 25.7m-5.3-25.7c2.6-7.4 11.2-26 22.4-22.2 16.2 5.3-13.7 36.6-14.1 73.3a571 571 0 0 0 4.4 73.8'/%3E%3Cpath fill='%23fff' stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m383.9 206-51.3-27.4c-.9-.5-.6-1.9.5-2l18.9-.6a1 1 0 0 0 1-1.1c0-.2-.2-.4-.3-.5l-6.9-9.9a1 1 0 0 1 1-1.5l8.3 2a1 1 0 0 0 1.2-1.1l-1.4-7.6a1 1 0 0 1 1.6-1l10.6 8.4a1 1 0 0 0 1.6-1l-5.1-18c-.3-1.1 1.1-1.8 1.8-1l8 11a1 1 0 0 0 1.8-.4l3.8-12a1 1 0 0 1 1.7-.4l5 4.9a1 1 0 0 0 1.1.1l.3-.2 9.4-11.2c.6-.8 2-.2 1.7.8l-12.9 69a1 1 0 0 1-.5.8 1 1 0 0 1-1 0Z'/%3E%3Cpath fill='%23fff' d='m370.1 177.8 4.4 4.4.4 8.7.9 10.8L380 215l4.4 13.2-30.4-10.4-.9-16.5 4-15.2 7.3-8.3h5.6Z'/%3E%3Cpath stroke='%231F292E' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M398.7 175.8c3.7 7.6 9.4 27.5-4.9 35.6-10.3 6-20-2.9-18.6-20.5 1.5-17.6-13.6-17-19-2.4-12 32.7 13.7 58.6 19.5 72.3 5.9 13.7 8.8 23 10.8 51.3'/%3E%3Cpath fill='%23FF6161' fill-rule='evenodd' d='m410.9 110.5-1 2.7c-.3.8-2 2.2-3.7 2.7-.8 0 .2-.5 1-1.7l.9-1.8a22.7 22.7 0 0 0 1.7-6.1l.1-.3.2-.3.6-.3c.3 0 .6 0 .8.2.*******.3.4l.1.4v.7a8.3 8.3 0 0 0 .3 1.8h.2c.4-.1 1-.5 2-1.3l.2-.3.4-.2a1 1 0 0 1 1 .2c.*******.5.7l-.1.8a10.7 10.7 0 0 1-.4 1.1c-.3.9-.7 2-.8 2.8v1.2l.2.2 1.7-.2c.9-.3 2.1-.9 3.7-1.8.3-.2.5-.3.8-.3.3 0 .7-.1 1 .*******.7.6 1 0 .4 0 .7-.2 1-.7 1.5-1.9 3.8-1.8 6.2.1 2.4 1.4 5 5.9 *******.7.6 1 1v.3a1 1 0 0 1-.8 1.3h-.5l-1.1-.3c-2-.6-6-.4-9.4.8-1.3.5-1.4.4-.3-.4 1.9-1.2 5-2.9 8.2-2.5a7.9 7.9 0 0 1-5-7.4c0-2.2.6-4.3 1.3-5.8-1 .6-2 1-2.8 1.3a5 5 0 0 1-2.8.2 2 2 0 0 1-1.6-1.5c-.2-.7-.2-1.4 0-2 0-.7.2-1.3.4-2l-.5.3c-.8.3-1.6.3-2.3-.3Zm1.3-1.5Z' clip-rule='evenodd'/%3E%3Cmask id='b' fill='%23fff'%3E%3Cpath fill-rule='evenodd' d='m228.2 205.7-1.5-22.6-6.6.1 1.5 22.7 6.6-.2Zm-.8-23.4-8.1.2 1.6 24.2 8-.2-1.5-24.2Z' clip-rule='evenodd'/%3E%3C/mask%3E%3Cpath fill='%23fff' fill-rule='evenodd' d='m228.2 205.7-1.5-22.6-6.6.1 1.5 22.7 6.6-.2Zm-.8-23.4-8.1.2 1.6 24.2 8-.2-1.5-24.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M226.7 183h1.1v-1h-1.2v1.2Zm1.5 22.7v1.2h1.2v-1.2h-1.2Zm-8-22.5-.1-1.1h-1.2v1.2h1.2Zm1.4 22.7h-1.1v1.2h1.2v-1.2Zm5.8-23.6h1.2l-.1-1.2h-1.2v1.2Zm-8.1.2v-1.2H218l.1 1.2h1.2Zm1.6 24.2h-1.2l.1 1.2h1.2v-1.2Zm8-.2.2 1.2h1.1v-1.3H229Zm-3.4-23.4 1.5 22.7 2.3-.1-1.5-22.7-2.3.1Zm-5.3 1.3 6.5-.2-.1-2.3-******* 2.3Zm2.6 21.5-1.5-22.7H219l1.5 22.7h2.3Zm5.3-1.3-******* 2.4 6.5-.2-.1-2.3Zm-1.9-22.3 1.6 24.2h2.4l-1.6-24.3-2.4.1Zm-6.8 1.4 8-.3v-2.3l-******* 2.4Zm2.7 23-1.6-24.2H218l1.6 24.2h2.4Zm6.8-1.4-8 .2v2.4l8.2-.2-.2-2.4Z' mask='url(%23b)'/%3E%3Cpath fill='%232E03A0' d='M225.2 210.6a4.2 4.2 0 0 1-4.3-4c-.1-2.1 1.5-4 3.8-4a4.3 4.3 0 0 1 4.3 3.9 4 4 0 0 1-3.8 4.1Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M221.7 206.7a3.4 3.4 0 0 0 3.5 3.1c1.8 0 3.1-1.5 3-3.3a3.4 3.4 0 0 0-3.5-3.2 3.1 3.1 0 0 0-3 3.4Zm-.8 0c.2 2.2 2 4 4.3 4a4 4 0 0 0 3.8-4.2c-.2-2.3-2-4-4.3-4-2.3.1-4 2-3.8 4.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='m226.7 180.2 13.5 20.7-6.7 4.2-13.6-20.6 6.8-4.3Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m239.1 200.6-12.6-19.3-5.5 3.4 12.7 19.3 5.4-3.4Zm-12.4-20.4-6.8 4.3 13.6 20.6 6.7-4.2-13.5-20.7Z' clip-rule='evenodd'/%3E%3Cpath fill='%23fff' d='M223.6 186.4a4.2 4.2 0 0 1-4.3-4c-.1-2.1 1.5-4 3.8-4a4.2 4.2 0 0 1 4.3 3.9 4 4 0 0 1-3.8 4.1Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M220 182.5a3.4 3.4 0 0 0 3.6 3.2c1.8 0 3.1-1.6 3-3.4a3.4 3.4 0 0 0-3.5-3.2 3.1 3.1 0 0 0-3 3.4Zm-.7 0c.2 2.2 2 4 4.3 4a4 4 0 0 0 3.8-4.2c-.2-2.3-2-4-4.3-4-2.3.1-4 2-3.8 4.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%2313C1FF' d='m243.5 181.9 1.6 24.2 8.1-.3-1.6-24.2-8 .3Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m245.8 205.3-1.5-22.7 6.6-.2 1.5 22.7-6.6.2Zm-2.3-23.4 8.1-.3 1.6 24.2-8 .3-1.7-24.2Z' clip-rule='evenodd'/%3E%3Cpath fill='%2306CF13' d='M249.4 210c2.3 0 4-2 3.8-4.2-.1-2.2-2-4-4.3-3.9-2.2 0-4 2-3.8 4.2.2 2.2 2 4 4.3 3.9Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M252.4 205.9a3.1 3.1 0 0 1-3 3.3 3.4 3.4 0 0 1-3.5-3.2 3.1 3.1 0 0 1 3-3.3c1.8 0 3.4 1.4 3.5 3.2Zm.8 0c.1 2.2-1.5 4-3.8 4a4.2 4.2 0 0 1-4.3-3.8 4 4 0 0 1 3.8-4.2c2.2 0 4.2 1.7 4.3 4Z' clip-rule='evenodd'/%3E%3Cpath fill='%23FA52D6' d='M244 179.8 233.2 201l7.3 3.8 10.7-21.3-7.2-3.8Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m234.3 200.8 10-20 5.9 3.1-10 20-6-3.1Zm9.7-21 7.2 3.8-10.7 21.3-7.3-3.8 10.8-21.3Z' clip-rule='evenodd'/%3E%3Cpath fill='red' d='M235 206.5c2 1.1 4.5.4 5.5-1.6s.1-4.4-1.9-5.5a4 4 0 0 0-5.4 1.7 4 4 0 0 0 1.9 5.4Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M239.8 204.5a3.2 3.2 0 0 1-4.4 1.4 3.3 3.3 0 0 1-1.5-4.5 3.2 3.2 0 0 1 4.4-1.3c1.6.9 2.3 2.9 1.5 4.4Zm.7.4a4 4 0 0 1-5.4 1.6c-2-1-2.9-3.5-1.9-5.4a4 4 0 0 1 5.4-1.7c2 1.1 2.9 3.6 1.9 5.5Z' clip-rule='evenodd'/%3E%3Cpath fill='%23FFC400' d='M247.8 185.8c2.3 0 4-2 3.8-4.2-.2-2.2-2-4-4.3-3.9-2.2 0-4 2-3.8 4.2.2 2.2 2 4 4.3 3.9Z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M250.8 181.7a3.1 3.1 0 0 1-3 3.3 3.4 3.4 0 0 1-3.5-3.2 3.1 3.1 0 0 1 3-3.3c1.9 0 3.4 1.3 3.5 3.2Zm.8 0c.1 2.2-1.5 4-3.8 4a4.2 4.2 0 0 1-4.3-3.8 4 4 0 0 1 3.8-4.2c2.2 0 4.1 1.7 4.3 4Z' clip-rule='evenodd'/%3E%3Cdefs%3E%3ClinearGradient id='a' x1='427.5' x2='219.7' y1='147.8' y2='310.6' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23EDF8FF'/%3E%3Cstop offset='1' stop-color='%23FAEFFF'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E";function J(x){const{className:I,source:D,rotate:l,width:L,height:R,onClick:se}=x,g={msTransform:"rotate("+l+"deg)",transform:"rotate("+l+"deg)",width:L,height:R};return(0,Z.jsx)("img",{className:I,src:D,style:g,onClick:se})}J.propTypes={className:p().string,source:p().string.isRequired,width:p().number,height:p().number,rotate:p().number,onClick:p().func};const he=J,V=403,Y=404,H=[500,501,502,503,504,505],w="link_expired";var C=n(80396);function f(x,I,D){return(I=y(I))in x?Object.defineProperty(x,I,{value:D,enumerable:!0,configurable:!0,writable:!0}):x[I]=D,x}function y(x){var I=v(x,"string");return typeof I=="symbol"?I:I+""}function v(x,I){if(typeof x!="object"||!x)return x;var D=x[Symbol.toPrimitive];if(D!==void 0){var l=D.call(x,I||"default");if(typeof l!="object")return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return(I==="string"?String:Number)(x)}class S extends M.PureComponent{constructor(){super(...arguments),f(this,"typeText",{title_403:{"zh-CN":"\u5BF9\u4E0D\u8D77\uFF0C\u4F60\u6CA1\u6709\u8BBF\u95EE\u6743\u9650",en:"Permission denied",jp:""},des_403_0:{"zh-CN":(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("p",{className:"proposal",children:["\u5F53\u524D\u8D26\u53F7\u662F ",(0,Z.jsx)("span",{className:"count-detail",children:this.props.account}),"\uFF0C\u6CA1\u6709\u8BBF\u95EE\u8BE5\u6587\u6863\u7684\u6743\u9650"]})}),en:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("p",{className:"proposal",children:["Current account ",(0,Z.jsx)("span",{className:"count-detail",children:this.props.account}),", permission denied."]})}),jp:""},des_403_1:{"zh-CN":(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("p",{className:"proposal",children:["\u5F53\u524D\u8D26\u53F7\u662F ",(0,Z.jsx)("span",{className:"count-detail",children:this.props.account}),"\uFF0C\u6CA1\u6709\u8BBF\u95EE\u8BE5\u6587\u6863\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u4FEE\u6539\u8BBF\u95EE\u6743\u9650"]})}),en:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("p",{className:"proposal",children:["Current account ",(0,Z.jsx)("span",{className:"count-detail",children:this.props.account}),", permission denied. Please contact administrator."]})}),jp:""},des_403_2:{"zh-CN":"\u60A8\u8FD8\u6CA1\u6709\u767B\u5F55\uFF0C\u8BF7\u767B\u5F55\u540E\u5C1D\u8BD5\u8BBF\u95EE",en:"You are not logged in. Please log in and try again.",jp:""},des_403_3:{"zh-CN":"\u5F53\u524D\u8D26\u53F7\u662F "+this.props.account+"\uFF0C\u6587\u4EF6\u6570\u8D85\u9650\uFF0C\u5347\u7EA7\u540E\u5373\u53EF\u7F16\u8F91",en:"Current account "+this.props.account+", permission denied.",jp:""},des_403_4:{"zh-CN":"\u5F53\u524D\u8D26\u53F7\u662F "+this.props.account+"\uFF0C\u4F01\u4E1A\u7248\u8FC7\u671F\uFF0C\u5347\u7EA7\u540E\u5373\u53EF\u7F16\u8F91",en:"Current account "+this.props.account+", permission denied. Please contact administrator. ",jp:""},title_404:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u627E\u4E0D\u5230\u6587\u4EF6",en:"Sorry, there is no visible page.",jp:""},des_404:{"zh-CN":"\u6587\u4EF6\u53EF\u80FD\u5DF2\u88AB\u5220\u9664\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u83B7\u53D6\u65B0\u7684\u5206\u4EAB\u94FE\u63A5",en:"The page may have been deleted. Please contact the sharer to inquire about the link status.",jp:""},title_link_expired:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u94FE\u63A5\u5931\u6548",en:"Sorry, the link is invalid",jp:""},des_link_expired:{"zh-CN":"\u94FE\u63A5\u53EF\u80FD\u5DF2\u88AB\u5220\u9664\u6216\u8005\u8BBE\u7F6E\u6709\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u5206\u4EAB\u8005\u83B7\u53D6\u6700\u65B0\u5206\u4EAB\u94FE\u63A5",en:"The link may have been deleted or set incorrectly. Please contact the sharer to obtain the latest link.",jp:""},title_5xx:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u65E0\u6CD5\u8FDE\u63A5\u58A8\u5200\u670D\u52A1\u5668",en:"Sorry, unable to connect to Mockitt server",jp:""},des_5xx:{"zh-CN":(0,Z.jsxs)("div",{className:"proposal text-left",children:[(0,Z.jsx)("p",{children:"\u8BF7\u5C1D\u8BD5\u4EE5\u4E0B\u65B9\u6CD5\uFF1A"}),(0,Z.jsxs)("ul",{className:"text-list",children:[(0,Z.jsx)("li",{children:"\u5982\u679C\u60A8\u5F00\u4E86\u7F51\u7EDC\u4EE3\u7406\uFF08VPN\uFF09\uFF0C\u5EFA\u8BAE\u60A8\u5B8C\u5168\u5173\u95ED\u540E\u518D\u5237\u65B0\u58A8\u5200"}),(0,Z.jsx)("li",{children:"\u5982\u679C\u4E0D\u662F\u4E0A\u8FF0\u539F\u56E0\u5BFC\u81F4\u7684\uFF0C\u8BF7\u60A8\u8FDE\u63A5\u624B\u673A\u70ED\u70B9\u6216\u5207\u6362\u53E6\u4E00\u4E2A\u7F51\u7EDC"})]}),(0,Z.jsx)("p",{children:"\u82E5\u4F9D\u65E7\u65E0\u6CD5\u6B63\u5E38\u8FDB\u5165\u58A8\u5200\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D"})]}),en:(0,Z.jsxs)("div",{className:"proposal text-left",children:[(0,Z.jsx)("p",{children:"Please try the following methods:"}),(0,Z.jsxs)("ul",{className:"text-list",children:[(0,Z.jsx)("li",{children:"If your VPN is on, please turn it off then refresh Mockitt."}),(0,Z.jsx)("li",{children:"If not, please switch to another network."})]}),(0,Z.jsx)("p",{children:"If the problem stays, please contact support."})]}),jp:""},title_0:{"zh-CN":"\u5F88\u62B1\u6B49\uFF0C\u9875\u9762\u5D29\u6E83\u4E86",en:"Sorry, this page is corrupted",jp:""},des_0:{"zh-CN":"\u8BF7\u5C1D\u8BD5\u5237\u65B0\u9875\u9762\u6216\u5173\u95ED\u518D\u91CD\u65B0\u6253\u5F00\u58A8\u5200\uFF0C\u82E5\u65E0\u6CD5\u89E3\u51B3\u8BF7\u8054\u7CFB\u5BA2\u670D",en:"Please refresh this page or close this page and start Mockitt again. If the problem stays, please contact  support.",jp:""},previewFile:{"zh-CN":"\u9884\u89C8\u6587\u4EF6",en:"Preview File",jp:""},goHome:{"zh-CN":"\u8FD4\u56DE\u58A8\u5200\u9996\u9875",en:"Back to Homepage",jp:""},reloadPage:{"zh-CN":"\u5237\u65B0\u9875\u9762",en:"Refresh this page",jp:""},contactUs:{"zh-CN":"\u8054\u7CFB\u5BA2\u670D",en:"Support",jp:""}}),f(this,"clickGoHome",()=>{let I="",{locale:D,homeUrl:l}=this.props;D==="zh-CN"?I=l:D==="jp"?I="https://mockitt.wondershare.jp":I="https://mockitt.wondershare.com",window.top!==window.self?window.top.location.replace(I):location.replace(I)}),f(this,"clickReloadPage",()=>{window.location.reload()}),f(this,"clickContactUs",()=>{let I="",{locale:D}=this.props;if(D==="zh-CN"){document.getElementById("mdOnlineSupport")&&document.getElementById("mdOnlineSupport").click();return}else D==="jp"?I="https://support.wondershare.jp":I="https://support.wondershare.com/product/mockitt.html";window.open(I)}),f(this,"getErrorConfig",()=>{const{errorType:I,primaryBtnText:D,primaryBtnClick:l,secondaryBtnText:L,secondaryBtnClick:R,locale:se}=this.props,{typeText:g,clickGoHome:A,clickContactUs:ee,clickReloadPage:le}=this;let re,X,we="",ze=null,He;const je={text:g.goHome[se],click:A},Ee={text:g.reloadPage[se],click:le},tt={text:g.contactUs[se],click:ee};switch(!0){case I===V:re=je.text,X=je.click,He=z;break;case I===Y:re=je.text,X=je.click,He=T;break;case I===w:re=je.text,X=je.click,He=T;break;case H.includes(I):re=Ee.text,X=Ee.click,we=tt.text,ze=tt.click,He=E;break;default:re=Ee.text,X=Ee.click,we=tt.text,ze=tt.click,He=se==="en"?q:U;break}return D&&(re=D),l&&(X=l),L&&(we=L),R&&(ze=R),{svgBg:He,primaryText:re,primaryClick:X,secondaryText:we,secondaryClick:ze}}),f(this,"getErrorInfo",()=>{const{errorType:I,identity:D,locale:l,customTitle:L,customDes:R,account:se}=this.props,{typeText:g}=this;let A,ee;if(L)A=L;else{const le=H.includes(I)?"title_5XX":"title_"+I;A=g[le]?g[le][l]:g.title_0[l]}if(R)ee=R;else{let le="des_"+I;I===V?le=se?"des_"+I+"_"+D:"des_"+I+"_2":H.includes(I)?le="des_5xx":I===w&&(le="des_link_expired"),ee=g[le]?g[le][l]:g.des_link_expired[l]}return{title:A,desc:ee}}),f(this,"handleClickLogo",()=>{let{isLogoClickable:I,logoUrl:D}=this.props;I&&(window.top!==window.self?window.top.location.href=D:window.location.href=D)})}render(){const{locale:I,isShowPrimaryBtn:D,isShowSecondaryBtn:l,isLogoClickable:L,previewFileBtnClick:R}=this.props,{typeText:se}=this,{svgBg:g,primaryText:A,primaryClick:ee,secondaryText:le,secondaryClick:re}=this.getErrorConfig(),{title:X,desc:we}=this.getErrorInfo();return(0,Z.jsxs)(te,{children:[(0,Z.jsx)("div",{className:"logo",children:(0,Z.jsx)("span",{className:L?"logo-clickable":"",onClick:this.handleClickLogo,children:(0,Z.jsx)(he,{source:g})})}),(0,Z.jsxs)("div",{className:"content",children:[(0,Z.jsx)("p",{className:"title",children:X}),typeof we=="string"?(0,Z.jsx)("p",{className:"proposal",children:we}):we]}),(0,Z.jsxs)("div",{className:"btn-list",children:[l&&re&&le&&(0,Z.jsx)("a",{className:"btn regular",onClick:re,children:le}),R&&(0,Z.jsx)("a",{className:"btn regular",onClick:R,children:se.previewFile[I]}),D&&ee&&A&&(0,Z.jsx)("a",{className:"btn primary",onClick:ee,children:A}),le===se.contactUs[I]&&(0,Z.jsx)(C.A,{})]})]})}}f(S,"propTypes",{locale:p().oneOf(["zh-CN","en","jp"]),identity:p().oneOf([0,1]),customTitle:p().string,customDes:p().string,isShowPrimaryBtn:p().bool,primaryBtnText:p().string,primaryBtnClick:p().func,isShowSecondaryBtn:p().bool,secondaryBtnText:p().string,secondaryBtnClick:p().func,errorType:p().oneOf([0,403,404,500,501,502,503,504,505,w]).isRequired,account:p().string,isLogoClickable:p().bool,logoUrl:p().string,homeUrl:p().string}),f(S,"defaultProps",{locale:"zh-CN",isShowPrimaryBtn:!0,isShowSecondaryBtn:!0,identity:0,errorType:0,isLogoClickable:!0,logoUrl:"/",homeUrl:"/workspace/home"})},80396:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>k});var s=n(51044),p=n(66748),M=n.n(p),u=n(57464);k.propTypes={className:M().string,children:M().oneOfType([M().array,M().string]),isWonderShare:M().bool,id:M().string,onClick:M().func,disabled:M().bool,canClick:M().bool};function k(te){let{className:Z,children:z,isWonderShare:T=!1,id:E="mdOnlineSupport",onClick:U,disabled:q,canClick:J}=te;const he="qd30090468111af7daaf09a6395b687fc3becd624cf3",V=T?"https://support.wondershare.com/":"";(0,s.useEffect)(()=>{if(!q)return!T&&Y(),()=>{const w=document.getElementById(he);w&&w.remove()}},[]);const Y=()=>{n.g?(n.g.wpaShowItemId=window.wpaShowItemId="123",n.g.qidian_ex1=window.qidian_ex1="12"):(window.wpaShowItemId="123",window.qidian_ex1="12");let w=document.createElement("script");w.id=he,w.src="https://wp.qiye.qq.com/qidian/3009046811/1af7daaf09a6395b687fc3becd624cf3",w.charset="utf-8",w.async=!0,w.defer=!0,document.body.appendChild(w)},H=w=>{if(q){J&&U&&U();return}if(U&&U(),T){window.open(V);return}if(E!=="mdOnlineSupport"){w.preventDefault();const C=document.getElementById("mdOnlineSupport");C&&C.click()}};return(0,u.jsx)("div",{style:{cursor:"pointer"},id:E,className:Z,onClick:H,children:z})}},29584:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>x,d:()=>q});var s=n(30108),p=n.n(s),M=n(98236),u=n.n(M),k=n(51044),te=n(60185),Z=n(66748),z=n.n(Z),T=n(480),E=n(28149);const U=9;function q(D){let{$opener:l,$menuBase:L,menuX:R="left",menuY:se="bottom",menuBaseStyle:g={},inflexible:A=!1,shouldSetMaxHeight:ee=!1}=D;if(!l||!L)return{};const le=L.querySelector("*"),re={styleFor$menuBase:{},styleFor$menu:{}},X=Je=>Object.assign(re.styleFor$menuBase,Je),we=Je=>Object.assign(re.styleFor$menu,Je),{offsetHeight:ze}=le,He=g.width||l.offsetWidth,je=g.height||l.offsetHeight,Ee=l.getBoundingClientRect(),{top:tt,bottom:Pe,left:Q}=Object.assign({top:Ee.top,right:Ee.right,bottom:Ee.bottom,left:Ee.left},g);X({top:tt+"px",left:Q+"px",width:He+"px",height:je+"px"});const{innerHeight:K}=window,me=10,Ce=K-10,ke=se==="top"?1/3:2/3,$e=K*ke,Me=tt+je/2,Ie=tt+je;return(A&&se==="bottom"||!A&&$e>=Me)&&Ie+ze+U<K?(re.isDownward=!0,ee&&Pe+ze>Ce&&we({maxHeight:Ce-Pe+"px"})):(re.isDownward=!1,ee&&tt-ze<me&&we({maxHeight:tt-me+"px"})),Object.assign(L.style,re.styleFor$menuBase),Object.assign(le.style,re.styleFor$menu),re}var J=n(21676);const he=J.Ay.label.withConfig({displayName:"styled__StyledDropDown",componentId:"sc-665wv9-0"})(["&.is-disabled{opacity:0.5;> button{cursor:not-allowed;}}"]),V=J.Ay.div.withConfig({displayName:"styled__StyledDropDownMenu",componentId:"sc-665wv9-1"})(["position:absolute;margin:0.25em 0;width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;min-width:7em;font-size:12px;filter:drop-shadow(0 2px 10px rgba(39,54,78,0.12)) drop-shadow(4px 12px 40px rgba(39,54,78,0.12));pointer-events:none;opacity:0;transition:0.2s ease-in-out;transition-property:transform,opacity;transform-origin:50% 0;transform:scale(0.9);&.is-open{pointer-events:initial;opacity:1;transform:scale(1);}&.x-left{left:0;}&.x-right{left:initial;right:0;}&.x-center{left:50%;transform:scale(0.9) translateX(-50%);&.is-open{transform:scale(1) translateX(-50%);}}&.is-downward{top:100%;bottom:initial;}&.is-upward{top:initial;bottom:100%;}&.arrowed{margin-top:0.75em;margin-bottom:0.75em;&.x-left.x-arrow-based{left:50%;margin-left:-14px;}&.x-right.x-arrow-based{left:initial;right:50%;margin-right:-14px;}}.arrow{position:absolute;top:0;left:calc(50% - 0.5em);width:1em;height:0.375em;font-size:14px;line-height:0;fill:currentColor;fill-rule:evenodd;color:#fff;svg{position:absolute;width:auto;height:5px;transform:translateY(-100%);}}&.dark .arrow{color:rgba(30,41,46,.9);}&.x-left .arrow{left:0.5em;right:initial;}&.x-right .arrow{left:initial;right:0.5em;}&.is-upward .arrow{transform:rotate(180deg);top:initial;bottom:0;}.content{padding:0.75em 0.5em;min-height:32px;background-color:#fff;border-radius:4px;}&.dark .content{background-color:rgba(30,41,46,.9);color:#fff;}ul.MenuList{margin:0;padding:0;list-style:none;text-align:center;color:#5b6b73;li{line-height:32px;border-radius:2px;cursor:pointer;&:not(.is-disabled):hover{background-color:#f6f7f8;}&.is-active{color:#298df8;}&.is-disabled{cursor:not-allowed;opacity:0.5;}}}"]),Y=J.Ay.div.withConfig({displayName:"styled__StyledDropDownBase",componentId:"sc-665wv9-2"})(["position:fixed;z-index:1100;pointer-events:none;"]);var H=n(57464);function w(D,l,L){return(l=C(l))in D?Object.defineProperty(D,l,{value:L,enumerable:!0,configurable:!0,writable:!0}):D[l]=L,D}function C(D){var l=f(D,"string");return typeof l=="symbol"?l:l+""}function f(D,l){if(typeof D!="object"||!D)return D;var L=D[Symbol.toPrimitive];if(L!==void 0){var R=L.call(D,l||"default");if(typeof R!="object")return R;throw new TypeError("@@toPrimitive must return a primitive value.")}return(l==="string"?String:Number)(D)}const y="IBOT_DROPDOWN_MENU_ROOT",v=document.getElementById(y)||Object.assign(document.createElement("div"),{id:y}),S=document.body;S.contains(v)||S.appendChild(v);class x extends k.PureComponent{constructor(){super(...arguments),w(this,"state",{prevProps:this.props,isOpen:this.props.isOpen,$opener:null,currentMenuListItemIdx:this.props.currentMenuListItemIdx}),w(this,"leaveTimeoutList",[]),w(this,"createMenuRef",l=>this.$menuRef=l),w(this,"toggle",l=>this.setState({isOpen:p()(l)?l:!this.state.isOpen})),w(this,"open",()=>this.toggle(!0)),w(this,"close",()=>this.toggle(!1)),w(this,"onMouseEnter",()=>{const{shouldOpenOnHover:l}=this.props;l&&(clearTimeout(this.closeTimeout),Object.assign(this,{hoverTimeout:setTimeout(this.open,this.props.hoverDelay)}))}),w(this,"onMouseLeave",()=>{const{shouldOpenOnHover:l}=this.props;l&&clearTimeout(this.hoverTimeout)}),w(this,"onMouseMove",l=>{var L;let{clientX:R,clientY:se}=l;const{shouldOpenOnHover:g,hoverDelay:A,hoverCloseDelay:ee}=this.props,{$opener:le}=this.state;if(!g)return;clearTimeout(this.hoverTimeout);const re=document.elementFromPoint(R,se),X=!le.contains(re),we=!((L=this.$menuRef)!=null&&(L=L.menuBaseRef)!=null&&(L=L.current)!=null&&L.contains(re));we?X&&we&&this.leaveTimeoutList.push(setTimeout(this.close,ee!==void 0?ee:Math.max(A,300))):(this.leaveTimeoutList.map(clearTimeout),Object.assign(this,{leaveTimeoutList:[]}))}),w(this,"set$opener",l=>this.setState({$opener:l})),w(this,"onSelect",l=>{let{currentTarget:L}=l;const{menuList:R,onSelect:se,shouldCloseOnSelect:g}=this.props;if(typeof se!="function")return;const A=L.dataset.idx,ee=R[A],le=typeof ee=="string"?ee:ee&&ee.value;se(A,le),this.setState({currentMenuListItemIdx:A}),g&&this.close()})}static getDerivedStateFromProps(l,L){let{prevProps:R,isOpen:se}=L;return u()(l,R)?null:p()(l.isOpen)?{prevProps:l,isOpen:l.isOpen}:{prevProps:l}}componentDidUpdate(l,L){let{isOpen:R}=L;const{onOpen:se,onClose:g,onToggle:A}=this.props,{isOpen:ee}=this.state;R!==ee&&(ee?(se(),A(!0)):(g(),A(!1)))}render(){const{className:l,opener:L,openerType:R,openerClassName:se,shouldCloseOnClickOutside:g}=this.props,{isOpen:A,$opener:ee,currentMenuListItemIdx:le}=this.state,re=this.props.isDisabled||this.props.disabled,X=(0,E.Hn)(["Dropdown",A&&"is-open",re&&"is-disabled",l]),we={onClick:this.toggle,onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseMove:this.onMouseEnter,disabled:re,className:se};return(0,H.jsxs)(he,{ref:this.set$opener,className:X,children:[R!=="button"&&(0,k.isValidElement)(L)?(0,k.cloneElement)(L,we):(0,H.jsx)("button",{type:"button",...we,children:L}),(0,H.jsx)(I,{ref:this.createMenuRef,...this.props,isOpen:A,$opener:ee,onSelect:this.onSelect,onClose:this.close,shouldCloseOnClickOutside:g,currentMenuListItemIdx:le}),A&&(0,H.jsx)(T.A,{target:document,onMouseMove:this.onMouseMove})]})}}w(x,"positionMenu",q),w(x,"propTypes",{isOpen:z().bool,mode:z().oneOf(["light","dark"]),opener:z().node,openerType:z().oneOf(["button","custom"]),className:z().string,portalClassName:z().string,menuBaseClassName:z().string,openerClassName:z().string,menuClassName:z().string,menuBaseStyle:z().shape({top:z().number,right:z().number,bottom:z().number,width:z().number,height:z().number}),menu:z().node,menuList:z().arrayOf(z().oneOfType([z().node,z().shape({label:z().node,value:z().any,isDisabled:z().bool})])),currentMenuListItemIdx:z().oneOfType([z().number,z().string]),shouldPreventScrollingPropagation:z().bool,shouldOpenOnHover:z().bool,shouldCloseOnClickOutside:z().bool,hoverDelay:z().oneOfType([z().number,z().string]),hoverCloseDelay:z().oneOfType([z().number,z().string]),arrowed:z().bool,inflexible:z().bool,menuX:z().oneOf(["left","center","right"]),menuY:z().oneOf(["top","bottom"]),menuBasedX:z().bool,isDisabled:z().bool,disabled:z().bool,onSelect:z().func,shouldCloseOnSelect:z().bool,onOpen:z().func.isRequired,onClose:z().func.isRequired,onToggle:z().func.isRequired}),w(x,"defaultProps",{arrowed:!1,openerType:"button",mode:"light",shouldPreventScrollingPropagation:!0,shouldCloseOnSelect:!0,shouldOpenOnHover:!1,shouldCloseOnClickOutside:!0,hoverDelay:200,menuX:"center",menuY:"bottom",inflexible:!1,menuBasedX:!1,onOpen:()=>null,onClose:()=>null,onToggle:()=>null});class I extends k.PureComponent{constructor(){super(...arguments),w(this,"state",{isDownward:this.props.position==="bottom"}),w(this,"portal",(0,E.ep)(v,(0,E.Hn)(["DropdownMenuPortal",this.props.portalClassName]))),w(this,"menuBaseRef",(0,k.createRef)()),w(this,"onResizeWindow",()=>this.props.isOpen&&this.position()),w(this,"onClickOutside",l=>{let{target:L}=l;const{$opener:R,onClose:se,shouldCloseOnClickOutside:g}=this.props;if(!g)return;const A=!v.contains(L),ee=L.closest("label"),le=ee&&ee.contains(R),re=!!(0,E.$)(".SelectMenu.is-open");A&&!le&&!re&&se()}),w(this,"position",()=>{const{$opener:l,menuX:L,menuY:R,menuBaseStyle:se,inflexible:g}=this.props,{menuBaseRef:{current:A}}=this,{isDownward:ee}=q({$menuBase:A,$opener:l,menuX:L,menuY:R,menuBaseStyle:se,inflexible:g});this.setState({isDownward:ee})})}componentDidMount(){const{isOpen:l,shouldPreventScrollingPropagation:L}=this.props,{menuBaseRef:{current:R}}=this;l&&setTimeout(this.position),L&&(0,E.sA)((0,E.$)(".content",R)),window.addEventListener("resize",this.onResizeWindow)}componentDidUpdate(l){let{isOpen:L}=l;const{isOpen:R}=this.props;!L&&R&&this.position()}componentWillUnmount(){this.portal&&this.portal.remove(),window.removeEventListener("resize",this.onResizeWindow)}render(){const{portal:l,menu:L}=this;return(0,te.createPortal)(L,l)}get menu(){const{isOpen:l,mode:L,menuBaseClassName:R,menuClassName:se,menu:g,menuList:A,arrowed:ee,menuX:le,menuBasedX:re,currentMenuListItemIdx:X,onSelect:we}=this.props,{isDownward:ze}=this.state,He=(0,E.Hn)(["DropdownMenu",L,l&&"is-open",ze?"is-downward":"is-upward","x-"+le,ee&&"arrowed "+(re?"x-menu-based":"x-arrow-based"),se]);return(0,H.jsx)(Y,{ref:this.menuBaseRef,className:(0,E.Hn)(["DropdownMenuBase",R]),children:(0,H.jsxs)(V,{className:He,children:[ee&&(0,H.jsx)("span",{className:"arrow",dangerouslySetInnerHTML:{__html:E.t4.mr}}),(0,H.jsx)("div",{className:"content",children:A?(0,H.jsx)("ul",{className:"MenuList",children:A.map((je,Ee)=>(0,H.jsx)("li",{role:"option","data-idx":Ee,className:(0,E.Hn)([je.isDisabled&&"is-disabled",Ee===Number(X)&&"is-active"]),onClick:je.isDisabled?void 0:we,children:je.label||je},Ee))}):g}),l&&(0,H.jsx)(T.A,{target:document,onClick:(0,T.t)(this.onClickOutside,{capture:!0})}),l&&(0,H.jsx)(T.A,{target:document,onScroll:(0,T.t)(this.position,{capture:!0})})]})})}}w(I,"propTypes",{...x.propTypes,isOpen:z().bool,$opener:z().instanceOf(Element),onSelect:z().func,onClose:z().func})},22379:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>q});var s=n(51044),p=n(66748),M=n.n(p),u=n(23485),k=n(28149),te=n(21676);const Z=te.Ay.span.withConfig({displayName:"styled__StyledEllipsis",componentId:"sc-z4nkzm-0"})(["null"]);var z=n(57464);function T(J,he,V){return(he=E(he))in J?Object.defineProperty(J,he,{value:V,enumerable:!0,configurable:!0,writable:!0}):J[he]=V,J}function E(J){var he=U(J,"string");return typeof he=="symbol"?he:he+""}function U(J,he){if(typeof J!="object"||!J)return J;var V=J[Symbol.toPrimitive];if(V!==void 0){var Y=V.call(J,he||"default");if(typeof Y!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(he==="string"?String:Number)(J)}class q extends s.PureComponent{constructor(){var he;super(...arguments),he=this,T(this,"state",{isTruncated:!1,isDetected:!1}),T(this,"set$ellipsis",V=>Object.assign(this,{$ellipsis:V.current})),T(this,"detectTruncation",function(V){return V===void 0&&(V=he.$ellipsis),V.offsetWidth<V.scrollWidth})}componentDidMount(){return this.setState({isDetected:!0,isTruncated:this.detectTruncation()})}componentDidUpdate(he){let{children:V}=he;const{children:Y}=this.props,{isDetected:H}=this.state;if(V!==Y)return this.setState({isDetected:!1});if(!H)return this.setState({isDetected:!0,isTruncated:this.detectTruncation()})}render(){const{className:he,to:V,type:Y,max:H,display:w,lang:C,theme:f,noTooltip:y,withTooltip:v,withQuote:S,withPeriod:x,withComma:I,withQuestionMark:D,children:l,...L}=this.props,{isTruncated:R,isDetected:se}=this.state,g={children:l},A=se&&(R?"is-truncated":"isnt-truncated"),ee={type:V?"link":"inline",theme:f,className:(0,k.Hn)(["Ellipsis",A,he,(S||x||I||D)&&"Punctuation",S&&"with-quote",x&&"with-period",I&&"with-comma",D&&"with-question-mark"]),href:V,"data-type":Y,"data-max":H,style:{display:w,maxWidth:isFinite(H)?H+"em":H},content:(v||R)&&!y&&(0,z.jsx)("div",{lang:C,className:"EllipsisTip",...g}),children:l,setRef:this.set$ellipsis,...L},le=(0,z.jsx)(u.A,{...ee});return S||x||I||D?(0,z.jsx)(Z,{className:(0,k.Hn)(["Punctuation",S&&"with-quote",x&&"with-period",I&&"with-comma",D&&"with-question-mark",A]),children:le}):le}}T(q,"propTypes",{className:M().string,theme:M().oneOf(["core","plain"]),type:M().oneOf(["user","id","email","org","team","app","widget"]),max:M().oneOfType([M().string,M().number]),display:M().oneOf(["inline-block","block"]),lang:M().string,to:M().string,children:M().node,noTooltip:M().bool,withTooltip:M().bool,withQuote:M().bool,withComma:M().bool,withPeriod:M().bool,withQuestionMark:M().bool}),T(q,"defaultProps",{lang:"en",theme:"core"})},99739:(Ne,de,n)=>{"use strict";n.d(de,{eB:()=>g,Ay:()=>se});var s=n(51044),p=n(60185),M=n(66748),u=n.n(M),k=n(480),te=n(60260),Z=n.n(te),z=n(77017),T=n.n(z),E=n(98236),U=n.n(E),q=n(17956),J=n.n(q),he=n(90503),V=n(22379),Y=n(28149),H=n(29584),w=n(21676);const C=w.Ay.label.withConfig({displayName:"styles__StyledSelectLabel",componentId:"sc-grbgid-0"})(["display:inline-flex;justify-content:space-between;align-items:center;max-width:100%;min-width:3em;height:34px;color:#5b6b73;cursor:pointer;transition:all 0.2s ease-out;button{flex:1;width:calc(100% - 2em);height:100%;text-align:start;cursor:inherit;}.caret{margin:0 0.75em 0 1em;line-height:0;color:#8d9ea7;svg{display:block;width:6px;height:4px;fill:currentColor;fill-rule:evenodd;transform:rotate(180deg);transition:all 0.3s ease-out;}}&:not(.unstyled){padding-left:.5em;background-color:#f6f7f8;border:1px solid #f2f2f3;border-radius:2px;}&.small{height:22px;font-size:12px;.caret{margin-right:0.5em;}}&:not(.is-disabled):not(.readonly):not(.unstyled):hover,&:not(.unstyled).is-open{border-color:#298df8;}&.is-open{&:not(.unstyled){background-color:#fff;box-shadow:0 0 6px 0 rgba(41,141,248,.5);}.caret svg{transform:rotate(0deg);}}&.is-disabled{cursor:not-allowed;opacity:0.6;}&.readonly{cursor:default}&.CoreSelect:not(.unstyled){background-color:#fff;border:1px solid #c8cdd1;}"]),f=w.Ay.div.withConfig({displayName:"styles__StyledSelectMenuBase",componentId:"sc-grbgid-1"})(["position:fixed;pointer-events:none;z-index:1100;"]),y=w.Ay.ul.withConfig({displayName:"styles__StyledSelectMenu",componentId:"sc-grbgid-2"})(["position:absolute;margin:2px 0;padding:0;display:block;max-width:20em;min-width:100%;min-height:30px;max-height:300px;overflow-x:hidden;overflow-y:auto;pointer-events:none;font-size:12px;list-style:none;background-color:#fff;border-radius:3px;box-shadow:0 2px 10px 0 rgba(39,54,78,0.08),4px 12px 40px 0 rgba(39,54,78,0.1);color:#5b6b73;transition:0.2s ease-out;transition-property:transform,opacity;transform-origin:50% 0;&::-webkit-scrollbar{display:block;width:4px;height:4px;}&::-webkit-scrollbar-thumb{background-color:#ccc;border-radius:2px;}&::-webkit-scrollbar-track{background-color:rgba(255,255,255,0.5);}&.is-empty{width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;}&:not(.is-open){opacity:0;transform:scale(0.8);}&.x-center{left:50%;transform:translateX(-50%);&:not(.is-open){transform:scale(0.8) translateX(-50%);}}&.x-right{right:0;}&.is-downward{top:100%;bottom:initial;}&.is-upward{transform-origin:50% 100%;top:initial;bottom:100%;}&.is-open{opacity:1;pointer-events:initial;}&.cant-select .SelectOption{cursor:default;}.SelectGroup{> .title{padding:0 0.75em;width:100%;height:30px;line-height:30px;font-weight:bold;}> ul{margin:0;padding:0;}}.SelectOption{display:flex;align-items:center;height:30px;line-height:30px;cursor:pointer;> .Ellipsis{padding:0 0.75em;}> .svg-icon{margin-left:auto;margin-right:0.75em;&.check path{fill:#5b6b73;}}&.is-disabled{cursor:not-allowed;opacity:0.5;}&.is-active{color:#298df8;}&:not(.empty-msg):not(.is-disabled):hover,&.is-active{background-color:#f6f7f8;}&.empty-msg{padding:0 0.5em;color:#8d9ea7;cursor:not-allowed;}}&.CheckSelectMenu{&:not(.is-empty){padding:6px 0;}.SelectOption{height:32px;.Ellipsis{padding:0 16px;}.Ellipsis + .icon{margin-right:10px;}&:hover,&.is-active:hover{color:#298df8;}&.is-active{background:none;color:inherit;}}}&.CoreSelectMenu{margin:4px 0;&:not(.is-empty){padding:8px;min-height:48px;max-height:336px;}.SelectGroup > .title,.SelectOption{height:32px;line-height:32px;border-radius:2px;}}"]);var v=n(57464);function S(le,re,X){return(re=x(re))in le?Object.defineProperty(le,re,{value:X,enumerable:!0,configurable:!0,writable:!0}):le[re]=X,le}function x(le){var re=I(le,"string");return typeof re=="symbol"?re:re+""}function I(le,re){if(typeof le!="object"||!le)return le;var X=le[Symbol.toPrimitive];if(X!==void 0){var we=X.call(le,re||"default");if(typeof we!="object")return we;throw new TypeError("@@toPrimitive must return a primitive value.")}return(re==="string"?String:Number)(le)}const D="IBOT_SELECT_MENU_ROOT",l={"zh-CN":{select_placeholder:"\u9009\u62E9\u4E00\u4E2A...",select_empty_msg:"\u6682\u65E0\u5185\u5BB9..."},en:{select_placeholder:"Choose one\u2026",select_empty_msg:"Nothing to display\u2026"}},L=document.getElementById(D)||Object.assign(document.createElement("div"),{id:D}),R=document.body;R.contains(L)||R.appendChild(L);class se extends s.PureComponent{constructor(){super(...arguments),S(this,"state",{isOpen:!1,prevProps:this.props,value:this.props.value}),S(this,"set$select",re=>this.setState({$select:re})),S(this,"open",()=>this.setState({isOpen:!0})),S(this,"close",()=>this.setState({isOpen:!1})),S(this,"toggle",()=>this.setState({isOpen:!this.state.isOpen})),S(this,"onResizeWindow",()=>this.state.isOpen&&this.close()),S(this,"onChange",async re=>{const{onChange:X,beforeOnChange:we}=this.props,ze=()=>{this.close(),X(re)};if(!we){this.setState({value:re},ze);return}if(await we(re)){this.setState({value:re},ze);return}ze()}),S(this,"onSelect",async re=>{let{currentTarget:X}=re;const{value:we}=this.props,{canSelect:ze}=this;await this.onChange(ze?X.dataset.value:we)})}static getDerivedStateFromProps(re,X){let{prevProps:we,value:ze}=X;return U()(we,re)?null:{prevProps:re,value:re.value}}componentDidMount(){window.addEventListener("resize",this.onResizeWindow)}componentWillUnmount(){window.removeEventListener("resize",this.onResizeWindow)}get isDisabled(){const{isDisabled:re,disabled:X}=this.props;return re||X}get readOnly(){return this.props.readOnly}get canSelect(){const{isDisabled:re,readOnly:X}=this;return!re&&!X}get displayText(){const{optionList:re,placeholder:X,optionLabelProp:we}=this.props,{value:ze}=this.state,je=(re.find(Ee=>T()(Ee)&&Ee.slice(0).some(tt=>(0,Y.o3)(tt,ze)))||re).find(Ee=>!T()(Ee)&&(0,Y.o3)(Ee,ze));return je?(0,Y.Oi)(je,we):X||l[this.props.lang].select_placeholder}render(){const{size:re,theme:X,unstyled:we,className:ze,menuX:He}=this.props,{isOpen:je,$select:Ee,value:tt}=this.state,{isDisabled:Pe,readOnly:Q,canSelect:K}=this,me=(0,Y.Hn)([X==="core"?"CoreSelect":"Select",re,we&&"unstyled",ze,je&&"is-open",Pe&&"is-disabled",Q&&"readonly"]);return(0,v.jsxs)(C,{className:me,role:"listbox",ref:this.set$select,children:[(0,v.jsx)("button",{type:"button",onClick:this.toggle,disabled:Pe,children:(0,v.jsx)(V.A,{children:this.displayText})}),typeof this.props.arrowSvg=="string"?(0,v.jsx)("span",{className:"caret",dangerouslySetInnerHTML:{__html:this.props.arrowSvg}}):(0,v.jsx)("span",{className:"caret",children:this.props.arrowSvg}),(0,v.jsx)(g,{isOpen:je,...this.props,value:tt,$select:Ee,canSelect:K,onChange:this.onSelect,onClose:this.close,menuX:He})]})}}S(se,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),menuTheme:u().oneOf(["core","plain","check"]),unstyled:u().bool,className:u().string,menuClassName:u().string,lang:u().string,placeholder:u().string,optionList:u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,value:u().any,isDisabled:u().bool}),u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,value:u().any,isDisabled:u().bool})]))])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func,menuX:u().oneOf(["left","center","right"]),optionLabelProp:u().string,arrowSvg:u().oneOfType([u().string,u().node])}),S(se,"defaultProps",{size:"regular",theme:"plain",menuTheme:"plain",className:"",menuClassName:"",lang:"zh-CN",optionList:[],isDisabled:!1,onChange:()=>null,menuX:"left",arrowSvg:Y.t4.rH});class g extends s.PureComponent{constructor(){super(...arguments),S(this,"state",{isDownward:!0}),S(this,"portal",(0,Y.ep)(L,"SelectMenuPortal")),S(this,"menuBaseRef",(0,s.createRef)()),S(this,"position",re=>{const{$select:X,menuX:we}=this.props,{menuBaseRef:{current:ze}}=this;if(re){const je=Z()(re,"target");if(je&&J()(je)&&je.matches(".SelectMenu"))return}const{isDownward:He}=(0,H.d)({$menuBase:ze,$opener:X,menuX:we,shouldSetMaxHeight:!0});this.setState({isDownward:He})}),S(this,"onChange",re=>{const{onChange:X}=this.props,{isDownward:we}=this.state,ze=re.currentTarget,He=ze.closest(".SelectMenu, .CoreSelectMenu, .CheckSelectMenu");if(!ze||!He)return this.onlose();const{top:je,bottom:Ee}=ze.getBoundingClientRect(),{top:tt,bottom:Pe}=He.getBoundingClientRect();return we&&je>=tt||!we&&Ee<=Pe?ze.classList.contains("title")?void 0:X(re):this.onClose()}),S(this,"onClose",()=>{const{onClose:re}=this.props;re()}),S(this,"scrollIntoActive",()=>{const{menuBaseRef:{current:re}}=this,X=(0,Y.$)("li[role=option].is-active",re);X&&X.scrollIntoView({block:"start"})}),S(this,"onClickOutside",re=>{let{target:X}=re;const{$select:we}=this.props,ze=!L.contains(X),He=X.closest("label"),je=He&&He.contains(we);ze&&!je&&this.onClose()})}componentDidMount(){const{menuBaseRef:{current:re}}=this;(0,Y.sA)((0,Y.$)(".SelectMenu",re))}componentDidUpdate(re){let{isOpen:X}=re;const{isOpen:we}=this.props;!X&&we&&(this.position(),this.scrollIntoActive())}componentWillUnmount(){this.portal&&this.portal.remove()}render(){return(0,p.createPortal)(this.menu,this.portal)}get menu(){const{isOpen:re,isDisabled:X,menuTheme:we,menuClassName:ze,menuX:He,optionList:je,lang:Ee,emptyMsg:tt,value:Pe,canSelect:Q}=this.props,{isDownward:K}=this.state,me=je.length===0,Ce=(0,Y.Hn)([we==="core"?"CoreSelectMenu":we==="check"?"CheckSelectMenu":"SelectMenu",ze,"x-"+He,re&&"is-open",K?"is-downward":"is-upward",X&&"is-disabled",me&&"is-empty",Q?"can-select":"cant-select"]);return(0,v.jsx)(f,{ref:this.menuBaseRef,className:"SelectMenuBase",children:(0,v.jsxs)(y,{className:Ce,onTransitionEnd:this.onTransitionEnd,children:[me?(0,v.jsx)("li",{className:"SelectOption empty-msg",children:tt||l[Ee].select_empty_msg}):je.map((ke,$e)=>T()(ke)?(0,v.jsx)(A,{menuTheme:we,optionList:ke,value:Pe,onChange:this.onChange},$e):(0,v.jsx)(ee,{menuTheme:we,isActive:(0,Y.o3)(ke,Pe),option:ke,isDisabled:ke.isDisabled,onChange:this.onChange},$e)),re&&(0,v.jsx)(k.A,{target:document,onClick:(0,k.t)(this.onClickOutside,{capture:!0})}),re&&(0,v.jsx)(k.A,{target:document,onScroll:(0,k.t)(this.position,{capture:!0})})]})})}}S(g,"propTypes",{...se.propTypes,isOpen:u().bool,canSelect:u().bool,onChange:u().func,onClose:u().func,$select:u().instanceOf(Element)}),S(g,"defaultProps",{isOpen:!1});function A(le){let{value:re,optionList:[X,...we],menuTheme:ze,onChange:He}=le;return(0,v.jsxs)("li",{className:"SelectGroup",children:[(0,v.jsx)(V.A,{className:"title",onClick:He,children:X}),(0,v.jsx)("ul",{children:we.map((je,Ee)=>(0,v.jsx)(ee,{menuTheme:ze,option:je,isActive:(0,Y.o3)(je,re),isDisabled:je.isDisabled,onChange:He},Ee))})]})}A.propTypes={optionList:u().array,onChange:u().func,menuTheme:u().string,value:u().string};function ee(le){let{option:re,isActive:X,isDisabled:we,menuTheme:ze,onChange:He}=le;const je=(0,Y.Hn)(["SelectOption",X&&"is-active",we&&"is-disabled"]),Ee=(0,Y.Oi)(re),tt=(0,Y.nE)(re);return(0,v.jsxs)("li",{role:"option","data-value":tt,className:je,onClick:we?void 0:He,children:[(0,v.jsx)(V.A,{children:Ee}),ze==="check"&&X&&(0,v.jsx)(he.A,{name:"check"})]})}ee.propTypes={option:u().oneOfType([u().node,u().object]),isActive:u().bool,isDisabled:u().bool,menuTheme:u().string,onChange:u().func}},10481:(Ne,de,n)=>{"use strict";n.d(de,{eB:()=>Ee,Ay:()=>je});var s=n(51044),p=n(60185),M=n(66748),u=n.n(M),k=n(480),te=n(60260),Z=n.n(te),z=n(77017),T=n.n(z),E=n(98236),U=n.n(E),q=n(17956),J=n.n(q),he=n(22379),V=n(28149),Y=n(67231),H=n.n(Y),w=n(68431),C=n.n(w);const f=9;function y(Q){let{$opener:K,$menuBase:me,menuBaseStyle:Ce={},inflexible:ke=!1,shouldSetMaxHeight:$e=!1,$menuContainer:Me,$fontTip:Ie}=Q;if(!K||!me)return;const Je=me.querySelector("*"),yt=((0,V.$)("li[role=option].is-active",Je)||(0,V.$)("li[role=option]",Je)||(0,V.$)("li[role=empty-msg]",Je)).getBoundingClientRect(),xt=Je.getBoundingClientRect(),bt={styleFor$menuBase:{},styleFor$menu:{},styleFor$menuContainer:{},styleFor$fontTip:{}},St=tn=>Object.assign(bt.styleFor$menuBase,tn),r=tn=>Object.assign(bt.styleFor$menu,tn),ue=tn=>Object.assign(bt.styleFor$menuContainer,tn),ve=tn=>Object.assign(bt.styleFor$fontTip,tn),{offsetHeight:Ae}=Je,Ve=Ce.width||K.offsetWidth,qe=Ce.height||K.offsetHeight,ut=K.getBoundingClientRect(),{top:jt,bottom:Ut,left:Zt}=Object.assign({top:ut.top,right:ut.right,bottom:ut.bottom,left:ut.left},Ce);St({top:jt+"px",left:Zt+"px",width:Ve+"px",height:qe+"px"});const{innerHeight:$t}=window,Dn=10,dn=$t-10,In=jt+qe;let un=0,jn=0;const mn=(yt.top-xt.top)/.8;L(xt,yt)?un=-Math.min(mn,jt):(jn=yt.bottom/.8-xt.bottom/.8,un=-mn+jn);let En=0;return Ie&&(En=Ie.getBoundingClientRect().height),In+Ae+f+un+En<$t?(bt.isDownward=!0,L(xt,yt)?(ue({top:un+"px"}),Ie&&ve({top:un+xt.height/.8+"px",width:xt.width/.8+"px"})):(ue({top:un+"px"}),Me.scrollTop=jn,Ie&&ve({top:un+xt.height/.8+"px",width:xt.width/.8+"px"})),$e&&Ut+Ae>dn&&r({maxHeight:dn-Ut+"px"})):(bt.isDownward=!1,$e&&jt-Ae<Dn&&r({maxHeight:jt-Dn+"px"})),Object.assign(me.style,bt.styleFor$menuBase),Object.assign(Je.style,bt.styleFor$menu),Object.assign(Me.style,bt.styleFor$menuContainer),Ie&&Object.assign(Ie.style,bt.styleFor$fontTip),bt}function v(Q,K){return C()(Q)||H()(Q)||(0,s.isValidElement)(Q)?Q:Q[K||"label"]?Q[K||"label"]:void 0}function S(Q){return C()(Q)||H()(Q)?String(Q):Q.value||Q.label?String(Q.value||Q.label):void 0}function x(Q){return C()(Q)||H()(Q)?String(Q):Q.ItemIcon?String(Q.ItemIcon):void 0}function I(Q){return C()(Q)||H()(Q)?String(Q):Q.shortcutKey?String(Q.shortcutKey):void 0}function D(Q,K){return!!K&&S(Q)===String(K)}const l='<svg width="10" height="10" ><path d="M5 5.255l1.87-2.043a.623.623 0 0 1 .936 0 .77.77 0 0 1 0 1.022L5.468 6.788a.623.623 0 0 1-.936 0L2.194 4.234a.77.77 0 0 1 0-1.022.623.623 0 0 1 .935 0L5 5.255z" /></svg>';function L(Q,K){return K.top/.8>=Q.top/.8&&K.bottom/.8<=Q.bottom/.8}var R=n(21676);const se=R.Ay.label.withConfig({displayName:"styles__StyledSelect",componentId:"sc-tgxfzg-0"})(["position:relative;display:inline-flex;align-items:center;max-width:100%;min-width:3em;height:28px;font-size:12px;color:#5b6b73;cursor:pointer;button{display:block;height:100%;color:#1F292E;text-align:start;cursor:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding-left:8px;line-height:26px;}.caret{padding:0 11px;color:#7D8694;height:100%;display:inline-flex;align-items:center;svg{width:10px;height:10px;fill:currentColor;transition:transform 0.2s ease-out;}}&{padding-left:5px;border:1px solid transparent;border-radius:2px;}&.is-open{border-color:transparent;.caret{margin-left:auto;visibility:visible;svg{transform:rotate(180deg);}}}&:not(.is-disabled):not(.readonly):hover{border:1px solid transparent;.caret{padding:0 11px;}}&.is-disabled{cursor:not-allowed;opacity:0.6;}&.readonly{cursor:default;}"]),g=R.Ay.div.withConfig({displayName:"styles__StyledSelectMenu",componentId:"sc-tgxfzg-1"})(["position:fixed;pointer-events:none;z-index:1100;.WorkspaceSelectMenu{position:absolute;padding:8px 0;display:block;max-width:20em;min-width:100%;min-height:30px;max-height:300px;overflow-x:hidden;overflow-y:auto;list-style:none;box-shadow:0 2px 8px 0 rgba(0,0,0,0.1);color:#fff;border-radius:2px;background-color:#1f292e;pointer-events:all;transition-property:transform,opacity;transform-origin:50% 0;margin:0;&::-webkit-scrollbar{display:block;width:6px;height:6px;}&::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,0.25);border-radius:4px;&:hover{background-color:rgba(255,255,255,0.30);}}&::-webkit-scrollbar-track{background-color:rgba(#fff,0.5);}&.is-empty{width:fit-content;}&:not(.is-open){opacity:0;transform:scale(0.8);}&.is-downward{top:100%;bottom:initial;}&.is-upward{transform-origin:50% 100%;top:initial;bottom:100%;}&.is-open{opacity:1;pointer-events:initial;border-radius:4px;background-color:#333;width:160px;padding:8px 0px;}> .divider{border-bottom:1px solid rgba(255,255,255,.1);margin:5px;}.SelectOption{display:flex;align-items:center;height:24px;line-height:24px;cursor:pointer;font-size:12px;font-weight:400;& > .Ellipsis{padding:0 16px 0 32px;min-width:160px;}&.is-disabled{cursor:not-allowed;opacity:0.5;}&.is-active{color:#fff;mix-blend-mode:normal;position:relative;}.shortcutkey{color:#999;float:right;}.svg-icon3 svg{color:#f2f4f5;width:12px;height:12px;position:relative;top:2px;}&.is-active .svg-icon2 svg{width:8px;height:6px;position:absolute;left:12px;top:9px;}&:not(.empty-msg):not(.is-disabled):not(.is-active):hover{background-color:#666;color:#fff;}&.empty-msg{padding:0 0.5em;color:#8d9ea6;cursor:not-allowed;}}&.cant-select .SelectOption{cursor:default;}.SelectGroup{.divider{height:1px;margin:4px 16px;background:#7d8694;}& > .title{font-size:12px;padding:0 8px 0 16px;width:100%;height:30px;line-height:30px;color:rgba(255,255,255,0.7);span{display:block;}}& > ul{margin:0;padding:0;}}}.font-select-menu-tip{position:absolute;margin-top:-1px;padding:8px 16px;font-size:10px;box-shadow:0 2px 10px 0 rgba(39,54,78,0.08),4px 12px 40px 0 rgba(39,54,78,0.1);color:#fff;background-color:#1f292e;border-radius:0 0 2px 2px;pointer-events:auto;transition:opacity 0.2s ease-in;.font-link{color:#fff;margin-left:10px;text-decoration:underline;}&:not(.is-show){opacity:0;}&.is-show{opacity:1;color:rgba(255,255,255,1);}}"]);var A=n(90503),ee=n(57464);function le(Q,K,me){return(K=re(K))in Q?Object.defineProperty(Q,K,{value:me,enumerable:!0,configurable:!0,writable:!0}):Q[K]=me,Q}function re(Q){var K=X(Q,"string");return typeof K=="symbol"?K:K+""}function X(Q,K){if(typeof Q!="object"||!Q)return Q;var me=Q[Symbol.toPrimitive];if(me!==void 0){var Ce=me.call(Q,K||"default");if(typeof Ce!="object")return Ce;throw new TypeError("@@toPrimitive must return a primitive value.")}return(K==="string"?String:Number)(Q)}const we="IBOT_SELECT_MENU_ROOT",ze=document.getElementById(we)||Object.assign(document.createElement("div"),{id:we}),He=document.body;He.contains(ze)||He.appendChild(ze);class je extends s.PureComponent{constructor(){super(...arguments),le(this,"state",{isOpen:!1,prevProps:this.props,value:this.props.value}),le(this,"set$select",K=>this.setState({$select:K})),le(this,"open",()=>this.setState({isOpen:!0})),le(this,"close",()=>this.setState({isOpen:!1})),le(this,"toggle",()=>{const K=!this.state.isOpen;this.setState({isOpen:K}),K?this.props.onOpen():this.props.onClose()}),le(this,"onResizeWindow",()=>this.state.isOpen&&this.close()),le(this,"onChange",K=>this.setState({value:K},()=>{this.close(),this.props.onChange(K,this.props.attr)})),le(this,"onSelect",K=>{let{currentTarget:me}=K;const{value:Ce}=this.props,{canSelect:ke}=this;return this.onChange(ke?me.dataset.value:Ce)})}static getDerivedStateFromProps(K,me){let{prevProps:Ce,value:ke}=me;return U()(Ce,K)?null:{prevProps:K,value:K.value}}componentDidMount(){window.addEventListener("resize",this.onResizeWindow)}componentWillUnmount(){window.removeEventListener("resize",this.onResizeWindow)}get isDisabled(){const{isDisabled:K,disabled:me}=this.props;return K||me}get readOnly(){return this.props.readOnly}get canSelect(){const{isDisabled:K,readOnly:me}=this;return!K&&!me}get displayText(){const{optionList:K,placeholder:me,optionLabelProp:Ce}=this.props,{value:ke}=this.state,Me=(K.find(Ie=>T()(Ie)&&Ie.slice(0).some(Je=>D(Je,ke)))||K).find(Ie=>!T()(Ie)&&D(Ie,ke));return Me?v(Me,Ce):me}render(){const{size:K,unstyled:me,className:Ce,onMouseEnter:ke,onMouseLeave:$e,arrowSvg:Me}=this.props,{isOpen:Ie,$select:Je,value:st}=this.state,{isDisabled:yt,readOnly:xt,canSelect:bt}=this,St=(0,V.Hn)(["WorkspaceSelect",K,me&&"unstyled",Ce,Ie&&"is-open",yt&&"is-disabled",xt&&"readonly"]);return(0,ee.jsxs)(se,{className:St,role:"listbox",ref:this.set$select,children:[(0,ee.jsx)("button",{onClick:this.toggle,disabled:yt,children:this.displayText}),typeof Me=="string"?(0,ee.jsx)("span",{className:"caret",dangerouslySetInnerHTML:{__html:Me}}):(0,ee.jsx)("span",{className:"caret",children:Me}),Ie&&(0,ee.jsx)(Ee,{isOpen:Ie,...this.props,value:st,$select:Je,canSelect:bt,onChange:this.onSelect,onMouseEnter:ke,onMouseLeave:$e,onClose:this.close})]})}}le(je,"propTypes",{size:u().oneOf(["regular","small"]),theme:u().oneOf(["core","plain"]),menuTheme:u().oneOf(["core","plain","check"]),unstyled:u().bool,className:u().string,menuClassName:u().string,placeholder:u().string,optionList:u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,shortcutKey:u().node,tooltipWrapper:u().func,ItemIcon:u().node,value:u().any,isDisabled:u().bool}),u().arrayOf(u().oneOfType([u().node,u().shape({label:u().node,ItemIcon:u().node,value:u().any,shortcutKey:u().node,tooltipWrapper:u().func,isDisabled:u().bool})]))])).isRequired,value:u().oneOfType([u().number,u().string]),isDisabled:u().bool,disabled:u().bool,readOnly:u().bool,onChange:u().func,onMouseEnter:u().func,onMouseLeave:u().func,onOpen:u().func,onClose:u().func,optionLabelProp:u().string,arrowSvg:u().oneOfType([u().string,u().node])}),le(je,"defaultProps",{size:"regular",theme:"plain",menuTheme:"plain",className:"",menuClassName:"",placeholder:"Choose one\u2026",emptyMsg:"Nothing to display\u2026",optionList:[],isDisabled:!1,arrowSvg:l,onChange:()=>null,onMouseEnter:()=>null,onMouseLeave:()=>null,onOpen:()=>null,onClose:()=>null});class Ee extends s.PureComponent{constructor(){super(...arguments),le(this,"state",{isDownward:!0}),le(this,"portal",(0,V.ep)(ze,"SelectMenuPortal")),le(this,"menuBaseRef",(0,s.createRef)()),le(this,"menuContainerRef",(0,s.createRef)()),le(this,"position",K=>{const{$select:me}=this.props,{menuBaseRef:{current:Ce},menuContainerRef:{current:ke}}=this;if(K){const Me=Z()(K,"target");if(Me&&J()(Me)&&Me.matches(".WorkspaceSelectMenu"))return}const{isDownward:$e}=y({$menuBase:Ce,$opener:me,shouldSetMaxHeight:!1,$menuContainer:ke});this.setState({isDownward:$e,isTransform:!0})}),le(this,"onChange",K=>{const{onChange:me}=this.props,{isDownward:Ce}=this.state,ke=K.currentTarget,$e=ke.closest(".WorkspaceSelectMenu");if(!ke||!$e)return this.onlose();const{top:Me,bottom:Ie}=ke.getBoundingClientRect(),{top:Je,bottom:st}=$e.getBoundingClientRect();return Ce&&Me>=Je||!Ce&&Ie<=st?ke.classList.contains("title")?void 0:me(K):this.onClose()}),le(this,"onClose",()=>{const{onClose:K}=this.props;K()}),le(this,"onClickOutside",K=>{let{target:me}=K;const{$select:Ce}=this.props,ke=!ze.contains(me),$e=me.closest("label"),Me=$e&&$e.contains(Ce);ke&&!Me&&this.onClose()})}componentDidMount(){const{menuBaseRef:{current:K}}=this;(0,V.sA)((0,V.$)(".WorkspaceSelectMenu",K)),this.position()}componentWillUnmount(){this.portal&&this.portal.remove()}render(){return(0,p.createPortal)(this.menu,this.portal)}get menu(){const{isOpen:K,isDisabled:me,menuTheme:Ce,menuClassName:ke,optionList:$e,emptyMsg:Me,value:Ie,canSelect:Je,onMouseEnter:st,onMouseLeave:yt}=this.props,{isDownward:xt,isTransform:bt}=this.state,St=$e.length===0,r=(0,V.Hn)(["WorkspaceSelectMenu",ke,bt&&"is-open",xt?"is-downward":"is-upward",me&&"is-disabled",St&&"is-empty",Je?"can-select":"cant-select"]);return(0,ee.jsxs)(g,{ref:this.menuBaseRef,children:[(0,ee.jsx)("ul",{className:r,ref:this.menuContainerRef,children:St?(0,ee.jsx)("li",{className:"SelectOption empty-msg",role:"empty-msg",children:Me}):$e.map((ue,ve)=>T()(ue)?(0,ee.jsx)(tt,{menuTheme:Ce,optionList:ue,value:Ie,onChange:this.onChange,onMouseEnter:st,onMouseLeave:yt},ve):(0,ee.jsx)(Pe,{menuTheme:Ce,isActive:D(ue,Ie),option:ue,isDisabled:ue.isDisabled,onChange:this.onChange,onMouseEnter:st,onMouseLeave:yt},ve))}),K&&(0,ee.jsx)(k.A,{target:document,onClick:this.onClickOutside})]})}}le(Ee,"propTypes",{...je.propTypes,isOpen:u().bool,canSelect:u().bool,onChange:u().func,onClose:u().func,$select:u().instanceOf(Element)}),le(Ee,"defaultProps",{isOpen:!1,isTransform:!1});function tt(Q){let{value:K,optionList:[me,...Ce],menuTheme:ke,onChange:$e,onMouseEnter:Me,onMouseLeave:Ie}=Q;return(0,ee.jsxs)("li",{className:"SelectGroup",children:[me==="HIDDELINE"?null:me==="DIVIDER"?(0,ee.jsx)("div",{className:"divider"}):(0,ee.jsx)(he.A,{className:"title",onClick:$e,children:me}),(0,ee.jsx)("ul",{children:Ce.map((Je,st)=>(0,ee.jsx)(Pe,{menuTheme:ke,option:Je,isActive:D(Je,K),isDisabled:Je.isDisabled,onChange:$e,onMouseEnter:Me,onMouseLeave:Ie},st))})]})}tt.propTypes={value:u().oneOfType([u().string,u().number]),optionList:u().array,onChange:u().func,menuTheme:u().string,onMouseEnter:u().func,onMouseLeave:u().func};function Pe(Q){let{option:K,isActive:me,isDisabled:Ce,menuTheme:ke,onChange:$e,onMouseEnter:Me,onMouseLeave:Ie}=Q;const Je=(0,V.Hn)(["SelectOption",me&&"is-active",Ce&&"is-disabled"]),st=v(K),yt=S(K),xt=x(K),bt=I(K),{tooltipWrapper:St}=K;if(K.type==="divider")return(0,ee.jsx)("li",{className:"divider"});const r=(0,ee.jsxs)("li",{role:"option","data-value":yt,className:Je,onClick:Ce?void 0:$e,onMouseEnter:Me,onMouseLeave:Ie,children:[me&&(0,ee.jsx)("span",{className:"svg-icon2",children:(0,ee.jsx)(A.A,{name:"select_right"})}),(0,ee.jsxs)(he.A,{children:[(0,ee.jsxs)("span",{className:"svg-icon3",children:[(0,ee.jsx)(A.A,{name:xt})," "]})," ",st,"  ",(0,ee.jsxs)("span",{className:"shortcutkey",children:[bt," "]})]})]});return St?St(r):r}Pe.propTypes={isActive:u().bool,option:u().oneOfType([u().node,u().object]),isDisabled:u().bool,menuTheme:u().string,onChange:u().func,onMouseEnter:u().func,onMouseLeave:u().func}},11334:(Ne,de,n)=>{"use strict";n.d(de,{Ay:()=>V});var s=n(51044),p=n(66748),M=n.n(p),u=n(70235),k=n.n(u),te=n(90503),Z=n(28149),z=n(21676);const T=z.Ay.button.withConfig({displayName:"styled__StyledButton",componentId:"sc-1p06iev-0"})(["&.RegularButton,&.PrimaryButton,&.PrimaryCoreButton,&.RegularCoreButton,&.SecondaryCoreButton,&.TertiaryCoreButton{padding:0 0.5em;height:2.125rem;font-size:0.875rem;border:1px solid;border-radius:2px;cursor:pointer;transition:all 0.2s ease-out;&:not(button){display:inline-flex;justify-content:center;align-items:center;}&:disabled{cursor:not-allowed;opacity:0.6;}.icon{margin-right:0.125em;}&.small{height:2em;font-size:0.75rem;}.svg-icon.loading{margin-right:0.5em;vertical-align:-.15em;animation:ibot-ani-spinning 1.5s infinite ease-out;}}&.PrimaryCoreButton,&.RegularCoreButton,&.SecondaryCoreButton,&.TertiaryCoreButton{padding:0 1em;height:2.375rem;&.small{height:2em;}&:disabled{background-color:#f6f7f8;border-color:#dedee4;color:#c8cdd1;opacity:1;}}&.PrimaryCoreButton{background-color:#eb5648;&,&:link,&:visited{border-color:transparent;color:#fff;}&:enabled:hover,a&:hover{background-color:#ef776c;color:#fff;}&:enabled:active,a&:active{background-color:#bc4439;color:#e4b4b0;}}&.RegularCoreButton,&.SecondaryCoreButton{background-color:#fff;&,&:link,&:visited{color:#eb5648;}&:enabled:hover,a&:hover{border-color:#ffa39e;color:#ef776c;}&:enabled:active,a&:active{border-color:#e84030;color:#bc4439;}}&.TertiaryCoreButton{background-color:#fff;border-color:#c8cdd1;&,&:link,&:visited{color:#415058;}&:enabled:hover,a&:hover{background-color:#8d9ea7;border-color:#7d8694;color:#fff;}&:enabled:active,a&:active{background-color:#5b6b73;border-color:#415058;}}&.RegularButton{&,&:link,&:visited{color:#8d9ea7;}&:enabled:hover,a&:hover{color:#5b6b73;}&:disabled{opacity:0.6;}}&.PrimaryButton{background-color:#8d9ea7;border-color:transparent;&,&:link,&:visited{color:#fff;}&:enabled:hover,a&:hover{background-color:#5b6b73;color:#fff;}&:enabled:active,a&:active{color:rgba(255,255,255,0.6);}}&.TextButton,&.TextCoreButton{transition:all 0.1s ease-out;&,&:link,&:visited{color:#298df8;}&:disabled{cursor:not-allowed;opacity:0.6;}&:enabled:hover,a&:hover{color:#0d7ef7;text-decoration:underline;}.svg-icon.loading{margin-right:0.25em;vertical-align:-.15em;animation:ibot-ani-spinning 1.5s infinite ease-out;}.icon{margin-right:0.125em;}&.small{height:2em;font-size:0.75rem;}}&.TextCoreButton{&,&:link,&:visited{color:#eb5648;}&:enabled:hover,a&:hover{color:#ef776c;text-decoration:none;}&:enabled:active,a&:active{color:#bc4439;}}"]);var E=n(57464);function U(C,f,y){return(f=q(f))in C?Object.defineProperty(C,f,{value:y,enumerable:!0,configurable:!0,writable:!0}):C[f]=y,C}function q(C){var f=J(C,"string");return typeof f=="symbol"?f:f+""}function J(C,f){if(typeof C!="object"||!C)return C;var y=C[Symbol.toPrimitive];if(y!==void 0){var v=y.call(C,f||"default");if(typeof v!="object")return v;throw new TypeError("@@toPrimitive must return a primitive value.")}return(f==="string"?String:Number)(C)}const he={primary:"Primary",regular:"Regular",secondary:"Regular",tertiary:"Tertiary",text:"Text"};class V extends s.PureComponent{get className(){const{type:f,theme:y,size:v,className:S}=this.props,{isDisabled:x,isLoading:I}=this;return(0,Z.Hn)(["Button",""+he[f]+(y==="core"?"CoreButton":"Button"),v!=="regular"&&v,I&&"is-loading",x&&"is-disabled",S])}get isDisabled(){const{isDisabled:f,disabled:y}=this.props;return f||y}get isLoading(){const{isLoading:f,loading:y}=this.props;return f||y}render(){const{className:f,isLoading:y,isDisabled:v}=this,{iconType:S,icon:x,children:I,...D}=this.props;return(0,E.jsx)(T,{className:f,disabled:v,onClick:l=>v&&l.preventDefault(),type:"button",...k()(D,["className","type","theme","isDisabled","disabled","isLoading","loading"]),children:(0,E.jsxs)(E.Fragment,{children:[y&&(0,E.jsx)(te.A,{name:"loading"}),x&&S==="svg"&&(0,E.jsx)(te.A,{name:x}),I]})})}}U(V,"propTypes",{type:M().oneOf(["primary","regular","secondary","tertiary","text"]),size:M().oneOf(["regular","small"]),theme:M().oneOf(["core","plain"]),iconType:M().oneOf(["svg","dora","mb","icon","fa","md"]),icon:M().string,className:M().string,isDisabled:M().bool,disabled:M().bool,isLoading:M().bool,loading:M().bool,children:M().any,html:M().string}),U(V,"defaultProps",{type:"regular",size:"regular",theme:"plain",icon:"",className:"",isDisabled:!1});function Y(C){return _jsx(V,{...C,theme:"core"})}function H(C){return _jsx(Y,{...C,type:"primary"})}function w(C){return _jsx(Y,{...C,type:"tertiary"})}},35736:function(Ne){(function(de,n){Ne.exports=n()})(this,function(){"use strict";return function(de,n,s){n.prototype.isBetween=function(p,M,u,k){var te=s(p),Z=s(M),z=(k=k||"()")[0]==="(",T=k[1]===")";return(z?this.isAfter(te,u):!this.isBefore(te,u))&&(T?this.isBefore(Z,u):!this.isAfter(Z,u))||(z?this.isBefore(te,u):!this.isAfter(te,u))&&(T?this.isAfter(Z,u):!this.isBefore(Z,u))}}})},54466:function(Ne){(function(de,n){Ne.exports=n()})(this,function(){"use strict";return function(de,n){n.prototype.isSameOrBefore=function(s,p){return this.isSame(s,p)||this.isBefore(s,p)}}})},36410:Ne=>{function de(n,s,p,M){var u=-1,k=n==null?0:n.length;for(M&&k&&(p=n[++u]);++u<k;)p=s(p,n[u],u,n);return p}Ne.exports=de},38621:Ne=>{var de=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function n(s){return s.match(de)||[]}Ne.exports=n},52304:Ne=>{function de(n){return function(s){return n==null?void 0:n[s]}}Ne.exports=de},48843:(Ne,de,n)=>{var s=n(36410),p=n(76628),M=n(83453),u="['\u2019]",k=RegExp(u,"g");function te(Z){return function(z){return s(M(p(z).replace(k,"")),Z,"")}}Ne.exports=te},84394:(Ne,de,n)=>{var s=n(25627);function p(M){return s(M)?void 0:M}Ne.exports=p},32575:(Ne,de,n)=>{var s=n(52304),p={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"Ae",\u00E6:"ae",\u00DE:"Th",\u00FE:"th",\u00DF:"ss",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"IJ",\u0133:"ij",\u0152:"Oe",\u0153:"oe",\u0149:"'n",\u017F:"s"},M=s(p);Ne.exports=M},95416:(Ne,de,n)=>{var s=n(66202),p=n(61965),M=n(29513);function u(k){return M(p(k,void 0,s),k+"")}Ne.exports=u},49298:Ne=>{var de=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function n(s){return de.test(s)}Ne.exports=n},32921:Ne=>{var de="\\ud800-\\udfff",n="\\u0300-\\u036f",s="\\ufe20-\\ufe2f",p="\\u20d0-\\u20ff",M=n+s+p,u="\\u2700-\\u27bf",k="a-z\\xdf-\\xf6\\xf8-\\xff",te="\\xac\\xb1\\xd7\\xf7",Z="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",z="\\u2000-\\u206f",T=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",E="A-Z\\xc0-\\xd6\\xd8-\\xde",U="\\ufe0e\\ufe0f",q=te+Z+z+T,J="['\u2019]",he="["+q+"]",V="["+M+"]",Y="\\d+",H="["+u+"]",w="["+k+"]",C="[^"+de+q+Y+u+k+E+"]",f="\\ud83c[\\udffb-\\udfff]",y="(?:"+V+"|"+f+")",v="[^"+de+"]",S="(?:\\ud83c[\\udde6-\\uddff]){2}",x="[\\ud800-\\udbff][\\udc00-\\udfff]",I="["+E+"]",D="\\u200d",l="(?:"+w+"|"+C+")",L="(?:"+I+"|"+C+")",R="(?:"+J+"(?:d|ll|m|re|s|t|ve))?",se="(?:"+J+"(?:D|LL|M|RE|S|T|VE))?",g=y+"?",A="["+U+"]?",ee="(?:"+D+"(?:"+[v,S,x].join("|")+")"+A+g+")*",le="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",re="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",X=A+g+ee,we="(?:"+[H,S,x].join("|")+")"+X,ze=RegExp([I+"?"+w+"+"+R+"(?="+[he,I,"$"].join("|")+")",L+"+"+se+"(?="+[he,I+l,"$"].join("|")+")",I+"?"+l+"+"+R,I+"+"+se,re,le,Y,we].join("|"),"g");function He(je){return je.match(ze)||[]}Ne.exports=He},76628:(Ne,de,n)=>{var s=n(32575),p=n(67998),M=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,u="\\u0300-\\u036f",k="\\ufe20-\\ufe2f",te="\\u20d0-\\u20ff",Z=u+k+te,z="["+Z+"]",T=RegExp(z,"g");function E(U){return U=p(U),U&&U.replace(M,s).replace(T,"")}Ne.exports=E},66202:(Ne,de,n)=>{var s=n(72840);function p(M){var u=M==null?0:M.length;return u?s(M,1):[]}Ne.exports=p},17956:(Ne,de,n)=>{var s=n(23522),p=n(25627);function M(u){return s(u)&&u.nodeType===1&&!p(u)}Ne.exports=M},25627:(Ne,de,n)=>{var s=n(22256),p=n(41703),M=n(23522),u="[object Object]",k=Function.prototype,te=Object.prototype,Z=k.toString,z=te.hasOwnProperty,T=Z.call(Object);function E(U){if(!M(U)||s(U)!=u)return!1;var q=p(U);if(q===null)return!0;var J=z.call(q,"constructor")&&q.constructor;return typeof J=="function"&&J instanceof J&&Z.call(J)==T}Ne.exports=E},70235:(Ne,de,n)=>{var s=n(79228),p=n(99159),M=n(92547),u=n(86225),k=n(80167),te=n(84394),Z=n(95416),z=n(94397),T=1,E=2,U=4,q=Z(function(J,he){var V={};if(J==null)return V;var Y=!1;he=s(he,function(w){return w=u(w,J),Y||(Y=w.length>1),w}),k(J,z(J),V),Y&&(V=p(V,T|E|U,te));for(var H=he.length;H--;)M(V,he[H]);return V});Ne.exports=q},63924:(Ne,de,n)=>{var s=n(48843),p=s(function(M,u,k){return M+(k?"_":"")+u.toLowerCase()});Ne.exports=p},83453:(Ne,de,n)=>{var s=n(38621),p=n(49298),M=n(67998),u=n(32921);function k(te,Z,z){return te=M(te),Z=z?void 0:Z,Z===void 0?p(te)?u(te):s(te):te.match(Z)||[]}Ne.exports=k},43441:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>he});var s=n(18824),p=n(95211),M=n(7043);function u(V,Y){return V.classList?!!Y&&V.classList.contains(Y):(" "+(V.className.baseVal||V.className)+" ").indexOf(" "+Y+" ")!==-1}function k(V,Y){V.classList?V.classList.add(Y):u(V,Y)||(typeof V.className=="string"?V.className=V.className+" "+Y:V.setAttribute("class",(V.className&&V.className.baseVal||"")+" "+Y))}function te(V,Y){return V.replace(new RegExp("(^|\\s)"+Y+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function Z(V,Y){V.classList?V.classList.remove(Y):typeof V.className=="string"?V.className=te(V.className,Y):V.setAttribute("class",te(V.className&&V.className.baseVal||"",Y))}var z=n(51044),T=n(93218),E=n(41083),U=function(Y,H){return Y&&H&&H.split(" ").forEach(function(w){return k(Y,w)})},q=function(Y,H){return Y&&H&&H.split(" ").forEach(function(w){return Z(Y,w)})},J=function(V){(0,M.A)(Y,V);function Y(){for(var w,C=arguments.length,f=new Array(C),y=0;y<C;y++)f[y]=arguments[y];return w=V.call.apply(V,[this].concat(f))||this,w.appliedClasses={appear:{},enter:{},exit:{}},w.onEnter=function(v,S){var x=w.resolveArguments(v,S),I=x[0],D=x[1];w.removeClasses(I,"exit"),w.addClass(I,D?"appear":"enter","base"),w.props.onEnter&&w.props.onEnter(v,S)},w.onEntering=function(v,S){var x=w.resolveArguments(v,S),I=x[0],D=x[1],l=D?"appear":"enter";w.addClass(I,l,"active"),w.props.onEntering&&w.props.onEntering(v,S)},w.onEntered=function(v,S){var x=w.resolveArguments(v,S),I=x[0],D=x[1],l=D?"appear":"enter";w.removeClasses(I,l),w.addClass(I,l,"done"),w.props.onEntered&&w.props.onEntered(v,S)},w.onExit=function(v){var S=w.resolveArguments(v),x=S[0];w.removeClasses(x,"appear"),w.removeClasses(x,"enter"),w.addClass(x,"exit","base"),w.props.onExit&&w.props.onExit(v)},w.onExiting=function(v){var S=w.resolveArguments(v),x=S[0];w.addClass(x,"exit","active"),w.props.onExiting&&w.props.onExiting(v)},w.onExited=function(v){var S=w.resolveArguments(v),x=S[0];w.removeClasses(x,"exit"),w.addClass(x,"exit","done"),w.props.onExited&&w.props.onExited(v)},w.resolveArguments=function(v,S){return w.props.nodeRef?[w.props.nodeRef.current,v]:[v,S]},w.getClassNames=function(v){var S=w.props.classNames,x=typeof S=="string",I=x&&S?S+"-":"",D=x?""+I+v:S[v],l=x?D+"-active":S[v+"Active"],L=x?D+"-done":S[v+"Done"];return{baseClassName:D,activeClassName:l,doneClassName:L}},w}var H=Y.prototype;return H.addClass=function(C,f,y){var v=this.getClassNames(f)[y+"ClassName"],S=this.getClassNames("enter"),x=S.doneClassName;f==="appear"&&y==="done"&&x&&(v+=" "+x),y==="active"&&C&&(0,E.F)(C),v&&(this.appliedClasses[f][y]=v,U(C,v))},H.removeClasses=function(C,f){var y=this.appliedClasses[f],v=y.base,S=y.active,x=y.done;this.appliedClasses[f]={},v&&q(C,v),S&&q(C,S),x&&q(C,x)},H.render=function(){var C=this.props,f=C.classNames,y=(0,p.A)(C,["classNames"]);return z.createElement(T.Ay,(0,s.A)({},y,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},Y}(z.Component);J.defaultProps={classNames:""},J.propTypes={};const he=J},93218:(Ne,de,n)=>{"use strict";n.d(de,{Ay:()=>V});var s=n(95211),p=n(7043),M=n(51044),u=n(60185);const k={disabled:!1},te=M.createContext(null);var Z=n(41083),z="unmounted",T="exited",E="entering",U="entered",q="exiting",J=function(Y){(0,p.A)(H,Y);function H(C,f){var y;y=Y.call(this,C,f)||this;var v=f,S=v&&!v.isMounting?C.enter:C.appear,x;return y.appearStatus=null,C.in?S?(x=T,y.appearStatus=E):x=U:C.unmountOnExit||C.mountOnEnter?x=z:x=T,y.state={status:x},y.nextCallback=null,y}H.getDerivedStateFromProps=function(f,y){var v=f.in;return v&&y.status===z?{status:T}:null};var w=H.prototype;return w.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},w.componentDidUpdate=function(f){var y=null;if(f!==this.props){var v=this.state.status;this.props.in?v!==E&&v!==U&&(y=E):(v===E||v===U)&&(y=q)}this.updateStatus(!1,y)},w.componentWillUnmount=function(){this.cancelNextCallback()},w.getTimeouts=function(){var f=this.props.timeout,y,v,S;return y=v=S=f,f!=null&&typeof f!="number"&&(y=f.exit,v=f.enter,S=f.appear!==void 0?f.appear:v),{exit:y,enter:v,appear:S}},w.updateStatus=function(f,y){if(f===void 0&&(f=!1),y!==null)if(this.cancelNextCallback(),y===E){if(this.props.unmountOnExit||this.props.mountOnEnter){var v=this.props.nodeRef?this.props.nodeRef.current:u.findDOMNode(this);v&&(0,Z.F)(v)}this.performEnter(f)}else this.performExit();else this.props.unmountOnExit&&this.state.status===T&&this.setState({status:z})},w.performEnter=function(f){var y=this,v=this.props.enter,S=this.context?this.context.isMounting:f,x=this.props.nodeRef?[S]:[u.findDOMNode(this),S],I=x[0],D=x[1],l=this.getTimeouts(),L=S?l.appear:l.enter;if(!f&&!v||k.disabled){this.safeSetState({status:U},function(){y.props.onEntered(I)});return}this.props.onEnter(I,D),this.safeSetState({status:E},function(){y.props.onEntering(I,D),y.onTransitionEnd(L,function(){y.safeSetState({status:U},function(){y.props.onEntered(I,D)})})})},w.performExit=function(){var f=this,y=this.props.exit,v=this.getTimeouts(),S=this.props.nodeRef?void 0:u.findDOMNode(this);if(!y||k.disabled){this.safeSetState({status:T},function(){f.props.onExited(S)});return}this.props.onExit(S),this.safeSetState({status:q},function(){f.props.onExiting(S),f.onTransitionEnd(v.exit,function(){f.safeSetState({status:T},function(){f.props.onExited(S)})})})},w.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},w.safeSetState=function(f,y){y=this.setNextCallback(y),this.setState(f,y)},w.setNextCallback=function(f){var y=this,v=!0;return this.nextCallback=function(S){v&&(v=!1,y.nextCallback=null,f(S))},this.nextCallback.cancel=function(){v=!1},this.nextCallback},w.onTransitionEnd=function(f,y){this.setNextCallback(y);var v=this.props.nodeRef?this.props.nodeRef.current:u.findDOMNode(this),S=f==null&&!this.props.addEndListener;if(!v||S){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var x=this.props.nodeRef?[this.nextCallback]:[v,this.nextCallback],I=x[0],D=x[1];this.props.addEndListener(I,D)}f!=null&&setTimeout(this.nextCallback,f)},w.render=function(){var f=this.state.status;if(f===z)return null;var y=this.props,v=y.children,S=y.in,x=y.mountOnEnter,I=y.unmountOnExit,D=y.appear,l=y.enter,L=y.exit,R=y.timeout,se=y.addEndListener,g=y.onEnter,A=y.onEntering,ee=y.onEntered,le=y.onExit,re=y.onExiting,X=y.onExited,we=y.nodeRef,ze=(0,s.A)(y,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return M.createElement(te.Provider,{value:null},typeof v=="function"?v(f,ze):M.cloneElement(M.Children.only(v),ze))},H}(M.Component);J.contextType=te,J.propTypes={};function he(){}J.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:he,onEntering:he,onEntered:he,onExit:he,onExiting:he,onExited:he},J.UNMOUNTED=z,J.EXITED=T,J.ENTERING=E,J.ENTERED=U,J.EXITING=q;const V=J},41083:(Ne,de,n)=>{"use strict";n.d(de,{F:()=>s});var s=function(M){return M.scrollTop}},82928:(Ne,de)=>{"use strict";var n;n={value:!0};var s=Object.assign||function(te){for(var Z=1;Z<arguments.length;Z++){var z=arguments[Z];for(var T in z)Object.prototype.hasOwnProperty.call(z,T)&&(te[T]=z[T])}return te},p=function(){var Z=null,z={},T=function(he){var V=z[he.type];return V&&V(Z,he)},E=function(he){if(typeof he.getState!="function"||typeof he.dispatch!="function")throw new Error("[ReduxEntry][middleware] invalid reduxMiddlewareStore");if(Z!==null)throw new Error("[ReduxEntry][middleware] already set reduxMiddlewareStore");return Z=he,function(V){return function(Y){return T(Y)===!0||V(Y)}}},U=function(he,V){if(typeof he!="string")throw new Error("[ReduxEntry][setEntry] non-string actionType: "+he);if(typeof V!="function")throw new Error("[ReduxEntry][setEntry] non-function entryFunction: "+he+", "+V);z[he]&&console.warn("[ReduxEntry][setEntry] possible unexpected entry overwrite: "+he),z[he]=V},q=function(he){return Object.keys(he).forEach(function(V){return U(V,he[V])})};return{middleware:E,setEntry:U,setEntryMap:q}},M=function(Z){if(Z===void 0)throw new Error("[ReduxEntry][createStateStore] initialState expected");return{getState:function(){return Z},setState:function(T){return Z=T},wrapEntry:function(T){return function(E,U){return T(Z,E,U)}}}},u=function(Z,z){var T=z.getState,E=z.setState;return function(U,q){var J=q.type,he=q.payload;return J===Z&&E(he),T()}},k=function(Z,z){var T=z.getState,E=z.setState;return function(U,q){var J=q.type,he=q.payload;return J===Z&&E(s({},T(),he)),T()}};de.RZ=p,de.J$=M,de.Tf=u,n=k},28055:(Ne,de,n)=>{"use strict";n.d(de,{Tw:()=>S,HY:()=>C,Zz:()=>v,y$:()=>he,Yl:()=>V});var s=n(74059);function p(x,I){var D=Object.keys(x);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(x);I&&(l=l.filter(function(L){return Object.getOwnPropertyDescriptor(x,L).enumerable})),D.push.apply(D,l)}return D}function M(x){for(var I=1;I<arguments.length;I++){var D=arguments[I]!=null?arguments[I]:{};I%2?p(Object(D),!0).forEach(function(l){(0,s.A)(x,l,D[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(x,Object.getOwnPropertyDescriptors(D)):p(Object(D)).forEach(function(l){Object.defineProperty(x,l,Object.getOwnPropertyDescriptor(D,l))})}return x}function u(x){return"Minified Redux error #"+x+"; visit https://redux.js.org/Errors?code="+x+" for the full message or use the non-minified dev environment for full errors. "}var k=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),te=function(){return Math.random().toString(36).substring(7).split("").join(".")},Z={INIT:"@@redux/INIT"+te(),REPLACE:"@@redux/REPLACE"+te(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+te()}};function z(x){if(typeof x!="object"||x===null)return!1;for(var I=x;Object.getPrototypeOf(I)!==null;)I=Object.getPrototypeOf(I);return Object.getPrototypeOf(x)===I}function T(x){if(x===void 0)return"undefined";if(x===null)return"null";var I=typeof x;switch(I){case"boolean":case"string":case"number":case"symbol":case"function":return I}if(Array.isArray(x))return"array";if(q(x))return"date";if(U(x))return"error";var D=E(x);switch(D){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return D}return I.slice(8,-1).toLowerCase().replace(/\s/g,"")}function E(x){return typeof x.constructor=="function"?x.constructor.name:null}function U(x){return x instanceof Error||typeof x.message=="string"&&x.constructor&&typeof x.constructor.stackTraceLimit=="number"}function q(x){return x instanceof Date?!0:typeof x.toDateString=="function"&&typeof x.getDate=="function"&&typeof x.setDate=="function"}function J(x){var I=typeof x;return I}function he(x,I,D){var l;if(typeof I=="function"&&typeof D=="function"||typeof D=="function"&&typeof arguments[3]=="function")throw new Error(u(0));if(typeof I=="function"&&typeof D>"u"&&(D=I,I=void 0),typeof D<"u"){if(typeof D!="function")throw new Error(u(1));return D(he)(x,I)}if(typeof x!="function")throw new Error(u(2));var L=x,R=I,se=[],g=se,A=!1;function ee(){g===se&&(g=se.slice())}function le(){if(A)throw new Error(u(3));return R}function re(He){if(typeof He!="function")throw new Error(u(4));if(A)throw new Error(u(5));var je=!0;return ee(),g.push(He),function(){if(je){if(A)throw new Error(u(6));je=!1,ee();var tt=g.indexOf(He);g.splice(tt,1),se=null}}}function X(He){if(!z(He))throw new Error(u(7));if(typeof He.type>"u")throw new Error(u(8));if(A)throw new Error(u(9));try{A=!0,R=L(R,He)}finally{A=!1}for(var je=se=g,Ee=0;Ee<je.length;Ee++){var tt=je[Ee];tt()}return He}function we(He){if(typeof He!="function")throw new Error(u(10));L=He,X({type:Z.REPLACE})}function ze(){var He,je=re;return He={subscribe:function(tt){if(typeof tt!="object"||tt===null)throw new Error(u(11));function Pe(){tt.next&&tt.next(le())}Pe();var Q=je(Pe);return{unsubscribe:Q}}},He[k]=function(){return this},He}return X({type:Z.INIT}),l={dispatch:X,subscribe:re,getState:le,replaceReducer:we},l[k]=ze,l}var V=n.j!=15?he:null;function Y(x){typeof console<"u"&&typeof console.error=="function"&&console.error(x);try{throw new Error(x)}catch(I){}}function H(x,I,D,l){var L=Object.keys(I),R=D&&D.type===Z.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(L.length===0)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!z(x))return"The "+R+' has unexpected type of "'+J(x)+'". Expected argument to be an object with the following '+('keys: "'+L.join('", "')+'"');var se=Object.keys(x).filter(function(g){return!I.hasOwnProperty(g)&&!l[g]});if(se.forEach(function(g){l[g]=!0}),!(D&&D.type===Z.REPLACE)&&se.length>0)return"Unexpected "+(se.length>1?"keys":"key")+" "+('"'+se.join('", "')+'" found in '+R+". ")+"Expected to find one of the known reducer keys instead: "+('"'+L.join('", "')+'". Unexpected keys will be ignored.')}function w(x){Object.keys(x).forEach(function(I){var D=x[I],l=D(void 0,{type:Z.INIT});if(typeof l>"u")throw new Error(u(12));if(typeof D(void 0,{type:Z.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(u(13))})}function C(x){for(var I=Object.keys(x),D={},l=0;l<I.length;l++){var L=I[l];typeof x[L]=="function"&&(D[L]=x[L])}var R=Object.keys(D),se,g;try{w(D)}catch(A){g=A}return function(ee,le){if(ee===void 0&&(ee={}),g)throw g;if(0)var re;for(var X=!1,we={},ze=0;ze<R.length;ze++){var He=R[ze],je=D[He],Ee=ee[He],tt=je(Ee,le);if(typeof tt>"u"){var Pe=le&&le.type;throw new Error(u(14))}we[He]=tt,X=X||tt!==Ee}return X=X||R.length!==Object.keys(ee).length,X?we:ee}}function f(x,I){return function(){return I(x.apply(this,arguments))}}function y(x,I){if(typeof x=="function")return f(x,I);if(typeof x!="object"||x===null)throw new Error(u(16));var D={};for(var l in x){var L=x[l];typeof L=="function"&&(D[l]=f(L,I))}return D}function v(){for(var x=arguments.length,I=new Array(x),D=0;D<x;D++)I[D]=arguments[D];return I.length===0?function(l){return l}:I.length===1?I[0]:I.reduce(function(l,L){return function(){return l(L.apply(void 0,arguments))}})}function S(){for(var x=arguments.length,I=new Array(x),D=0;D<x;D++)I[D]=arguments[D];return function(l){return function(){var L=l.apply(void 0,arguments),R=function(){throw new Error(u(15))},se={getState:L.getState,dispatch:function(){return R.apply(void 0,arguments)}},g=I.map(function(A){return A(se)});return R=v.apply(void 0,g)(L.dispatch),M(M({},L),{},{dispatch:R})}}}},7043:(Ne,de,n)=>{"use strict";n.d(de,{A:()=>p});function s(M,u){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(k,te){return k.__proto__=te,k},s(M,u)}function p(M,u){M.prototype=Object.create(u.prototype),M.prototype.constructor=M,s(M,u)}}}]);

//# sourceMappingURL=4.n9fxu-vendor-d0dffe41217822c8c754.js.map