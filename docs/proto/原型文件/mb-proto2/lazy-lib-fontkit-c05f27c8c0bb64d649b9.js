try{let be=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},H=new be.Error().stack;H&&(be._sentryDebugIds=be._sentryDebugIds||{},be._sentryDebugIds[H]="9cac8595-95b4-46e8-acbd-57fdac45ca45",be._sentryDebugIdIdentifier="sentry-dbid-9cac8595-95b4-46e8-acbd-57fdac45ca45")}catch(be){}{let be=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};be.SENTRY_RELEASE={id:"21.3.4"}}(self.webpackChunk_mb2024_mb_proto=self.webpackChunk_mb2024_mb_proto||[]).push([[437],{95090:(be,H,m)=>{"use strict";m.r(H),m.d(H,{fontkitCreate:()=>Fn});const G={utf16le:"utf-16le",ucs2:"utf-16le",utf16be:"utf-16be"};class w{constructor(e){this.buffer=e,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.pos=0,this.length=this.buffer.length}readString(e,t){t===void 0&&(t="ascii"),t=G[t]||t;let r=this.readBuffer(e);try{return new TextDecoder(t).decode(r)}catch(n){return r}}readBuffer(e){return this.buffer.slice(this.pos,this.pos+=e)}readUInt24BE(){return(this.readUInt16BE()<<8)+this.readUInt8()}readUInt24LE(){return this.readUInt16LE()+(this.readUInt8()<<16)}readInt24BE(){return(this.readInt16BE()<<8)+this.readUInt8()}readInt24LE(){return this.readUInt16LE()+(this.readInt8()<<16)}}w.TYPES={UInt8:1,UInt16:2,UInt24:3,UInt32:4,Int8:1,Int16:2,Int24:3,Int32:4,Float:4,Double:8};for(let s of Object.getOwnPropertyNames(DataView.prototype))if(s.slice(0,3)==="get"){let e=s.slice(3).replace("Ui","UI");e==="Float32"?e="Float":e==="Float64"&&(e="Double");let t=w.TYPES[e];w.prototype["read"+e+(t===1?"":"BE")]=function(){const r=this.view[s](this.pos,!1);return this.pos+=t,r},t!==1&&(w.prototype["read"+e+"LE"]=function(){const r=this.view[s](this.pos,!0);return this.pos+=t,r})}const N=new TextEncoder,D=new Uint8Array(new Uint16Array([4660]).buffer)[0]==18;class L{constructor(e){this.buffer=e,this.view=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),this.pos=0}writeBuffer(e){this.buffer.set(e,this.pos),this.pos+=e.length}writeString(e,t){t===void 0&&(t="ascii");let r;switch(t){case"utf16le":case"utf16-le":case"ucs2":r=M(e,D);break;case"utf16be":case"utf16-be":r=M(e,!D);break;case"utf8":r=N.encode(e);break;case"ascii":r=ce(e);break;default:throw new Error("Unsupported encoding: "+t)}this.writeBuffer(r)}writeUInt24BE(e){this.buffer[this.pos++]=e>>>16&255,this.buffer[this.pos++]=e>>>8&255,this.buffer[this.pos++]=e&255}writeUInt24LE(e){this.buffer[this.pos++]=e&255,this.buffer[this.pos++]=e>>>8&255,this.buffer[this.pos++]=e>>>16&255}writeInt24BE(e){e>=0?this.writeUInt24BE(e):this.writeUInt24BE(e+16777215+1)}writeInt24LE(e){e>=0?this.writeUInt24LE(e):this.writeUInt24LE(e+16777215+1)}fill(e,t){if(t<this.buffer.length)this.buffer.fill(e,this.pos,this.pos+t),this.pos+=t;else{const r=new Uint8Array(t);r.fill(e),this.writeBuffer(r)}}}function M(s,e){let t=new Uint16Array(s.length);for(let r=0;r<s.length;r++){let n=s.charCodeAt(r);e&&(n=n>>8|(n&255)<<8),t[r]=n}return new Uint8Array(t.buffer)}function ce(s){let e=new Uint8Array(s.length);for(let t=0;t<s.length;t++)e[t]=s.charCodeAt(t);return e}for(let s of Object.getOwnPropertyNames(DataView.prototype))if(s.slice(0,3)==="set"){let e=s.slice(3).replace("Ui","UI");e==="Float32"?e="Float":e==="Float64"&&(e="Double");let t=w.TYPES[e];L.prototype["write"+e+(t===1?"":"BE")]=function(r){this.view[s](this.pos,r,!1),this.pos+=t},t!==1&&(L.prototype["write"+e+"LE"]=function(r){this.view[s](this.pos,r,!0),this.pos+=t})}class oe{fromBuffer(e){let t=new w(e);return this.decode(t)}toBuffer(e){let t=this.size(e),r=new Uint8Array(t),n=new L(r);return this.encode(n,e),r}}class $ extends oe{constructor(e,t){t===void 0&&(t="BE"),super(),this.type=e,this.endian=t,this.fn=this.type,this.type[this.type.length-1]!=="8"&&(this.fn+=this.endian)}size(){return w.TYPES[this.type]}decode(e){return e["read"+this.fn]()}encode(e,t){return e["write"+this.fn](t)}}const y=new $("UInt8"),o=new $("UInt16","BE"),R=new $("UInt16","LE"),ne=new $("UInt24","BE"),Se=new $("UInt24","LE"),p=new $("UInt32","BE"),W=new $("UInt32","LE"),Z=new $("Int8"),x=new $("Int16","BE"),h=new $("Int16","LE"),X=new $("Int24","BE"),P=null,J=new $("Int24","LE"),ae=new $("Int32","BE"),ve=new $("Int32","LE"),Be=new $("Float","BE"),Oe=null,t0=new $("Float","LE"),Ge=new $("Double","BE"),Je=null,H0=new $("Double","LE");class u0 extends ${constructor(e,t,r){r===void 0&&(r=e>>1),super("Int"+e,t),this._point=1<<r}decode(e){return super.decode(e)/this._point}encode(e,t){return super.encode(e,t*this._point|0)}}const s0=new u0(16,"BE"),tt=new u0(16,"LE"),Le=new u0(32,"BE"),kn=new u0(32,"LE");function r0(s,e,t){let r;if(typeof s=="number"?r=s:typeof s=="function"?r=s.call(t,t):t&&typeof s=="string"?r=t[s]:e&&s instanceof $&&(r=s.decode(e)),isNaN(r))throw new Error("Not a fixed size");return r}class Tt{constructor(e){e===void 0&&(e={}),this.enumerable=!0,this.configurable=!0;for(let t in e){const r=e[t];this[t]=r}}}class g extends oe{constructor(e,t,r){r===void 0&&(r="count"),super(),this.type=e,this.length=t,this.lengthType=r}decode(e,t){let r;const{pos:n}=e,a=[];let i=t;if(this.length!=null&&(r=r0(this.length,e,t)),this.length instanceof $&&(Object.defineProperties(a,{parent:{value:t},_startOffset:{value:n},_currentOffset:{value:0,writable:!0},_length:{value:r}}),i=a),r==null||this.lengthType==="bytes"){const l=r!=null?e.pos+r:t!=null&&t._length?t._startOffset+t._length:e.length;for(;e.pos<l;)a.push(this.type.decode(e,i))}else for(let l=0,u=r;l<u;l++)a.push(this.type.decode(e,i));return a}size(e,t,r){if(r===void 0&&(r=!0),!e)return this.type.size(null,t)*r0(this.length,null,t);let n=0;this.length instanceof $&&(n+=this.length.size(),t={parent:t,pointerSize:0});for(let a of e)n+=this.type.size(a,t);return t&&r&&this.length instanceof $&&(n+=t.pointerSize),n}encode(e,t,r){let n=r;this.length instanceof $&&(n={pointers:[],startOffset:e.pos,parent:r},n.pointerOffset=e.pos+this.size(t,n,!1),this.length.encode(e,t.length));for(let a of t)this.type.encode(e,a,n);if(this.length instanceof $){let a=0;for(;a<n.pointers.length;){const i=n.pointers[a++];i.type.encode(e,i.val,i.parent)}}}}class A extends g{decode(e,t){const{pos:r}=e,n=r0(this.length,e,t);this.length instanceof $&&(t={parent:t,_startOffset:r,_currentOffset:0,_length:n});const a=new I(this.type,n,e,t);return e.pos+=n*this.type.size(null,t),a}size(e,t){return e instanceof I&&(e=e.toArray()),super.size(e,t)}encode(e,t,r){return t instanceof I&&(t=t.toArray()),super.encode(e,t,r)}}class I{constructor(e,t,r,n){this.type=e,this.length=t,this.stream=r,this.ctx=n,this.base=this.stream.pos,this.items=[]}get(e){if(!(e<0||e>=this.length)){if(this.items[e]==null){const{pos:t}=this.stream;this.stream.pos=this.base+this.type.size(null,this.ctx)*e,this.items[e]=this.type.decode(this.stream,this.ctx),this.stream.pos=t}return this.items[e]}}toArray(){const e=[];for(let t=0,r=this.length;t<r;t++)e.push(this.get(t));return e}}class O extends oe{constructor(e,t){t===void 0&&(t=[]),super(),this.type=e,this.flags=t}decode(e){const t=this.type.decode(e),r={};for(let n=0;n<this.flags.length;n++){const a=this.flags[n];a!=null&&(r[a]=!!(t&1<<n))}return r}size(){return this.type.size()}encode(e,t){let r=0;for(let n=0;n<this.flags.length;n++){const a=this.flags[n];a!=null&&t[a]&&(r|=1<<n)}return this.type.encode(e,r)}}class E extends oe{constructor(e){super(),this.type=e}decode(e,t){return!!this.type.decode(e,t)}size(e,t){return this.type.size(e,t)}encode(e,t,r){return this.type.encode(e,+t,r)}}class z extends oe{constructor(e){super(),this.length=e}decode(e,t){const r=r0(this.length,e,t);return e.readBuffer(r)}size(e,t){if(!e)return r0(this.length,null,t);let r=e.length;return this.length instanceof $&&(r+=this.length.size()),r}encode(e,t,r){return this.length instanceof $&&this.length.encode(e,t.length),e.writeBuffer(t)}}class fe extends oe{constructor(e,t){t===void 0&&(t=[]),super(),this.type=e,this.options=t}decode(e){const t=this.type.decode(e);return this.options[t]||t}size(){return this.type.size()}encode(e,t){const r=this.options.indexOf(t);if(r===-1)throw new Error("Unknown option in enum: "+t);return this.type.encode(e,r)}}class K extends oe{constructor(e,t){t===void 0&&(t=!0),super(),this.type=e,this.condition=t}decode(e,t){let{condition:r}=this;if(typeof r=="function"&&(r=r.call(t,t)),r)return this.type.decode(e,t)}size(e,t){let{condition:r}=this;return typeof r=="function"&&(r=r.call(t,t)),r?this.type.size(e,t):0}encode(e,t,r){let{condition:n}=this;if(typeof n=="function"&&(n=n.call(r,r)),n)return this.type.encode(e,t,r)}}class V extends oe{constructor(e,t){t===void 0&&(t=1),super(),this.type=e,this.count=t}decode(e,t){e.pos+=this.size(null,t)}size(e,t){const r=r0(this.count,null,t);return this.type.size()*r}encode(e,t,r){return e.fill(0,this.size(t,r))}}class j extends oe{constructor(e,t){t===void 0&&(t="ascii"),super(),this.length=e,this.encoding=t}decode(e,t){let r,n,{encoding:a}=this;typeof a=="function"&&(a=a.call(t,t)||"ascii");let i=se(a);if(this.length!=null)r=r0(this.length,e,t);else{let u;for({buffer:u,length:r,pos:n}=e;n<r-i+1&&(u[n]!==0||i===2&&u[n+1]!==0);)n+=i;r=n-e.pos}const l=e.readString(r,a);return this.length==null&&e.pos<e.length&&(e.pos+=i),l}size(e,t){if(e==null)return r0(this.length,null,t);let{encoding:r}=this;typeof r=="function"&&(r=r.call(t!=null?t.val:void 0,t!=null?t.val:void 0)||"ascii"),r==="utf16be"&&(r="utf16le");let n=q(e,r);return this.length instanceof $&&(n+=this.length.size()),this.length==null&&(n+=se(r)),n}encode(e,t,r){let{encoding:n}=this;if(typeof n=="function"&&(n=n.call(r!=null?r.val:void 0,r!=null?r.val:void 0)||"ascii"),this.length instanceof $&&this.length.encode(e,q(t,n)),e.writeString(t,n),this.length==null)return se(n)==2?e.writeUInt16LE(0):e.writeUInt8(0)}}function se(s){switch(s){case"ascii":case"utf8":return 1;case"utf16le":case"utf16-le":case"utf-16be":case"utf-16le":case"utf16be":case"utf16-be":case"ucs2":return 2;default:return 1}}function q(s,e){switch(e){case"ascii":return s.length;case"utf8":let t=0;for(let r=0;r<s.length;r++){let n=s.charCodeAt(r);if(n>=55296&&n<=56319&&r<s.length-1){let a=s.charCodeAt(++r);(a&64512)===56320?n=((n&1023)<<10)+(a&1023)+65536:r--}n&4294967168?n&4294965248?n&4294901760?n&4292870144||(t+=4):t+=3:t+=2:t++}return t;case"utf16le":case"utf16-le":case"utf16be":case"utf16-be":case"ucs2":return s.length*2;default:throw new Error("Unknown encoding "+e)}}class v extends oe{constructor(e){e===void 0&&(e={}),super(),this.fields=e}decode(e,t,r){r===void 0&&(r=0);const n=this._setup(e,t,r);return this._parseFields(e,n,this.fields),this.process!=null&&this.process.call(n,e),n}_setup(e,t,r){const n={};return Object.defineProperties(n,{parent:{value:t},_startOffset:{value:e.pos},_currentOffset:{value:0,writable:!0},_length:{value:r}}),n}_parseFields(e,t,r){for(let a in r){var n;const i=r[a];typeof i=="function"?n=i.call(t,t):n=i.decode(e,t),n!==void 0&&(n instanceof Tt?Object.defineProperty(t,a,n):t[a]=n),t._currentOffset=e.pos-t._startOffset}}size(e,t,r){r===void 0&&(r=!0),e==null&&(e={});const n={parent:t,val:e,pointerSize:0};this.preEncode!=null&&this.preEncode.call(e);let a=0;for(let i in this.fields){const l=this.fields[i];l.size!=null&&(a+=l.size(e[i],n))}return r&&(a+=n.pointerSize),a}encode(e,t,r){let n;this.preEncode!=null&&this.preEncode.call(t,e);const a={pointers:[],startOffset:e.pos,parent:r,val:t,pointerSize:0};a.pointerOffset=e.pos+this.size(t,a,!1);for(let l in this.fields)n=this.fields[l],n.encode!=null&&n.encode(e,t[l],a);let i=0;for(;i<a.pointers.length;){const l=a.pointers[i++];l.type.encode(e,l.val,l.parent)}}}const Xe=(s,e)=>e.reduce((t,r)=>t&&t[r],s);class re extends v{constructor(e,t){t===void 0&&(t={}),super(),this.type=e,this.versions=t,typeof e=="string"&&(this.versionPath=e.split("."))}decode(e,t,r){r===void 0&&(r=0);const n=this._setup(e,t,r);typeof this.type=="string"?n.version=Xe(t,this.versionPath):n.version=this.type.decode(e),this.versions.header&&this._parseFields(e,n,this.versions.header);const a=this.versions[n.version];if(a==null)throw new Error("Unknown version "+n.version);return a instanceof re?a.decode(e,t):(this._parseFields(e,n,a),this.process!=null&&this.process.call(n,e),n)}size(e,t,r){r===void 0&&(r=!0);let n,a;if(!e)throw new Error("Not a fixed size");this.preEncode!=null&&this.preEncode.call(e);const i={parent:t,val:e,pointerSize:0};let l=0;if(typeof this.type!="string"&&(l+=this.type.size(e.version,i)),this.versions.header)for(n in this.versions.header)a=this.versions.header[n],a.size!=null&&(l+=a.size(e[n],i));const u=this.versions[e.version];if(u==null)throw new Error("Unknown version "+e.version);for(n in u)a=u[n],a.size!=null&&(l+=a.size(e[n],i));return r&&(l+=i.pointerSize),l}encode(e,t,r){let n,a;this.preEncode!=null&&this.preEncode.call(t,e);const i={pointers:[],startOffset:e.pos,parent:r,val:t,pointerSize:0};if(i.pointerOffset=e.pos+this.size(t,i,!1),typeof this.type!="string"&&this.type.encode(e,t.version),this.versions.header)for(n in this.versions.header)a=this.versions.header[n],a.encode!=null&&a.encode(e,t[n],i);const l=this.versions[t.version];for(n in l)a=l[n],a.encode!=null&&a.encode(e,t[n],i);let u=0;for(;u<i.pointers.length;){const c=i.pointers[u++];c.type.encode(e,c.val,c.parent)}}}class b extends oe{constructor(e,t,r){if(r===void 0&&(r={}),super(),this.offsetType=e,this.type=t,this.options=r,this.type==="void"&&(this.type=null),this.options.type==null&&(this.options.type="local"),this.options.allowNull==null&&(this.options.allowNull=!0),this.options.nullValue==null&&(this.options.nullValue=0),this.options.lazy==null&&(this.options.lazy=!1),this.options.relativeTo){if(typeof this.options.relativeTo!="function")throw new Error("relativeTo option must be a function");this.relativeToGetter=r.relativeTo}}decode(e,t){const r=this.offsetType.decode(e,t);if(r===this.options.nullValue&&this.options.allowNull)return null;let n;switch(this.options.type){case"local":n=t._startOffset;break;case"immediate":n=e.pos-this.offsetType.size();break;case"parent":n=t.parent._startOffset;break;default:for(var a=t;a.parent;)a=a.parent;n=a._startOffset||0}this.options.relativeTo&&(n+=this.relativeToGetter(t));const i=r+n;if(this.type!=null){let l=null;const u=()=>{if(l!=null)return l;const{pos:c}=e;return e.pos=i,l=this.type.decode(e,t),e.pos=c,l};return this.options.lazy?new Tt({get:u}):u()}else return i}size(e,t){const r=t;switch(this.options.type){case"local":case"immediate":break;case"parent":t=t.parent;break;default:for(;t.parent;)t=t.parent}let{type:n}=this;if(n==null){if(!(e instanceof qe))throw new Error("Must be a VoidPointer");({type:n}=e),e=e.value}if(e&&t){let a=n.size(e,r);t.pointerSize+=a}return this.offsetType.size()}encode(e,t,r){let n;const a=r;if(t==null){this.offsetType.encode(e,this.options.nullValue);return}switch(this.options.type){case"local":n=r.startOffset;break;case"immediate":n=e.pos+this.offsetType.size(t,a);break;case"parent":r=r.parent,n=r.startOffset;break;default:for(n=0;r.parent;)r=r.parent}this.options.relativeTo&&(n+=this.relativeToGetter(a.val)),this.offsetType.encode(e,r.pointerOffset-n);let{type:i}=this;if(i==null){if(!(t instanceof qe))throw new Error("Must be a VoidPointer");({type:i}=t),t=t.value}return r.pointers.push({type:i,val:t,parent:a}),r.pointerOffset+=i.size(t,a)}}class qe{constructor(e,t){this.type=e,this.value=t}}function we(s,e,t){return e in s?Object.defineProperty(s,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):s[e]=t,s}var We=function(s,e){return We=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},We(s,e)};function _e(s,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");We(s,e);function t(){this.constructor=s}s.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var de=function(){return de=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},de.apply(this,arguments)};function Xr(s,e){var t={};for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,r=Object.getOwnPropertySymbols(s);n<r.length;n++)e.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(s,r[n])&&(t[r[n]]=s[r[n]]);return t}function Me(s,e,t,r){var n=arguments.length,a=n<3?e:r===null?r=Object.getOwnPropertyDescriptor(e,t):r,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")a=Reflect.decorate(s,e,t,r);else for(var l=s.length-1;l>=0;l--)(i=s[l])&&(a=(n<3?i(a):n>3?i(e,t,a):i(e,t))||a);return n>3&&a&&Object.defineProperty(e,t,a),a}function $e(s,e){return function(t,r){e(t,r,s)}}function rr(s,e,t,r,n,a){function i(ee){if(ee!==void 0&&typeof ee!="function")throw new TypeError("Function expected");return ee}for(var l=r.kind,u=l==="getter"?"get":l==="setter"?"set":"value",c=!e&&s?r.static?s:s.prototype:null,f=e||(c?Object.getOwnPropertyDescriptor(c,r.name):{}),d,C=!1,k=t.length-1;k>=0;k--){var B={};for(var _ in r)B[_]=_==="access"?{}:r[_];for(var _ in r.access)B.access[_]=r.access[_];B.addInitializer=function(ee){if(C)throw new TypeError("Cannot add initializers after decoration has completed");a.push(i(ee||null))};var ye=(0,t[k])(l==="accessor"?{get:f.get,set:f.set}:f[u],B);if(l==="accessor"){if(ye===void 0)continue;if(ye===null||typeof ye!="object")throw new TypeError("Object expected");(d=i(ye.get))&&(f.get=d),(d=i(ye.set))&&(f.set=d),(d=i(ye.init))&&n.unshift(d)}else(d=i(ye))&&(l==="field"?n.unshift(d):f[u]=d)}c&&Object.defineProperty(c,r.name,f),C=!0}function i0(s,e,t){for(var r=arguments.length>2,n=0;n<e.length;n++)t=r?e[n].call(s,t):e[n].call(s);return r?t:void 0}function b0(s){return typeof s=="symbol"?s:"".concat(s)}function n0(s,e,t){return typeof e=="symbol"&&(e=e.description?"[".concat(e.description,"]"):""),Object.defineProperty(s,"name",{configurable:!0,value:t?"".concat(t," ",e):e})}function Ft(s,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(s,e)}function rt(s,e,t,r){function n(a){return a instanceof t?a:new t(function(i){i(a)})}return new(t||(t=Promise))(function(a,i){function l(f){try{c(r.next(f))}catch(d){i(d)}}function u(f){try{c(r.throw(f))}catch(d){i(d)}}function c(f){f.done?a(f.value):n(f.value).then(l,u)}c((r=r.apply(s,e||[])).next())})}function T0(s,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,n,a,i=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return i.next=l(0),i.throw=l(1),i.return=l(2),typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function l(c){return function(f){return u([c,f])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(t=0)),t;)try{if(r=1,n&&(a=c[0]&2?n.return:c[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,c[1])).done)return a;switch(n=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return t.label++,{value:c[1],done:!1};case 5:t.label++,n=c[1],c=[0];continue;case 7:c=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){t.label=c[1];break}if(c[0]===6&&t.label<a[1]){t.label=a[1],a=c;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(c);break}a[2]&&t.ops.pop(),t.trys.pop();continue}c=e.call(s,t)}catch(f){c=[6,f],n=0}finally{r=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}var I0=Object.create?function(s,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(s,r,n)}:function(s,e,t,r){r===void 0&&(r=t),s[r]=e[t]};function nr(s,e){for(var t in s)t!=="default"&&!Object.prototype.hasOwnProperty.call(e,t)&&I0(e,s,t)}function nt(s){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&s[e],r=0;if(t)return t.call(s);if(s&&typeof s.length=="number")return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Dt(s,e){var t=typeof Symbol=="function"&&s[Symbol.iterator];if(!t)return s;var r=t.call(s),n,a=[],i;try{for(;(e===void 0||e-- >0)&&!(n=r.next()).done;)a.push(n.value)}catch(l){i={error:l}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(i)throw i.error}}return a}function at(){for(var s=[],e=0;e<arguments.length;e++)s=s.concat(Dt(arguments[e]));return s}function ar(){for(var s=0,e=0,t=arguments.length;e<t;e++)s+=arguments[e].length;for(var r=Array(s),n=0,e=0;e<t;e++)for(var a=arguments[e],i=0,l=a.length;i<l;i++,n++)r[n]=a[i];return r}function sr(s,e,t){if(t||arguments.length===2)for(var r=0,n=e.length,a;r<n;r++)(a||!(r in e))&&(a||(a=Array.prototype.slice.call(e,0,r)),a[r]=e[r]);return s.concat(a||Array.prototype.slice.call(e))}function F0(s){return this instanceof F0?(this.v=s,this):new F0(s)}function ir(s,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(s,e||[]),n,a=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),l("next"),l("throw"),l("return",i),n[Symbol.asyncIterator]=function(){return this},n;function i(k){return function(B){return Promise.resolve(B).then(k,d)}}function l(k,B){r[k]&&(n[k]=function(_){return new Promise(function(ye,ee){a.push([k,_,ye,ee])>1||u(k,_)})},B&&(n[k]=B(n[k])))}function u(k,B){try{c(r[k](B))}catch(_){C(a[0][3],_)}}function c(k){k.value instanceof F0?Promise.resolve(k.value.v).then(f,d):C(a[0][2],k)}function f(k){u("next",k)}function d(k){u("throw",k)}function C(k,B){k(B),a.shift(),a.length&&u(a[0][0],a[0][1])}}function Bt(s){var e,t;return e={},r("next"),r("throw",function(n){throw n}),r("return"),e[Symbol.iterator]=function(){return this},e;function r(n,a){e[n]=s[n]?function(i){return(t=!t)?{value:F0(s[n](i)),done:!1}:a?a(i):i}:a}}function or(s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=s[Symbol.asyncIterator],t;return e?e.call(s):(s=typeof nt=="function"?nt(s):s[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(a){t[a]=s[a]&&function(i){return new Promise(function(l,u){i=s[a](i),n(l,u,i.done,i.value)})}}function n(a,i,l,u){Promise.resolve(u).then(function(c){a({value:c,done:l})},i)}}function lr(s,e){return Object.defineProperty?Object.defineProperty(s,"raw",{value:e}):s.raw=e,s}var Lt=Object.create?function(s,e){Object.defineProperty(s,"default",{enumerable:!0,value:e})}:function(s,e){s.default=e},st=function(s){return st=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},st(s)};function Mt(s){if(s&&s.__esModule)return s;var e={};if(s!=null)for(var t=st(s),r=0;r<t.length;r++)t[r]!=="default"&&I0(e,s,t[r]);return Lt(e,s),e}function Rt(s){return s&&s.__esModule?s:{default:s}}function D0(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)}function Nt(s,e,t,r,n){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?n.call(s,t):n?n.value=t:e.set(s,t),t}function it(s,e){if(e===null||typeof e!="object"&&typeof e!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof s=="function"?e===s:s.has(e)}function ur(s,e,t){if(e!=null){if(typeof e!="object"&&typeof e!="function")throw new TypeError("Object expected.");var r,n;if(t){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=e[Symbol.asyncDispose]}if(r===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=e[Symbol.dispose],t&&(n=r)}if(typeof r!="function")throw new TypeError("Object not disposable.");n&&(r=function(){try{n.call(this)}catch(a){return Promise.reject(a)}}),s.stack.push({value:e,dispose:r,async:t})}else t&&s.stack.push({async:!0});return e}var cr=typeof SuppressedError=="function"?SuppressedError:function(s,e,t){var r=new Error(t);return r.name="SuppressedError",r.error=s,r.suppressed=e,r};function fr(s){function e(a){s.error=s.hasError?new cr(a,s.error,"An error was suppressed during disposal."):a,s.hasError=!0}var t,r=0;function n(){for(;t=s.stack.pop();)try{if(!t.async&&r===1)return r=0,s.stack.push(t),Promise.resolve().then(n);if(t.dispose){var a=t.dispose.call(t.value);if(t.async)return r|=2,Promise.resolve(a).then(n,function(i){return e(i),n()})}else r|=1}catch(i){e(i)}if(r===1)return s.hasError?Promise.reject(s.error):Promise.resolve();if(s.hasError)throw s.error}return n()}function ot(s,e){return typeof s=="string"&&/^\.\.?\//.test(s)?s.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(t,r,n,a,i){return r?e?".jsx":".js":n&&(!a||!i)?t:n+a+"."+i.toLowerCase()+"js"}):s}const lt={__extends:_e,__assign:de,__rest:Xr,__decorate:Me,__param:$e,__esDecorate:rr,__runInitializers:i0,__propKey:b0,__setFunctionName:n0,__metadata:Ft,__awaiter:rt,__generator:T0,__createBinding:I0,__exportStar:nr,__values:nt,__read:Dt,__spread:at,__spreadArrays:ar,__spreadArray:sr,__await:F0,__asyncGenerator:ir,__asyncDelegator:Bt,__asyncValues:or,__makeTemplateObject:lr,__importStar:Mt,__importDefault:Rt,__classPrivateFieldGet:D0,__classPrivateFieldSet:Nt,__classPrivateFieldIn:it,__addDisposableResource:ur,__disposeResources:fr,__rewriteRelativeImportExtension:ot};var ut=m(40937),Ut=m(53742),B0=m(43803);function Re(s){return s&&s.__esModule?s.default:s}var Ne={};Ne=JSON.parse('{"categories":["Cc","Zs","Po","Sc","Ps","Pe","Sm","Pd","Nd","Lu","Sk","Pc","Ll","So","Lo","Pi","Cf","No","Pf","Lt","Lm","Mn","Me","Mc","Nl","Zl","Zp","Cs","Co"],"combiningClasses":["Not_Reordered","Above","Above_Right","Below","Attached_Above_Right","Attached_Below","Overlay","Iota_Subscript","Double_Below","Double_Above","Below_Right","Above_Left","CCC10","CCC11","CCC12","CCC13","CCC14","CCC15","CCC16","CCC17","CCC18","CCC19","CCC20","CCC21","CCC22","CCC23","CCC24","CCC25","CCC30","CCC31","CCC32","CCC27","CCC28","CCC29","CCC33","CCC34","CCC35","CCC36","Nukta","Virama","CCC84","CCC91","CCC103","CCC107","CCC118","CCC122","CCC129","CCC130","CCC132","Attached_Above","Below_Left","Left","Kana_Voicing","CCC26","Right"],"scripts":["Common","Latin","Bopomofo","Inherited","Greek","Coptic","Cyrillic","Armenian","Hebrew","Arabic","Syriac","Thaana","Nko","Samaritan","Mandaic","Devanagari","Bengali","Gurmukhi","Gujarati","Oriya","Tamil","Telugu","Kannada","Malayalam","Sinhala","Thai","Lao","Tibetan","Myanmar","Georgian","Hangul","Ethiopic","Cherokee","Canadian_Aboriginal","Ogham","Runic","Tagalog","Hanunoo","Buhid","Tagbanwa","Khmer","Mongolian","Limbu","Tai_Le","New_Tai_Lue","Buginese","Tai_Tham","Balinese","Sundanese","Batak","Lepcha","Ol_Chiki","Braille","Glagolitic","Tifinagh","Han","Hiragana","Katakana","Yi","Lisu","Vai","Bamum","Syloti_Nagri","Phags_Pa","Saurashtra","Kayah_Li","Rejang","Javanese","Cham","Tai_Viet","Meetei_Mayek","null","Linear_B","Lycian","Carian","Old_Italic","Gothic","Old_Permic","Ugaritic","Old_Persian","Deseret","Shavian","Osmanya","Osage","Elbasan","Caucasian_Albanian","Linear_A","Cypriot","Imperial_Aramaic","Palmyrene","Nabataean","Hatran","Phoenician","Lydian","Meroitic_Hieroglyphs","Meroitic_Cursive","Kharoshthi","Old_South_Arabian","Old_North_Arabian","Manichaean","Avestan","Inscriptional_Parthian","Inscriptional_Pahlavi","Psalter_Pahlavi","Old_Turkic","Old_Hungarian","Hanifi_Rohingya","Old_Sogdian","Sogdian","Elymaic","Brahmi","Kaithi","Sora_Sompeng","Chakma","Mahajani","Sharada","Khojki","Multani","Khudawadi","Grantha","Newa","Tirhuta","Siddham","Modi","Takri","Ahom","Dogra","Warang_Citi","Nandinagari","Zanabazar_Square","Soyombo","Pau_Cin_Hau","Bhaiksuki","Marchen","Masaram_Gondi","Gunjala_Gondi","Makasar","Cuneiform","Egyptian_Hieroglyphs","Anatolian_Hieroglyphs","Mro","Bassa_Vah","Pahawh_Hmong","Medefaidrin","Miao","Tangut","Nushu","Duployan","SignWriting","Nyiakeng_Puachue_Hmong","Wancho","Mende_Kikakui","Adlam"],"eaw":["N","Na","A","W","H","F"]}');const Ze=new B0(Ut.toByteArray("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")),Y0=Math.log2||(s=>Math.log(s)/Math.LN2),c0=s=>Y0(s)+1|0,Z0=c0(Re(Ne).categories.length-1),ct=c0(Re(Ne).combiningClasses.length-1),ft=c0(Re(Ne).scripts.length-1),g0=c0(Re(Ne).eaw.length-1),L0=10,dr=ct+ft+g0+L0,Yr=ft+g0+L0,Zr=g0+L0,hr=L0,Gt=(1<<Z0)-1,dt=(1<<ct)-1,is=(1<<ft)-1,os=(1<<g0)-1,ls=(1<<L0)-1;function o0(s){const e=Ze.get(s);return Re(Ne).categories[e>>dr&Gt]}function On(s){const e=Ze.get(s);return Re(Ne).combiningClasses[e>>Yr&dt]}function Kr(s){const e=Ze.get(s);return Re(Ne).scripts[e>>Zr&is]}function us(s){const e=Ze.get(s);return Re(Ne).eaw[e>>hr&os]}function cs(s){let e=Ze.get(s),t=e&ls;if(t===0)return null;if(t<=50)return t-1;if(t<480){const r=(t>>4)-12,n=(t&15)+1;return r/n}else if(t<768){e=(t>>5)-14;let r=(t&31)+2;for(;r>0;)e*=10,r--;return e}else{e=(t>>2)-191;let r=(t&3)+1;for(;r>0;)e*=60,r--;return e}}function fs(s){const e=o0(s);return e==="Lu"||e==="Ll"||e==="Lt"||e==="Lm"||e==="Lo"||e==="Nl"}function Jr(s){return o0(s)==="Nd"}function ds(s){const e=o0(s);return e==="Pc"||e==="Pd"||e==="Pe"||e==="Pf"||e==="Pi"||e==="Po"||e==="Ps"}function hs(s){return o0(s)==="Ll"}function ps(s){return o0(s)==="Lu"}function bs(s){return o0(s)==="Lt"}function gs(s){const e=o0(s);return e==="Zs"||e==="Zl"||e==="Zp"}function vs(s){const e=o0(s);return e==="Nd"||e==="No"||e==="Nl"||e==="Lu"||e==="Ll"||e==="Lt"||e==="Lm"||e==="Lo"||e==="Me"||e==="Mc"}function _r(s){const e=o0(s);return e==="Mn"||e==="Me"||e==="Mc"}var lf={getCategory:o0,getCombiningClass:On,getScript:Kr,getEastAsianWidth:us,getNumericValue:cs,isAlphabetic:fs,isDigit:Jr,isPunctuation:ds,isLowerCase:hs,isUpperCase:ps,isTitleCase:bs,isWhiteSpace:gs,isBaseForm:vs,isMark:_r},Pn=m(65614),Qr=m(93067),ms=m(93477),xs=m(98834);function Vt(s,e,t,r){Object.defineProperty(s,e,{get:t,set:r,enumerable:!0,configurable:!0})}function pr(s){return s&&s.__esModule?s.default:s}var zt={};Vt(zt,"logErrors",()=>Tn),Vt(zt,"registerFormat",()=>ht),Vt(zt,"create",()=>Fn),Vt(zt,"defaultLanguage",()=>br),Vt(zt,"setDefaultLanguage",()=>ws);let Tn=!1,en=[];function ht(s){en.push(s)}function Fn(s,e){for(let t=0;t<en.length;t++){let r=en[t];if(r.probe(s)){let n=new r(new w(s));return e?n.getFont(e):n}}throw new Error("Unknown font format")}let br="en";function ws(s){s===void 0&&(s="en"),br=s}function He(s,e,t){if(t.get){let r=t.get;t.get=function(){let n=r.call(this);return Object.defineProperty(this,e,{value:n}),n}}else if(typeof t.value=="function"){let r=t.value;return{get(){let n=new Map;function a(){for(var i=arguments.length,l=new Array(i),u=0;u<i;u++)l[u]=arguments[u];let c=l.length>0?l[0]:"value";if(n.has(c))return n.get(c);let f=r.apply(this,l);return n.set(c,f),f}return Object.defineProperty(this,e,{value:a}),a}}}}let ys=new v({firstCode:o,entryCount:o,idDelta:x,idRangeOffset:o}),tn=new v({startCharCode:p,endCharCode:p,glyphID:p}),Cs=new v({startUnicodeValue:ne,additionalCount:y}),Ss=new v({unicodeValue:ne,glyphID:o}),As=new g(Cs,p),Is=new g(Ss,p),Es=new v({varSelector:ne,defaultUVS:new b(p,As,{type:"parent"}),nonDefaultUVS:new b(p,Is,{type:"parent"})}),ks=new re(o,{0:{length:o,language:o,codeMap:new A(y,256)},2:{length:o,language:o,subHeaderKeys:new g(o,256),subHeaderCount:s=>Math.max.apply(Math,s.subHeaderKeys),subHeaders:new A(ys,"subHeaderCount"),glyphIndexArray:new A(o,"subHeaderCount")},4:{length:o,language:o,segCountX2:o,segCount:s=>s.segCountX2>>1,searchRange:o,entrySelector:o,rangeShift:o,endCode:new A(o,"segCount"),reservedPad:new V(o),startCode:new A(o,"segCount"),idDelta:new A(x,"segCount"),idRangeOffset:new A(o,"segCount"),glyphIndexArray:new A(o,s=>(s.length-s._currentOffset)/2)},6:{length:o,language:o,firstCode:o,entryCount:o,glyphIndices:new A(o,"entryCount")},8:{reserved:new V(o),length:p,language:o,is32:new A(y,8192),nGroups:p,groups:new A(tn,"nGroups")},10:{reserved:new V(o),length:p,language:p,firstCode:p,entryCount:p,glyphIndices:new A(o,"numChars")},12:{reserved:new V(o),length:p,language:p,nGroups:p,groups:new A(tn,"nGroups")},13:{reserved:new V(o),length:p,language:p,nGroups:p,groups:new A(tn,"nGroups")},14:{length:p,numRecords:p,varSelectors:new A(Es,"numRecords")}}),Os=new v({platformID:o,encodingID:o,table:new b(p,ks,{type:"parent",lazy:!0})});var Ps=new v({version:o,numSubtables:o,tables:new g(Os,"numSubtables")}),Ts=new v({version:ae,revision:ae,checkSumAdjustment:p,magicNumber:p,flags:o,unitsPerEm:o,created:new g(ae,2),modified:new g(ae,2),xMin:x,yMin:x,xMax:x,yMax:x,macStyle:new O(o,["bold","italic","underline","outline","shadow","condensed","extended"]),lowestRecPPEM:o,fontDirectionHint:x,indexToLocFormat:x,glyphDataFormat:x}),Fs=new v({version:ae,ascent:x,descent:x,lineGap:x,advanceWidthMax:o,minLeftSideBearing:x,minRightSideBearing:x,xMaxExtent:x,caretSlopeRise:x,caretSlopeRun:x,caretOffset:x,reserved:new V(x,4),metricDataFormat:x,numberOfMetrics:o});let Ds=new v({advance:o,bearing:x});var Bs=new v({metrics:new A(Ds,s=>s.parent.hhea.numberOfMetrics),bearings:new A(x,s=>s.parent.maxp.numGlyphs-s.parent.hhea.numberOfMetrics)}),Ls=new v({version:ae,numGlyphs:o,maxPoints:o,maxContours:o,maxComponentPoints:o,maxComponentContours:o,maxZones:o,maxTwilightPoints:o,maxStorage:o,maxFunctionDefs:o,maxInstructionDefs:o,maxStackElements:o,maxSizeOfInstructions:o,maxComponentElements:o,maxComponentDepth:o});function Dn(s,e,t){return t===void 0&&(t=0),s===1&&Bn[t]?Bn[t]:Us[s][e]}const Ms=new Set(["x-mac-roman","x-mac-cyrillic","iso-8859-6","iso-8859-8"]),Rs={"x-mac-croatian":"\xC4\xC5\xC7\xC9\xD1\xD6\xDC\xE1\xE0\xE2\xE4\xE3\xE5\xE7\xE9\xE8\xEA\xEB\xED\xEC\xEE\xEF\xF1\xF3\xF2\xF4\xF6\xF5\xFA\xF9\xFB\xFC\u2020\xB0\xA2\xA3\xA7\u2022\xB6\xDF\xAE\u0160\u2122\xB4\xA8\u2260\u017D\xD8\u221E\xB1\u2264\u2265\u2206\xB5\u2202\u2211\u220F\u0161\u222B\xAA\xBA\u03A9\u017E\xF8\xBF\xA1\xAC\u221A\u0192\u2248\u0106\xAB\u010C\u2026 \xC0\xC3\xD5\u0152\u0153\u0110\u2014\u201C\u201D\u2018\u2019\xF7\u25CA\uF8FF\xA9\u2044\u20AC\u2039\u203A\xC6\xBB\u2013\xB7\u201A\u201E\u2030\xC2\u0107\xC1\u010D\xC8\xCD\xCE\xCF\xCC\xD3\xD4\u0111\xD2\xDA\xDB\xD9\u0131\u02C6\u02DC\xAF\u03C0\xCB\u02DA\xB8\xCA\xE6\u02C7","x-mac-gaelic":"\xC4\xC5\xC7\xC9\xD1\xD6\xDC\xE1\xE0\xE2\xE4\xE3\xE5\xE7\xE9\xE8\xEA\xEB\xED\xEC\xEE\xEF\xF1\xF3\xF2\xF4\xF6\xF5\xFA\xF9\xFB\xFC\u2020\xB0\xA2\xA3\xA7\u2022\xB6\xDF\xAE\xA9\u2122\xB4\xA8\u2260\xC6\xD8\u1E02\xB1\u2264\u2265\u1E03\u010A\u010B\u1E0A\u1E0B\u1E1E\u1E1F\u0120\u0121\u1E40\xE6\xF8\u1E41\u1E56\u1E57\u027C\u0192\u017F\u1E60\xAB\xBB\u2026 \xC0\xC3\xD5\u0152\u0153\u2013\u2014\u201C\u201D\u2018\u2019\u1E61\u1E9B\xFF\u0178\u1E6A\u20AC\u2039\u203A\u0176\u0177\u1E6B\xB7\u1EF2\u1EF3\u204A\xC2\xCA\xC1\xCB\xC8\xCD\xCE\xCF\xCC\xD3\xD4\u2663\xD2\xDA\xDB\xD9\u0131\xDD\xFD\u0174\u0175\u1E84\u1E85\u1E80\u1E81\u1E82\u1E83","x-mac-greek":"\xC4\xB9\xB2\xC9\xB3\xD6\xDC\u0385\xE0\xE2\xE4\u0384\xA8\xE7\xE9\xE8\xEA\xEB\xA3\u2122\xEE\xEF\u2022\xBD\u2030\xF4\xF6\xA6\u20AC\xF9\xFB\xFC\u2020\u0393\u0394\u0398\u039B\u039E\u03A0\xDF\xAE\xA9\u03A3\u03AA\xA7\u2260\xB0\xB7\u0391\xB1\u2264\u2265\xA5\u0392\u0395\u0396\u0397\u0399\u039A\u039C\u03A6\u03AB\u03A8\u03A9\u03AC\u039D\xAC\u039F\u03A1\u2248\u03A4\xAB\xBB\u2026 \u03A5\u03A7\u0386\u0388\u0153\u2013\u2015\u201C\u201D\u2018\u2019\xF7\u0389\u038A\u038C\u038E\u03AD\u03AE\u03AF\u03CC\u038F\u03CD\u03B1\u03B2\u03C8\u03B4\u03B5\u03C6\u03B3\u03B7\u03B9\u03BE\u03BA\u03BB\u03BC\u03BD\u03BF\u03C0\u03CE\u03C1\u03C3\u03C4\u03B8\u03C9\u03C2\u03C7\u03C5\u03B6\u03CA\u03CB\u0390\u03B0\xAD","x-mac-icelandic":"\xC4\xC5\xC7\xC9\xD1\xD6\xDC\xE1\xE0\xE2\xE4\xE3\xE5\xE7\xE9\xE8\xEA\xEB\xED\xEC\xEE\xEF\xF1\xF3\xF2\xF4\xF6\xF5\xFA\xF9\xFB\xFC\xDD\xB0\xA2\xA3\xA7\u2022\xB6\xDF\xAE\xA9\u2122\xB4\xA8\u2260\xC6\xD8\u221E\xB1\u2264\u2265\xA5\xB5\u2202\u2211\u220F\u03C0\u222B\xAA\xBA\u03A9\xE6\xF8\xBF\xA1\xAC\u221A\u0192\u2248\u2206\xAB\xBB\u2026 \xC0\xC3\xD5\u0152\u0153\u2013\u2014\u201C\u201D\u2018\u2019\xF7\u25CA\xFF\u0178\u2044\u20AC\xD0\xF0\xDE\xFE\xFD\xB7\u201A\u201E\u2030\xC2\xCA\xC1\xCB\xC8\xCD\xCE\xCF\xCC\xD3\xD4\uF8FF\xD2\xDA\xDB\xD9\u0131\u02C6\u02DC\xAF\u02D8\u02D9\u02DA\xB8\u02DD\u02DB\u02C7","x-mac-inuit":"\u1403\u1404\u1405\u1406\u140A\u140B\u1431\u1432\u1433\u1434\u1438\u1439\u1449\u144E\u144F\u1450\u1451\u1455\u1456\u1466\u146D\u146E\u146F\u1470\u1472\u1473\u1483\u148B\u148C\u148D\u148E\u1490\u1491\xB0\u14A1\u14A5\u14A6\u2022\xB6\u14A7\xAE\xA9\u2122\u14A8\u14AA\u14AB\u14BB\u14C2\u14C3\u14C4\u14C5\u14C7\u14C8\u14D0\u14EF\u14F0\u14F1\u14F2\u14F4\u14F5\u1505\u14D5\u14D6\u14D7\u14D8\u14DA\u14DB\u14EA\u1528\u1529\u152A\u152B\u152D\u2026 \u152E\u153E\u1555\u1556\u1557\u2013\u2014\u201C\u201D\u2018\u2019\u1558\u1559\u155A\u155D\u1546\u1547\u1548\u1549\u154B\u154C\u1550\u157F\u1580\u1581\u1582\u1583\u1584\u1585\u158F\u1590\u1591\u1592\u1593\u1594\u1595\u1671\u1672\u1673\u1674\u1675\u1676\u1596\u15A0\u15A1\u15A2\u15A3\u15A4\u15A5\u15A6\u157C\u0141\u0142","x-mac-ce":"\xC4\u0100\u0101\xC9\u0104\xD6\xDC\xE1\u0105\u010C\xE4\u010D\u0106\u0107\xE9\u0179\u017A\u010E\xED\u010F\u0112\u0113\u0116\xF3\u0117\xF4\xF6\xF5\xFA\u011A\u011B\xFC\u2020\xB0\u0118\xA3\xA7\u2022\xB6\xDF\xAE\xA9\u2122\u0119\xA8\u2260\u0123\u012E\u012F\u012A\u2264\u2265\u012B\u0136\u2202\u2211\u0142\u013B\u013C\u013D\u013E\u0139\u013A\u0145\u0146\u0143\xAC\u221A\u0144\u0147\u2206\xAB\xBB\u2026 \u0148\u0150\xD5\u0151\u014C\u2013\u2014\u201C\u201D\u2018\u2019\xF7\u25CA\u014D\u0154\u0155\u0158\u2039\u203A\u0159\u0156\u0157\u0160\u201A\u201E\u0161\u015A\u015B\xC1\u0164\u0165\xCD\u017D\u017E\u016A\xD3\xD4\u016B\u016E\xDA\u016F\u0170\u0171\u0172\u0173\xDD\xFD\u0137\u017B\u0141\u017C\u0122\u02C7","x-mac-romanian":"\xC4\xC5\xC7\xC9\xD1\xD6\xDC\xE1\xE0\xE2\xE4\xE3\xE5\xE7\xE9\xE8\xEA\xEB\xED\xEC\xEE\xEF\xF1\xF3\xF2\xF4\xF6\xF5\xFA\xF9\xFB\xFC\u2020\xB0\xA2\xA3\xA7\u2022\xB6\xDF\xAE\xA9\u2122\xB4\xA8\u2260\u0102\u0218\u221E\xB1\u2264\u2265\xA5\xB5\u2202\u2211\u220F\u03C0\u222B\xAA\xBA\u03A9\u0103\u0219\xBF\xA1\xAC\u221A\u0192\u2248\u2206\xAB\xBB\u2026 \xC0\xC3\xD5\u0152\u0153\u2013\u2014\u201C\u201D\u2018\u2019\xF7\u25CA\xFF\u0178\u2044\u20AC\u2039\u203A\u021A\u021B\u2021\xB7\u201A\u201E\u2030\xC2\xCA\xC1\xCB\xC8\xCD\xCE\xCF\xCC\xD3\xD4\uF8FF\xD2\xDA\xDB\xD9\u0131\u02C6\u02DC\xAF\u02D8\u02D9\u02DA\xB8\u02DD\u02DB\u02C7","x-mac-turkish":"\xC4\xC5\xC7\xC9\xD1\xD6\xDC\xE1\xE0\xE2\xE4\xE3\xE5\xE7\xE9\xE8\xEA\xEB\xED\xEC\xEE\xEF\xF1\xF3\xF2\xF4\xF6\xF5\xFA\xF9\xFB\xFC\u2020\xB0\xA2\xA3\xA7\u2022\xB6\xDF\xAE\xA9\u2122\xB4\xA8\u2260\xC6\xD8\u221E\xB1\u2264\u2265\xA5\xB5\u2202\u2211\u220F\u03C0\u222B\xAA\xBA\u03A9\xE6\xF8\xBF\xA1\xAC\u221A\u0192\u2248\u2206\xAB\xBB\u2026 \xC0\xC3\xD5\u0152\u0153\u2013\u2014\u201C\u201D\u2018\u2019\xF7\u25CA\xFF\u0178\u011E\u011F\u0130\u0131\u015E\u015F\u2021\xB7\u201A\u201E\u2030\xC2\xCA\xC1\xCB\xC8\xCD\xCE\xCF\xCC\xD3\xD4\uF8FF\xD2\xDA\xDB\xD9\uF8A0\u02C6\u02DC\xAF\u02D8\u02D9\u02DA\xB8\u02DD\u02DB\u02C7"},rn=new Map;function Ns(s){let e=rn.get(s);if(e)return e;let t=Rs[s];if(t){let r=new Map;for(let n=0;n<t.length;n++)r.set(t.charCodeAt(n),128+n);return rn.set(s,r),r}if(Ms.has(s)){let r=new TextDecoder(s),n=new Uint8Array(128);for(let l=0;l<128;l++)n[l]=128+l;let a=new Map,i=r.decode(n);for(let l=0;l<128;l++)a.set(i.charCodeAt(l),128+l);return rn.set(s,a),a}}const Us=[["utf-16be","utf-16be","utf-16be","utf-16be","utf-16be","utf-16be","utf-16be"],["x-mac-roman","shift-jis","big5","euc-kr","iso-8859-6","iso-8859-8","x-mac-greek","x-mac-cyrillic","x-mac-symbol","x-mac-devanagari","x-mac-gurmukhi","x-mac-gujarati","Oriya","Bengali","Tamil","Telugu","Kannada","Malayalam","Sinhalese","Burmese","Khmer","iso-8859-11","Laotian","Georgian","Armenian","gbk","Tibetan","Mongolian","Geez","x-mac-ce","Vietnamese","Sindhi"],["ascii",null,"iso-8859-1"],["symbol","utf-16be","shift-jis","gb18030","big5","euc-kr","johab",null,null,null,"utf-16be"]],Bn={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"},Gs=[[],{0:"en",30:"fo",60:"ks",90:"rw",1:"fr",31:"fa",61:"ku",91:"rn",2:"de",32:"ru",62:"sd",92:"ny",3:"it",33:"zh",63:"bo",93:"mg",4:"nl",34:"nl-BE",64:"ne",94:"eo",5:"sv",35:"ga",65:"sa",128:"cy",6:"es",36:"sq",66:"mr",129:"eu",7:"da",37:"ro",67:"bn",130:"ca",8:"pt",38:"cz",68:"as",131:"la",9:"no",39:"sk",69:"gu",132:"qu",10:"he",40:"si",70:"pa",133:"gn",11:"ja",41:"yi",71:"or",134:"ay",12:"ar",42:"sr",72:"ml",135:"tt",13:"fi",43:"mk",73:"kn",136:"ug",14:"el",44:"bg",74:"ta",137:"dz",15:"is",45:"uk",75:"te",138:"jv",16:"mt",46:"be",76:"si",139:"su",17:"tr",47:"uz",77:"my",140:"gl",18:"hr",48:"kk",78:"km",141:"af",19:"zh-Hant",49:"az-Cyrl",79:"lo",142:"br",20:"ur",50:"az-Arab",80:"vi",143:"iu",21:"hi",51:"hy",81:"id",144:"gd",22:"th",52:"ka",82:"tl",145:"gv",23:"ko",53:"mo",83:"ms",146:"ga",24:"lt",54:"ky",84:"ms-Arab",147:"to",25:"pl",55:"tg",85:"am",148:"el-polyton",26:"hu",56:"tk",86:"ti",149:"kl",27:"es",57:"mn-CN",87:"om",150:"az",28:"lv",58:"mn",88:"so",151:"nn",29:"se",59:"ps",89:"sw"},[],{1078:"af",16393:"en-IN",1159:"rw",1074:"tn",1052:"sq",6153:"en-IE",1089:"sw",1115:"si",1156:"gsw",8201:"en-JM",1111:"kok",1051:"sk",1118:"am",17417:"en-MY",1042:"ko",1060:"sl",5121:"ar-DZ",5129:"en-NZ",1088:"ky",11274:"es-AR",15361:"ar-BH",13321:"en-PH",1108:"lo",16394:"es-BO",3073:"ar",18441:"en-SG",1062:"lv",13322:"es-CL",2049:"ar-IQ",7177:"en-ZA",1063:"lt",9226:"es-CO",11265:"ar-JO",11273:"en-TT",2094:"dsb",5130:"es-CR",13313:"ar-KW",2057:"en-GB",1134:"lb",7178:"es-DO",12289:"ar-LB",1033:"en",1071:"mk",12298:"es-EC",4097:"ar-LY",12297:"en-ZW",2110:"ms-BN",17418:"es-SV",6145:"ary",1061:"et",1086:"ms",4106:"es-GT",8193:"ar-OM",1080:"fo",1100:"ml",18442:"es-HN",16385:"ar-QA",1124:"fil",1082:"mt",2058:"es-MX",1025:"ar-SA",1035:"fi",1153:"mi",19466:"es-NI",10241:"ar-SY",2060:"fr-BE",1146:"arn",6154:"es-PA",7169:"aeb",3084:"fr-CA",1102:"mr",15370:"es-PY",14337:"ar-AE",1036:"fr",1148:"moh",10250:"es-PE",9217:"ar-YE",5132:"fr-LU",1104:"mn",20490:"es-PR",1067:"hy",6156:"fr-MC",2128:"mn-CN",3082:"es",1101:"as",4108:"fr-CH",1121:"ne",1034:"es",2092:"az-Cyrl",1122:"fy",1044:"nb",21514:"es-US",1068:"az",1110:"gl",2068:"nn",14346:"es-UY",1133:"ba",1079:"ka",1154:"oc",8202:"es-VE",1069:"eu",3079:"de-AT",1096:"or",2077:"sv-FI",1059:"be",1031:"de",1123:"ps",1053:"sv",2117:"bn",5127:"de-LI",1045:"pl",1114:"syr",1093:"bn-IN",4103:"de-LU",1046:"pt",1064:"tg",8218:"bs-Cyrl",2055:"de-CH",2070:"pt-PT",2143:"tzm",5146:"bs",1032:"el",1094:"pa",1097:"ta",1150:"br",1135:"kl",1131:"qu-BO",1092:"tt",1026:"bg",1095:"gu",2155:"qu-EC",1098:"te",1027:"ca",1128:"ha",3179:"qu",1054:"th",3076:"zh-HK",1037:"he",1048:"ro",1105:"bo",5124:"zh-MO",1081:"hi",1047:"rm",1055:"tr",2052:"zh",1038:"hu",1049:"ru",1090:"tk",4100:"zh-SG",1039:"is",9275:"smn",1152:"ug",1028:"zh-TW",1136:"ig",4155:"smj-NO",1058:"uk",1155:"co",1057:"id",5179:"smj",1070:"hsb",1050:"hr",1117:"iu",3131:"se-FI",1056:"ur",4122:"hr-BA",2141:"iu-Latn",1083:"se",2115:"uz-Cyrl",1029:"cs",2108:"ga",2107:"se-SE",1091:"uz",1030:"da",1076:"xh",8251:"sms",1066:"vi",1164:"prs",1077:"zu",6203:"sma-NO",1106:"cy",1125:"dv",1040:"it",7227:"sms",1160:"wo",2067:"nl-BE",2064:"it-CH",1103:"sa",1157:"sah",1043:"nl",1041:"ja",7194:"sr-Cyrl-BA",1144:"ii",3081:"en-AU",1099:"kn",3098:"sr",1130:"yo",10249:"en-BZ",1087:"kk",6170:"sr-Latn-BA",4105:"en-CA",1107:"km",2074:"sr-Latn",9225:"en-029",1158:"quc",1132:"nso"}];let Ln=new v({platformID:o,encodingID:o,languageID:o,nameID:o,length:o,string:new b(o,new j("length",s=>Dn(s.platformID,s.encodingID,s.languageID)),{type:"parent",relativeTo:s=>s.parent.stringOffset,allowNull:!1})}),Vs=new v({length:o,tag:new b(o,new j("length","utf16be"),{type:"parent",relativeTo:s=>s.stringOffset})});var gr=new re(o,{0:{count:o,stringOffset:o,records:new g(Ln,"count")},1:{count:o,stringOffset:o,records:new g(Ln,"count"),langTagCount:o,langTags:new g(Vs,"langTagCount")}}),zs=gr;const nn=["copyright","fontFamily","fontSubfamily","uniqueSubfamily","fullName","version","postscriptName","trademark","manufacturer","designer","description","vendorURL","designerURL","license","licenseURL",null,"preferredFamily","preferredSubfamily","compatibleFull","sampleText","postscriptCIDFontName","wwsFamilyName","wwsSubfamilyName"];gr.process=function(s){var e={};for(let t of this.records){let r=Gs[t.platformID][t.languageID];r==null&&this.langTags!=null&&t.languageID>=32768&&(r=this.langTags[t.languageID-32768].tag),r==null&&(r=t.platformID+"-"+t.languageID);let n=t.nameID>=256?"fontFeatures":nn[t.nameID]||t.nameID;e[n]==null&&(e[n]={});let a=e[n];t.nameID>=256&&(a=a[t.nameID]||(a[t.nameID]={})),(typeof t.string=="string"||typeof a[r]!="string")&&(a[r]=t.string)}this.records=e},gr.preEncode=function(){if(Array.isArray(this.records))return;this.version=0;let s=[];for(let e in this.records){let t=this.records[e];e!=="fontFeatures"&&(s.push({platformID:3,encodingID:1,languageID:1033,nameID:nn.indexOf(e),length:t.en.length*2,string:t.en}),e==="postscriptName"&&s.push({platformID:1,encodingID:0,languageID:0,nameID:nn.indexOf(e),length:t.en.length,string:t.en}))}this.records=s,this.count=s.length,this.stringOffset=gr.size(this,null,!1)};var Mn=new re(o,{header:{xAvgCharWidth:x,usWeightClass:o,usWidthClass:o,fsType:new O(o,[null,"noEmbedding","viewOnly","editable",null,null,null,null,"noSubsetting","bitmapOnly"]),ySubscriptXSize:x,ySubscriptYSize:x,ySubscriptXOffset:x,ySubscriptYOffset:x,ySuperscriptXSize:x,ySuperscriptYSize:x,ySuperscriptXOffset:x,ySuperscriptYOffset:x,yStrikeoutSize:x,yStrikeoutPosition:x,sFamilyClass:x,panose:new g(y,10),ulCharRange:new g(p,4),vendorID:new j(4),fsSelection:new O(o,["italic","underscore","negative","outlined","strikeout","bold","regular","useTypoMetrics","wws","oblique"]),usFirstCharIndex:o,usLastCharIndex:o},0:{},1:{typoAscender:x,typoDescender:x,typoLineGap:x,winAscent:o,winDescent:o,codePageRange:new g(p,2)},2:{typoAscender:x,typoDescender:x,typoLineGap:x,winAscent:o,winDescent:o,codePageRange:new g(p,2),xHeight:x,capHeight:x,defaultChar:o,breakChar:o,maxContent:o},5:{typoAscender:x,typoDescender:x,typoLineGap:x,winAscent:o,winDescent:o,codePageRange:new g(p,2),xHeight:x,capHeight:x,defaultChar:o,breakChar:o,maxContent:o,usLowerOpticalPointSize:o,usUpperOpticalPointSize:o}});let an=Mn.versions;an[3]=an[4]=an[2];var $s=Mn,Ws=new re(Le,{header:{italicAngle:Le,underlinePosition:x,underlineThickness:x,isFixedPitch:p,minMemType42:p,maxMemType42:p,minMemType1:p,maxMemType1:p},1:{},2:{numberOfGlyphs:o,glyphNameIndex:new g(o,"numberOfGlyphs"),names:new g(new j(y))},2.5:{numberOfGlyphs:o,offsets:new g(y,"numberOfGlyphs")},3:{},4:{map:new g(p,s=>s.parent.maxp.numGlyphs)}}),qs=new v({controlValues:new g(x)}),Hs=new v({instructions:new g(y)});let sn=new re("head.indexToLocFormat",{0:{offsets:new g(o)},1:{offsets:new g(p)}});sn.process=function(){if(this.version===0&&!this._processed){for(let s=0;s<this.offsets.length;s++)this.offsets[s]<<=1;this._processed=!0}},sn.preEncode=function(){if(this.version===0&&this._processed!==!1){for(let s=0;s<this.offsets.length;s++)this.offsets[s]>>>=1;this._processed=!1}};var js=sn,Xs=new v({controlValueProgram:new g(y)}),Ys=new g(new z);class v0{getCFFVersion(e){for(;e&&!e.hdrSize;)e=e.parent;return e?e.version:-1}decode(e,t){let n=this.getCFFVersion(t)>=2?e.readUInt32BE():e.readUInt16BE();if(n===0)return[];let a=e.readUInt8(),i;if(a===1)i=y;else if(a===2)i=o;else if(a===3)i=ne;else if(a===4)i=p;else throw new Error("Bad offset size in CFFIndex: "+a+" "+e.pos);let l=[],u=e.pos+(n+1)*a-1,c=i.decode(e);for(let f=0;f<n;f++){let d=i.decode(e);if(this.type!=null){let C=e.pos;e.pos=u+c,t.length=d-c,l.push(this.type.decode(e,t)),e.pos=C}else l.push({offset:u+c,length:d-c});c=d}return e.pos=u+c,l}size(e,t){let r=2;if(e.length===0)return r;let n=this.type||new z,a=1;for(let l=0;l<e.length;l++){let u=e[l];a+=n.size(u,t)}let i;if(a<=255)i=y;else if(a<=65535)i=o;else if(a<=16777215)i=ne;else if(a<=4294967295)i=p;else throw new Error("Bad offset in CFFIndex");return r+=1+i.size()*(e.length+1),r+=a-1,r}encode(e,t,r){if(e.writeUInt16BE(t.length),t.length===0)return;let n=this.type||new z,a=[],i=1;for(let u of t){let c=n.size(u,r);a.push(c),i+=c}let l;if(i<=255)l=y;else if(i<=65535)l=o;else if(i<=16777215)l=ne;else if(i<=4294967295)l=p;else throw new Error("Bad offset in CFFIndex");e.writeUInt8(l.size()),i=1,l.encode(e,i);for(let u of a)i+=u,l.encode(e,i);for(let u of t)n.encode(e,u,r)}constructor(e){this.type=e}}const $t=15,Rn=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"],Nn={".":10,E:11,"E-":12,"-":14};class on{static decode(e,t){if(32<=t&&t<=246)return t-139;if(247<=t&&t<=250)return(t-247)*256+e.readUInt8()+108;if(251<=t&&t<=254)return-(t-251)*256-e.readUInt8()-108;if(t===28)return e.readInt16BE();if(t===29)return e.readInt32BE();if(t===30){let r="";for(;;){let n=e.readUInt8(),a=n>>4;if(a===$t)break;r+=Rn[a];let i=n&15;if(i===$t)break;r+=Rn[i]}return parseFloat(r)}return null}static size(e){if(e.forceLarge&&(e=32768),(e|0)!==e){let t=""+e;return 1+Math.ceil((t.length+1)/2)}else return-107<=e&&e<=107?1:108<=e&&e<=1131||-1131<=e&&e<=-108?2:-32768<=e&&e<=32767?3:5}static encode(e,t){let r=Number(t);if(t.forceLarge)return e.writeUInt8(29),e.writeInt32BE(r);if((r|0)!==r){e.writeUInt8(30);let a=""+r;for(let i=0;i<a.length;i+=2){let l=a[i],u=Nn[l]||+l;if(i===a.length-1)var n=$t;else{let c=a[i+1];var n=Nn[c]||+c}e.writeUInt8(u<<4|n&15)}if(n!==$t)return e.writeUInt8($t<<4)}else return-107<=r&&r<=107?e.writeUInt8(r+139):108<=r&&r<=1131?(r-=108,e.writeUInt8((r>>8)+247),e.writeUInt8(r&255)):-1131<=r&&r<=-108?(r=-r-108,e.writeUInt8((r>>8)+251),e.writeUInt8(r&255)):-32768<=r&&r<=32767?(e.writeUInt8(28),e.writeInt16BE(r)):(e.writeUInt8(29),e.writeInt32BE(r))}}class vr{decodeOperands(e,t,r,n){if(Array.isArray(e))return n.map((a,i)=>this.decodeOperands(e[i],t,r,[a]));if(e.decode!=null)return e.decode(t,r,n);switch(e){case"number":case"offset":case"sid":return n[0];case"boolean":return!!n[0];default:return n}}encodeOperands(e,t,r,n){return Array.isArray(e)?n.map((a,i)=>this.encodeOperands(e[i],t,r,a)[0]):e.encode!=null?e.encode(t,n,r):typeof n=="number"?[n]:typeof n=="boolean"?[+n]:Array.isArray(n)?n:[n]}decode(e,t){let r=e.pos+t.length,n={},a=[];Object.defineProperties(n,{parent:{value:t},_startOffset:{value:e.pos}});for(let i in this.fields){let l=this.fields[i];n[l[1]]=l[3]}for(;e.pos<r;){let i=e.readUInt8();if(i<28){i===12&&(i=i<<8|e.readUInt8());let l=this.fields[i];if(!l)throw new Error("Unknown operator "+i);let u=this.decodeOperands(l[2],e,n,a);u!=null&&(u instanceof Tt?Object.defineProperty(n,l[1],u):n[l[1]]=u),a=[]}else a.push(on.decode(e,i))}return n}size(e,t,r){r===void 0&&(r=!0);let n={parent:t,val:e,pointerSize:0,startOffset:t.startOffset||0},a=0;for(let i in this.fields){let l=this.fields[i],u=e[l[1]];if(u==null||ut(u,l[3]))continue;let c=this.encodeOperands(l[2],null,n,u);for(let d of c)a+=on.size(d);let f=Array.isArray(l[0])?l[0]:[l[0]];a+=f.length}return r&&(a+=n.pointerSize),a}encode(e,t,r){let n={pointers:[],startOffset:e.pos,parent:r,val:t,pointerSize:0};n.pointerOffset=e.pos+this.size(t,n,!1);for(let i of this.ops){let l=t[i[1]];if(l==null||ut(l,i[3]))continue;let u=this.encodeOperands(i[2],e,n,l);for(let f of u)on.encode(e,f);let c=Array.isArray(i[0])?i[0]:[i[0]];for(let f of c)e.writeUInt8(f)}let a=0;for(;a<n.pointers.length;){let i=n.pointers[a++];i.type.encode(e,i.val,i.parent)}}constructor(e){e===void 0&&(e=[]),this.ops=e,this.fields={};for(let t of e){let r=Array.isArray(t[0])?t[0][0]<<8|t[0][1]:t[0];this.fields[r]=t}}}class f0 extends b{decode(e,t,r){return this.offsetType={decode:()=>r[0]},super.decode(e,t,r)}encode(e,t,r){if(!e)return this.offsetType={size:()=>0},this.size(t,r),[new Un(0)];let n=null;return this.offsetType={encode:(a,i)=>n=i},super.encode(e,t,r),[new Un(n)]}constructor(e,t){t===void 0&&(t={}),t.type==null&&(t.type="global"),super(null,e,t)}}class Un{valueOf(){return this.val}constructor(e){this.val=e,this.forceLarge=!0}}class Zs{static decode(e,t,r){let n=r.pop();for(;r.length>n;)r.pop()}}var ln=new vr([[6,"BlueValues","delta",null],[7,"OtherBlues","delta",null],[8,"FamilyBlues","delta",null],[9,"FamilyOtherBlues","delta",null],[[12,9],"BlueScale","number",.039625],[[12,10],"BlueShift","number",7],[[12,11],"BlueFuzz","number",1],[10,"StdHW","number",null],[11,"StdVW","number",null],[[12,12],"StemSnapH","delta",null],[[12,13],"StemSnapV","delta",null],[[12,14],"ForceBold","boolean",!1],[[12,17],"LanguageGroup","number",0],[[12,18],"ExpansionFactor","number",.06],[[12,19],"initialRandomSeed","number",0],[20,"defaultWidthX","number",0],[21,"nominalWidthX","number",0],[22,"vsindex","number",0],[23,"blend",Zs,null],[19,"Subrs",new f0(new v0,{type:"local"}),null]]),mr=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"];let Gn=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],Ks=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],Vn=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron"],Js=[".notdef","space","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","fi","fl","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],_s=[".notdef","space","dollaroldstyle","dollarsuperior","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","fi","fl","ffi","ffl","parenleftinferior","parenrightinferior","hyphensuperior","colonmonetary","onefitted","rupiah","centoldstyle","figuredash","hypheninferior","onequarter","onehalf","threequarters","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior"],zn=new v({reserved:new V(o),reqFeatureIndex:o,featureCount:o,featureIndexes:new g(o,"featureCount")}),Qs=new v({tag:new j(4),langSys:new b(o,zn,{type:"parent"})}),ei=new v({defaultLangSys:new b(o,zn),count:o,langSysRecords:new g(Qs,"count")}),ti=new v({tag:new j(4),script:new b(o,ei,{type:"parent"})}),$n=new g(ti,o),ri=new v({version:o,nameID:o}),Wn=new v({featureParams:new b(o,ri),lookupCount:o,lookupListIndexes:new g(o,"lookupCount")}),ni=new v({tag:new j(4),feature:new b(o,Wn,{type:"parent"})}),qn=new g(ni,o),ai=new v({markAttachmentType:y,flags:new O(y,["rightToLeft","ignoreBaseGlyphs","ignoreLigatures","ignoreMarks","useMarkFilteringSet"])});function xr(s){let e=new v({lookupType:o,flags:ai,subTableCount:o,subTables:new g(new b(o,s),"subTableCount"),markFilteringSet:new K(o,t=>t.flags.flags.useMarkFilteringSet)});return new A(new b(o,e),o)}let si=new v({start:o,end:o,startCoverageIndex:o}),Ae=new re(o,{1:{glyphCount:o,glyphs:new g(o,"glyphCount")},2:{rangeCount:o,rangeRecords:new g(si,"rangeCount")}}),ii=new v({start:o,end:o,class:o}),M0=new re(o,{1:{startGlyph:o,glyphCount:o,classValueArray:new g(o,"glyphCount")},2:{classRangeCount:o,classRangeRecord:new g(ii,"classRangeCount")}}),R0=new v({a:o,b:o,deltaFormat:o}),Wt=new v({sequenceIndex:o,lookupListIndex:o}),oi=new v({glyphCount:o,lookupCount:o,input:new g(o,s=>s.glyphCount-1),lookupRecords:new g(Wt,"lookupCount")}),li=new g(new b(o,oi),o),ui=new v({glyphCount:o,lookupCount:o,classes:new g(o,s=>s.glyphCount-1),lookupRecords:new g(Wt,"lookupCount")}),ci=new g(new b(o,ui),o),Hn=new re(o,{1:{coverage:new b(o,Ae),ruleSetCount:o,ruleSets:new g(new b(o,li),"ruleSetCount")},2:{coverage:new b(o,Ae),classDef:new b(o,M0),classSetCnt:o,classSet:new g(new b(o,ci),"classSetCnt")},3:{glyphCount:o,lookupCount:o,coverages:new g(new b(o,Ae),"glyphCount"),lookupRecords:new g(Wt,"lookupCount")}}),fi=new v({backtrackGlyphCount:o,backtrack:new g(o,"backtrackGlyphCount"),inputGlyphCount:o,input:new g(o,s=>s.inputGlyphCount-1),lookaheadGlyphCount:o,lookahead:new g(o,"lookaheadGlyphCount"),lookupCount:o,lookupRecords:new g(Wt,"lookupCount")}),jn=new g(new b(o,fi),o),Xn=new re(o,{1:{coverage:new b(o,Ae),chainCount:o,chainRuleSets:new g(new b(o,jn),"chainCount")},2:{coverage:new b(o,Ae),backtrackClassDef:new b(o,M0),inputClassDef:new b(o,M0),lookaheadClassDef:new b(o,M0),chainCount:o,chainClassSet:new g(new b(o,jn),"chainCount")},3:{backtrackGlyphCount:o,backtrackCoverage:new g(new b(o,Ae),"backtrackGlyphCount"),inputGlyphCount:o,inputCoverage:new g(new b(o,Ae),"inputGlyphCount"),lookaheadGlyphCount:o,lookaheadCoverage:new g(new b(o,Ae),"lookaheadGlyphCount"),lookupCount:o,lookupRecords:new g(Wt,"lookupCount")}}),qt=new u0(16,"BE",14),di=new v({startCoord:qt,peakCoord:qt,endCoord:qt}),hi=new v({axisCount:o,regionCount:o,variationRegions:new g(new g(di,"axisCount"),"regionCount")}),pi=new v({shortDeltas:new g(x,s=>s.parent.shortDeltaCount),regionDeltas:new g(Z,s=>s.parent.regionIndexCount-s.parent.shortDeltaCount),deltas:s=>s.shortDeltas.concat(s.regionDeltas)}),bi=new v({itemCount:o,shortDeltaCount:o,regionIndexCount:o,regionIndexes:new g(o,"regionIndexCount"),deltaSets:new g(pi,"itemCount")}),wr=new v({format:o,variationRegionList:new b(p,hi),variationDataCount:o,itemVariationData:new g(new b(p,bi),"variationDataCount")}),gi=new re(o,{1:{axisIndex:o,axisIndex:o,filterRangeMinValue:qt,filterRangeMaxValue:qt}}),vi=new v({conditionCount:o,conditionTable:new g(new b(p,gi),"conditionCount")}),mi=new v({featureIndex:o,alternateFeatureTable:new b(p,Wn,{type:"parent"})}),xi=new v({version:Le,substitutionCount:o,substitutions:new g(mi,"substitutionCount")}),wi=new v({conditionSet:new b(p,vi,{type:"parent"}),featureTableSubstitution:new b(p,xi,{type:"parent"})}),Yn=new v({majorVersion:o,minorVersion:o,featureVariationRecordCount:p,featureVariationRecords:new g(wi,"featureVariationRecordCount")});class Zn{decode(e,t,r){return this.predefinedOps[r[0]]?this.predefinedOps[r[0]]:this.type.decode(e,t,r)}size(e,t){return this.type.size(e,t)}encode(e,t,r){let n=this.predefinedOps.indexOf(t);return n!==-1?n:this.type.encode(e,t,r)}constructor(e,t){this.predefinedOps=e,this.type=t}}class yi extends ${decode(e){return y.decode(e)&127}constructor(){super("UInt8")}}let Kn=new v({first:o,nLeft:y}),Ci=new v({first:o,nLeft:o}),Si=new re(new yi,{0:{nCodes:y,codes:new g(y,"nCodes")},1:{nRanges:y,ranges:new g(Kn,"nRanges")}}),Ai=new Zn([Gn,Ks],new f0(Si,{lazy:!0}));class Jn extends g{decode(e,t){let r=r0(this.length,e,t),n=0,a=[];for(;n<r;){let i=this.type.decode(e,t);i.offset=n,n+=i.nLeft+1,a.push(i)}return a}}let Ii=new re(y,{0:{glyphs:new g(o,s=>s.parent.CharStrings.length-1)},1:{ranges:new Jn(Kn,s=>s.parent.CharStrings.length-1)},2:{ranges:new Jn(Ci,s=>s.parent.CharStrings.length-1)}}),Ei=new Zn([Vn,Js,_s],new f0(Ii,{lazy:!0})),ki=new v({first:o,fd:y}),Oi=new v({first:p,fd:o}),_n=new re(y,{0:{fds:new g(y,s=>s.parent.CharStrings.length)},3:{nRanges:o,ranges:new g(ki,"nRanges"),sentinel:o},4:{nRanges:p,ranges:new g(Oi,"nRanges"),sentinel:p}}),un=new f0(ln);class Qn{decode(e,t,r){return t.length=r[0],un.decode(e,t,[r[1]])}size(e,t){return[ln.size(e,t,!1),un.size(e,t)[0]]}encode(e,t,r){return[ln.size(t,r,!1),un.encode(e,t,r)[0]]}}let ea=new vr([[18,"Private",new Qn,null],[[12,38],"FontName","sid",null],[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[[12,5],"PaintType","number",0]]),Pi=new vr([[[12,30],"ROS",["sid","sid","number"],null],[0,"version","sid",null],[1,"Notice","sid",null],[[12,0],"Copyright","sid",null],[2,"FullName","sid",null],[3,"FamilyName","sid",null],[4,"Weight","sid",null],[[12,1],"isFixedPitch","boolean",!1],[[12,2],"ItalicAngle","number",0],[[12,3],"UnderlinePosition","number",-100],[[12,4],"UnderlineThickness","number",50],[[12,5],"PaintType","number",0],[[12,6],"CharstringType","number",2],[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[13,"UniqueID","number",null],[5,"FontBBox","array",[0,0,0,0]],[[12,8],"StrokeWidth","number",0],[14,"XUID","array",null],[15,"charset",Ei,Vn],[16,"Encoding",Ai,Gn],[17,"CharStrings",new f0(new v0),null],[18,"Private",new Qn,null],[[12,20],"SyntheticBase","number",null],[[12,21],"PostScript","sid",null],[[12,22],"BaseFontName","sid",null],[[12,23],"BaseFontBlend","delta",null],[[12,31],"CIDFontVersion","number",0],[[12,32],"CIDFontRevision","number",0],[[12,33],"CIDFontType","number",0],[[12,34],"CIDCount","number",8720],[[12,35],"UIDBase","number",null],[[12,37],"FDSelect",new f0(_n),null],[[12,36],"FDArray",new f0(new v0(ea)),null],[[12,38],"FontName","sid",null]]),Ti=new v({length:o,itemVariationStore:wr}),Fi=new vr([[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[17,"CharStrings",new f0(new v0),null],[[12,37],"FDSelect",new f0(_n),null],[[12,36],"FDArray",new f0(new v0(ea)),null],[24,"vstore",new f0(Ti),null],[25,"maxstack","number",193]]);var ta=new re(s0,{1:{hdrSize:y,offSize:y,nameIndex:new v0(new j("length")),topDictIndex:new v0(Pi),stringIndex:new v0(new j("length")),globalSubrIndex:new v0},2:{hdrSize:y,length:o,topDict:Fi,globalSubrIndex:new v0}});class cn{static decode(e){return new cn(e)}decode(){let e=this.stream.pos,t=ta.decode(this.stream);for(let r in t){let n=t[r];this[r]=n}if(this.version<2){if(this.topDictIndex.length!==1)throw new Error("Only a single font is allowed in CFF");this.topDict=this.topDictIndex[0]}return this.isCIDFont=this.topDict.ROS!=null,this}string(e){return this.version>=2?null:e<mr.length?mr[e]:this.stringIndex[e-mr.length]}get postscriptName(){return this.version<2?this.nameIndex[0]:null}get fullName(){return this.string(this.topDict.FullName)}get familyName(){return this.string(this.topDict.FamilyName)}getCharString(e){return this.stream.pos=this.topDict.CharStrings[e].offset,this.stream.readBuffer(this.topDict.CharStrings[e].length)}getGlyphName(e){if(this.version>=2||this.isCIDFont)return null;let{charset:t}=this.topDict;if(Array.isArray(t))return t[e];if(e===0)return".notdef";switch(e-=1,t.version){case 0:return this.string(t.glyphs[e]);case 1:case 2:for(let r=0;r<t.ranges.length;r++){let n=t.ranges[r];if(n.offset<=e&&e<=n.offset+n.nLeft)return this.string(n.first+(e-n.offset))}break}return null}fdForGlyph(e){if(!this.topDict.FDSelect)return null;switch(this.topDict.FDSelect.version){case 0:return this.topDict.FDSelect.fds[e];case 3:case 4:let{ranges:t}=this.topDict.FDSelect,r=0,n=t.length-1;for(;r<=n;){let a=r+n>>1;if(e<t[a].first)n=a-1;else if(a<n&&e>=t[a+1].first)r=a+1;else return t[a].fd}default:throw new Error("Unknown FDSelect version: "+this.topDict.FDSelect.version)}}privateDictForGlyph(e){if(this.topDict.FDSelect){let t=this.fdForGlyph(e);return this.topDict.FDArray[t]?this.topDict.FDArray[t].Private:null}return this.version<2?this.topDict.Private:this.topDict.FDArray[0].Private}constructor(e){this.stream=e,this.decode()}}var ra=cn;let Di=new v({glyphIndex:o,vertOriginY:x});var Bi=new v({majorVersion:o,minorVersion:o,defaultVertOriginY:x,numVertOriginYMetrics:o,metrics:new g(Di,"numVertOriginYMetrics")});let pt=new v({height:y,width:y,horiBearingX:Z,horiBearingY:Z,horiAdvance:y,vertBearingX:Z,vertBearingY:Z,vertAdvance:y}),yr=new v({height:y,width:y,bearingX:Z,bearingY:Z,advance:y}),na=new v({glyph:o,xOffset:Z,yOffset:Z});class aa{}class fn{}let cf=new re("version",{1:{metrics:yr,data:aa},2:{metrics:yr,data:fn},5:{data:fn},6:{metrics:pt,data:aa},7:{metrics:pt,data:fn},8:{metrics:yr,pad:new V(y),numComponents:o,components:new g(na,"numComponents")},9:{metrics:pt,pad:new V(y),numComponents:o,components:new g(na,"numComponents")},17:{metrics:yr,dataLen:p,data:new z("dataLen")},18:{metrics:pt,dataLen:p,data:new z("dataLen")},19:{dataLen:p,data:new z("dataLen")}}),sa=new v({ascender:Z,descender:Z,widthMax:y,caretSlopeNumerator:Z,caretSlopeDenominator:Z,caretOffset:Z,minOriginSB:Z,minAdvanceSB:Z,maxBeforeBL:Z,minAfterBL:Z,pad:new V(Z,2)}),Li=new v({glyphCode:o,offset:o}),Mi=new re(o,{header:{imageFormat:o,imageDataOffset:p},1:{offsetArray:new g(p,s=>s.parent.lastGlyphIndex-s.parent.firstGlyphIndex+1)},2:{imageSize:p,bigMetrics:pt},3:{offsetArray:new g(o,s=>s.parent.lastGlyphIndex-s.parent.firstGlyphIndex+1)},4:{numGlyphs:p,glyphArray:new g(Li,s=>s.numGlyphs+1)},5:{imageSize:p,bigMetrics:pt,numGlyphs:p,glyphCodeArray:new g(o,"numGlyphs")}}),Ri=new v({firstGlyphIndex:o,lastGlyphIndex:o,subtable:new b(p,Mi)}),Ni=new v({indexSubTableArray:new b(p,new g(Ri,1),{type:"parent"}),indexTablesSize:p,numberOfIndexSubTables:p,colorRef:p,hori:sa,vert:sa,startGlyphIndex:o,endGlyphIndex:o,ppemX:y,ppemY:y,bitDepth:y,flags:new O(y,["horizontal","vertical"])});var Ui=new v({version:p,numSizes:p,sizes:new g(Ni,"numSizes")});let Gi=new v({ppem:o,resolution:o,imageOffsets:new g(new b(p,"void"),s=>s.parent.parent.maxp.numGlyphs+1)});var Vi=new v({version:o,flags:new O(o,["renderOutlines"]),numImgTables:p,imageTables:new g(new b(p,Gi),"numImgTables")});let zi=new v({gid:o,paletteIndex:o}),$i=new v({gid:o,firstLayerIndex:o,numLayers:o});var Wi=new v({version:o,numBaseGlyphRecords:o,baseGlyphRecord:new b(p,new g($i,"numBaseGlyphRecords")),layerRecords:new b(p,new g(zi,"numLayerRecords"),{lazy:!0}),numLayerRecords:o});let qi=new v({blue:y,green:y,red:y,alpha:y});var Hi=new re(o,{header:{numPaletteEntries:o,numPalettes:o,numColorRecords:o,colorRecords:new b(p,new g(qi,"numColorRecords")),colorRecordIndices:new g(o,"numPalettes")},0:{},1:{offsetPaletteTypeArray:new b(p,new g(p,"numPalettes")),offsetPaletteLabelArray:new b(p,new g(o,"numPalettes")),offsetPaletteEntryLabelArray:new b(p,new g(o,"numPaletteEntries"))}});let Ht=new re(o,{1:{coordinate:x},2:{coordinate:x,referenceGlyph:o,baseCoordPoint:o},3:{coordinate:x,deviceTable:new b(o,R0)}}),ji=new v({defaultIndex:o,baseCoordCount:o,baseCoords:new g(new b(o,Ht),"baseCoordCount")}),Xi=new v({tag:new j(4),minCoord:new b(o,Ht,{type:"parent"}),maxCoord:new b(o,Ht,{type:"parent"})}),ia=new v({minCoord:new b(o,Ht),maxCoord:new b(o,Ht),featMinMaxCount:o,featMinMaxRecords:new g(Xi,"featMinMaxCount")}),Yi=new v({tag:new j(4),minMax:new b(o,ia,{type:"parent"})}),Zi=new v({baseValues:new b(o,ji),defaultMinMax:new b(o,ia),baseLangSysCount:o,baseLangSysRecords:new g(Yi,"baseLangSysCount")}),Ki=new v({tag:new j(4),script:new b(o,Zi,{type:"parent"})}),Ji=new g(Ki,o),_i=new g(new j(4),o),oa=new v({baseTagList:new b(o,_i),baseScriptList:new b(o,Ji)});var Qi=new re(p,{header:{horizAxis:new b(o,oa),vertAxis:new b(o,oa)},65536:{},65537:{itemVariationStore:new b(p,wr)}});let eo=new g(o,o),to=new v({coverage:new b(o,Ae),glyphCount:o,attachPoints:new g(new b(o,eo),"glyphCount")}),ro=new re(o,{1:{coordinate:x},2:{caretValuePoint:o},3:{coordinate:x,deviceTable:new b(o,R0)}}),no=new g(new b(o,ro),o),ao=new v({coverage:new b(o,Ae),ligGlyphCount:o,ligGlyphs:new g(new b(o,no),"ligGlyphCount")}),la=new v({markSetTableFormat:o,markSetCount:o,coverage:new g(new b(p,Ae),"markSetCount")});var so=new re(p,{header:{glyphClassDef:new b(o,M0),attachList:new b(o,to),ligCaretList:new b(o,ao),markAttachClassDef:new b(o,M0)},65536:{},65538:{markGlyphSetsDef:new b(o,la)},65539:{markGlyphSetsDef:new b(o,la),itemVariationStore:new b(p,wr)}});let bt=new O(o,["xPlacement","yPlacement","xAdvance","yAdvance","xPlaDevice","yPlaDevice","xAdvDevice","yAdvDevice"]),io={xPlacement:x,yPlacement:x,xAdvance:x,yAdvance:x,xPlaDevice:new b(o,R0,{type:"global",relativeTo:s=>s.rel}),yPlaDevice:new b(o,R0,{type:"global",relativeTo:s=>s.rel}),xAdvDevice:new b(o,R0,{type:"global",relativeTo:s=>s.rel}),yAdvDevice:new b(o,R0,{type:"global",relativeTo:s=>s.rel})};class gt{buildStruct(e){let t=e;for(;!t[this.key]&&t.parent;)t=t.parent;if(!t[this.key])return;let r={};r.rel=()=>t._startOffset;let n=t[this.key];for(let a in n)n[a]&&(r[a]=io[a]);return new v(r)}size(e,t){return this.buildStruct(t).size(e,t)}decode(e,t){let r=this.buildStruct(t).decode(e,t);return delete r.rel,r}constructor(e){e===void 0&&(e="valueFormat"),this.key=e}}let oo=new v({secondGlyph:o,value1:new gt("valueFormat1"),value2:new gt("valueFormat2")}),lo=new g(oo,o),uo=new v({value1:new gt("valueFormat1"),value2:new gt("valueFormat2")}),jt=new re(o,{1:{xCoordinate:x,yCoordinate:x},2:{xCoordinate:x,yCoordinate:x,anchorPoint:o},3:{xCoordinate:x,yCoordinate:x,xDeviceTable:new b(o,R0),yDeviceTable:new b(o,R0)}}),co=new v({entryAnchor:new b(o,jt,{type:"parent"}),exitAnchor:new b(o,jt,{type:"parent"})}),fo=new v({class:o,markAnchor:new b(o,jt,{type:"parent"})}),dn=new g(fo,o),ho=new g(new b(o,jt),s=>s.parent.classCount),ua=new g(ho,o),po=new g(new b(o,jt),s=>s.parent.parent.classCount),bo=new g(po,o),go=new g(new b(o,bo),o),Xt=new re("lookupType",{1:new re(o,{1:{coverage:new b(o,Ae),valueFormat:bt,value:new gt},2:{coverage:new b(o,Ae),valueFormat:bt,valueCount:o,values:new A(new gt,"valueCount")}}),2:new re(o,{1:{coverage:new b(o,Ae),valueFormat1:bt,valueFormat2:bt,pairSetCount:o,pairSets:new A(new b(o,lo),"pairSetCount")},2:{coverage:new b(o,Ae),valueFormat1:bt,valueFormat2:bt,classDef1:new b(o,M0),classDef2:new b(o,M0),class1Count:o,class2Count:o,classRecords:new A(new A(uo,"class2Count"),"class1Count")}}),3:{format:o,coverage:new b(o,Ae),entryExitCount:o,entryExitRecords:new g(co,"entryExitCount")},4:{format:o,markCoverage:new b(o,Ae),baseCoverage:new b(o,Ae),classCount:o,markArray:new b(o,dn),baseArray:new b(o,ua)},5:{format:o,markCoverage:new b(o,Ae),ligatureCoverage:new b(o,Ae),classCount:o,markArray:new b(o,dn),ligatureArray:new b(o,go)},6:{format:o,mark1Coverage:new b(o,Ae),mark2Coverage:new b(o,Ae),classCount:o,mark1Array:new b(o,dn),mark2Array:new b(o,ua)},7:Hn,8:Xn,9:{posFormat:o,lookupType:o,extension:new b(p,null)}});Xt.versions[9].extension.type=Xt;var vo=new re(p,{header:{scriptList:new b(o,$n),featureList:new b(o,qn),lookupList:new b(o,new xr(Xt))},65536:{},65537:{featureVariations:new b(p,Yn)}});let ca=new g(o,o),mo=ca,xo=new v({glyph:o,compCount:o,components:new g(o,s=>s.compCount-1)}),wo=new g(new b(o,xo),o),hn=new re("lookupType",{1:new re(o,{1:{coverage:new b(o,Ae),deltaGlyphID:x},2:{coverage:new b(o,Ae),glyphCount:o,substitute:new A(o,"glyphCount")}}),2:{substFormat:o,coverage:new b(o,Ae),count:o,sequences:new A(new b(o,ca),"count")},3:{substFormat:o,coverage:new b(o,Ae),count:o,alternateSet:new A(new b(o,mo),"count")},4:{substFormat:o,coverage:new b(o,Ae),count:o,ligatureSets:new A(new b(o,wo),"count")},5:Hn,6:Xn,7:{substFormat:o,lookupType:o,extension:new b(p,null)},8:{substFormat:o,coverage:new b(o,Ae),backtrackCoverage:new g(new b(o,Ae),"backtrackGlyphCount"),lookaheadGlyphCount:o,lookaheadCoverage:new g(new b(o,Ae),"lookaheadGlyphCount"),glyphCount:o,substitutes:new g(o,"glyphCount")}});hn.versions[7].extension.type=hn;var yo=new re(p,{header:{scriptList:new b(o,$n),featureList:new b(o,qn),lookupList:new b(o,new xr(hn))},65536:{},65537:{featureVariations:new b(p,Yn)}});let N0=new g(o,o),Co=new v({shrinkageEnableGSUB:new b(o,N0),shrinkageDisableGSUB:new b(o,N0),shrinkageEnableGPOS:new b(o,N0),shrinkageDisableGPOS:new b(o,N0),shrinkageJstfMax:new b(o,new xr(Xt)),extensionEnableGSUB:new b(o,N0),extensionDisableGSUB:new b(o,N0),extensionEnableGPOS:new b(o,N0),extensionDisableGPOS:new b(o,N0),extensionJstfMax:new b(o,new xr(Xt))}),fa=new g(new b(o,Co),o),So=new v({tag:new j(4),jstfLangSys:new b(o,fa)}),Ao=new v({extenderGlyphs:new b(o,new g(o,o)),defaultLangSys:new b(o,fa),langSysCount:o,langSysRecords:new g(So,"langSysCount")}),Io=new v({tag:new j(4),script:new b(o,Ao,{type:"parent"})});var Eo=new v({version:p,scriptCount:o,scriptList:new g(Io,"scriptCount")});class ko{decode(e,t){switch(this.size(0,t)){case 1:return e.readUInt8();case 2:return e.readUInt16BE();case 3:return e.readUInt24BE();case 4:return e.readUInt32BE()}}size(e,t){return r0(this._size,null,t)}constructor(e){this._size=e}}let Oo=new v({entry:new ko(s=>((s.parent.entryFormat&48)>>4)+1),outerIndex:s=>s.entry>>(s.parent.entryFormat&15)+1,innerIndex:s=>s.entry&(1<<(s.parent.entryFormat&15)+1)-1}),pn=new v({entryFormat:o,mapCount:o,mapData:new g(Oo,"mapCount")});var Po=new v({majorVersion:o,minorVersion:o,itemVariationStore:new b(p,wr),advanceWidthMapping:new b(p,pn),LSBMapping:new b(p,pn),RSBMapping:new b(p,pn)});let To=new v({format:p,length:p,offset:p}),Fo=new v({reserved:new V(o,2),cbSignature:p,signature:new z("cbSignature")});var Do=new v({ulVersion:p,usNumSigs:o,usFlag:o,signatures:new g(To,"usNumSigs"),signatureBlocks:new g(Fo,"usNumSigs")});let Bo=new v({rangeMaxPPEM:o,rangeGaspBehavior:new O(o,["grayscale","gridfit","symmetricSmoothing","symmetricGridfit"])});var Lo=new v({version:o,numRanges:o,gaspRanges:new g(Bo,"numRanges")});let Mo=new v({pixelSize:y,maximumWidth:y,widths:new g(y,s=>s.parent.parent.maxp.numGlyphs)});var Ro=new v({version:o,numRecords:x,sizeDeviceRecord:ae,records:new g(Mo,"numRecords")});let No=new v({left:o,right:o,value:x}),da=new v({firstGlyph:o,nGlyphs:o,offsets:new g(o,"nGlyphs"),max:s=>s.offsets.length&&Math.max.apply(Math,s.offsets)}),Uo=new v({off:s=>s._startOffset-s.parent.parent._startOffset,len:s=>((s.parent.leftTable.max-s.off)/s.parent.rowWidth+1)*(s.parent.rowWidth/2),values:new A(x,"len")}),ha=new re("format",{0:{nPairs:o,searchRange:o,entrySelector:o,rangeShift:o,pairs:new g(No,"nPairs")},2:{rowWidth:o,leftTable:new b(o,da,{type:"parent"}),rightTable:new b(o,da,{type:"parent"}),array:new b(o,Uo,{type:"parent"})},3:{glyphCount:o,kernValueCount:y,leftClassCount:y,rightClassCount:y,flags:y,kernValue:new g(x,"kernValueCount"),leftClass:new g(y,"glyphCount"),rightClass:new g(y,"glyphCount"),kernIndex:new g(y,s=>s.leftClassCount*s.rightClassCount)}}),pa=new re("version",{0:{subVersion:o,length:o,format:y,coverage:new O(y,["horizontal","minimum","crossStream","override"]),subtable:ha,padding:new V(y,s=>s.length-s._currentOffset)},1:{length:p,coverage:new O(y,[null,null,null,null,null,"variation","crossStream","vertical"]),format:y,tupleIndex:o,subtable:ha,padding:new V(y,s=>s.length-s._currentOffset)}});var Go=new re(o,{0:{nTables:o,tables:new g(pa,"nTables")},1:{reserved:new V(o),nTables:p,tables:new g(pa,"nTables")}}),Vo=new v({version:o,numGlyphs:o,yPels:new g(y,"numGlyphs")}),zo=new v({version:o,fontNumber:p,pitch:o,xHeight:o,style:o,typeFamily:o,capHeight:o,symbolSet:o,typeface:new j(16),characterComplement:new j(8),fileName:new j(6),strokeWeight:new j(1),widthType:new j(1),serifStyle:y,reserved:new V(y)});let $o=new v({bCharSet:y,xRatio:y,yStartRatio:y,yEndRatio:y}),Wo=new v({yPelHeight:o,yMax:x,yMin:x}),qo=new v({recs:o,startsz:y,endsz:y,entries:new g(Wo,"recs")});var Ho=new v({version:o,numRecs:o,numRatios:o,ratioRanges:new g($o,"numRatios"),offsets:new g(o,"numRatios"),groups:new g(qo,"numRecs")}),jo=new v({version:o,ascent:x,descent:x,lineGap:x,advanceHeightMax:x,minTopSideBearing:x,minBottomSideBearing:x,yMaxExtent:x,caretSlopeRise:x,caretSlopeRun:x,caretOffset:x,reserved:new V(x,4),metricDataFormat:x,numberOfMetrics:o});let Xo=new v({advance:o,bearing:x});var Yo=new v({metrics:new A(Xo,s=>s.parent.vhea.numberOfMetrics),bearings:new A(x,s=>s.parent.maxp.numGlyphs-s.parent.vhea.numberOfMetrics)});let ba=new u0(16,"BE",14),Zo=new v({fromCoord:ba,toCoord:ba}),Ko=new v({pairCount:o,correspondence:new g(Zo,"pairCount")});var Jo=new v({version:Le,axisCount:p,segment:new g(Ko,"axisCount")});class _o{getItem(e){if(this._items[e]==null){let t=this.stream.pos;this.stream.pos=this.base+this.type.size(null,this.parent)*e,this._items[e]=this.type.decode(this.stream,this.parent),this.stream.pos=t}return this._items[e]}inspect(){return"[UnboundedArray "+this.type.constructor.name+"]"}constructor(e,t,r){this.type=e,this.stream=t,this.parent=r,this.base=this.stream.pos,this._items=[]}}class m0 extends g{decode(e,t){return new _o(this.type,e,t)}constructor(e){super(e,0)}}let U0=function(s){s===void 0&&(s=o);class e{decode(l,u){return u=u.parent.parent,this.type.decode(l,u)}size(l,u){return u=u.parent.parent,this.type.size(l,u)}encode(l,u,c){return c=c.parent.parent,this.type.encode(l,u,c)}constructor(l){this.type=l}}s=new e(s);let t=new v({unitSize:o,nUnits:o,searchRange:o,entrySelector:o,rangeShift:o}),r=new v({lastGlyph:o,firstGlyph:o,value:s}),n=new v({lastGlyph:o,firstGlyph:o,values:new b(o,new g(s,i=>i.lastGlyph-i.firstGlyph+1),{type:"parent"})}),a=new v({glyph:o,value:s});return new re(o,{0:{values:new m0(s)},2:{binarySearchHeader:t,segments:new g(r,i=>i.binarySearchHeader.nUnits)},4:{binarySearchHeader:t,segments:new g(n,i=>i.binarySearchHeader.nUnits)},6:{binarySearchHeader:t,segments:new g(a,i=>i.binarySearchHeader.nUnits)},8:{firstGlyph:o,count:o,values:new g(s,"count")}})};function Cr(s,e){s===void 0&&(s={}),e===void 0&&(e=o);let t=Object.assign({newState:o,flags:o},s),r=new v(t),n=new m0(new g(o,i=>i.nClasses));return new v({nClasses:p,classTable:new b(p,new U0(e)),stateArray:new b(p,n),entryTable:new b(p,new m0(r))})}function Qo(s,e){s===void 0&&(s={}),e===void 0&&(e=o);let t=new v({version(){return 8},firstGlyph:o,values:new g(y,o)}),r=Object.assign({newStateOffset:o,newState:l=>(l.newStateOffset-(l.parent.stateArray.base-l.parent._startOffset))/l.parent.nClasses,flags:o},s),n=new v(r),a=new m0(new g(y,l=>l.nClasses));return new v({nClasses:o,classTable:new b(o,t),stateArray:new b(o,a),entryTable:new b(o,new m0(n))})}let el=new re("format",{0:{deltas:new g(x,32)},1:{deltas:new g(x,32),mappingData:new U0(o)},2:{standardGlyph:o,controlPoints:new g(o,32)},3:{standardGlyph:o,controlPoints:new g(o,32),mappingData:new U0(o)}});var tl=new v({version:Le,format:o,defaultBaseline:o,subtable:el});let rl=new v({setting:o,nameIndex:x,name:s=>s.parent.parent.parent.name.records.fontFeatures[s.nameIndex]}),nl=new v({feature:o,nSettings:o,settingTable:new b(p,new g(rl,"nSettings"),{type:"parent"}),featureFlags:new O(y,[null,null,null,null,null,null,"hasDefault","exclusive"]),defaultSetting:y,nameIndex:x,name:s=>s.parent.parent.name.records.fontFeatures[s.nameIndex]});var al=new v({version:Le,featureNameCount:o,reserved1:new V(o),reserved2:new V(p),featureNames:new g(nl,"featureNameCount")});let sl=new v({axisTag:new j(4),minValue:Le,defaultValue:Le,maxValue:Le,flags:o,nameID:o,name:s=>s.parent.parent.name.records.fontFeatures[s.nameID]}),il=new v({nameID:o,name:s=>s.parent.parent.name.records.fontFeatures[s.nameID],flags:o,coord:new g(Le,s=>s.parent.axisCount),postscriptNameID:new K(o,s=>s.parent.instanceSize-s._currentOffset>0)});var ol=new v({version:Le,offsetToData:o,countSizePairs:o,axisCount:o,axisSize:o,instanceCount:o,instanceSize:o,axis:new g(sl,"axisCount"),instance:new g(il,"instanceCount")});let ll=new u0(16,"BE",14);class ul{static decode(e,t){return t.flags?e.readUInt32BE():e.readUInt16BE()*2}}var cl=new v({version:o,reserved:new V(o),axisCount:o,globalCoordCount:o,globalCoords:new b(p,new g(new g(ll,"axisCount"),"globalCoordCount")),glyphCount:o,flags:o,offsetToData:p,offsets:new g(new b(ul,"void",{relativeTo:s=>s.offsetToData,allowNull:!1}),s=>s.glyphCount+1)});let fl=new v({length:o,coverage:o,subFeatureFlags:p,stateTable:new Qo}),dl=new v({justClass:p,beforeGrowLimit:Le,beforeShrinkLimit:Le,afterGrowLimit:Le,afterShrinkLimit:Le,growFlags:o,shrinkFlags:o}),hl=new g(dl,p),pl=new re("actionType",{0:{lowerLimit:Le,upperLimit:Le,order:o,glyphs:new g(o,o)},1:{addGlyph:o},2:{substThreshold:Le,addGlyph:o,substGlyph:o},3:{},4:{variationAxis:p,minimumLimit:Le,noStretchValue:Le,maximumLimit:Le},5:{flags:o,glyph:o}}),bl=new v({actionClass:o,actionType:o,actionLength:p,actionData:pl,padding:new V(y,s=>s.actionLength-s._currentOffset)}),gl=new g(bl,p),vl=new v({lookupTable:new U0(new b(o,gl))}),ga=new v({classTable:new b(o,fl,{type:"parent"}),wdcOffset:o,postCompensationTable:new b(o,vl,{type:"parent"}),widthDeltaClusters:new U0(new b(o,hl,{type:"parent",relativeTo:s=>s.wdcOffset}))});var ml=new v({version:p,format:o,horizontal:new b(o,ga),vertical:new b(o,ga)});let xl={action:o},wl={markIndex:o,currentIndex:o},yl={currentInsertIndex:o,markedInsertIndex:o},Cl=new v({items:new m0(new b(p,new U0))}),Sl=new re("type",{0:{stateTable:new Cr},1:{stateTable:new Cr(wl),substitutionTable:new b(p,Cl)},2:{stateTable:new Cr(xl),ligatureActions:new b(p,new m0(p)),components:new b(p,new m0(o)),ligatureList:new b(p,new m0(o))},4:{lookupTable:new U0},5:{stateTable:new Cr(yl),insertionActions:new b(p,new m0(o))}}),Al=new v({length:p,coverage:ne,type:y,subFeatureFlags:p,table:Sl,padding:new V(y,s=>s.length-s._currentOffset)}),Il=new v({featureType:o,featureSetting:o,enableFlags:p,disableFlags:p}),El=new v({defaultFlags:p,chainLength:p,nFeatureEntries:p,nSubtables:p,features:new g(Il,"nFeatureEntries"),subtables:new g(Al,"nSubtables")});var kl=new v({version:o,unused:new V(o),nChains:p,chains:new g(El,"nChains")});let Ol=new v({left:x,top:x,right:x,bottom:x});var Pl=new v({version:Le,format:o,lookupTable:new U0(Ol)});let le={};var Sr=le;le.cmap=Ps,le.head=Ts,le.hhea=Fs,le.hmtx=Bs,le.maxp=Ls,le.name=zs,le["OS/2"]=$s,le.post=Ws,le.fpgm=Hs,le.loca=js,le.prep=Xs,le["cvt "]=qs,le.glyf=Ys,le["CFF "]=ra,le.CFF2=ra,le.VORG=Bi,le.EBLC=Ui,le.CBLC=le.EBLC,le.sbix=Vi,le.COLR=Wi,le.CPAL=Hi,le.BASE=Qi,le.GDEF=so,le.GPOS=vo,le.GSUB=yo,le.JSTF=Eo,le.HVAR=Po,le.DSIG=Do,le.gasp=Lo,le.hdmx=Ro,le.kern=Go,le.LTSH=Vo,le.PCLT=zo,le.VDMX=Ho,le.vhea=jo,le.vmtx=Yo,le.avar=Jo,le.bsln=tl,le.feat=al,le.fvar=ol,le.gvar=cl,le.just=ml,le.morx=kl,le.opbd=Pl;let Tl=new v({tag:new j(4),checkSum:p,offset:new b(p,"void",{type:"global"}),length:p}),bn=new v({tag:new j(4),numTables:o,searchRange:o,entrySelector:o,rangeShift:o,tables:new g(Tl,"numTables")});bn.process=function(){let s={};for(let e of this.tables)s[e.tag]=e;this.tables=s},bn.preEncode=function(){if(!Array.isArray(this.tables)){let t=[];for(let r in this.tables){let n=this.tables[r];n&&t.push({tag:r,checkSum:0,offset:new qe(Sr[r],n),length:Sr[r].size(n)})}this.tables=t}this.tag="true",this.numTables=this.tables.length;let s=Math.floor(Math.log(this.numTables)/Math.LN2),e=Math.pow(2,s);this.searchRange=e*16,this.entrySelector=Math.log(e)/Math.LN2,this.rangeShift=this.numTables*16-this.searchRange};var va=bn;function Ar(s,e){let t=0,r=s.length-1;for(;t<=r;){let n=t+r>>1,a=e(s[n]);if(a<0)r=n-1;else if(a>0)t=n+1;else return n}return-1}function vt(s,e){let t=[];for(;s<e;)t.push(s++);return t}const Ir=new TextDecoder("ascii"),ma="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Yt=new Uint8Array(256);for(let s=0;s<ma.length;s++)Yt[ma.charCodeAt(s)]=s;function gn(s){let e=s.length*.75;s[s.length-1]==="="&&(e--,s[s.length-2]==="="&&e--);let t=new Uint8Array(e),r=0;for(let n=0,a=s.length;n<a;n+=4){let i=Yt[s.charCodeAt(n)],l=Yt[s.charCodeAt(n+1)],u=Yt[s.charCodeAt(n+2)],c=Yt[s.charCodeAt(n+3)];t[r++]=i<<2|l>>4,t[r++]=(l&15)<<4|u>>2,t[r++]=(u&3)<<6|c&63}return t}class vn{findSubtable(e,t){for(let[r,n]of t)for(let a of e.tables)if(a.platformID===r&&a.encodingID===n)return a.table;return null}lookup(e,t){if(this.encoding)e=this.encoding.get(e)||e;else if(t){let n=this.getVariationSelector(e,t);if(n)return n}let r=this.cmap;switch(r.version){case 0:return r.codeMap.get(e)||0;case 4:{let n=0,a=r.segCount-1;for(;n<=a;){let i=n+a>>1;if(e<r.startCode.get(i))a=i-1;else if(e>r.endCode.get(i))n=i+1;else{let l=r.idRangeOffset.get(i),u;if(l===0)u=e+r.idDelta.get(i);else{let c=l/2+(e-r.startCode.get(i))-(r.segCount-i);u=r.glyphIndexArray.get(c)||0,u!==0&&(u+=r.idDelta.get(i))}return u&65535}}return 0}case 8:throw new Error("TODO: cmap format 8");case 6:case 10:return r.glyphIndices.get(e-r.firstCode)||0;case 12:case 13:{let n=0,a=r.nGroups-1;for(;n<=a;){let i=n+a>>1,l=r.groups.get(i);if(e<l.startCharCode)a=i-1;else if(e>l.endCharCode)n=i+1;else return r.version===12?l.glyphID+(e-l.startCharCode):l.glyphID}return 0}case 14:throw new Error("TODO: cmap format 14");default:throw new Error("Unknown cmap format "+r.version)}}getVariationSelector(e,t){if(!this.uvs)return 0;let r=this.uvs.varSelectors.toArray(),n=Ar(r,i=>t-i.varSelector),a=r[n];return n!==-1&&a.defaultUVS&&(n=Ar(a.defaultUVS,i=>e<i.startUnicodeValue?-1:e>i.startUnicodeValue+i.additionalCount?1:0)),n!==-1&&a.nonDefaultUVS&&(n=Ar(a.nonDefaultUVS,i=>e-i.unicodeValue),n!==-1)?a.nonDefaultUVS[n].glyphID:0}getCharacterSet(){let e=this.cmap;switch(e.version){case 0:return vt(0,e.codeMap.length);case 4:{let t=[],r=e.endCode.toArray();for(let n=0;n<r.length;n++){let a=r[n]+1,i=e.startCode.get(n);t.push(...vt(i,a))}return t}case 8:throw new Error("TODO: cmap format 8");case 6:case 10:return vt(e.firstCode,e.firstCode+e.glyphIndices.length);case 12:case 13:{let t=[];for(let r of e.groups.toArray())t.push(...vt(r.startCharCode,r.endCharCode+1));return t}case 14:throw new Error("TODO: cmap format 14");default:throw new Error("Unknown cmap format "+e.version)}}codePointsForGlyph(e){let t=this.cmap;switch(t.version){case 0:{let n=[];for(let a=0;a<256;a++)t.codeMap.get(a)===e&&n.push(a);return n}case 4:{let n=[];for(let a=0;a<t.segCount;a++){let i=t.endCode.get(a),l=t.startCode.get(a),u=t.idRangeOffset.get(a),c=t.idDelta.get(a);for(var r=l;r<=i;r++){let f=0;if(u===0)f=r+c;else{let d=u/2+(r-l)-(t.segCount-a);f=t.glyphIndexArray.get(d)||0,f!==0&&(f+=c)}f===e&&n.push(r)}}return n}case 12:{let n=[];for(let a of t.groups.toArray())e>=a.glyphID&&e<=a.glyphID+(a.endCharCode-a.startCharCode)&&n.push(a.startCharCode+(e-a.glyphID));return n}case 13:{let n=[];for(let a of t.groups.toArray())e===a.glyphID&&n.push(...vt(a.startCharCode,a.endCharCode+1));return n}default:throw new Error("Unknown cmap format "+t.version)}}constructor(e){if(this.encoding=null,this.cmap=this.findSubtable(e,[[3,10],[0,6],[0,4],[3,1],[0,3],[0,2],[0,1],[0,0]]),!this.cmap)for(let t of e.tables){let r=Dn(t.platformID,t.encodingID,t.table.language-1),n=Ns(r);n&&(this.cmap=t.table,this.encoding=n)}if(!this.cmap)throw new Error("Could not find a supported cmap table");this.uvs=this.findSubtable(e,[[0,5]]),this.uvs&&this.uvs.version!==14&&(this.uvs=null)}}Me([He],vn.prototype,"getCharacterSet",null),Me([He],vn.prototype,"codePointsForGlyph",null);class Fl{process(e,t){for(let r=0;r<e.length-1;r++){let n=e[r].id,a=e[r+1].id;t[r].xAdvance+=this.getKerning(n,a)}}getKerning(e,t){let r=0;for(let n of this.kern.tables){if(n.coverage.crossStream)continue;switch(n.version){case 0:if(!n.coverage.horizontal)continue;break;case 1:if(n.coverage.vertical||n.coverage.variation)continue;break;default:throw new Error("Unsupported kerning table version "+n.version)}let a=0,i=n.subtable;switch(n.format){case 0:let l=Ar(i.pairs,function(d){return e-d.left||t-d.right});l>=0&&(a=i.pairs[l].value);break;case 2:let u=0,c=0;e>=i.leftTable.firstGlyph&&e<i.leftTable.firstGlyph+i.leftTable.nGlyphs?u=i.leftTable.offsets[e-i.leftTable.firstGlyph]:u=i.array.off,t>=i.rightTable.firstGlyph&&t<i.rightTable.firstGlyph+i.rightTable.nGlyphs&&(c=i.rightTable.offsets[t-i.rightTable.firstGlyph]);let f=(u+c-i.array.off)/2;a=i.array.values.get(f);break;case 3:if(e>=i.glyphCount||t>=i.glyphCount)return 0;a=i.kernValue[i.kernIndex[i.leftClass[e]*i.rightClassCount+i.rightClass[t]]];break;default:throw new Error("Unsupported kerning sub-table format "+n.format)}n.coverage.override?r=a:r+=a}return r}constructor(e){this.kern=e.kern}}class Dl{positionGlyphs(e,t){let r=0,n=0;for(let a=0;a<e.length;a++)e[a].isMark?n=a:(r!==n&&this.positionCluster(e,t,r,n),r=n=a);return r!==n&&this.positionCluster(e,t,r,n),t}positionCluster(e,t,r,n){let a=e[r],i=a.cbox.copy();a.codePoints.length>1&&(i.minX+=(a.codePoints.length-1)*i.width/a.codePoints.length);let l=-t[r].xAdvance,u=0,c=this.font.unitsPerEm/16;for(let f=r+1;f<=n;f++){let d=e[f],C=d.cbox,k=t[f],B=this.getCombiningClass(d.codePoints[0]);if(B!=="Not_Reordered"){switch(k.xOffset=k.yOffset=0,B){case"Double_Above":case"Double_Below":k.xOffset+=i.minX-C.width/2-C.minX;break;case"Attached_Below_Left":case"Below_Left":case"Above_Left":k.xOffset+=i.minX-C.minX;break;case"Attached_Above_Right":case"Below_Right":case"Above_Right":k.xOffset+=i.maxX-C.width-C.minX;break;default:k.xOffset+=i.minX+(i.width-C.width)/2-C.minX}switch(B){case"Double_Below":case"Below_Left":case"Below":case"Below_Right":case"Attached_Below_Left":case"Attached_Below":(B==="Attached_Below_Left"||B==="Attached_Below")&&(i.minY+=c),k.yOffset=-i.minY-C.maxY,i.minY+=C.height;break;case"Double_Above":case"Above_Left":case"Above":case"Above_Right":case"Attached_Above":case"Attached_Above_Right":(B==="Attached_Above"||B==="Attached_Above_Right")&&(i.maxY+=c),k.yOffset=i.maxY-C.minY,i.maxY+=C.height;break}k.xAdvance=k.yAdvance=0,k.xOffset+=l,k.yOffset+=u}else l-=k.xAdvance,u-=k.yAdvance}}getCombiningClass(e){let t=On(e);if((e&-256)===3584){if(t==="Not_Reordered")switch(e){case 3633:case 3636:case 3637:case 3638:case 3639:case 3655:case 3660:case 3645:case 3662:return"Above_Right";case 3761:case 3764:case 3765:case 3766:case 3767:case 3771:case 3788:case 3789:return"Above";case 3772:return"Below"}else if(e===3642)return"Below_Right"}switch(t){case"CCC10":case"CCC11":case"CCC12":case"CCC13":case"CCC14":case"CCC15":case"CCC16":case"CCC17":case"CCC18":case"CCC20":case"CCC22":return"Below";case"CCC23":return"Attached_Above";case"CCC24":return"Above_Right";case"CCC25":case"CCC19":return"Above_Left";case"CCC26":return"Above";case"CCC21":break;case"CCC27":case"CCC28":case"CCC30":case"CCC31":case"CCC33":case"CCC34":case"CCC35":case"CCC36":return"Above";case"CCC29":case"CCC32":return"Below";case"CCC103":return"Below_Right";case"CCC107":return"Above_Right";case"CCC118":return"Below";case"CCC122":return"Above";case"CCC129":case"CCC132":return"Below";case"CCC130":return"Above"}return t}constructor(e){this.font=e}}class G0{get width(){return this.maxX-this.minX}get height(){return this.maxY-this.minY}addPoint(e,t){Math.abs(e)!==1/0&&(e<this.minX&&(this.minX=e),e>this.maxX&&(this.maxX=e)),Math.abs(t)!==1/0&&(t<this.minY&&(this.minY=t),t>this.maxY&&(this.maxY=t))}copy(){return new G0(this.minX,this.minY,this.maxX,this.maxY)}constructor(e,t,r,n){e===void 0&&(e=1/0),t===void 0&&(t=1/0),r===void 0&&(r=-1/0),n===void 0&&(n=-1/0),this.minX=e,this.minY=t,this.maxX=r,this.maxY=n}}const K0={Caucasian_Albanian:"aghb",Arabic:"arab",Imperial_Aramaic:"armi",Armenian:"armn",Avestan:"avst",Balinese:"bali",Bamum:"bamu",Bassa_Vah:"bass",Batak:"batk",Bengali:["bng2","beng"],Bopomofo:"bopo",Brahmi:"brah",Braille:"brai",Buginese:"bugi",Buhid:"buhd",Chakma:"cakm",Canadian_Aboriginal:"cans",Carian:"cari",Cham:"cham",Cherokee:"cher",Coptic:"copt",Cypriot:"cprt",Cyrillic:"cyrl",Devanagari:["dev2","deva"],Deseret:"dsrt",Duployan:"dupl",Egyptian_Hieroglyphs:"egyp",Elbasan:"elba",Ethiopic:"ethi",Georgian:"geor",Glagolitic:"glag",Gothic:"goth",Grantha:"gran",Greek:"grek",Gujarati:["gjr2","gujr"],Gurmukhi:["gur2","guru"],Hangul:"hang",Han:"hani",Hanunoo:"hano",Hebrew:"hebr",Hiragana:"hira",Pahawh_Hmong:"hmng",Katakana_Or_Hiragana:"hrkt",Old_Italic:"ital",Javanese:"java",Kayah_Li:"kali",Katakana:"kana",Kharoshthi:"khar",Khmer:"khmr",Khojki:"khoj",Kannada:["knd2","knda"],Kaithi:"kthi",Tai_Tham:"lana",Lao:"lao ",Latin:"latn",Lepcha:"lepc",Limbu:"limb",Linear_A:"lina",Linear_B:"linb",Lisu:"lisu",Lycian:"lyci",Lydian:"lydi",Mahajani:"mahj",Mandaic:"mand",Manichaean:"mani",Mende_Kikakui:"mend",Meroitic_Cursive:"merc",Meroitic_Hieroglyphs:"mero",Malayalam:["mlm2","mlym"],Modi:"modi",Mongolian:"mong",Mro:"mroo",Meetei_Mayek:"mtei",Myanmar:["mym2","mymr"],Old_North_Arabian:"narb",Nabataean:"nbat",Nko:"nko ",Ogham:"ogam",Ol_Chiki:"olck",Old_Turkic:"orkh",Oriya:["ory2","orya"],Osmanya:"osma",Palmyrene:"palm",Pau_Cin_Hau:"pauc",Old_Permic:"perm",Phags_Pa:"phag",Inscriptional_Pahlavi:"phli",Psalter_Pahlavi:"phlp",Phoenician:"phnx",Miao:"plrd",Inscriptional_Parthian:"prti",Rejang:"rjng",Runic:"runr",Samaritan:"samr",Old_South_Arabian:"sarb",Saurashtra:"saur",Shavian:"shaw",Sharada:"shrd",Siddham:"sidd",Khudawadi:"sind",Sinhala:"sinh",Sora_Sompeng:"sora",Sundanese:"sund",Syloti_Nagri:"sylo",Syriac:"syrc",Tagbanwa:"tagb",Takri:"takr",Tai_Le:"tale",New_Tai_Lue:"talu",Tamil:["tml2","taml"],Tai_Viet:"tavt",Telugu:["tel2","telu"],Tifinagh:"tfng",Tagalog:"tglg",Thaana:"thaa",Thai:"thai",Tibetan:"tibt",Tirhuta:"tirh",Ugaritic:"ugar",Vai:"vai ",Warang_Citi:"wara",Old_Persian:"xpeo",Cuneiform:"xsux",Yi:"yi  ",Inherited:"zinh",Common:"zyyy",Unknown:"zzzz"},mn={};for(let s in K0){let e=K0[s];if(Array.isArray(e))for(let t of e)mn[t]=s;else mn[e]=s}function df(s){return K0[s]}function Bl(s){return mn[s]}function Ll(s){let e=s.length,t=0;for(;t<e;){let r=s.charCodeAt(t++);if(55296<=r&&r<=56319&&t<e){let a=s.charCodeAt(t);56320<=a&&a<=57343&&(t++,r=((r&1023)<<10)+(a&1023)+65536)}let n=Kr(r);if(n!=="Common"&&n!=="Inherited"&&n!=="Unknown")return K0[n]}return K0.Unknown}function Ml(s){for(let e=0;e<s.length;e++){let t=s[e],r=Kr(t);if(r!=="Common"&&r!=="Inherited"&&r!=="Unknown")return K0[r]}return K0.Unknown}const Rl={arab:!0,hebr:!0,syrc:!0,thaa:!0,cprt:!0,khar:!0,phnx:!0,"nko ":!0,lydi:!0,avst:!0,armi:!0,phli:!0,prti:!0,sarb:!0,orkh:!0,samr:!0,mand:!0,merc:!0,mero:!0,mani:!0,mend:!0,nbat:!0,narb:!0,palm:!0,phlp:!0};function xa(s){return Rl[s]?"rtl":"ltr"}class Nl{get advanceWidth(){let e=0;for(let t of this.positions)e+=t.xAdvance;return e}get advanceHeight(){let e=0;for(let t of this.positions)e+=t.yAdvance;return e}get bbox(){let e=new G0,t=0,r=0;for(let n=0;n<this.glyphs.length;n++){let a=this.glyphs[n],i=this.positions[n],l=a.bbox;e.addPoint(l.minX+t+i.xOffset,l.minY+r+i.yOffset),e.addPoint(l.maxX+t+i.xOffset,l.maxY+r+i.yOffset),t+=i.xAdvance,r+=i.yAdvance}return e}constructor(e,t,r,n,a){if(this.glyphs=e,this.positions=null,this.script=r,this.language=n||null,this.direction=a||xa(r),this.features={},Array.isArray(t))for(let i of t)this.features[i]=!0;else typeof t=="object"&&(this.features=t)}}class Ul{constructor(e,t,r,n){e===void 0&&(e=0),t===void 0&&(t=0),r===void 0&&(r=0),n===void 0&&(n=0),this.xAdvance=e,this.yAdvance=t,this.xOffset=r,this.yOffset=n}}const J0={allTypographicFeatures:{code:0,exclusive:!1,allTypeFeatures:0},ligatures:{code:1,exclusive:!1,requiredLigatures:0,commonLigatures:2,rareLigatures:4,rebusPictures:8,diphthongLigatures:10,squaredLigatures:12,abbrevSquaredLigatures:14,symbolLigatures:16,contextualLigatures:18,historicalLigatures:20},cursiveConnection:{code:2,exclusive:!0,unconnected:0,partiallyConnected:1,cursive:2},letterCase:{code:3,exclusive:!0},verticalSubstitution:{code:4,exclusive:!1,substituteVerticalForms:0},linguisticRearrangement:{code:5,exclusive:!1,linguisticRearrangement:0},numberSpacing:{code:6,exclusive:!0,monospacedNumbers:0,proportionalNumbers:1,thirdWidthNumbers:2,quarterWidthNumbers:3},smartSwash:{code:8,exclusive:!1,wordInitialSwashes:0,wordFinalSwashes:2,nonFinalSwashes:8},diacritics:{code:9,exclusive:!0,showDiacritics:0,hideDiacritics:1,decomposeDiacritics:2},verticalPosition:{code:10,exclusive:!0,normalPosition:0,superiors:1,inferiors:2,ordinals:3,scientificInferiors:4},fractions:{code:11,exclusive:!0,noFractions:0,verticalFractions:1,diagonalFractions:2},overlappingCharacters:{code:13,exclusive:!1,preventOverlap:0},typographicExtras:{code:14,exclusive:!1,slashedZero:4},mathematicalExtras:{code:15,exclusive:!1,mathematicalGreek:10},ornamentSets:{code:16,exclusive:!0,noOrnaments:0,dingbats:1,piCharacters:2,fleurons:3,decorativeBorders:4,internationalSymbols:5,mathSymbols:6},characterAlternatives:{code:17,exclusive:!0,noAlternates:0},designComplexity:{code:18,exclusive:!0,designLevel1:0,designLevel2:1,designLevel3:2,designLevel4:3,designLevel5:4},styleOptions:{code:19,exclusive:!0,noStyleOptions:0,displayText:1,engravedText:2,illuminatedCaps:3,titlingCaps:4,tallCaps:5},characterShape:{code:20,exclusive:!0,traditionalCharacters:0,simplifiedCharacters:1,JIS1978Characters:2,JIS1983Characters:3,JIS1990Characters:4,traditionalAltOne:5,traditionalAltTwo:6,traditionalAltThree:7,traditionalAltFour:8,traditionalAltFive:9,expertCharacters:10,JIS2004Characters:11,hojoCharacters:12,NLCCharacters:13,traditionalNamesCharacters:14},numberCase:{code:21,exclusive:!0,lowerCaseNumbers:0,upperCaseNumbers:1},textSpacing:{code:22,exclusive:!0,proportionalText:0,monospacedText:1,halfWidthText:2,thirdWidthText:3,quarterWidthText:4,altProportionalText:5,altHalfWidthText:6},transliteration:{code:23,exclusive:!0,noTransliteration:0},annotation:{code:24,exclusive:!0,noAnnotation:0,boxAnnotation:1,roundedBoxAnnotation:2,circleAnnotation:3,invertedCircleAnnotation:4,parenthesisAnnotation:5,periodAnnotation:6,romanNumeralAnnotation:7,diamondAnnotation:8,invertedBoxAnnotation:9,invertedRoundedBoxAnnotation:10},kanaSpacing:{code:25,exclusive:!0,fullWidthKana:0,proportionalKana:1},ideographicSpacing:{code:26,exclusive:!0,fullWidthIdeographs:0,proportionalIdeographs:1,halfWidthIdeographs:2},unicodeDecomposition:{code:27,exclusive:!1,canonicalComposition:0,compatibilityComposition:2,transcodingComposition:4},rubyKana:{code:28,exclusive:!1,rubyKana:2},CJKSymbolAlternatives:{code:29,exclusive:!0,noCJKSymbolAlternatives:0,CJKSymbolAltOne:1,CJKSymbolAltTwo:2,CJKSymbolAltThree:3,CJKSymbolAltFour:4,CJKSymbolAltFive:5},ideographicAlternatives:{code:30,exclusive:!0,noIdeographicAlternatives:0,ideographicAltOne:1,ideographicAltTwo:2,ideographicAltThree:3,ideographicAltFour:4,ideographicAltFive:5},CJKVerticalRomanPlacement:{code:31,exclusive:!0,CJKVerticalRomanCentered:0,CJKVerticalRomanHBaseline:1},italicCJKRoman:{code:32,exclusive:!1,CJKItalicRoman:2},caseSensitiveLayout:{code:33,exclusive:!1,caseSensitiveLayout:0,caseSensitiveSpacing:2},alternateKana:{code:34,exclusive:!1,alternateHorizKana:0,alternateVertKana:2},stylisticAlternatives:{code:35,exclusive:!1,noStylisticAlternates:0,stylisticAltOne:2,stylisticAltTwo:4,stylisticAltThree:6,stylisticAltFour:8,stylisticAltFive:10,stylisticAltSix:12,stylisticAltSeven:14,stylisticAltEight:16,stylisticAltNine:18,stylisticAltTen:20,stylisticAltEleven:22,stylisticAltTwelve:24,stylisticAltThirteen:26,stylisticAltFourteen:28,stylisticAltFifteen:30,stylisticAltSixteen:32,stylisticAltSeventeen:34,stylisticAltEighteen:36,stylisticAltNineteen:38,stylisticAltTwenty:40},contextualAlternates:{code:36,exclusive:!1,contextualAlternates:0,swashAlternates:2,contextualSwashAlternates:4},lowerCase:{code:37,exclusive:!0,defaultLowerCase:0,lowerCaseSmallCaps:1,lowerCasePetiteCaps:2},upperCase:{code:38,exclusive:!0,defaultUpperCase:0,upperCaseSmallCaps:1,upperCasePetiteCaps:2},languageTag:{code:39,exclusive:!0},CJKRomanSpacing:{code:103,exclusive:!0,halfWidthCJKRoman:0,proportionalCJKRoman:1,defaultCJKRoman:2,fullWidthCJKRoman:3}},F=(s,e)=>[J0[s].code,J0[s][e]],Er={rlig:F("ligatures","requiredLigatures"),clig:F("ligatures","contextualLigatures"),dlig:F("ligatures","rareLigatures"),hlig:F("ligatures","historicalLigatures"),liga:F("ligatures","commonLigatures"),hist:F("ligatures","historicalLigatures"),smcp:F("lowerCase","lowerCaseSmallCaps"),pcap:F("lowerCase","lowerCasePetiteCaps"),frac:F("fractions","diagonalFractions"),dnom:F("fractions","diagonalFractions"),numr:F("fractions","diagonalFractions"),afrc:F("fractions","verticalFractions"),case:F("caseSensitiveLayout","caseSensitiveLayout"),ccmp:F("unicodeDecomposition","canonicalComposition"),cpct:F("CJKVerticalRomanPlacement","CJKVerticalRomanCentered"),valt:F("CJKVerticalRomanPlacement","CJKVerticalRomanCentered"),swsh:F("contextualAlternates","swashAlternates"),cswh:F("contextualAlternates","contextualSwashAlternates"),curs:F("cursiveConnection","cursive"),c2pc:F("upperCase","upperCasePetiteCaps"),c2sc:F("upperCase","upperCaseSmallCaps"),init:F("smartSwash","wordInitialSwashes"),fin2:F("smartSwash","wordFinalSwashes"),medi:F("smartSwash","nonFinalSwashes"),med2:F("smartSwash","nonFinalSwashes"),fin3:F("smartSwash","wordFinalSwashes"),fina:F("smartSwash","wordFinalSwashes"),pkna:F("kanaSpacing","proportionalKana"),half:F("textSpacing","halfWidthText"),halt:F("textSpacing","altHalfWidthText"),hkna:F("alternateKana","alternateHorizKana"),vkna:F("alternateKana","alternateVertKana"),ital:F("italicCJKRoman","CJKItalicRoman"),lnum:F("numberCase","upperCaseNumbers"),onum:F("numberCase","lowerCaseNumbers"),mgrk:F("mathematicalExtras","mathematicalGreek"),calt:F("contextualAlternates","contextualAlternates"),vrt2:F("verticalSubstitution","substituteVerticalForms"),vert:F("verticalSubstitution","substituteVerticalForms"),tnum:F("numberSpacing","monospacedNumbers"),pnum:F("numberSpacing","proportionalNumbers"),sups:F("verticalPosition","superiors"),subs:F("verticalPosition","inferiors"),ordn:F("verticalPosition","ordinals"),pwid:F("textSpacing","proportionalText"),hwid:F("textSpacing","halfWidthText"),qwid:F("textSpacing","quarterWidthText"),twid:F("textSpacing","thirdWidthText"),fwid:F("textSpacing","proportionalText"),palt:F("textSpacing","altProportionalText"),trad:F("characterShape","traditionalCharacters"),smpl:F("characterShape","simplifiedCharacters"),jp78:F("characterShape","JIS1978Characters"),jp83:F("characterShape","JIS1983Characters"),jp90:F("characterShape","JIS1990Characters"),jp04:F("characterShape","JIS2004Characters"),expt:F("characterShape","expertCharacters"),hojo:F("characterShape","hojoCharacters"),nlck:F("characterShape","NLCCharacters"),tnam:F("characterShape","traditionalNamesCharacters"),ruby:F("rubyKana","rubyKana"),titl:F("styleOptions","titlingCaps"),zero:F("typographicExtras","slashedZero"),ss01:F("stylisticAlternatives","stylisticAltOne"),ss02:F("stylisticAlternatives","stylisticAltTwo"),ss03:F("stylisticAlternatives","stylisticAltThree"),ss04:F("stylisticAlternatives","stylisticAltFour"),ss05:F("stylisticAlternatives","stylisticAltFive"),ss06:F("stylisticAlternatives","stylisticAltSix"),ss07:F("stylisticAlternatives","stylisticAltSeven"),ss08:F("stylisticAlternatives","stylisticAltEight"),ss09:F("stylisticAlternatives","stylisticAltNine"),ss10:F("stylisticAlternatives","stylisticAltTen"),ss11:F("stylisticAlternatives","stylisticAltEleven"),ss12:F("stylisticAlternatives","stylisticAltTwelve"),ss13:F("stylisticAlternatives","stylisticAltThirteen"),ss14:F("stylisticAlternatives","stylisticAltFourteen"),ss15:F("stylisticAlternatives","stylisticAltFifteen"),ss16:F("stylisticAlternatives","stylisticAltSixteen"),ss17:F("stylisticAlternatives","stylisticAltSeventeen"),ss18:F("stylisticAlternatives","stylisticAltEighteen"),ss19:F("stylisticAlternatives","stylisticAltNineteen"),ss20:F("stylisticAlternatives","stylisticAltTwenty")};for(let s=1;s<=99;s++)Er["cv"+("00"+s).slice(-2)]=[J0.characterAlternatives.code,s];let _0={};for(let s in Er){let e=Er[s];_0[e[0]]==null&&(_0[e[0]]={}),_0[e[0]][e[1]]=s}function Gl(s){let e={};for(let t in s){let r;(r=Er[t])&&(e[r[0]]==null&&(e[r[0]]={}),e[r[0]][r[1]]=s[t])}return e}function wa(s){let[e,t]=s;if(isNaN(e))var r=J0[e]&&J0[e].code;else var r=e;if(isNaN(t))var n=J0[e]&&J0[e][t];else var n=t;return[r,n]}function Vl(s){let e={};if(Array.isArray(s))for(let t=0;t<s.length;t++){let r,n=wa(s[t]);(r=_0[n[0]]&&_0[n[0]][n[1]])&&(e[r]=!0)}else if(typeof s=="object")for(let t in s){let r=s[t];for(let n in r){let a,i=wa([t,n]);r[n]&&(a=_0[i[0]]&&_0[i[0]][i[1]])&&(e[a]=!0)}}return Object.keys(e)}class Zt{lookup(e){switch(this.table.version){case 0:return this.table.values.getItem(e);case 2:case 4:{let n=0,a=this.table.binarySearchHeader.nUnits-1;for(;n<=a;){var t=n+a>>1,r=this.table.segments[t];if(r.firstGlyph===65535)return null;if(e<r.firstGlyph)a=t-1;else if(e>r.lastGlyph)n=t+1;else return this.table.version===2?r.value:r.values[e-r.firstGlyph]}return null}case 6:{let n=0,a=this.table.binarySearchHeader.nUnits-1;for(;n<=a;){var t=n+a>>1,r=this.table.segments[t];if(r.glyph===65535)return null;if(e<r.glyph)a=t-1;else if(e>r.glyph)n=t+1;else return r.value}return null}case 8:return this.table.values[e-this.table.firstGlyph];default:throw new Error("Unknown lookup table format: "+this.table.version)}}glyphsForValue(e){let t=[];switch(this.table.version){case 2:case 4:for(let r of this.table.segments)if(this.table.version===2&&r.value===e)t.push(...vt(r.firstGlyph,r.lastGlyph+1));else for(let n=0;n<r.values.length;n++)r.values[n]===e&&t.push(r.firstGlyph+n);break;case 6:for(let r of this.table.segments)r.value===e&&t.push(r.glyph);break;case 8:for(let r=0;r<this.table.values.length;r++)this.table.values[r]===e&&t.push(this.table.firstGlyph+r);break;default:throw new Error("Unknown lookup table format: "+this.table.version)}return t}constructor(e){this.table=e}}Me([He],Zt.prototype,"glyphsForValue",null);const zl=0,hf=1,ya=0,Ca=1,Sa=2,pf=3,$l=16384;class Wl{process(e,t,r){let n=zl,a=t?e.length-1:0,i=t?-1:1;for(;i===1&&a<=e.length||i===-1&&a>=-1;){let l=null,u=Ca,c=!0;a===e.length||a===-1?u=ya:(l=e[a],l.id===65535?u=Sa:(u=this.lookupTable.lookup(l.id),u==null&&(u=Ca)));let d=this.stateTable.stateArray.getItem(n)[u],C=this.stateTable.entryTable.getItem(d);u!==ya&&u!==Sa&&(r(l,C,a),c=!(C.flags&$l)),n=C.newState,c&&(a+=i)}return e}traverse(e,t,r){if(t===void 0&&(t=0),r===void 0&&(r=new Set),r.has(t))return;r.add(t);let{nClasses:n,stateArray:a,entryTable:i}=this.stateTable,l=a.getItem(t);for(let u=4;u<n;u++){let c=l[u],f=i.getItem(c);for(let d of this.lookupTable.glyphsForValue(u))e.enter&&e.enter(d,f),f.newState!==0&&this.traverse(e,f.newState,r),e.exit&&e.exit(d,f)}}constructor(e){this.stateTable=e,this.lookupTable=new Zt(e.classTable)}}const ql=32768,Hl=8192,jl=15,Aa=32768,Xl=32768,Yl=8192,Zl=2147483648,Kl=1073741824,Jl=1073741823,bf=8388608,Ia=4194304,gf=2097152,vf=8192,mf=4096,_l=2048,Ql=1024,eu=992,tu=31;class Ea{process(e,t){t===void 0&&(t={});for(let n of this.morx.chains){let a=n.defaultFlags;for(let i of n.features){let l;(l=t[i.featureType])&&(l[i.featureSetting]?(a&=i.disableFlags,a|=i.enableFlags):l[i.featureSetting]===!1&&(a|=~i.disableFlags,a&=~i.enableFlags))}for(let i of n.subtables)i.subFeatureFlags&a&&this.processSubtable(i,e)}let r=e.length-1;for(;r>=0;)e[r].id===65535&&e.splice(r,1),r--;return e}processSubtable(e,t){if(this.subtable=e,this.glyphs=t,this.subtable.type===4){this.processNoncontextualSubstitutions(this.subtable,this.glyphs);return}this.ligatureStack=[],this.markedGlyph=null,this.firstGlyph=null,this.lastGlyph=null,this.markedIndex=null;let r=this.getStateMachine(e),n=this.getProcessor(),a=!!(this.subtable.coverage&Ia);return r.process(this.glyphs,a,n)}getStateMachine(e){return new Wl(e.table.stateTable)}getProcessor(){switch(this.subtable.type){case 0:return this.processIndicRearragement;case 1:return this.processContextualSubstitution;case 2:return this.processLigature;case 4:return this.processNoncontextualSubstitutions;case 5:return this.processGlyphInsertion;default:throw new Error("Invalid morx subtable type: "+this.subtable.type)}}processIndicRearragement(e,t,r){t.flags&ql&&(this.firstGlyph=r),t.flags&Hl&&(this.lastGlyph=r),ru(this.glyphs,t.flags&jl,this.firstGlyph,this.lastGlyph)}processContextualSubstitution(e,t,r){let n=this.subtable.table.substitutionTable.items;if(t.markIndex!==65535){let i=n.getItem(t.markIndex),l=new Zt(i);e=this.glyphs[this.markedGlyph];var a=l.lookup(e.id);a&&(this.glyphs[this.markedGlyph]=this.font.getGlyph(a,e.codePoints))}if(t.currentIndex!==65535){let i=n.getItem(t.currentIndex),l=new Zt(i);e=this.glyphs[r];var a=l.lookup(e.id);a&&(this.glyphs[r]=this.font.getGlyph(a,e.codePoints))}t.flags&Aa&&(this.markedGlyph=r)}processLigature(e,t,r){if(t.flags&Xl&&this.ligatureStack.push(r),t.flags&Yl){let n=this.subtable.table.ligatureActions,a=this.subtable.table.components,i=this.subtable.table.ligatureList,l=t.action,u=!1,c=0,f=[],d=[];for(;!u;){let C=this.ligatureStack.pop();f.unshift(...this.glyphs[C].codePoints);let k=n.getItem(l++);u=!!(k&Zl);let B=!!(k&Kl),_=(k&Jl)<<2>>2;_+=this.glyphs[C].id;let ye=a.getItem(_);if(c+=ye,u||B){let ee=i.getItem(c);this.glyphs[C]=this.font.getGlyph(ee,f),d.push(C),c=0,f=[]}else this.glyphs[C]=this.font.getGlyph(65535)}this.ligatureStack.push(...d)}}processNoncontextualSubstitutions(e,t,r){let n=new Zt(e.table.lookupTable);for(r=0;r<t.length;r++){let a=t[r];if(a.id!==65535){let i=n.lookup(a.id);i&&(t[r]=this.font.getGlyph(i,a.codePoints))}}}_insertGlyphs(e,t,r,n){let a=[];for(;r--;){let i=this.subtable.table.insertionActions.getItem(t++);a.push(this.font.getGlyph(i))}n||e++,this.glyphs.splice(e,0,...a)}processGlyphInsertion(e,t,r){if(t.flags&Aa&&(this.markedIndex=r),t.markedInsertIndex!==65535){let n=(t.flags&tu)>>>5,a=!!(t.flags&Ql);this._insertGlyphs(this.markedIndex,t.markedInsertIndex,n,a)}if(t.currentInsertIndex!==65535){let n=(t.flags&eu)>>>5,a=!!(t.flags&_l);this._insertGlyphs(r,t.currentInsertIndex,n,a)}}getSupportedFeatures(){let e=[];for(let t of this.morx.chains)for(let r of t.features)e.push([r.featureType,r.featureSetting]);return e}generateInputs(e){return this.inputCache||this.generateInputCache(),this.inputCache[e]||[]}generateInputCache(){this.inputCache={};for(let e of this.morx.chains){let t=e.defaultFlags;for(let r of e.subtables)r.subFeatureFlags&t&&this.generateInputsForSubtable(r)}}generateInputsForSubtable(e){if(e.type!==2)return;if(!!(e.coverage&Ia))throw new Error("Reverse subtable, not supported.");this.subtable=e,this.ligatureStack=[];let r=this.getStateMachine(e),n=this.getProcessor(),a=[],i=[];this.glyphs=[],r.traverse({enter:(l,u)=>{let c=this.glyphs;i.push({glyphs:c.slice(),ligatureStack:this.ligatureStack.slice()});let f=this.font.getGlyph(l);a.push(f),c.push(a[a.length-1]),n(c[c.length-1],u,c.length-1);let d=0,C=0;for(let k=0;k<c.length&&d<=1;k++)c[k].id!==65535&&(d++,C=c[k].id);if(d===1){let k=a.map(_=>_.id),B=this.inputCache[C];B?B.push(k):this.inputCache[C]=[k]}},exit:()=>{({glyphs:this.glyphs,ligatureStack:this.ligatureStack}=i.pop()),a.pop()}})}constructor(e){this.processIndicRearragement=this.processIndicRearragement.bind(this),this.processContextualSubstitution=this.processContextualSubstitution.bind(this),this.processLigature=this.processLigature.bind(this),this.processNoncontextualSubstitutions=this.processNoncontextualSubstitutions.bind(this),this.processGlyphInsertion=this.processGlyphInsertion.bind(this),this.font=e,this.morx=e.morx,this.inputCache=null}}Me([He],Ea.prototype,"getStateMachine",null);function Ke(s,e,t,r,n){r===void 0&&(r=!1),n===void 0&&(n=!1);let a=s.splice(t[0]-(t[1]-1),t[1]);n&&a.reverse();let i=s.splice(e[0],e[1],...a);return r&&i.reverse(),s.splice(t[0]-(e[1]-1),0,...i),s}function ru(s,e,t,r){let n=r-t+1;switch(e){case 0:return s;case 1:return Ke(s,[t,1],[r,0]);case 2:return Ke(s,[t,0],[r,1]);case 3:return Ke(s,[t,1],[r,1]);case 4:return Ke(s,[t,2],[r,0]);case 5:return Ke(s,[t,2],[r,0],!0,!1);case 6:return Ke(s,[t,0],[r,2]);case 7:return Ke(s,[t,0],[r,2],!1,!0);case 8:return Ke(s,[t,1],[r,2]);case 9:return Ke(s,[t,1],[r,2],!1,!0);case 10:return Ke(s,[t,2],[r,1]);case 11:return Ke(s,[t,2],[r,1],!0,!1);case 12:return Ke(s,[t,2],[r,2]);case 13:return Ke(s,[t,2],[r,2],!0,!1);case 14:return Ke(s,[t,2],[r,2],!1,!0);case 15:return Ke(s,[t,2],[r,2],!0,!0);default:throw new Error("Unknown verb: "+e)}}class nu{substitute(e){e.direction==="rtl"&&e.glyphs.reverse(),this.morxProcessor.process(e.glyphs,Gl(e.features))}getAvailableFeatures(e,t){return Vl(this.morxProcessor.getSupportedFeatures())}stringsForGlyph(e){let t=this.morxProcessor.generateInputs(e),r=new Set;for(let n of t)this._addStrings(n,0,r,"");return r}_addStrings(e,t,r,n){let a=this.font._cmapProcessor.codePointsForGlyph(e[t]);for(let i of a){let l=n+String.fromCodePoint(i);t<e.length-1?this._addStrings(e,t+1,r,l):r.add(l)}}constructor(e){this.font=e,this.morxProcessor=new Ea(e),this.fallbackPosition=!1}}class au{_addFeatures(e,t){let r=this.stages.length-1,n=this.stages[r];for(let a of e)this.allFeatures[a]==null&&(n.push(a),this.allFeatures[a]=r,t&&(this.globalFeatures[a]=!0))}add(e,t){if(t===void 0&&(t=!0),this.stages.length===0&&this.stages.push([]),typeof e=="string"&&(e=[e]),Array.isArray(e))this._addFeatures(e,t);else if(typeof e=="object")this._addFeatures(e.global||[],!0),this._addFeatures(e.local||[],!1);else throw new Error("Unsupported argument to ShapingPlan#add")}addStage(e,t){typeof e=="function"?this.stages.push(e,[]):(this.stages.push([]),this.add(e,t))}setFeatureOverrides(e){if(Array.isArray(e))this.add(e);else if(typeof e=="object"){for(let t in e)if(e[t])this.add(t);else if(this.allFeatures[t]!=null){let r=this.stages[this.allFeatures[t]];r.splice(r.indexOf(t),1),delete this.allFeatures[t],delete this.globalFeatures[t]}}}assignGlobalFeatures(e){for(let t of e)for(let r in this.globalFeatures)t.features[r]=!0}process(e,t,r){for(let n of this.stages)typeof n=="function"?r||n(this.font,t,this):n.length>0&&e.applyFeatures(n,t,r)}constructor(e,t,r){this.font=e,this.script=t,this.direction=r,this.stages=[],this.globalFeatures={},this.allFeatures={}}}const su=["rvrn"],iu=["ccmp","locl","rlig","mark","mkmk"],ou=["frac","numr","dnom"],lu=["calt","clig","liga","rclt","curs","kern"],xf=null,uu={ltr:["ltra","ltrm"],rtl:["rtla","rtlm"]};class V0{static plan(e,t,r){this.planPreprocessing(e),this.planFeatures(e),this.planPostprocessing(e,r),e.assignGlobalFeatures(t),this.assignFeatures(e,t)}static planPreprocessing(e){e.add({global:[...su,...uu[e.direction]],local:ou})}static planFeatures(e){}static planPostprocessing(e,t){e.add([...iu,...lu]),e.setFeatureOverrides(t)}static assignFeatures(e,t){for(let r=0;r<t.length;r++){let n=t[r];if(n.codePoints[0]===8260){let a=r,i=r+1;for(;a>0&&Jr(t[a-1].codePoints[0]);)t[a-1].features.numr=!0,t[a-1].features.frac=!0,a--;for(;i<t.length&&Jr(t[i].codePoints[0]);)t[i].features.dnom=!0,t[i].features.frac=!0,i++;n.features.frac=!0,r=i-1}}}}we(V0,"zeroMarkWidths","AFTER_GPOS");const cu=new B0(gn("APABAAAAAAAAOAAAAf0BAv7tmi1MxDAUx7vtvjhAgcDgkEgEAnmXEBIMCYaEcygEiqBQ4FAkCE4ikUgMiiBJSAgSiUQSDMn9L9eSl6bddddug9t7yS/trevre+3r27pcNxZiG+yCfdCVv/9LeQxOwRm4AJegD27ALbgD9+ABPJF+z+BN/h7yDj5k/VOWX6SdmU5+wLWknggxDxaS8u0qiiX4uiz9XamQ3wzDMAzDMAzDMAzDVI/h959V/v7BMAzDMAzDMLlyNTNiMSdewVxbiA44B4/guz1qW58VYlMI0WsJ0W+N6kXw0spvPtdwhtkwnGM6uLaV4Xyzg3v3PM9DPfQ/sOg4xPWjipy31P8LTqbU304c/cLCUmWJLNB2Uz2U1KTeRKNmKHVMfbJC+/0loTZRH/W5cvEvBJPMbREkWt3FD1NcqXZBSpuE2Ad0PBehPtNrPtIEdYP+hiRt/V1jIiE69X4NT/uVZI3PUHE9bm5M7ePGdZWy951v7Nn6j8v1WWKP3mt6ttnsigx6VN7Vc0VomSSGqW2mGNP1muZPl7LfjNUaKNFtDGVf2fvE9O7VlBS5j333c5p/eeoOqcs1R/hIqDWLJ7TTlksirVT1SI7l8k4Yp+g3jafGcrU1RM6l9th80XOpnlN97bDNY4i4s61B0Si/ipa0uHMl6zqEjlFfCZm/TM8KmzQDjmuTAQ==")),ka=["isol","fina","fin2","fin3","medi","med2","init"],xn={Non_Joining:0,Left_Joining:1,Right_Joining:2,Dual_Joining:3,Join_Causing:3,ALAPH:4,"DALATH RISH":5,Transparent:6},Ee="isol",z0="fina",wn="fin2",fu="fin3",kr="medi",Or="med2",Pr="init",xe=null,du=[[[xe,xe,0],[xe,Ee,2],[xe,Ee,1],[xe,Ee,2],[xe,Ee,1],[xe,Ee,6]],[[xe,xe,0],[xe,Ee,2],[xe,Ee,1],[xe,Ee,2],[xe,wn,5],[xe,Ee,6]],[[xe,xe,0],[xe,Ee,2],[Pr,z0,1],[Pr,z0,3],[Pr,z0,4],[Pr,z0,6]],[[xe,xe,0],[xe,Ee,2],[kr,z0,1],[kr,z0,3],[kr,z0,4],[kr,z0,6]],[[xe,xe,0],[xe,Ee,2],[Or,Ee,1],[Or,Ee,2],[Or,wn,5],[Or,Ee,6]],[[xe,xe,0],[xe,Ee,2],[Ee,Ee,1],[Ee,Ee,2],[Ee,wn,5],[Ee,Ee,6]],[[xe,xe,0],[xe,Ee,2],[xe,Ee,1],[xe,Ee,2],[xe,fu,5],[xe,Ee,6]]];class $0 extends V0{static planFeatures(e){e.add(["ccmp","locl"]);for(let t=0;t<ka.length;t++){let r=ka[t];e.addStage(r,!1)}e.addStage("mset")}static assignFeatures(e,t){super.assignFeatures(e,t);let r=-1,n=0,a=[];for(let l=0;l<t.length;l++){let u,c;var i=t[l];let f=hu(i.codePoints[0]);if(f===xn.Transparent){a[l]=xe;continue}[c,u,n]=du[n][f],c!==xe&&r!==-1&&(a[r]=c),a[l]=u,r=l}for(let l=0;l<t.length;l++){let u;var i=t[l];(u=a[l])&&(i.features[u]=!0)}}}function hu(s){let e=cu.get(s);if(e)return e-1;let t=o0(s);return t==="Mn"||t==="Me"||t==="Cf"?xn.Transparent:xn.Non_Joining}class pu{reset(e,t){e===void 0&&(e={}),t===void 0&&(t=0),this.options=e,this.flags=e.flags||{},this.markAttachmentType=e.markAttachmentType||0,this.index=t}get cur(){return this.glyphs[this.index]||null}shouldIgnore(e){return this.flags.ignoreMarks&&e.isMark||this.flags.ignoreBaseGlyphs&&e.isBase||this.flags.ignoreLigatures&&e.isLigature||this.markAttachmentType&&e.isMark&&e.markAttachmentType!==this.markAttachmentType}move(e){for(this.index+=e;0<=this.index&&this.index<this.glyphs.length&&this.shouldIgnore(this.glyphs[this.index]);)this.index+=e;return 0>this.index||this.index>=this.glyphs.length?null:this.glyphs[this.index]}next(){return this.move(1)}prev(){return this.move(-1)}peek(e){e===void 0&&(e=1);let t=this.index,r=this.increment(e);return this.index=t,r}peekIndex(e){e===void 0&&(e=1);let t=this.index;this.increment(e);let r=this.index;return this.index=t,r}increment(e){e===void 0&&(e=1);let t=e<0?-1:1;for(e=Math.abs(e);e--;)this.move(t);return this.glyphs[this.index]}constructor(e,t){this.glyphs=e,this.reset(t)}}const bu=["DFLT","dflt","latn"];class Tr{findScript(e){if(this.table.scriptList==null)return null;Array.isArray(e)||(e=[e]);for(let t of e)for(let r of this.table.scriptList)if(r.tag===t)return r;return null}selectScript(e,t,r){let n=!1,a;if(!this.script||e!==this.scriptTag){if(a=this.findScript(e),a||(a=this.findScript(bu)),!a)return this.scriptTag;this.scriptTag=a.tag,this.script=a.script,this.language=null,this.languageTag=null,n=!0}if((!r||r!==this.direction)&&(this.direction=r||xa(e)),t&&t.length<4&&(t+=" ".repeat(4-t.length)),!t||t!==this.languageTag){this.language=null;for(let i of this.script.langSysRecords)if(i.tag===t){this.language=i.langSys,this.languageTag=i.tag;break}this.language||(this.language=this.script.defaultLangSys,this.languageTag=null),n=!0}if(n&&(this.features={},this.language))for(let i of this.language.featureIndexes){let l=this.table.featureList[i],u=this.substituteFeatureForVariations(i);this.features[l.tag]=u||l.feature}return this.scriptTag}lookupsForFeatures(e,t){e===void 0&&(e=[]);let r=[];for(let n of e){let a=this.features[n];if(a)for(let i of a.lookupListIndexes)t&&t.indexOf(i)!==-1||r.push({feature:n,index:i,lookup:this.table.lookupList.get(i)})}return r.sort((n,a)=>n.index-a.index),r}substituteFeatureForVariations(e){if(this.variationsIndex===-1)return null;let r=this.table.featureVariations.featureVariationRecords[this.variationsIndex].featureTableSubstitution.substitutions;for(let n of r)if(n.featureIndex===e)return n.alternateFeatureTable;return null}findVariationsIndex(e){let t=this.table.featureVariations;if(!t)return-1;let r=t.featureVariationRecords;for(let n=0;n<r.length;n++){let a=r[n].conditionSet.conditionTable;if(this.variationConditionsMatch(a,e))return n}return-1}variationConditionsMatch(e,t){return e.every(r=>{let n=r.axisIndex<t.length?t[r.axisIndex]:0;return r.filterRangeMinValue<=n&&n<=r.filterRangeMaxValue})}applyFeatures(e,t,r){let n=this.lookupsForFeatures(e);this.applyLookups(n,t,r)}applyLookups(e,t,r){this.glyphs=t,this.positions=r,this.glyphIterator=new pu(t);for(let{feature:n,lookup:a}of e)for(this.currentFeature=n,this.glyphIterator.reset(a.flags);this.glyphIterator.index<t.length;){if(!(n in this.glyphIterator.cur.features)){this.glyphIterator.next();continue}for(let i of a.subTables)if(this.applyLookup(a.lookupType,i))break;this.glyphIterator.next()}}applyLookup(e,t){throw new Error("applyLookup must be implemented by subclasses")}applyLookupList(e){let t=this.glyphIterator.options,r=this.glyphIterator.index;for(let n of e){this.glyphIterator.reset(t,r),this.glyphIterator.increment(n.sequenceIndex);let a=this.table.lookupList.get(n.lookupListIndex);this.glyphIterator.reset(a.flags,this.glyphIterator.index);for(let i of a.subTables)if(this.applyLookup(a.lookupType,i))break}return this.glyphIterator.reset(t,r),!0}coverageIndex(e,t){switch(t==null&&(t=this.glyphIterator.cur.id),e.version){case 1:return e.glyphs.indexOf(t);case 2:for(let r of e.rangeRecords)if(r.start<=t&&t<=r.end)return r.startCoverageIndex+t-r.start;break}return-1}match(e,t,r,n){let a=this.glyphIterator.index,i=this.glyphIterator.increment(e),l=0;for(;l<t.length&&i&&r(t[l],i);)n&&n.push(this.glyphIterator.index),l++,i=this.glyphIterator.next();return this.glyphIterator.index=a,l<t.length?!1:n||!0}sequenceMatches(e,t){return this.match(e,t,(r,n)=>r===n.id)}sequenceMatchIndices(e,t){return this.match(e,t,(r,n)=>this.currentFeature in n.features?r===n.id:!1,[])}coverageSequenceMatches(e,t){return this.match(e,t,(r,n)=>this.coverageIndex(r,n.id)>=0)}getClassID(e,t){switch(t.version){case 1:let r=e-t.startGlyph;if(r>=0&&r<t.classValueArray.length)return t.classValueArray[r];break;case 2:for(let n of t.classRangeRecord)if(n.start<=e&&e<=n.end)return n.class;break}return 0}classSequenceMatches(e,t,r){return this.match(e,t,(n,a)=>n===this.getClassID(a.id,r))}applyContext(e){let t,r;switch(e.version){case 1:if(t=this.coverageIndex(e.coverage),t===-1)return!1;r=e.ruleSets[t];for(let n of r)if(this.sequenceMatches(1,n.input))return this.applyLookupList(n.lookupRecords);break;case 2:if(this.coverageIndex(e.coverage)===-1||(t=this.getClassID(this.glyphIterator.cur.id,e.classDef),t===-1))return!1;r=e.classSet[t];for(let n of r)if(this.classSequenceMatches(1,n.classes,e.classDef))return this.applyLookupList(n.lookupRecords);break;case 3:if(this.coverageSequenceMatches(0,e.coverages))return this.applyLookupList(e.lookupRecords);break}return!1}applyChainingContext(e){let t;switch(e.version){case 1:if(t=this.coverageIndex(e.coverage),t===-1)return!1;let r=e.chainRuleSets[t];for(let a of r)if(this.sequenceMatches(-a.backtrack.length,a.backtrack)&&this.sequenceMatches(1,a.input)&&this.sequenceMatches(1+a.input.length,a.lookahead))return this.applyLookupList(a.lookupRecords);break;case 2:if(this.coverageIndex(e.coverage)===-1)return!1;t=this.getClassID(this.glyphIterator.cur.id,e.inputClassDef);let n=e.chainClassSet[t];if(!n)return!1;for(let a of n)if(this.classSequenceMatches(-a.backtrack.length,a.backtrack,e.backtrackClassDef)&&this.classSequenceMatches(1,a.input,e.inputClassDef)&&this.classSequenceMatches(1+a.input.length,a.lookahead,e.lookaheadClassDef))return this.applyLookupList(a.lookupRecords);break;case 3:if(this.coverageSequenceMatches(-e.backtrackGlyphCount,e.backtrackCoverage)&&this.coverageSequenceMatches(0,e.inputCoverage)&&this.coverageSequenceMatches(e.inputGlyphCount,e.lookaheadCoverage))return this.applyLookupList(e.lookupRecords);break}return!1}constructor(e,t){this.font=e,this.table=t,this.script=null,this.scriptTag=null,this.language=null,this.languageTag=null,this.features={},this.lookups={},this.variationsIndex=e._variationProcessor?this.findVariationsIndex(e._variationProcessor.normalizedCoords):-1,this.selectScript(),this.glyphs=[],this.positions=[],this.ligatureID=1,this.currentFeature=null}}class d0{get id(){return this._id}set id(e){this._id=e,this.substituted=!0;let t=this._font.GDEF;if(t&&t.glyphClassDef){let r=Tr.prototype.getClassID(e,t.glyphClassDef);this.isBase=r===1,this.isLigature=r===2,this.isMark=r===3,this.markAttachmentType=t.markAttachClassDef?Tr.prototype.getClassID(e,t.markAttachClassDef):0}else this.isMark=this.codePoints.length>0&&this.codePoints.every(_r),this.isBase=!this.isMark,this.isLigature=this.codePoints.length>1,this.markAttachmentType=0}copy(){return new d0(this._font,this.id,this.codePoints,this.features)}constructor(e,t,r,n){if(r===void 0&&(r=[]),this._font=e,this.codePoints=r,this.id=t,this.features={},Array.isArray(n))for(let a=0;a<n.length;a++){let i=n[a];this.features[i]=!0}else typeof n=="object"&&Object.assign(this.features,n);this.ligatureID=null,this.ligatureComponent=null,this.isLigated=!1,this.cursiveAttachment=null,this.markAttachment=null,this.shaperInfo=null,this.substituted=!1,this.isMultiplied=!1}}class Oa extends V0{static planFeatures(e){e.add(["ljmo","vjmo","tjmo"],!1)}static assignFeatures(e,t){let r=0,n=0;for(;n<t.length;){let a,l=t[n].codePoints[0],u=Rr(l);switch([a,r]=Bu[r][u],a){case E0:e.font.hasGlyphForCodePoint(l)||(n=Da(t,n,e.font));break;case Cn:n=Lu(t,n,e.font);break;case Sn:Ru(t,n,e.font);break;case An:n=Nu(t,n,e.font);break}n++}}}we(Oa,"zeroMarkWidths","NONE");const mt=44032,Pa=55204,gu=Pa-mt+1,Fr=4352,Dr=4449,W0=4519,vu=19,Br=21,Kt=28,mu=Fr+vu-1,xu=Dr+Br-1,wu=W0+Kt-1,Ta=9676,yu=s=>4352<=s&&s<=4447||43360<=s&&s<=43388,Cu=s=>4448<=s&&s<=4519||55216<=s&&s<=55238,Su=s=>4520<=s&&s<=4607||55243<=s&&s<=55291,Au=s=>12334<=s&&s<=12335,Iu=s=>mt<=s&&s<=Pa,Eu=s=>s-mt<gu&&(s-mt)%Kt===0,ku=s=>Fr<=s&&s<=mu,Ou=s=>Dr<=s&&s<=xu,Pu=s=>W0+1&&1<=s&&s<=wu,Tu=0,Fu=1,Lr=2,yn=3,Mr=4,Fa=5,Du=6;function Rr(s){return yu(s)?Fu:Cu(s)?Lr:Su(s)?yn:Eu(s)?Mr:Iu(s)?Fa:Au(s)?Du:Tu}const Qe=0,E0=1,Cn=2,Sn=4,An=5,Bu=[[[Qe,0],[Qe,1],[Qe,0],[Qe,0],[E0,2],[E0,3],[An,0]],[[Qe,0],[Qe,1],[Cn,2],[Qe,0],[E0,2],[E0,3],[An,0]],[[Qe,0],[Qe,1],[Qe,0],[Cn,3],[E0,2],[E0,3],[Sn,0]],[[Qe,0],[Qe,1],[Qe,0],[Qe,0],[E0,2],[E0,3],[Sn,0]]];function Jt(s,e,t){return new d0(s,s.glyphForCodePoint(e).id,[e],t)}function Da(s,e,t){let r=s[e],a=r.codePoints[0]-mt,i=W0+a%Kt;a=a/Kt|0;let l=Fr+a/Br|0,u=Dr+a%Br;if(!t.hasGlyphForCodePoint(l)||!t.hasGlyphForCodePoint(u)||i!==W0&&!t.hasGlyphForCodePoint(i))return e;let c=Jt(t,l,r.features);c.features.ljmo=!0;let f=Jt(t,u,r.features);f.features.vjmo=!0;let d=[c,f];if(i>W0){let C=Jt(t,i,r.features);C.features.tjmo=!0,d.push(C)}return s.splice(e,1,...d),e+d.length-1}function Lu(s,e,t){let r=s[e],n=s[e].codePoints[0],a=Rr(n),i=s[e-1].codePoints[0],l=Rr(i),u,c,f,d;if(l===Mr&&a===yn)u=i,d=r;else{a===Lr?(c=s[e-1],f=r):(c=s[e-2],f=s[e-1],d=r);let k=c.codePoints[0],B=f.codePoints[0];ku(k)&&Ou(B)&&(u=mt+((k-Fr)*Br+(B-Dr))*Kt)}let C=d&&d.codePoints[0]||W0;if(u!=null&&(C===W0||Pu(C))){let k=u+(C-W0);if(t.hasGlyphForCodePoint(k)){let B=l===Lr?3:2;return s.splice(e-B+1,B,Jt(t,k,r.features)),e-B+1}}return c&&(c.features.ljmo=!0),f&&(f.features.vjmo=!0),d&&(d.features.tjmo=!0),l===Mr?(Da(s,e-1,t),e+1):e}function Mu(s){switch(Rr(s)){case Mr:case Fa:return 1;case Lr:return 2;case yn:return 3}}function Ru(s,e,t){let r=s[e],n=s[e].codePoints[0];if(t.glyphForCodePoint(n).advanceWidth===0)return;let a=s[e-1].codePoints[0],i=Mu(a);return s.splice(e,1),s.splice(e-i,0,r)}function Nu(s,e,t){let r=s[e],n=s[e].codePoints[0];if(t.hasGlyphForCodePoint(Ta)){let a=Jt(t,Ta,r.features),i=t.glyphForCodePoint(n).advanceWidth===0?e:e+1;s.splice(i,0,a),e++}return e}var Ba={};Ba=JSON.parse('{"stateTable":[[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,2,3,4,5,6,7,8,9,0,10,11,11,12,13,14,15,16,17],[0,0,0,18,19,20,21,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,28,29,30,31,32,33,0,34,0,0,35,36,0,0,37,0],[0,0,0,38,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,39,0,0,0,40,41,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,43,44,44,8,9,0,0,0,0,12,43,0,0,0,0],[0,0,0,0,43,44,44,8,9,0,0,0,0,0,43,0,0,0,0],[0,0,0,45,46,47,48,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,50,0,0,51,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,52,0,0,0,0,0,0,0,0],[0,0,0,53,54,55,56,57,58,0,59,0,0,60,61,0,0,62,0],[0,0,0,4,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,63,64,0,0,40,41,0,9,0,10,0,0,0,42,0,63,0,0],[0,2,3,4,5,6,7,8,9,0,10,11,11,12,13,0,2,16,0],[0,0,0,18,65,20,21,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,0,66,67,67,8,9,0,10,0,0,0,68,0,0,0,0],[0,0,0,69,0,70,70,0,71,0,72,0,0,0,0,0,0,0,0],[0,0,0,73,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,75,0,0,0,76,77,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,79,80,80,22,23,0,0,0,0,25,79,0,0,0,0],[0,0,0,18,19,20,74,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,81,82,83,84,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,86,0,0,87,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,88,0,0,0,0,0,0,0,0],[0,0,0,18,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,89,90,0,0,76,77,0,23,0,24,0,0,0,78,0,89,0,0],[0,0,0,0,91,92,92,22,23,0,24,0,0,0,93,0,0,0,0],[0,0,0,94,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,96,0,0,0,97,98,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,35,100,0,0,0,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,0,100,0,0,0,0],[0,0,0,102,103,104,105,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,107,0,0,108,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,109,0,0,0,0,0,0,0,0],[0,0,0,28,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,110,111,0,0,97,98,0,33,0,34,0,0,0,99,0,110,0,0],[0,0,0,0,112,113,113,32,33,0,34,0,0,0,114,0,0,0,0],[0,0,0,0,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,0,0,115,116,117,118,8,9,0,10,0,0,119,120,0,0,16,0],[0,0,0,0,0,121,121,0,9,0,10,0,0,0,42,0,0,0,0],[0,39,0,122,0,123,123,8,9,0,10,0,0,0,42,0,39,0,0],[0,124,64,0,0,0,0,0,0,0,0,0,0,0,0,0,124,0,0],[0,39,0,0,0,121,125,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,126,126,8,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,46,47,48,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,47,47,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,127,127,49,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,128,127,127,49,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,129,130,131,132,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,50,0,0,0,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,134,0,0,0,0,0,0,0,0],[0,0,0,135,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,136,0,0,0,137,138,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,140,141,141,57,58,0,0,0,0,60,140,0,0,0,0],[0,0,0,0,140,141,141,57,58,0,0,0,0,0,140,0,0,0,0],[0,0,0,142,143,144,145,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,147,0,0,148,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,149,0,0,0,0,0,0,0,0],[0,0,0,53,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,150,151,0,0,137,138,0,58,0,59,0,0,0,139,0,150,0,0],[0,0,0,0,152,153,153,57,58,0,59,0,0,0,154,0,0,0,0],[0,0,0,155,116,156,157,8,9,0,10,0,0,158,120,0,0,16,0],[0,0,0,0,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,75,3,4,5,159,160,8,161,0,162,0,11,12,163,0,75,16,0],[0,0,0,0,0,40,164,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,0,165,0,0,0,0],[0,124,64,0,0,40,164,0,9,0,10,0,0,0,42,0,124,0,0],[0,0,0,0,0,70,70,0,71,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,71,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,166,0,0,167,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,168,0,0,0,0,0,0,0,0],[0,0,0,0,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,0,0,0,79,80,80,22,23,0,0,0,0,0,79,0,0,0,0],[0,0,0,169,170,171,172,22,23,0,24,0,0,173,174,0,0,27,0],[0,0,0,0,0,175,175,0,23,0,24,0,0,0,78,0,0,0,0],[0,75,0,176,0,177,177,22,23,0,24,0,0,0,78,0,75,0,0],[0,178,90,0,0,0,0,0,0,0,0,0,0,0,0,0,178,0,0],[0,75,0,0,0,175,179,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,180,180,22,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,82,83,84,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,83,83,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,181,181,85,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,182,181,181,85,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,183,184,185,186,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,86,0,0,0,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,188,0,0,0,0,0,0,0,0],[0,0,0,189,170,190,191,22,23,0,24,0,0,192,174,0,0,27,0],[0,0,0,0,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,76,193,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,0,194,0,0,0,0],[0,178,90,0,0,76,193,0,23,0,24,0,0,0,78,0,178,0,0],[0,0,0,0,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,0,100,0,0,0,0],[0,0,0,195,196,197,198,32,33,0,34,0,0,199,200,0,0,37,0],[0,0,0,0,0,201,201,0,33,0,34,0,0,0,99,0,0,0,0],[0,96,0,202,0,203,203,32,33,0,34,0,0,0,99,0,96,0,0],[0,204,111,0,0,0,0,0,0,0,0,0,0,0,0,0,204,0,0],[0,96,0,0,0,201,205,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,206,206,32,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,103,104,105,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,104,104,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,207,207,106,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,208,207,207,106,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,209,210,211,212,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,107,0,0,0,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,214,0,0,0,0,0,0,0,0],[0,0,0,215,196,216,217,32,33,0,34,0,0,218,200,0,0,37,0],[0,0,0,0,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,97,219,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,0,220,0,0,0,0],[0,204,111,0,0,97,219,0,33,0,34,0,0,0,99,0,204,0,0],[0,0,0,221,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,223,0,0,0,40,224,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,119,225,0,0,0,0],[0,0,0,115,116,117,222,8,9,0,10,0,0,119,120,0,0,16,0],[0,0,0,115,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,226,64,0,0,40,224,0,9,0,10,0,0,0,42,0,226,0,0],[0,0,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0],[0,39,0,0,0,121,121,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,44,44,8,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,227,0,228,229,0,9,0,10,0,0,230,0,0,0,0,0],[0,39,0,122,0,121,121,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,231,231,49,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,232,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,130,131,132,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,131,131,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,233,233,133,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,234,233,233,133,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,235,236,237,238,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,0,0,240,241,242,243,57,58,0,59,0,0,244,245,0,0,62,0],[0,0,0,0,0,246,246,0,58,0,59,0,0,0,139,0,0,0,0],[0,136,0,247,0,248,248,57,58,0,59,0,0,0,139,0,136,0,0],[0,249,151,0,0,0,0,0,0,0,0,0,0,0,0,0,249,0,0],[0,136,0,0,0,246,250,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,251,251,57,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,143,144,145,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,144,144,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,252,252,146,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,253,252,252,146,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,254,255,256,257,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,147,0,0,0,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,259,0,0,0,0,0,0,0,0],[0,0,0,260,241,261,262,57,58,0,59,0,0,263,245,0,0,62,0],[0,0,0,0,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,137,264,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,0,265,0,0,0,0],[0,249,151,0,0,137,264,0,58,0,59,0,0,0,139,0,249,0,0],[0,0,0,221,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,158,225,0,0,0,0],[0,0,0,155,116,156,222,8,9,0,10,0,0,158,120,0,0,16,0],[0,0,0,155,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,43,266,266,8,161,0,24,0,0,12,267,0,0,0,0],[0,75,0,176,43,268,268,269,161,0,24,0,0,0,267,0,75,0,0],[0,0,0,0,0,270,0,0,271,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,272,0,0,0,0,0,0,0,0],[0,273,274,0,0,40,41,0,9,0,10,0,0,0,42,0,273,0,0],[0,0,0,40,0,123,123,8,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,121,275,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,166,0,0,0,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,276,0,0,0,0,0,0,0,0],[0,0,0,277,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,279,0,0,0,76,280,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,173,281,0,0,0,0],[0,0,0,169,170,171,278,22,23,0,24,0,0,173,174,0,0,27,0],[0,0,0,169,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,282,90,0,0,76,280,0,23,0,24,0,0,0,78,0,282,0,0],[0,0,0,0,0,0,0,0,23,0,0,0,0,0,0,0,0,0,0],[0,75,0,0,0,175,175,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,80,80,22,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,283,0,284,285,0,23,0,24,0,0,286,0,0,0,0,0],[0,75,0,176,0,175,175,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,0,0,22,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,287,287,85,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,288,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,184,185,186,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,185,185,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,289,289,187,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,290,289,289,187,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,291,292,293,294,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,277,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,192,281,0,0,0,0],[0,0,0,189,170,190,278,22,23,0,24,0,0,192,174,0,0,27,0],[0,0,0,189,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,76,0,177,177,22,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,175,296,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,297,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,299,0,0,0,97,300,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,199,301,0,0,0,0],[0,0,0,195,196,197,298,32,33,0,34,0,0,199,200,0,0,37,0],[0,0,0,195,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,302,111,0,0,97,300,0,33,0,34,0,0,0,99,0,302,0,0],[0,0,0,0,0,0,0,0,33,0,0,0,0,0,0,0,0,0,0],[0,96,0,0,0,201,201,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,101,101,32,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,303,0,304,305,0,33,0,34,0,0,306,0,0,0,0,0],[0,96,0,202,0,201,201,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,0,0,32,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,307,307,106,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,308,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,210,211,212,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,211,211,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,309,309,213,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,310,309,309,213,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,311,312,313,314,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,297,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,218,301,0,0,0,0],[0,0,0,215,196,216,298,32,33,0,34,0,0,218,200,0,0,37,0],[0,0,0,215,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,97,0,203,203,32,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,201,316,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,0,225,0,0,0,0],[0,0,0,317,318,319,320,8,9,0,10,0,0,321,322,0,0,16,0],[0,223,0,323,0,123,123,8,9,0,10,0,0,0,42,0,223,0,0],[0,223,0,0,0,121,324,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,325,318,326,327,8,9,0,10,0,0,328,322,0,0,16,0],[0,0,0,64,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,9,0,0,0,0,230,0,0,0,0,0],[0,0,0,227,0,228,121,0,9,0,10,0,0,230,0,0,0,0,0],[0,0,0,227,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,49,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0],[0,0,0,0,0,329,329,133,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,330,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,236,237,238,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,237,237,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,331,331,239,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,332,331,331,239,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,333,40,121,334,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,335,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,337,0,0,0,137,338,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,244,339,0,0,0,0],[0,0,0,240,241,242,336,57,58,0,59,0,0,244,245,0,0,62,0],[0,0,0,240,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,340,151,0,0,137,338,0,58,0,59,0,0,0,139,0,340,0,0],[0,0,0,0,0,0,0,0,58,0,0,0,0,0,0,0,0,0,0],[0,136,0,0,0,246,246,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,141,141,57,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,341,0,342,343,0,58,0,59,0,0,344,0,0,0,0,0],[0,136,0,247,0,246,246,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,0,0,57,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,345,345,146,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,346,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,255,256,257,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,256,256,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,347,347,258,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,348,347,347,258,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,349,350,351,352,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,335,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,263,339,0,0,0,0],[0,0,0,260,241,261,336,57,58,0,59,0,0,263,245,0,0,62,0],[0,0,0,260,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,137,0,248,248,57,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,246,354,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,126,126,8,23,0,0,0,0,0,0,0,0,0,0],[0,355,90,0,0,121,125,0,9,0,10,0,0,0,42,0,355,0,0],[0,0,0,0,0,356,356,269,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,357,358,359,360,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,270,0,0,0,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,363,0,0,0,0,0,0,0,0],[0,0,0,364,116,365,366,8,161,0,162,0,0,367,120,0,0,16,0],[0,0,0,0,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,40,0,121,121,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,0,281,0,0,0,0],[0,0,0,369,370,371,372,22,23,0,24,0,0,373,374,0,0,27,0],[0,279,0,375,0,177,177,22,23,0,24,0,0,0,78,0,279,0,0],[0,279,0,0,0,175,376,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,377,370,378,379,22,23,0,24,0,0,380,374,0,0,27,0],[0,0,0,90,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,23,0,0,0,0,286,0,0,0,0,0],[0,0,0,283,0,284,175,0,23,0,24,0,0,286,0,0,0,0,0],[0,0,0,283,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,85,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,82,0,0],[0,0,0,0,0,381,381,187,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,382,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,292,293,294,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,293,293,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,383,383,295,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,384,383,383,295,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,385,76,175,386,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,76,0,175,175,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,0,301,0,0,0,0],[0,0,0,387,388,389,390,32,33,0,34,0,0,391,392,0,0,37,0],[0,299,0,393,0,203,203,32,33,0,34,0,0,0,99,0,299,0,0],[0,299,0,0,0,201,394,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,395,388,396,397,32,33,0,34,0,0,398,392,0,0,37,0],[0,0,0,111,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,33,0,0,0,0,306,0,0,0,0,0],[0,0,0,303,0,304,201,0,33,0,34,0,0,306,0,0,0,0,0],[0,0,0,303,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,106,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,103,0,0],[0,0,0,0,0,399,399,213,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,400,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,312,313,314,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,313,313,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,401,401,315,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,402,401,401,315,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,403,97,201,404,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,97,0,201,201,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,405,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,407,0,0,0,40,408,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,321,409,0,0,0,0],[0,0,0,317,318,319,406,8,9,0,10,0,0,321,322,0,0,16,0],[0,0,0,317,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,410,64,0,0,40,408,0,9,0,10,0,0,0,42,0,410,0,0],[0,223,0,0,0,121,121,0,9,0,10,0,0,0,42,0,223,0,0],[0,223,0,323,0,121,121,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,405,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,328,409,0,0,0,0],[0,0,0,325,318,326,406,8,9,0,10,0,0,328,322,0,0,16,0],[0,0,0,325,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,0,0,0,133,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,130,0,0],[0,0,0,0,0,411,411,239,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,412,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,40,121,334,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,413,0,0,0,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,0,339,0,0,0,0],[0,0,0,414,415,416,417,57,58,0,59,0,0,418,419,0,0,62,0],[0,337,0,420,0,248,248,57,58,0,59,0,0,0,139,0,337,0,0],[0,337,0,0,0,246,421,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,422,415,423,424,57,58,0,59,0,0,425,419,0,0,62,0],[0,0,0,151,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,58,0,0,0,0,344,0,0,0,0,0],[0,0,0,341,0,342,246,0,58,0,59,0,0,344,0,0,0,0,0],[0,0,0,341,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,146,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,143,0,0],[0,0,0,0,0,426,426,258,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,427,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,350,351,352,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,351,351,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,428,428,353,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,429,428,428,353,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,430,137,246,431,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,137,0,246,246,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,432,116,433,434,8,161,0,162,0,0,435,120,0,0,16,0],[0,0,0,0,0,180,180,269,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,358,359,360,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,359,359,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,436,436,361,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,437,436,436,361,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,438,439,440,441,442,161,0,162,0,0,0,362,0,0,0,0],[0,443,274,0,0,0,0,0,0,0,0,0,0,0,0,0,443,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,444,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,367,225,0,0,0,0],[0,0,0,364,116,365,445,8,161,0,162,0,0,367,120,0,0,16,0],[0,0,0,364,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,0,0,0,0,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,446,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,448,0,0,0,76,449,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,373,450,0,0,0,0],[0,0,0,369,370,371,447,22,23,0,24,0,0,373,374,0,0,27,0],[0,0,0,369,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,451,90,0,0,76,449,0,23,0,24,0,0,0,78,0,451,0,0],[0,279,0,0,0,175,175,0,23,0,24,0,0,0,78,0,279,0,0],[0,279,0,375,0,175,175,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,446,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,380,450,0,0,0,0],[0,0,0,377,370,378,447,22,23,0,24,0,0,380,374,0,0,27,0],[0,0,0,377,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,0,0,0,187,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,184,0,0],[0,0,0,0,0,452,452,295,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,453,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,76,175,386,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,454,0,0,0,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,455,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,457,0,0,0,97,458,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,391,459,0,0,0,0],[0,0,0,387,388,389,456,32,33,0,34,0,0,391,392,0,0,37,0],[0,0,0,387,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,460,111,0,0,97,458,0,33,0,34,0,0,0,99,0,460,0,0],[0,299,0,0,0,201,201,0,33,0,34,0,0,0,99,0,299,0,0],[0,299,0,393,0,201,201,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,455,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,398,459,0,0,0,0],[0,0,0,395,388,396,456,32,33,0,34,0,0,398,392,0,0,37,0],[0,0,0,395,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,0,0,0,213,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,210,0,0],[0,0,0,0,0,461,461,315,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,462,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,97,201,404,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,463,0,0,0,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,0,409,0,0,0,0],[0,0,0,464,465,466,467,8,9,0,10,0,0,468,469,0,0,16,0],[0,407,0,470,0,123,123,8,9,0,10,0,0,0,42,0,407,0,0],[0,407,0,0,0,121,471,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,472,465,473,474,8,9,0,10,0,0,475,469,0,0,16,0],[0,0,0,0,0,0,0,239,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,236,0,0],[0,0,0,0,0,0,476,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,477,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,479,0,0,0,137,480,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,418,481,0,0,0,0],[0,0,0,414,415,416,478,57,58,0,59,0,0,418,419,0,0,62,0],[0,0,0,414,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,482,151,0,0,137,480,0,58,0,59,0,0,0,139,0,482,0,0],[0,337,0,0,0,246,246,0,58,0,59,0,0,0,139,0,337,0,0],[0,337,0,420,0,246,246,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,477,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,425,481,0,0,0,0],[0,0,0,422,415,423,478,57,58,0,59,0,0,425,419,0,0,62,0],[0,0,0,422,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,0,0,0,258,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,0,0],[0,0,0,0,0,483,483,353,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,484,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,137,246,431,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,485,0,0,0,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,444,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,435,225,0,0,0,0],[0,0,0,432,116,433,445,8,161,0,162,0,0,435,120,0,0,16,0],[0,0,0,432,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,0,486,486,361,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,487,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,439,440,441,442,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,440,440,442,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,488,488,442,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,489,488,488,442,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,490,491,492,493,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,495,0,496,497,0,161,0,162,0,0,498,0,0,0,0,0],[0,0,0,0,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,0,225,0,0,0,0],[0,0,0,0,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,0,450,0,0,0,0],[0,0,0,499,500,501,502,22,23,0,24,0,0,503,504,0,0,27,0],[0,448,0,505,0,177,177,22,23,0,24,0,0,0,78,0,448,0,0],[0,448,0,0,0,175,506,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,507,500,508,509,22,23,0,24,0,0,510,504,0,0,27,0],[0,0,0,0,0,0,0,295,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,292,0,0],[0,0,0,0,0,0,511,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,0,459,0,0,0,0],[0,0,0,512,513,514,515,32,33,0,34,0,0,516,517,0,0,37,0],[0,457,0,518,0,203,203,32,33,0,34,0,0,0,99,0,457,0,0],[0,457,0,0,0,201,519,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,520,513,521,522,32,33,0,34,0,0,523,517,0,0,37,0],[0,0,0,0,0,0,0,315,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,312,0,0],[0,0,0,0,0,0,524,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,525,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,527,0,0,0,40,528,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,468,529,0,0,0,0],[0,0,0,464,465,466,526,8,9,0,10,0,0,468,469,0,0,16,0],[0,0,0,464,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,530,64,0,0,40,528,0,9,0,10,0,0,0,42,0,530,0,0],[0,407,0,0,0,121,121,0,9,0,10,0,0,0,42,0,407,0,0],[0,407,0,470,0,121,121,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,525,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,475,529,0,0,0,0],[0,0,0,472,465,473,526,8,9,0,10,0,0,475,469,0,0,16,0],[0,0,0,472,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,40,0,0],[0,0,0,0,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,0,481,0,0,0,0],[0,0,0,531,532,533,534,57,58,0,59,0,0,535,536,0,0,62,0],[0,479,0,537,0,248,248,57,58,0,59,0,0,0,139,0,479,0,0],[0,479,0,0,0,246,538,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,539,532,540,541,57,58,0,59,0,0,542,536,0,0,62,0],[0,0,0,0,0,0,0,353,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,350,0,0],[0,0,0,0,0,0,543,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,361,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,358,0,0],[0,0,0,0,0,544,544,442,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,545,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,491,492,493,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,492,492,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,546,546,494,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,547,546,546,494,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,548,549,368,550,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,274,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,161,0,0,0,0,498,0,0,0,0,0],[0,0,0,495,0,496,368,0,161,0,162,0,0,498,0,0,0,0,0],[0,0,0,495,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,551,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,553,0,0,0,76,554,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,503,555,0,0,0,0],[0,0,0,499,500,501,552,22,23,0,24,0,0,503,504,0,0,27,0],[0,0,0,499,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,556,90,0,0,76,554,0,23,0,24,0,0,0,78,0,556,0,0],[0,448,0,0,0,175,175,0,23,0,24,0,0,0,78,0,448,0,0],[0,448,0,505,0,175,175,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,551,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,510,555,0,0,0,0],[0,0,0,507,500,508,552,22,23,0,24,0,0,510,504,0,0,27,0],[0,0,0,507,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,76,0,0],[0,0,0,557,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,559,0,0,0,97,560,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,516,561,0,0,0,0],[0,0,0,512,513,514,558,32,33,0,34,0,0,516,517,0,0,37,0],[0,0,0,512,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,562,111,0,0,97,560,0,33,0,34,0,0,0,99,0,562,0,0],[0,457,0,0,0,201,201,0,33,0,34,0,0,0,99,0,457,0,0],[0,457,0,518,0,201,201,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,557,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,523,561,0,0,0,0],[0,0,0,520,513,521,558,32,33,0,34,0,0,523,517,0,0,37,0],[0,0,0,520,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,97,0,0],[0,0,0,0,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,0,529,0,0,0,0],[0,0,0,563,66,564,565,8,9,0,10,0,0,566,68,0,0,16,0],[0,527,0,567,0,123,123,8,9,0,10,0,0,0,42,0,527,0,0],[0,527,0,0,0,121,568,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,569,66,570,571,8,9,0,10,0,0,572,68,0,0,16,0],[0,0,0,573,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,575,0,0,0,137,576,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,535,577,0,0,0,0],[0,0,0,531,532,533,574,57,58,0,59,0,0,535,536,0,0,62,0],[0,0,0,531,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,578,151,0,0,137,576,0,58,0,59,0,0,0,139,0,578,0,0],[0,479,0,0,0,246,246,0,58,0,59,0,0,0,139,0,479,0,0],[0,479,0,537,0,246,246,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,573,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,542,577,0,0,0,0],[0,0,0,539,532,540,574,57,58,0,59,0,0,542,536,0,0,62,0],[0,0,0,539,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,137,0,0],[0,0,0,0,0,0,0,442,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,439,0,0],[0,0,0,0,0,579,579,494,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,580,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,549,368,550,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,368,368,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,581,0,0,0,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,0,555,0,0,0,0],[0,0,0,582,91,583,584,22,23,0,24,0,0,585,93,0,0,27,0],[0,553,0,586,0,177,177,22,23,0,24,0,0,0,78,0,553,0,0],[0,553,0,0,0,175,587,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,588,91,589,590,22,23,0,24,0,0,591,93,0,0,27,0],[0,0,0,0,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,0,561,0,0,0,0],[0,0,0,592,112,593,594,32,33,0,34,0,0,595,114,0,0,37,0],[0,559,0,596,0,203,203,32,33,0,34,0,0,0,99,0,559,0,0],[0,559,0,0,0,201,597,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,598,112,599,600,32,33,0,34,0,0,601,114,0,0,37,0],[0,0,0,602,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,566,165,0,0,0,0],[0,0,0,563,66,564,67,8,9,0,10,0,0,566,68,0,0,16,0],[0,0,0,563,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,527,0,0,0,121,121,0,9,0,10,0,0,0,42,0,527,0,0],[0,527,0,567,0,121,121,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,602,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,572,165,0,0,0,0],[0,0,0,569,66,570,67,8,9,0,10,0,0,572,68,0,0,16,0],[0,0,0,569,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,0,577,0,0,0,0],[0,0,0,603,152,604,605,57,58,0,59,0,0,606,154,0,0,62,0],[0,575,0,607,0,248,248,57,58,0,59,0,0,0,139,0,575,0,0],[0,575,0,0,0,246,608,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,609,152,610,611,57,58,0,59,0,0,612,154,0,0,62,0],[0,0,0,0,0,0,0,494,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,491,0,0],[0,0,0,0,0,0,613,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,614,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,585,194,0,0,0,0],[0,0,0,582,91,583,92,22,23,0,24,0,0,585,93,0,0,27,0],[0,0,0,582,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,553,0,0,0,175,175,0,23,0,24,0,0,0,78,0,553,0,0],[0,553,0,586,0,175,175,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,614,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,591,194,0,0,0,0],[0,0,0,588,91,589,92,22,23,0,24,0,0,591,93,0,0,27,0],[0,0,0,588,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,615,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,595,220,0,0,0,0],[0,0,0,592,112,593,113,32,33,0,34,0,0,595,114,0,0,37,0],[0,0,0,592,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,559,0,0,0,201,201,0,33,0,34,0,0,0,99,0,559,0,0],[0,559,0,596,0,201,201,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,615,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,601,220,0,0,0,0],[0,0,0,598,112,599,113,32,33,0,34,0,0,601,114,0,0,37,0],[0,0,0,598,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,616,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,606,265,0,0,0,0],[0,0,0,603,152,604,153,57,58,0,59,0,0,606,154,0,0,62,0],[0,0,0,603,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,575,0,0,0,246,246,0,58,0,59,0,0,0,139,0,575,0,0],[0,575,0,607,0,246,246,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,616,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,612,265,0,0,0,0],[0,0,0,609,152,610,153,57,58,0,59,0,0,612,154,0,0,62,0],[0,0,0,609,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,549,0,0],[0,0,0,0,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0]],"accepting":[false,true,true,true,true,true,false,false,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,false,false,true,true,true,true,true,true,true,true,true,true,false,true,true,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,false,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,false,true,true,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,false,true,false,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,false,false,true,false,true,true,false,true,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,true,true,false,false,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,false,false,true,true,false,false,true,true,true,false,true,false,true,true,true,true,false,false,false,true,false,true,true,true,true,false,false,false,true,true,false,true,true,true,true,true,true,false,true,true,false,true,false,true,true,true,true,false,false,false,false,false,false,false,true,true,false,false,true,true,false,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,true,false,true,true,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,false,true,true,false,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,true,false,true,true,true,true,false,false,false,true,false,true,true,true,true,true,false,true,true,true,false,true,true,true,true,true,false,true,true,true,true,false,true,true,true,true,true,false,true,true,false,true,true,true],"tags":[[],["broken_cluster"],["consonant_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["consonant_syllable"],["broken_cluster"],["symbol_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["broken_cluster"],["broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],[],["broken_cluster"],["symbol_cluster"],[],["symbol_cluster"],["symbol_cluster"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["symbol_cluster"],["symbol_cluster"],["symbol_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],["symbol_cluster"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],[],["broken_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],[],["consonant_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],[],["vowel_syllable"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],[],[],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],["standalone_cluster"],[],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],[],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],[],[],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],[],[],[],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],["consonant_syllable"],["vowel_syllable"],["standalone_cluster"]]}');var Nr={};Nr=JSON.parse('{"categories":["O","IND","S","GB","B","FM","CGJ","VMAbv","VMPst","VAbv","VPst","CMBlw","VPre","VBlw","H","VMBlw","CMAbv","MBlw","CS","R","SUB","MPst","MPre","FAbv","FPst","FBlw","null","SMAbv","SMBlw","VMPre","ZWNJ","ZWJ","WJ","M","VS","N","HN","MAbv"],"decompositions":{"2507":[2503,2494],"2508":[2503,2519],"2888":[2887,2902],"2891":[2887,2878],"2892":[2887,2903],"3018":[3014,3006],"3019":[3015,3006],"3020":[3014,3031],"3144":[3142,3158],"3264":[3263,3285],"3271":[3270,3285],"3272":[3270,3286],"3274":[3270,3266],"3275":[3270,3266,3285],"3402":[3398,3390],"3403":[3399,3390],"3404":[3398,3415],"3546":[3545,3530],"3548":[3545,3535],"3549":[3545,3535,3530],"3550":[3545,3551],"3635":[3661,3634],"3763":[3789,3762],"3955":[3953,3954],"3957":[3953,3956],"3958":[4018,3968],"3959":[4018,3953,3968],"3960":[4019,3968],"3961":[4019,3953,3968],"3969":[3953,3968],"6971":[6970,6965],"6973":[6972,6965],"6976":[6974,6965],"6977":[6975,6965],"6979":[6978,6965],"69934":[69937,69927],"69935":[69938,69927],"70475":[70471,70462],"70476":[70471,70487],"70843":[70841,70842],"70844":[70841,70832],"70846":[70841,70845],"71098":[71096,71087],"71099":[71097,71087]},"stateTable":[[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[2,2,3,4,4,5,0,6,7,8,9,10,11,12,13,14,15,16,0,17,18,11,19,20,21,22,0,0,0,23,0,0,2,0,0,24,0,25],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,26,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,27,28,0,0,0,0,0,27,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,39,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,39,0,0,47],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,0,0,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,9,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,10,11,12,13,14,0,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,9,0,0,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,10,11,12,13,14,15,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,0,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,4,4,5,0,6,7,8,9,10,11,12,13,14,15,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,48,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,49,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,16,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,21,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,51,0],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,16,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,27,28,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,28,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,31,0,0,0,0,0,0,0,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,0,0,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,33,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,0,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,33,0,0,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,41,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,39,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,0,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,52,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,53,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,40,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,44,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,40,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,0,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,48,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,51,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,54,0,0],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,0,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,52,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,51,0]],"accepting":[false,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true],"tags":[[],["broken_cluster"],["independent_cluster"],["symbol_cluster"],["standard_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["numeral_cluster"],["broken_cluster"],["independent_cluster"],["symbol_cluster"],["symbol_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["virama_terminated_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["broken_cluster"],["broken_cluster"],["numeral_cluster"],["number_joiner_terminated_cluster"],["standard_cluster"],["standard_cluster"],["numeral_cluster"]]}');const pe={X:1,C:2,V:4,N:8,H:16,ZWNJ:32,ZWJ:64,M:128,SM:256,VD:512,A:1024,Placeholder:2048,Dotted_Circle:4096,RS:8192,Coeng:16384,Repha:32768,Ra:65536,CM:131072,Symbol:262144},te={Start:1,Ra_To_Become_Reph:2,Pre_M:4,Pre_C:8,Base_C:16,After_Main:32,Above_C:64,Before_Sub:128,Below_C:256,After_Sub:512,Before_Post:1024,Post_C:2048,After_Post:4096,Final_C:8192,SMVD:16384,End:32768},Uu=pe.C|pe.Ra|pe.CM|pe.V|pe.Placeholder|pe.Dotted_Circle,La=pe.ZWJ|pe.ZWNJ,_t=pe.H|pe.Coeng,Ma={Default:{hasOldSpec:!1,virama:0,basePos:"Last",rephPos:te.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Devanagari:{hasOldSpec:!0,virama:2381,basePos:"Last",rephPos:te.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Bengali:{hasOldSpec:!0,virama:2509,basePos:"Last",rephPos:te.After_Sub,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Gurmukhi:{hasOldSpec:!0,virama:2637,basePos:"Last",rephPos:te.Before_Sub,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Gujarati:{hasOldSpec:!0,virama:2765,basePos:"Last",rephPos:te.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Oriya:{hasOldSpec:!0,virama:2893,basePos:"Last",rephPos:te.After_Main,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Tamil:{hasOldSpec:!0,virama:3021,basePos:"Last",rephPos:te.After_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Telugu:{hasOldSpec:!0,virama:3149,basePos:"Last",rephPos:te.After_Post,rephMode:"Explicit",blwfMode:"Post_Only"},Kannada:{hasOldSpec:!0,virama:3277,basePos:"Last",rephPos:te.After_Post,rephMode:"Implicit",blwfMode:"Post_Only"},Malayalam:{hasOldSpec:!0,virama:3405,basePos:"Last",rephPos:te.After_Main,rephMode:"Log_Repha",blwfMode:"Pre_And_Post"},Khmer:{hasOldSpec:!1,virama:6098,basePos:"First",rephPos:te.Ra_To_Become_Reph,rephMode:"Vis_Repha",blwfMode:"Pre_And_Post"}},Gu={6078:[6081,6078],6079:[6081,6079],6080:[6081,6080],6084:[6081,6084],6085:[6081,6085]},{decompositions:Vu}=pr(Nr),Ra=new B0(gn("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")),zu=new Pn(pr(Ba));class Ve extends V0{static planFeatures(e){e.addStage($u),e.addStage(["locl","ccmp"]),e.addStage(qu),e.addStage("nukt"),e.addStage("akhn"),e.addStage("rphf",!1),e.addStage("rkrf"),e.addStage("pref",!1),e.addStage("blwf",!1),e.addStage("abvf",!1),e.addStage("half",!1),e.addStage("pstf",!1),e.addStage("vatu"),e.addStage("cjct"),e.addStage("cfar",!1),e.addStage(Hu),e.addStage({local:["init"],global:["pres","abvs","blws","psts","haln","dist","abvm","blwm","calt","clig"]}),e.unicodeScript=Bl(e.script),e.indicConfig=Ma[e.unicodeScript]||Ma.Default,e.isOldSpec=e.indicConfig.hasOldSpec&&e.script[e.script.length-1]!=="2"}static assignFeatures(e,t){for(let r=t.length-1;r>=0;r--){let n=t[r].codePoints[0],a=Gu[n]||Vu[n];if(a){let i=a.map(l=>{let u=e.font.glyphForCodePoint(l);return new d0(e.font,u.id,[l],t[r].features)});t.splice(r,1,...i)}}}}we(Ve,"zeroMarkWidths","NONE");function In(s){return Ra.get(s.codePoints[0])>>8}function Na(s){return 1<<(Ra.get(s.codePoints[0])&255)}class Ur{constructor(e,t,r,n){this.category=e,this.position=t,this.syllableType=r,this.syllable=n}}function $u(s,e){let t=0,r=0;for(let[n,a,i]of zu.match(e.map(In))){if(n>r){++t;for(let l=r;l<n;l++)e[l].shaperInfo=new Ur(pe.X,te.End,"non_indic_cluster",t)}++t;for(let l=n;l<=a;l++)e[l].shaperInfo=new Ur(1<<In(e[l]),Na(e[l]),i[0],t);r=a+1}if(r<e.length){++t;for(let n=r;n<e.length;n++)e[n].shaperInfo=new Ur(pe.X,te.End,"non_indic_cluster",t)}}function Q0(s){return s.shaperInfo.category&Uu}function x0(s){return s.shaperInfo.category&La}function k0(s){return s.shaperInfo.category&_t}function O0(s,e){for(let r of s)r.features={[e]:!0};return s[0]._font._layoutEngine.engine.GSUBProcessor.applyFeatures([e],s),s.length===1}function Wu(s,e,t){let r=[t,e,t];return O0(r.slice(0,2),"blwf")||O0(r.slice(1,3),"blwf")?te.Below_C:O0(r.slice(0,2),"pstf")||O0(r.slice(1,3),"pstf")||O0(r.slice(0,2),"pref")||O0(r.slice(1,3),"pref")?te.Post_C:te.Base_C}function qu(s,e,t){let r=t.indicConfig,n=s._layoutEngine.engine.GSUBProcessor.features,a=s.glyphForCodePoint(9676).id,i=s.glyphForCodePoint(r.virama).id;if(i){let l=new d0(s,i,[r.virama]);for(let u=0;u<e.length;u++)e[u].shaperInfo.position===te.Base_C&&(e[u].shaperInfo.position=Wu(s,e[u].copy(),l))}for(let l=0,u=Gr(e,0);l<e.length;l=u,u=Gr(e,l)){let{category:c,syllableType:f}=e[l].shaperInfo;if(f==="symbol_cluster"||f==="non_indic_cluster")continue;if(f==="broken_cluster"&&a){let S=new d0(s,a,[9676]);S.shaperInfo=new Ur(1<<In(S),Na(S),e[l].shaperInfo.syllableType,e[l].shaperInfo.syllable);let Q=l;for(;Q<u&&e[Q].shaperInfo.category===pe.Repha;)Q++;e.splice(Q++,0,S),u++}let d=u,C=l,k=!1;if(r.rephPos!==te.Ra_To_Become_Reph&&n.rphf&&l+3<=u&&(r.rephMode==="Implicit"&&!x0(e[l+2])||r.rephMode==="Explicit"&&e[l+2].shaperInfo.category===pe.ZWJ)){let S=[e[l].copy(),e[l+1].copy(),e[l+2].copy()];if(O0(S.slice(0,2),"rphf")||r.rephMode==="Explicit"&&O0(S,"rphf")){for(C+=2;C<u&&x0(e[C]);)C++;d=l,k=!0}}else if(r.rephMode==="Log_Repha"&&e[l].shaperInfo.category===pe.Repha){for(C++;C<u&&x0(e[C]);)C++;d=l,k=!0}switch(r.basePos){case"Last":{let S=u,Q=!1;do{let ue=e[--S].shaperInfo;if(Q0(e[S])){if(ue.position!==te.Below_C&&(ue.position!==te.Post_C||Q)){d=S;break}ue.position===te.Below_C&&(Q=!0),d=S}else if(l<S&&ue.category===pe.ZWJ&&e[S-1].shaperInfo.category===pe.H)break}while(S>C);break}case"First":d=l;for(let S=d+1;S<u;S++)Q0(e[S])&&(e[S].shaperInfo.position=te.Below_C)}k&&d===l&&C-d<=2&&(k=!1);for(let S=l;S<d;S++){let Q=e[S].shaperInfo;Q.position=Math.min(te.Pre_C,Q.position)}d<u&&(e[d].shaperInfo.position=te.Base_C);for(let S=d+1;S<u;S++)if(e[S].shaperInfo.category===pe.M){for(let Q=S+1;Q<u;Q++)if(Q0(e[Q])){e[Q].shaperInfo.position=te.Final_C;break}break}if(k&&(e[l].shaperInfo.position=te.Ra_To_Become_Reph),t.isOldSpec){let S=t.unicodeScript!=="Malayalam";for(let Q=d+1;Q<u;Q++)if(e[Q].shaperInfo.category===pe.H){let ue;for(ue=u-1;ue>Q&&!(Q0(e[ue])||S&&e[ue].shaperInfo.category===pe.H);ue--);if(e[ue].shaperInfo.category!==pe.H&&ue>Q){let ze=e[Q];e.splice(Q,0,...e.splice(Q+1,ue-Q)),e[ue]=ze}break}}let B=te.Start;for(let S=l;S<u;S++){let Q=e[S].shaperInfo;if(Q.category&(La|pe.N|pe.RS|pe.CM|_t&Q.category)){if(Q.position=B,Q.category===pe.H&&Q.position===te.Pre_M){for(let ue=S;ue>l;ue--)if(e[ue-1].shaperInfo.position!==te.Pre_M){Q.position=e[ue-1].shaperInfo.position;break}}}else Q.position!==te.SMVD&&(B=Q.position)}let _=d;for(let S=d+1;S<u;S++)if(Q0(e[S])){for(let Q=_+1;Q<S;Q++)e[Q].shaperInfo.position<te.SMVD&&(e[Q].shaperInfo.position=e[S].shaperInfo.position);_=S}else e[S].shaperInfo.category===pe.M&&(_=S);let ye=e.slice(l,u);ye.sort((S,Q)=>S.shaperInfo.position-Q.shaperInfo.position),e.splice(l,ye.length,...ye);for(let S=l;S<u;S++)if(e[S].shaperInfo.position===te.Base_C){d=S;break}for(let S=l;S<u&&e[S].shaperInfo.position===te.Ra_To_Become_Reph;S++)e[S].features.rphf=!0;let ee=!t.isOldSpec&&r.blwfMode==="Pre_And_Post";for(let S=l;S<d;S++)e[S].features.half=!0,ee&&(e[S].features.blwf=!0);for(let S=d+1;S<u;S++)e[S].features.abvf=!0,e[S].features.pstf=!0,e[S].features.blwf=!0;if(t.isOldSpec&&t.unicodeScript==="Devanagari")for(let S=l;S+1<d;S++)e[S].shaperInfo.category===pe.Ra&&e[S+1].shaperInfo.category===pe.H&&(S+1===d||e[S+2].shaperInfo.category===pe.ZWJ)&&(e[S].features.blwf=!0,e[S+1].features.blwf=!0);let me=2;if(n.pref&&d+me<u)for(let S=d+1;S+me-1<u;S++){let Q=[e[S].copy(),e[S+1].copy()];if(O0(Q,"pref")){for(let ue=0;ue<me;ue++)e[S++].features.pref=!0;if(n.cfar)for(;S<u;S++)e[S].features.cfar=!0;break}}for(let S=l+1;S<u;S++)if(x0(e[S])){let Q=e[S].shaperInfo.category===pe.ZWNJ,ue=S;do ue--,Q&&delete e[ue].features.half;while(ue>l&&!Q0(e[ue]))}}}function Hu(s,e,t){let r=t.indicConfig,n=s._layoutEngine.engine.GSUBProcessor.features;for(let a=0,i=Gr(e,0);a<e.length;a=i,i=Gr(e,a)){let l=!!n.pref,u=a;for(;u<i;u++)if(e[u].shaperInfo.position>=te.Base_C){if(l&&u+1<i){for(let c=u+1;c<i;c++)if(e[c].features.pref){if(!(e[c].substituted&&e[c].isLigated&&!e[c].isMultiplied)){for(u=c;u<i&&k0(e[u]);)u++;e[u].shaperInfo.position=te.BASE_C,l=!1}break}}if(t.unicodeScript==="Malayalam")for(let c=u+1;c<i;c++){for(;c<i&&x0(e[c]);)c++;if(c===i||!k0(e[c]))break;for(c++;c<i&&x0(e[c]);)c++;c<i&&Q0(e[c])&&e[c].shaperInfo.position===te.Below_C&&(u=c,e[u].shaperInfo.position=te.Base_C)}a<u&&e[u].shaperInfo.position>te.Base_C&&u--;break}if(u===i&&a<u&&e[u-1].shaperInfo.category===pe.ZWJ&&u--,u<i)for(;a<u&&e[u].shaperInfo.category&(pe.N|_t);)u--;if(a+1<i&&a<u){let c=u===i?u-2:u-1;if(t.unicodeScript!=="Malayalam"&&t.unicodeScript!=="Tamil"){for(;c>a&&!(e[c].shaperInfo.category&(pe.M|_t));)c--;k0(e[c])&&e[c].shaperInfo.position!==te.Pre_M?c+1<i&&x0(e[c+1])&&c++:c=a}if(a<c&&e[c].shaperInfo.position!==te.Pre_M){for(let f=c;f>a;f--)if(e[f-1].shaperInfo.position===te.Pre_M){let d=f-1;d<u&&u<=c&&u--;let C=e[d];e.splice(d,0,...e.splice(d+1,c-d)),e[c]=C,c--}}}if(a+1<i&&e[a].shaperInfo.position===te.Ra_To_Become_Reph&&e[a].shaperInfo.category===pe.Repha!==(e[a].isLigated&&!e[a].isMultiplied)){let c,f=r.rephPos,d=!1;if(f!==te.After_Post){for(c=a+1;c<u&&!k0(e[c]);)c++;if(c<u&&k0(e[c])&&(c+1<u&&x0(e[c+1])&&c++,d=!0),!d&&f===te.After_Main){for(c=u;c+1<i&&e[c+1].shaperInfo.position<=te.After_Main;)c++;d=c<i}if(!d&&f===te.After_Sub){for(c=u;c+1<i&&!(e[c+1].shaperInfo.position&(te.Post_C|te.After_Post|te.SMVD));)c++;d=c<i}}if(!d){for(c=a+1;c<u&&!k0(e[c]);)c++;c<u&&k0(e[c])&&(c+1<u&&x0(e[c+1])&&c++,d=!0)}if(!d){for(c=i-1;c>a&&e[c].shaperInfo.position===te.SMVD;)c--;if(k0(e[c]))for(let k=u+1;k<c;k++)e[k].shaperInfo.category===pe.M&&c--}let C=e[a];e.splice(a,0,...e.splice(a+1,c-a)),e[c]=C,a<u&&u<=c&&u--}if(l&&u+1<i){for(let c=u+1;c<i;c++)if(e[c].features.pref){if(e[c].isLigated&&!e[c].isMultiplied){let f=u;if(t.unicodeScript!=="Malayalam"&&t.unicodeScript!=="Tamil"){for(;f>a&&!(e[f-1].shaperInfo.category&(pe.M|_t));)f--;if(f>a&&e[f-1].shaperInfo.category===pe.M){let k=c;for(let B=u+1;B<k;B++)if(e[B].shaperInfo.category===pe.M){f--;break}}}f>a&&k0(e[f-1])&&f<i&&x0(e[f])&&f++;let d=c,C=e[d];e.splice(f+1,0,...e.splice(f,d-f)),e[f]=C,f<=u&&u<d&&u++}break}}e[a].shaperInfo.position===te.Pre_M&&(!a||!/Cf|Mn/.test(o0(e[a-1].codePoints[0])))&&(e[a].features.init=!0)}}function Gr(s,e){if(e>=s.length)return e;let t=s[e].shaperInfo.syllable;for(;++e<s.length&&s[e].shaperInfo.syllable===t;);return e}const{categories:ju,decompositions:Ua}=pr(Nr),Xu=new B0(gn("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")),Yu=new Pn(pr(Nr));class he extends V0{static planFeatures(e){e.addStage(Ku),e.addStage(["locl","ccmp","nukt","akhn"]),e.addStage(Va),e.addStage(["rphf"],!1),e.addStage(Ju),e.addStage(Va),e.addStage(["pref"]),e.addStage(_u),e.addStage(["rkrf","abvf","blwf","half","pstf","vatu","cjct"]),e.addStage(Qu),e.addStage(["abvs","blws","pres","psts","dist","abvm","blwm"])}static assignFeatures(e,t){for(let r=t.length-1;r>=0;r--){let n=t[r].codePoints[0];if(Ua[n]){let a=Ua[n].map(i=>{let l=e.font.glyphForCodePoint(i);return new d0(e.font,l.id,[i],t[r].features)});t.splice(r,1,...a)}}}}we(he,"zeroMarkWidths","BEFORE_GPOS");function Ga(s){return Xu.get(s.codePoints[0])}class Zu{constructor(e,t,r){this.category=e,this.syllableType=t,this.syllable=r}}function Ku(s,e){let t=0;for(let[r,n,a]of Yu.match(e.map(Ga))){++t;for(let l=r;l<=n;l++)e[l].shaperInfo=new Zu(ju[Ga(e[l])],a[0],t);let i=e[r].shaperInfo.category==="R"?1:Math.min(3,n-r);for(let l=r;l<r+i;l++)e[l].features.rphf=!0}}function Va(s,e){for(let t of e)t.substituted=!1}function Ju(s,e){for(let t of e)t.substituted&&t.features.rphf&&(t.shaperInfo.category="R")}function _u(s,e){for(let t of e)t.substituted&&(t.shaperInfo.category="VPre")}function Qu(s,e){let t=s.glyphForCodePoint(9676).id;for(let r=0,n=za(e,0);r<e.length;r=n,n=za(e,r)){let a,i,l=e[r].shaperInfo,u=l.syllableType;if(!(u!=="virama_terminated_cluster"&&u!=="standard_cluster"&&u!=="broken_cluster")){if(u==="broken_cluster"&&t){let c=new d0(s,t,[9676]);for(c.shaperInfo=l,a=r;a<n&&e[a].shaperInfo.category==="R";a++);e.splice(++a,0,c),n++}if(l.category==="R"&&n-r>1){for(a=r+1;a<n;a++)if(l=e[a].shaperInfo,$a(l)||Vr(e[a])){Vr(e[a])&&a--,e.splice(r,0,...e.splice(r+1,a-r),e[a]);break}}for(a=r,i=n;a<n;a++)l=e[a].shaperInfo,$a(l)||Vr(e[a])?i=Vr(e[a])?a+1:a:(l.category==="VPre"||l.category==="VMPre")&&i<a&&e.splice(i,1,e[a],...e.splice(i,a-i))}}}function za(s,e){if(e>=s.length)return e;let t=s[e].shaperInfo.syllable;for(;++e<s.length&&s[e].shaperInfo.syllable===t;);return e}function Vr(s){return s.shaperInfo.category==="H"&&!s.isLigated}function $a(s){return s.category==="B"||s.category==="GB"}const ec={arab:$0,mong:$0,syrc:$0,"nko ":$0,phag:$0,mand:$0,mani:$0,phlp:$0,hang:Oa,bng2:Ve,beng:Ve,dev2:Ve,deva:Ve,gjr2:Ve,gujr:Ve,guru:Ve,gur2:Ve,knda:Ve,knd2:Ve,mlm2:Ve,mlym:Ve,ory2:Ve,orya:Ve,taml:Ve,tml2:Ve,telu:Ve,tel2:Ve,khmr:Ve,bali:he,batk:he,brah:he,bugi:he,buhd:he,cakm:he,cham:he,dupl:he,egyp:he,gran:he,hano:he,java:he,kthi:he,kali:he,khar:he,khoj:he,sind:he,lepc:he,limb:he,mahj:he,mtei:he,modi:he,hmng:he,rjng:he,saur:he,shrd:he,sidd:he,sinh:Ve,sund:he,sylo:he,tglg:he,tagb:he,tale:he,lana:he,tavt:he,takr:he,tibt:he,tfng:he,tirh:he,latn:V0,DFLT:V0};function tc(s){Array.isArray(s)||(s=[s]);for(let e of s){let t=ec[e];if(t)return t}return V0}class rc extends Tr{applyLookup(e,t){switch(e){case 1:{let n=this.coverageIndex(t.coverage);if(n===-1)return!1;let a=this.glyphIterator.cur;switch(t.version){case 1:a.id=a.id+t.deltaGlyphID&65535;break;case 2:a.id=t.substitute.get(n);break}return!0}case 2:{let n=this.coverageIndex(t.coverage);if(n!==-1){let a=t.sequences.get(n);if(a.length===0)return this.glyphs.splice(this.glyphIterator.index,1),!0;this.glyphIterator.cur.id=a[0],this.glyphIterator.cur.ligatureComponent=0;let i=this.glyphIterator.cur.features,l=this.glyphIterator.cur,u=a.slice(1).map((c,f)=>{let d=new d0(this.font,c,void 0,i);return d.shaperInfo=l.shaperInfo,d.isLigated=l.isLigated,d.ligatureComponent=f+1,d.substituted=!0,d.isMultiplied=!0,d});return this.glyphs.splice(this.glyphIterator.index+1,0,...u),!0}return!1}case 3:{let n=this.coverageIndex(t.coverage);if(n!==-1){let a=0;return this.glyphIterator.cur.id=t.alternateSet.get(n)[a],!0}return!1}case 4:{let n=this.coverageIndex(t.coverage);if(n===-1)return!1;for(let a of t.ligatureSets.get(n)){let i=this.sequenceMatchIndices(1,a.components);if(!i)continue;let l=this.glyphIterator.cur,u=l.codePoints.slice();for(let _ of i)u.push(...this.glyphs[_].codePoints);let c=new d0(this.font,a.glyph,u,l.features);c.shaperInfo=l.shaperInfo,c.isLigated=!0,c.substituted=!0;let f=l.isMark;for(let _=0;_<i.length&&f;_++)f=this.glyphs[i[_]].isMark;c.ligatureID=f?null:this.ligatureID++;let d=l.ligatureID,C=l.codePoints.length,k=C,B=this.glyphIterator.index+1;for(let _ of i){if(f)B=_;else for(;B<_;){var r=k-C+Math.min(this.glyphs[B].ligatureComponent||1,C);this.glyphs[B].ligatureID=c.ligatureID,this.glyphs[B].ligatureComponent=r,B++}d=this.glyphs[B].ligatureID,C=this.glyphs[B].codePoints.length,k+=C,B++}if(d&&!f)for(let _=B;_<this.glyphs.length&&this.glyphs[_].ligatureID===d;_++){var r=k-C+Math.min(this.glyphs[_].ligatureComponent||1,C);this.glyphs[_].ligatureComponent=r}for(let _=i.length-1;_>=0;_--)this.glyphs.splice(i[_],1);return this.glyphs[this.glyphIterator.index]=c,!0}return!1}case 5:return this.applyContext(t);case 6:return this.applyChainingContext(t);case 7:return this.applyLookup(t.lookupType,t.extension);default:throw new Error("GSUB lookupType "+e+" is not supported")}}}class nc extends Tr{applyPositionValue(e,t){let r=this.positions[this.glyphIterator.peekIndex(e)];t.xAdvance!=null&&(r.xAdvance+=t.xAdvance),t.yAdvance!=null&&(r.yAdvance+=t.yAdvance),t.xPlacement!=null&&(r.xOffset+=t.xPlacement),t.yPlacement!=null&&(r.yOffset+=t.yPlacement);let n=this.font._variationProcessor,a=this.font.GDEF&&this.font.GDEF.itemVariationStore;n&&a&&(t.xPlaDevice&&(r.xOffset+=n.getDelta(a,t.xPlaDevice.a,t.xPlaDevice.b)),t.yPlaDevice&&(r.yOffset+=n.getDelta(a,t.yPlaDevice.a,t.yPlaDevice.b)),t.xAdvDevice&&(r.xAdvance+=n.getDelta(a,t.xAdvDevice.a,t.xAdvDevice.b)),t.yAdvDevice&&(r.yAdvance+=n.getDelta(a,t.yAdvDevice.a,t.yAdvDevice.b)))}applyLookup(e,t){switch(e){case 1:{let n=this.coverageIndex(t.coverage);if(n===-1)return!1;switch(t.version){case 1:this.applyPositionValue(0,t.value);break;case 2:this.applyPositionValue(0,t.values.get(n));break}return!0}case 2:{let n=this.glyphIterator.peek();if(!n)return!1;let a=this.coverageIndex(t.coverage);if(a===-1)return!1;switch(t.version){case 1:let i=t.pairSets.get(a);for(let c of i)if(c.secondGlyph===n.id)return this.applyPositionValue(0,c.value1),this.applyPositionValue(1,c.value2),!0;return!1;case 2:let l=this.getClassID(this.glyphIterator.cur.id,t.classDef1),u=this.getClassID(n.id,t.classDef2);if(l===-1||u===-1)return!1;var r=t.classRecords.get(l).get(u);return this.applyPositionValue(0,r.value1),this.applyPositionValue(1,r.value2),!0}}case 3:{let n=this.glyphIterator.peekIndex(),a=this.glyphs[n];if(!a)return!1;let i=t.entryExitRecords[this.coverageIndex(t.coverage)];if(!i||!i.exitAnchor)return!1;let l=t.entryExitRecords[this.coverageIndex(t.coverage,a.id)];if(!l||!l.entryAnchor)return!1;let u=this.getAnchor(l.entryAnchor),c=this.getAnchor(i.exitAnchor),f=this.positions[this.glyphIterator.index],d=this.positions[n],C;switch(this.direction){case"ltr":f.xAdvance=c.x+f.xOffset,C=u.x+d.xOffset,d.xAdvance-=C,d.xOffset-=C;break;case"rtl":C=c.x+f.xOffset,f.xAdvance-=C,f.xOffset-=C,d.xAdvance=u.x+d.xOffset;break}return this.glyphIterator.flags.rightToLeft?(this.glyphIterator.cur.cursiveAttachment=n,f.yOffset=u.y-c.y):(a.cursiveAttachment=this.glyphIterator.index,f.yOffset=c.y-u.y),!0}case 4:{let n=this.coverageIndex(t.markCoverage);if(n===-1)return!1;let a=this.glyphIterator.index;for(;--a>=0&&(this.glyphs[a].isMark||this.glyphs[a].ligatureComponent>0););if(a<0)return!1;let i=this.coverageIndex(t.baseCoverage,this.glyphs[a].id);if(i===-1)return!1;let l=t.markArray[n],u=t.baseArray[i][l.class];return this.applyAnchor(l,u,a),!0}case 5:{let n=this.coverageIndex(t.markCoverage);if(n===-1)return!1;let a=this.glyphIterator.index;for(;--a>=0&&this.glyphs[a].isMark;);if(a<0)return!1;let i=this.coverageIndex(t.ligatureCoverage,this.glyphs[a].id);if(i===-1)return!1;let l=t.ligatureArray[i],u=this.glyphIterator.cur,c=this.glyphs[a],f=c.ligatureID&&c.ligatureID===u.ligatureID&&u.ligatureComponent>0?Math.min(u.ligatureComponent,c.codePoints.length)-1:c.codePoints.length-1,d=t.markArray[n],C=l[f][d.class];return this.applyAnchor(d,C,a),!0}case 6:{let n=this.coverageIndex(t.mark1Coverage);if(n===-1)return!1;let a=this.glyphIterator.peekIndex(-1),i=this.glyphs[a];if(!i||!i.isMark)return!1;let l=this.glyphIterator.cur,u=!1;if(l.ligatureID===i.ligatureID?l.ligatureID?l.ligatureComponent===i.ligatureComponent&&(u=!0):u=!0:(l.ligatureID&&!l.ligatureComponent||i.ligatureID&&!i.ligatureComponent)&&(u=!0),!u)return!1;let c=this.coverageIndex(t.mark2Coverage,i.id);if(c===-1)return!1;let f=t.mark1Array[n],d=t.mark2Array[c][f.class];return this.applyAnchor(f,d,a),!0}case 7:return this.applyContext(t);case 8:return this.applyChainingContext(t);case 9:return this.applyLookup(t.lookupType,t.extension);default:throw new Error("Unsupported GPOS table: "+e)}}applyAnchor(e,t,r){let n=this.getAnchor(t),a=this.getAnchor(e.markAnchor),i=this.positions[r],l=this.positions[this.glyphIterator.index];l.xOffset=n.x-a.x,l.yOffset=n.y-a.y,this.glyphIterator.cur.markAttachment=r}getAnchor(e){let t=e.xCoordinate,r=e.yCoordinate,n=this.font._variationProcessor,a=this.font.GDEF&&this.font.GDEF.itemVariationStore;return n&&a&&(e.xDeviceTable&&(t+=n.getDelta(a,e.xDeviceTable.a,e.xDeviceTable.b)),e.yDeviceTable&&(r+=n.getDelta(a,e.yDeviceTable.a,e.yDeviceTable.b))),{x:t,y:r}}applyFeatures(e,t,r){super.applyFeatures(e,t,r);for(var n=0;n<this.glyphs.length;n++)this.fixCursiveAttachment(n);this.fixMarkAttachment()}fixCursiveAttachment(e){let t=this.glyphs[e];if(t.cursiveAttachment!=null){let r=t.cursiveAttachment;t.cursiveAttachment=null,this.fixCursiveAttachment(r),this.positions[e].yOffset+=this.positions[r].yOffset}}fixMarkAttachment(){for(let e=0;e<this.glyphs.length;e++){let t=this.glyphs[e];if(t.markAttachment!=null){let r=t.markAttachment;if(this.positions[e].xOffset+=this.positions[r].xOffset,this.positions[e].yOffset+=this.positions[r].yOffset,this.direction==="ltr")for(let n=r;n<e;n++)this.positions[e].xOffset-=this.positions[n].xAdvance,this.positions[e].yOffset-=this.positions[n].yAdvance;else for(let n=r+1;n<e+1;n++)this.positions[e].xOffset+=this.positions[n].xAdvance,this.positions[e].yOffset+=this.positions[n].yAdvance}}}}class ac{setup(e){this.glyphInfos=e.glyphs.map(r=>new d0(this.font,r.id,[...r.codePoints]));let t=null;this.GPOSProcessor&&(t=this.GPOSProcessor.selectScript(e.script,e.language,e.direction)),this.GSUBProcessor&&(t=this.GSUBProcessor.selectScript(e.script,e.language,e.direction)),this.shaper=tc(t),this.plan=new au(this.font,t,e.direction),this.shaper.plan(this.plan,this.glyphInfos,e.features);for(let r in this.plan.allFeatures)e.features[r]=!0}substitute(e){this.GSUBProcessor&&(this.plan.process(this.GSUBProcessor,this.glyphInfos),e.glyphs=this.glyphInfos.map(t=>this.font.getGlyph(t.id,t.codePoints)))}position(e){return this.shaper.zeroMarkWidths==="BEFORE_GPOS"&&this.zeroMarkAdvances(e.positions),this.GPOSProcessor&&this.plan.process(this.GPOSProcessor,this.glyphInfos,e.positions),this.shaper.zeroMarkWidths==="AFTER_GPOS"&&this.zeroMarkAdvances(e.positions),e.direction==="rtl"&&(e.glyphs.reverse(),e.positions.reverse()),this.GPOSProcessor&&this.GPOSProcessor.features}zeroMarkAdvances(e){for(let t=0;t<this.glyphInfos.length;t++)this.glyphInfos[t].isMark&&(e[t].xAdvance=0,e[t].yAdvance=0)}cleanup(){this.glyphInfos=null,this.plan=null,this.shaper=null}getAvailableFeatures(e,t){let r=[];return this.GSUBProcessor&&(this.GSUBProcessor.selectScript(e,t),r.push(...Object.keys(this.GSUBProcessor.features))),this.GPOSProcessor&&(this.GPOSProcessor.selectScript(e,t),r.push(...Object.keys(this.GPOSProcessor.features))),r}constructor(e){this.font=e,this.glyphInfos=null,this.plan=null,this.GSUBProcessor=null,this.GPOSProcessor=null,this.fallbackPosition=!0,e.GSUB&&(this.GSUBProcessor=new rc(e,e.GSUB)),e.GPOS&&(this.GPOSProcessor=new nc(e,e.GPOS))}}class sc{layout(e,t,r,n,a){if(typeof t=="string"&&(a=n,n=r,r=t,t=[]),typeof e=="string"){r==null&&(r=Ll(e));var i=this.font.glyphsForString(e)}else{if(r==null){let c=[];for(let f of e)c.push(...f.codePoints);r=Ml(c)}var i=e}let l=new Nl(i,t,r,n,a);return i.length===0?(l.positions=[],l):(this.engine&&this.engine.setup&&this.engine.setup(l),this.substitute(l),this.position(l),this.hideDefaultIgnorables(l.glyphs,l.positions),this.engine&&this.engine.cleanup&&this.engine.cleanup(),l)}substitute(e){this.engine&&this.engine.substitute&&this.engine.substitute(e)}position(e){e.positions=e.glyphs.map(r=>new Ul(r.advanceWidth));let t=null;this.engine&&this.engine.position&&(t=this.engine.position(e)),!t&&(!this.engine||this.engine.fallbackPosition)&&(this.unicodeLayoutEngine||(this.unicodeLayoutEngine=new Dl(this.font)),this.unicodeLayoutEngine.positionGlyphs(e.glyphs,e.positions)),(!t||!t.kern)&&e.features.kern!==!1&&this.font.kern&&(this.kernProcessor||(this.kernProcessor=new Fl(this.font)),this.kernProcessor.process(e.glyphs,e.positions),e.features.kern=!0)}hideDefaultIgnorables(e,t){let r=this.font.glyphForCodePoint(32);for(let n=0;n<e.length;n++)this.isDefaultIgnorable(e[n].codePoints[0])&&(e[n]=r,t[n].xAdvance=0,t[n].yAdvance=0)}isDefaultIgnorable(e){let t=e>>16;if(t===0)switch(e>>8){case 0:return e===173;case 3:return e===847;case 6:return e===1564;case 23:return 6068<=e&&e<=6069;case 24:return 6155<=e&&e<=6158;case 32:return 8203<=e&&e<=8207||8234<=e&&e<=8238||8288<=e&&e<=8303;case 254:return 65024<=e&&e<=65039||e===65279;case 255:return 65520<=e&&e<=65528;default:return!1}else switch(t){case 1:return 113824<=e&&e<=113827||119155<=e&&e<=119162;case 14:return 917504<=e&&e<=921599;default:return!1}}getAvailableFeatures(e,t){let r=[];return this.engine&&r.push(...this.engine.getAvailableFeatures(e,t)),this.font.kern&&r.indexOf("kern")===-1&&r.push("kern"),r}stringsForGlyph(e){let t=new Set,r=this.font._cmapProcessor.codePointsForGlyph(e);for(let n of r)t.add(String.fromCodePoint(n));if(this.engine&&this.engine.stringsForGlyph)for(let n of this.engine.stringsForGlyph(e))t.add(n);return Array.from(t)}constructor(e){this.font=e,this.unicodeLayoutEngine=null,this.kernProcessor=null,this.font.morx?this.engine=new nu(this.font):(this.font.GSUB||this.font.GPOS)&&(this.engine=new ac(this.font))}}const ic={moveTo:"M",lineTo:"L",quadraticCurveTo:"Q",bezierCurveTo:"C",closePath:"Z"};class xt{toFunction(){return e=>{this.commands.forEach(t=>e[t.command].apply(e,t.args))}}toSVG(){return this.commands.map(t=>{let r=t.args.map(n=>Math.round(n*100)/100);return""+ic[t.command]+r.join(" ")}).join("")}get cbox(){if(!this._cbox){let e=new G0;for(let t of this.commands)for(let r=0;r<t.args.length;r+=2)e.addPoint(t.args[r],t.args[r+1]);this._cbox=Object.freeze(e)}return this._cbox}get bbox(){if(this._bbox)return this._bbox;let e=new G0,t=0,r=0,n=me=>Math.pow(1-me,3)*k[ee]+3*Math.pow(1-me,2)*me*B[ee]+3*(1-me)*Math.pow(me,2)*_[ee]+Math.pow(me,3)*ye[ee];for(let me of this.commands)switch(me.command){case"moveTo":case"lineTo":let[S,Q]=me.args;e.addPoint(S,Q),t=S,r=Q;break;case"quadraticCurveTo":case"bezierCurveTo":if(me.command==="quadraticCurveTo")var[a,i,d,C]=me.args,l=t+2/3*(a-t),u=r+2/3*(i-r),c=d+2/3*(a-d),f=C+2/3*(i-C);else var[l,u,c,f,d,C]=me.args;e.addPoint(d,C);for(var k=[t,r],B=[l,u],_=[c,f],ye=[d,C],ee=0;ee<=1;ee++){let ue=6*k[ee]-12*B[ee]+6*_[ee],ze=-3*k[ee]+9*B[ee]-9*_[ee]+3*ye[ee];if(me=3*B[ee]-3*k[ee],ze===0){if(ue===0)continue;let ke=-me/ue;0<ke&&ke<1&&(ee===0?e.addPoint(n(ke),e.maxY):ee===1&&e.addPoint(e.maxX,n(ke)));continue}let je=Math.pow(ue,2)-4*me*ze;if(je<0)continue;let Ie=(-ue+Math.sqrt(je))/(2*ze);0<Ie&&Ie<1&&(ee===0?e.addPoint(n(Ie),e.maxY):ee===1&&e.addPoint(e.maxX,n(Ie)));let a0=(-ue-Math.sqrt(je))/(2*ze);0<a0&&a0<1&&(ee===0?e.addPoint(n(a0),e.maxY):ee===1&&e.addPoint(e.maxX,n(a0)))}t=d,r=C;break}return this._bbox=Object.freeze(e)}mapPoints(e){let t=new xt;for(let r of this.commands){let n=[];for(let a=0;a<r.args.length;a+=2){let[i,l]=e(r.args[a],r.args[a+1]);n.push(i,l)}t[r.command](...n)}return t}transform(e,t,r,n,a,i){return this.mapPoints((l,u)=>{const c=e*l+r*u+a,f=t*l+n*u+i;return[c,f]})}translate(e,t){return this.transform(1,0,0,1,e,t)}rotate(e){let t=Math.cos(e),r=Math.sin(e);return this.transform(t,r,-r,t,0,0)}scale(e,t){return t===void 0&&(t=e),this.transform(e,0,0,t,0,0)}constructor(){this.commands=[],this._bbox=null,this._cbox=null}}for(let s of["moveTo","lineTo","quadraticCurveTo","bezierCurveTo","closePath"])xt.prototype[s]=function(){this._bbox=this._cbox=null;for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.commands.push({command:s,args:t}),this};var Qt=[".notdef",".null","nonmarkingreturn","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quotesingle","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","grave","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","Adieresis","Aring","Ccedilla","Eacute","Ntilde","Odieresis","Udieresis","aacute","agrave","acircumflex","adieresis","atilde","aring","ccedilla","eacute","egrave","ecircumflex","edieresis","iacute","igrave","icircumflex","idieresis","ntilde","oacute","ograve","ocircumflex","odieresis","otilde","uacute","ugrave","ucircumflex","udieresis","dagger","degree","cent","sterling","section","bullet","paragraph","germandbls","registered","copyright","trademark","acute","dieresis","notequal","AE","Oslash","infinity","plusminus","lessequal","greaterequal","yen","mu","partialdiff","summation","product","pi","integral","ordfeminine","ordmasculine","Omega","ae","oslash","questiondown","exclamdown","logicalnot","radical","florin","approxequal","Delta","guillemotleft","guillemotright","ellipsis","nonbreakingspace","Agrave","Atilde","Otilde","OE","oe","endash","emdash","quotedblleft","quotedblright","quoteleft","quoteright","divide","lozenge","ydieresis","Ydieresis","fraction","currency","guilsinglleft","guilsinglright","fi","fl","daggerdbl","periodcentered","quotesinglbase","quotedblbase","perthousand","Acircumflex","Ecircumflex","Aacute","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Oacute","Ocircumflex","apple","Ograve","Uacute","Ucircumflex","Ugrave","dotlessi","circumflex","tilde","macron","breve","dotaccent","ring","cedilla","hungarumlaut","ogonek","caron","Lslash","lslash","Scaron","scaron","Zcaron","zcaron","brokenbar","Eth","eth","Yacute","yacute","Thorn","thorn","minus","multiply","onesuperior","twosuperior","threesuperior","onehalf","onequarter","threequarters","franc","Gbreve","gbreve","Idotaccent","Scedilla","scedilla","Cacute","cacute","Ccaron","ccaron","dcroat"];class w0{_getPath(){return new xt}_getCBox(){return this.path.cbox}_getBBox(){return this.path.bbox}_getTableMetrics(e){if(this.id<e.metrics.length)return e.metrics.get(this.id);let t=e.metrics.get(e.metrics.length-1);return{advance:t?t.advance:0,bearing:e.bearings.get(this.id-e.metrics.length)||0}}_getMetrics(e){if(this._metrics)return this._metrics;let{advance:t,bearing:r}=this._getTableMetrics(this._font.hmtx);if(this._font.vmtx)var{advance:n,bearing:a}=this._getTableMetrics(this._font.vmtx);else{let i;if((typeof e>"u"||e===null)&&({cbox:e}=this),(i=this._font["OS/2"])&&i.version>0)var n=Math.abs(i.typoAscender-i.typoDescender),a=i.typoAscender-e.maxY;else{let{hhea:l}=this._font;var n=Math.abs(l.ascent-l.descent),a=l.ascent-e.maxY}}return this._font._variationProcessor&&this._font.HVAR&&(t+=this._font._variationProcessor.getAdvanceAdjustment(this.id,this._font.HVAR)),this._metrics={advanceWidth:t,advanceHeight:n,leftBearing:r,topBearing:a}}get cbox(){return this._getCBox()}get bbox(){return this._getBBox()}get path(){return this._getPath()}getScaledPath(e){let t=1/this._font.unitsPerEm*e;return this.path.scale(t)}get advanceWidth(){return this._getMetrics().advanceWidth}get advanceHeight(){return this._getMetrics().advanceHeight}get ligatureCaretPositions(){}_getName(){let{post:e}=this._font;if(!e)return null;switch(e.version){case 1:return Qt[this.id];case 2:let t=e.glyphNameIndex[this.id];return t<Qt.length?Qt[t]:e.names[t-Qt.length];case 2.5:return Qt[this.id+e.offsets[this.id]];case 4:return String.fromCharCode(e.map[this.id])}}get name(){return this._getName()}render(e,t){e.save();let r=1/this._font.head.unitsPerEm*t;e.scale(r,r),this.path.toFunction()(e),e.fill(),e.restore()}constructor(e,t,r){this.id=e,this.codePoints=t,this._font=r,this.isMark=this.codePoints.length>0&&this.codePoints.every(_r),this.isLigature=this.codePoints.length>1}}Me([He],w0.prototype,"cbox",null),Me([He],w0.prototype,"bbox",null),Me([He],w0.prototype,"path",null),Me([He],w0.prototype,"advanceWidth",null),Me([He],w0.prototype,"advanceHeight",null),Me([He],w0.prototype,"name",null);let Wa=new v({numberOfContours:x,xMin:x,yMin:x,xMax:x,yMax:x});const oc=1,lc=2,uc=4,cc=8,fc=16,dc=32,hc=1,wf=2,yf=4,pc=8,qa=32,bc=64,gc=128,vc=256,Cf=512,Sf=1024,Af=2048,If=4096;class h0{copy(){return new h0(this.onCurve,this.endContour,this.x,this.y)}constructor(e,t,r,n){r===void 0&&(r=0),n===void 0&&(n=0),this.onCurve=e,this.endContour=t,this.x=r,this.y=n}}class mc{constructor(e,t,r){this.glyphID=e,this.dx=t,this.dy=r,this.pos=0,this.scaleX=this.scaleY=1,this.scale01=this.scale10=0}}class zr extends w0{_getCBox(e){if(this._font._variationProcessor&&!e)return this.path.cbox;let t=this._font._getTableStream("glyf");t.pos+=this._font.loca.offsets[this.id];let r=Wa.decode(t),n=new G0(r.xMin,r.yMin,r.xMax,r.yMax);return Object.freeze(n)}_parseGlyphCoord(e,t,r,n){if(r){var a=e.readUInt8();n||(a=-a),a+=t}else if(n)var a=t;else var a=t+e.readInt16BE();return a}_decode(){let e=this._font.loca.offsets[this.id],t=this._font.loca.offsets[this.id+1];if(e===t)return null;let r=this._font._getTableStream("glyf");r.pos+=e;let n=r.pos,a=Wa.decode(r);return a.numberOfContours>0?this._decodeSimple(a,r):a.numberOfContours<0&&this._decodeComposite(a,r,n),a}_decodeSimple(e,t){e.points=[];let r=new g(o,e.numberOfContours).decode(t);e.instructions=new g(y,o).decode(t);let n=[],a=r[r.length-1]+1;for(;n.length<a;){var i=t.readUInt8();if(n.push(i),i&cc){let f=t.readUInt8();for(let d=0;d<f;d++)n.push(i)}}for(var l=0;l<n.length;l++){var i=n[l];let d=new h0(!!(i&oc),r.indexOf(l)>=0,0,0);e.points.push(d)}let u=0;for(var l=0;l<n.length;l++){var i=n[l];e.points[l].x=u=this._parseGlyphCoord(t,u,i&lc,i&fc)}let c=0;for(var l=0;l<n.length;l++){var i=n[l];e.points[l].y=c=this._parseGlyphCoord(t,c,i&uc,i&dc)}if(this._font._variationProcessor){let f=e.points.slice();f.push(...this._getPhantomPoints(e)),this._font._variationProcessor.transformPoints(this.id,f),e.phantomPoints=f.slice(-4)}}_decodeComposite(e,t,r){r===void 0&&(r=0),e.components=[];let n=!1,a=qa;for(;a&qa;){a=t.readUInt16BE();let c=t.pos-r,f=t.readUInt16BE();if(n||(n=(a&vc)!==0),a&hc)var i=t.readInt16BE(),l=t.readInt16BE();else var i=t.readInt8(),l=t.readInt8();var u=new mc(f,i,l);u.pos=c,a&pc?u.scaleX=u.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824:a&bc?(u.scaleX=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824,u.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824):a&gc&&(u.scaleX=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824,u.scale01=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824,u.scale10=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824,u.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/1073741824),e.components.push(u)}if(this._font._variationProcessor){let c=[];for(let f=0;f<e.components.length;f++){var u=e.components[f];c.push(new h0(!0,!0,u.dx,u.dy))}c.push(...this._getPhantomPoints(e)),this._font._variationProcessor.transformPoints(this.id,c),e.phantomPoints=c.splice(-4,4);for(let f=0;f<c.length;f++){let d=c[f];e.components[f].dx=d.x,e.components[f].dy=d.y}}return n}_getPhantomPoints(e){let t=this._getCBox(!0);this._metrics==null&&(this._metrics=w0.prototype._getMetrics.call(this,t));let{advanceWidth:r,advanceHeight:n,leftBearing:a,topBearing:i}=this._metrics;return[new h0(!1,!0,e.xMin-a,0),new h0(!1,!0,e.xMin-a+r,0),new h0(!1,!0,0,e.yMax+i),new h0(!1,!0,0,e.yMax+i+n)]}_getContours(){let e=this._decode();if(!e)return[];let t=[];if(e.numberOfContours<0)for(let i of e.components){let l=this._font.getGlyph(i.glyphID)._getContours();for(let u=0;u<l.length;u++){let c=l[u];for(let f=0;f<c.length;f++){let d=c[f],C=d.x*i.scaleX+d.y*i.scale01+i.dx,k=d.y*i.scaleY+d.x*i.scale10+i.dy;t.push(new h0(d.onCurve,d.endContour,C,k))}}}else t=e.points||[];e.phantomPoints&&!this._font.directory.tables.HVAR&&(this._metrics.advanceWidth=e.phantomPoints[1].x-e.phantomPoints[0].x,this._metrics.advanceHeight=e.phantomPoints[3].y-e.phantomPoints[2].y,this._metrics.leftBearing=e.xMin-e.phantomPoints[0].x,this._metrics.topBearing=e.phantomPoints[2].y-e.yMax);let r=[],n=[];for(let i=0;i<t.length;i++){var a=t[i];n.push(a),a.endContour&&(r.push(n),n=[])}return r}_getMetrics(){if(this._metrics)return this._metrics;let e=this._getCBox(!0);return super._getMetrics(e),this._font._variationProcessor&&!this._font.HVAR&&this.path,this._metrics}_getPath(){let e=this._getContours(),t=new xt;for(let n=0;n<e.length;n++){let a=e[n],i=a[0],l=a[a.length-1],u=0;if(i.onCurve){var r=null;u=1}else{l.onCurve?i=l:i=new h0(!1,!1,(i.x+l.x)/2,(i.y+l.y)/2);var r=i}t.moveTo(i.x,i.y);for(let c=u;c<a.length;c++){let f=a[c],d=c===0?i:a[c-1];if(d.onCurve&&f.onCurve)t.lineTo(f.x,f.y);else if(d.onCurve&&!f.onCurve)var r=f;else if(!d.onCurve&&!f.onCurve){let k=(d.x+f.x)/2,B=(d.y+f.y)/2;t.quadraticCurveTo(d.x,d.y,k,B);var r=f}else if(!d.onCurve&&f.onCurve){t.quadraticCurveTo(r.x,r.y,f.x,f.y);var r=null}else throw new Error("Unknown TTF path state")}r&&t.quadraticCurveTo(r.x,r.y,i.x,i.y),t.closePath()}return t}constructor(){super(...arguments),we(this,"type","TTF")}}class xc extends w0{_getName(){return this._font.CFF2?super._getName():this._font["CFF "].getGlyphName(this.id)}bias(e){return e.length<1240?107:e.length<33900?1131:32768}_getPath(){let e=this._font.CFF2||this._font["CFF "],{stream:t}=e,r=e.topDict.CharStrings[this.id],n=r.offset+r.length;t.pos=r.offset;let a=new xt,i=[],l=[],u=null,c=0,f=0,d=0,C,k,B=!1;this._usedGsubrs=C={},this._usedSubrs=k={};let _=e.globalSubrIndex||[],ye=this.bias(_),ee=e.privateDictForGlyph(this.id)||{},me=ee.Subrs||[],S=this.bias(me),Q=e.topDict.vstore&&e.topDict.vstore.itemVariationStore,ue=ee.vsindex,ze=this._font._variationProcessor;function je(){u==null&&(u=i.shift()+ee.nominalWidthX)}function Ie(){return i.length%2!==0&&je(),c+=i.length>>1,i.length=0}function a0(p0,C0){B&&a.closePath(),a.moveTo(p0,C0),B=!0}let ke=function(){for(;t.pos<n;){let e0=t.readUInt8();if(e0<32){let wt,S0,q0,Pe,Te,Fe,De,yt,Ct,St,At,It,Et,kt,Ot,P0;switch(e0){case 1:case 3:case 18:case 23:Ie();break;case 4:i.length>1&&je(),d+=i.shift(),a0(f,d);break;case 5:for(;i.length>=2;)f+=i.shift(),d+=i.shift(),a.lineTo(f,d);break;case 6:case 7:for(q0=e0===6;i.length>=1;)q0?f+=i.shift():d+=i.shift(),a.lineTo(f,d),q0=!q0;break;case 8:for(;i.length>0;)Pe=f+i.shift(),Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe+i.shift(),d=De+i.shift(),a.bezierCurveTo(Pe,Te,Fe,De,f,d);break;case 10:if(wt=i.pop()+S,S0=me[wt],S0){k[wt]=!0;let Ce=t.pos,Ue=n;t.pos=S0.offset,n=S0.offset+S0.length,ke(),t.pos=Ce,n=Ue}break;case 11:if(e.version>=2)break;return;case 14:if(e.version>=2)break;i.length>0&&je(),B&&(a.closePath(),B=!1);break;case 15:if(e.version<2)throw new Error("vsindex operator not supported in CFF v1");ue=i.pop();break;case 16:{if(e.version<2)throw new Error("blend operator not supported in CFF v1");if(!ze)throw new Error("blend operator in non-variation font");let Ce=ze.getBlendVector(Q,ue),Ue=i.pop(),$r=Ue*Ce.length,l0=i.length-$r,Wr=l0-Ue;for(let Pt=0;Pt<Ue;Pt++){let qr=i[Wr+Pt];for(let er=0;er<Ce.length;er++)qr+=Ce[er]*i[l0++];i[Wr+Pt]=qr}for(;$r--;)i.pop();break}case 19:case 20:Ie(),t.pos+=c+7>>3;break;case 21:i.length>2&&je(),f+=i.shift(),d+=i.shift(),a0(f,d);break;case 22:i.length>1&&je(),f+=i.shift(),a0(f,d);break;case 24:for(;i.length>=8;)Pe=f+i.shift(),Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe+i.shift(),d=De+i.shift(),a.bezierCurveTo(Pe,Te,Fe,De,f,d);f+=i.shift(),d+=i.shift(),a.lineTo(f,d);break;case 25:for(;i.length>=8;)f+=i.shift(),d+=i.shift(),a.lineTo(f,d);Pe=f+i.shift(),Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe+i.shift(),d=De+i.shift(),a.bezierCurveTo(Pe,Te,Fe,De,f,d);break;case 26:for(i.length%2&&(f+=i.shift());i.length>=4;)Pe=f,Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe,d=De+i.shift(),a.bezierCurveTo(Pe,Te,Fe,De,f,d);break;case 27:for(i.length%2&&(d+=i.shift());i.length>=4;)Pe=f+i.shift(),Te=d,Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe+i.shift(),d=De,a.bezierCurveTo(Pe,Te,Fe,De,f,d);break;case 28:i.push(t.readInt16BE());break;case 29:if(wt=i.pop()+ye,S0=_[wt],S0){C[wt]=!0;let Ce=t.pos,Ue=n;t.pos=S0.offset,n=S0.offset+S0.length,ke(),t.pos=Ce,n=Ue}break;case 30:case 31:for(q0=e0===31;i.length>=4;)q0?(Pe=f+i.shift(),Te=d,Fe=Pe+i.shift(),De=Te+i.shift(),d=De+i.shift(),f=Fe+(i.length===1?i.shift():0)):(Pe=f,Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),f=Fe+i.shift(),d=De+(i.length===1?i.shift():0)),a.bezierCurveTo(Pe,Te,Fe,De,f,d),q0=!q0;break;case 12:switch(e0=t.readUInt8(),e0){case 3:let Ce=i.pop(),Ue=i.pop();i.push(Ce&&Ue?1:0);break;case 4:Ce=i.pop(),Ue=i.pop(),i.push(Ce||Ue?1:0);break;case 5:Ce=i.pop(),i.push(Ce?0:1);break;case 9:Ce=i.pop(),i.push(Math.abs(Ce));break;case 10:Ce=i.pop(),Ue=i.pop(),i.push(Ce+Ue);break;case 11:Ce=i.pop(),Ue=i.pop(),i.push(Ce-Ue);break;case 12:Ce=i.pop(),Ue=i.pop(),i.push(Ce/Ue);break;case 14:Ce=i.pop(),i.push(-Ce);break;case 15:Ce=i.pop(),Ue=i.pop(),i.push(Ce===Ue?1:0);break;case 18:i.pop();break;case 20:let $r=i.pop(),l0=i.pop();l[l0]=$r;break;case 21:l0=i.pop(),i.push(l[l0]||0);break;case 22:let Wr=i.pop(),Pt=i.pop(),qr=i.pop(),er=i.pop();i.push(qr<=er?Wr:Pt);break;case 23:i.push(Math.random());break;case 24:Ce=i.pop(),Ue=i.pop(),i.push(Ce*Ue);break;case 26:Ce=i.pop(),i.push(Math.sqrt(Ce));break;case 27:Ce=i.pop(),i.push(Ce,Ce);break;case 28:Ce=i.pop(),Ue=i.pop(),i.push(Ue,Ce);break;case 29:l0=i.pop(),l0<0?l0=0:l0>i.length-1&&(l0=i.length-1),i.push(i[l0]);break;case 30:let Hr=i.pop(),tr=i.pop();if(tr>=0)for(;tr>0;){var p0=i[Hr-1];for(let A0=Hr-2;A0>=0;A0--)i[A0+1]=i[A0];i[0]=p0,tr--}else for(;tr<0;){var p0=i[0];for(let jr=0;jr<=Hr;jr++)i[jr]=i[jr+1];i[Hr-1]=p0,tr++}break;case 34:Pe=f+i.shift(),Te=d,Fe=Pe+i.shift(),De=Te+i.shift(),yt=Fe+i.shift(),Ct=De,St=yt+i.shift(),At=Ct,It=St+i.shift(),Et=At,kt=It+i.shift(),Ot=Et,f=kt,d=Ot,a.bezierCurveTo(Pe,Te,Fe,De,yt,Ct),a.bezierCurveTo(St,At,It,Et,kt,Ot);break;case 35:P0=[];for(let A0=0;A0<=5;A0++)f+=i.shift(),d+=i.shift(),P0.push(f,d);a.bezierCurveTo(...P0.slice(0,6)),a.bezierCurveTo(...P0.slice(6)),i.shift();break;case 36:Pe=f+i.shift(),Te=d+i.shift(),Fe=Pe+i.shift(),De=Te+i.shift(),yt=Fe+i.shift(),Ct=De,St=yt+i.shift(),At=Ct,It=St+i.shift(),Et=At+i.shift(),kt=It+i.shift(),Ot=Et,f=kt,d=Ot,a.bezierCurveTo(Pe,Te,Fe,De,yt,Ct),a.bezierCurveTo(St,At,It,Et,kt,Ot);break;case 37:let as=f,ss=d;P0=[];for(let A0=0;A0<=4;A0++)f+=i.shift(),d+=i.shift(),P0.push(f,d);Math.abs(f-as)>Math.abs(d-ss)?(f+=i.shift(),d=ss):(f=as,d+=i.shift()),P0.push(f,d),a.bezierCurveTo(...P0.slice(0,6)),a.bezierCurveTo(...P0.slice(6));break;default:throw new Error("Unknown op: 12 "+e0)}break;default:throw new Error("Unknown op: "+e0)}}else if(e0<247)i.push(e0-139);else if(e0<251){var C0=t.readUInt8();i.push((e0-247)*256+C0+108)}else if(e0<255){var C0=t.readUInt8();i.push(-(e0-251)*256-C0-108)}else i.push(t.readInt32BE()/65536)}};return ke(),B&&a.closePath(),a}constructor(){super(...arguments),we(this,"type","CFF")}}let wc=new v({originX:o,originY:o,type:new j(4),data:new z(s=>s.parent.buflen-s._currentOffset)});class yc extends zr{getImageForSize(e){for(let i=0;i<this._font.sbix.imageTables.length;i++){var t=this._font.sbix.imageTables[i];if(t.ppem>=e)break}let r=t.imageOffsets,n=r[this.id],a=r[this.id+1];return n===a?null:(this._font.stream.pos=n,wc.decode(this._font.stream,{buflen:a-n}))}render(e,t){let r=this.getImageForSize(t);if(r!=null){let n=t/this._font.unitsPerEm;e.image(r.data,{height:t,x:r.originX,y:(this.bbox.minY-r.originY)*n})}this._font.sbix.flags.renderOutlines&&super.render(e,t)}constructor(){super(...arguments),we(this,"type","SBIX")}}class Ha{constructor(e,t){this.glyph=e,this.color=t}}class Cc extends w0{_getBBox(){let e=new G0;for(let t=0;t<this.layers.length;t++){let n=this.layers[t].glyph.bbox;e.addPoint(n.minX,n.minY),e.addPoint(n.maxX,n.maxY)}return e}get layers(){let e=this._font.CPAL,t=this._font.COLR,r=0,n=t.baseGlyphRecord.length-1;for(;r<=n;){let f=r+n>>1;var a=t.baseGlyphRecord[f];if(this.id<a.gid)n=f-1;else if(this.id>a.gid)r=f+1;else{var i=a;break}}if(i==null){var l=this._font._getBaseGlyph(this.id),u={red:0,green:0,blue:0,alpha:255};return[new Ha(l,u)]}let c=[];for(let f=i.firstLayerIndex;f<i.firstLayerIndex+i.numLayers;f++){var a=t.layerRecords[f],u=e.colorRecords[a.paletteIndex],l=this._font._getBaseGlyph(a.gid);c.push(new Ha(l,u))}return c}render(e,t){for(let{glyph:r,color:n}of this.layers)e.fillColor([n.red,n.green,n.blue],n.alpha/255*100),r.render(e,t)}constructor(){super(...arguments),we(this,"type","COLR")}}const Sc=32768,Ac=4095,Ic=32768,ja=16384,Ec=8192,Xa=4095,Ya=128,Za=127,kc=128,Oc=64,Pc=63;class Tc{normalizeCoords(e){let t=[];for(var r=0;r<this.font.fvar.axis.length;r++){let n=this.font.fvar.axis[r];e[r]<n.defaultValue?t.push((e[r]-n.defaultValue+Number.EPSILON)/(n.defaultValue-n.minValue+Number.EPSILON)):t.push((e[r]-n.defaultValue+Number.EPSILON)/(n.maxValue-n.defaultValue+Number.EPSILON))}if(this.font.avar)for(var r=0;r<this.font.avar.segment.length;r++){let a=this.font.avar.segment[r];for(let i=0;i<a.correspondence.length;i++){let l=a.correspondence[i];if(i>=1&&t[r]<l.fromCoord){let u=a.correspondence[i-1];t[r]=((t[r]-u.fromCoord)*(l.toCoord-u.toCoord)+Number.EPSILON)/(l.fromCoord-u.fromCoord+Number.EPSILON)+u.toCoord;break}}}return t}transformPoints(e,t){if(!this.font.fvar||!this.font.gvar)return;let{gvar:r}=this.font;if(e>=r.glyphCount)return;let n=r.offsets[e];if(n===r.offsets[e+1])return;let{stream:a}=this.font;if(a.pos=n,a.pos>=a.length)return;let i=a.readUInt16BE(),l=n+a.readUInt16BE();if(i&Sc){var u=a.pos;a.pos=l;var c=this.decodePoints();l=a.pos,a.pos=u}let f=t.map(ye=>ye.copy());i&=Ac;for(let ye=0;ye<i;ye++){let ee=a.readUInt16BE(),me=a.readUInt16BE();if(me&Ic){var d=[];for(let Ie=0;Ie<r.axisCount;Ie++)d.push(a.readInt16BE()/16384)}else{if((me&Xa)>=r.globalCoordCount)throw new Error("Invalid gvar table");var d=r.globalCoords[me&Xa]}if(me&ja){var C=[];for(let Ie=0;Ie<r.axisCount;Ie++)C.push(a.readInt16BE()/16384);var k=[];for(let Ie=0;Ie<r.axisCount;Ie++)k.push(a.readInt16BE()/16384)}let S=this.tupleFactor(me,d,C,k);if(S===0){l+=ee;continue}var u=a.pos;if(a.pos=l,me&Ec)var B=this.decodePoints();else var B=c;let ue=B.length===0?t.length:B.length,ze=this.decodeDeltas(ue),je=this.decodeDeltas(ue);if(B.length===0)for(let Ie=0;Ie<t.length;Ie++){var _=t[Ie];_.x+=Math.round(ze[Ie]*S),_.y+=Math.round(je[Ie]*S)}else{let Ie=f.map(ke=>ke.copy()),a0=t.map(()=>!1);for(let ke=0;ke<B.length;ke++){let p0=B[ke];if(p0<t.length){let C0=Ie[p0];a0[p0]=!0,C0.x+=ze[ke]*S,C0.y+=je[ke]*S}}this.interpolateMissingDeltas(Ie,f,a0);for(let ke=0;ke<t.length;ke++){let p0=Ie[ke].x-f[ke].x,C0=Ie[ke].y-f[ke].y;t[ke].x=Math.round(t[ke].x+p0),t[ke].y=Math.round(t[ke].y+C0)}}l+=ee,a.pos=u}}decodePoints(){let e=this.font.stream,t=e.readUInt8();t&Ya&&(t=(t&Za)<<8|e.readUInt8());let r=new Uint16Array(t),n=0,a=0;for(;n<t;){let i=e.readUInt8(),l=(i&Za)+1,u=i&Ya?e.readUInt16:e.readUInt8;for(let c=0;c<l&&n<t;c++)a+=u.call(e),r[n++]=a}return r}decodeDeltas(e){let t=this.font.stream,r=0,n=new Int16Array(e);for(;r<e;){let a=t.readUInt8(),i=(a&Pc)+1;if(a&kc)r+=i;else{let l=a&Oc?t.readInt16BE:t.readInt8;for(let u=0;u<i&&r<e;u++)n[r++]=l.call(t)}}return n}tupleFactor(e,t,r,n){let a=this.normalizedCoords,{gvar:i}=this.font,l=1;for(let u=0;u<i.axisCount;u++)if(t[u]!==0){if(a[u]===0)return 0;if(e&ja){if(a[u]<r[u]||a[u]>n[u])return 0;a[u]<t[u]?l=l*(a[u]-r[u]+Number.EPSILON)/(t[u]-r[u]+Number.EPSILON):l=l*(n[u]-a[u]+Number.EPSILON)/(n[u]-t[u]+Number.EPSILON)}else{if(a[u]<Math.min(0,t[u])||a[u]>Math.max(0,t[u]))return 0;l=(l*a[u]+Number.EPSILON)/(t[u]+Number.EPSILON)}}return l}interpolateMissingDeltas(e,t,r){if(e.length===0)return;let n=0;for(;n<e.length;){let a=n,i=n,l=e[i];for(;!l.endContour;)l=e[++i];for(;n<=i&&!r[n];)n++;if(n>i)continue;let u=n,c=n;for(n++;n<=i;)r[n]&&(this.deltaInterpolate(c+1,n-1,c,n,t,e),c=n),n++;c===u?this.deltaShift(a,i,c,t,e):(this.deltaInterpolate(c+1,i,c,u,t,e),u>0&&this.deltaInterpolate(a,u-1,c,u,t,e)),n=i+1}}deltaInterpolate(e,t,r,n,a,i){if(e>t)return;let l=["x","y"];for(let c=0;c<l.length;c++){let f=l[c];if(a[r][f]>a[n][f]){var u=r;r=n,n=u}let d=a[r][f],C=a[n][f],k=i[r][f],B=i[n][f];if(d!==C||k===B){let _=d===C?0:(B-k)/(C-d);for(let ye=e;ye<=t;ye++){let ee=a[ye][f];ee<=d?ee+=k-d:ee>=C?ee+=B-C:ee=k+(ee-d)*_,i[ye][f]=ee}}}}deltaShift(e,t,r,n,a){let i=a[r].x-n[r].x,l=a[r].y-n[r].y;if(!(i===0&&l===0))for(let u=e;u<=t;u++)u!==r&&(a[u].x+=i,a[u].y+=l)}getAdvanceAdjustment(e,t){let r,n;if(t.advanceWidthMapping){let a=e;a>=t.advanceWidthMapping.mapCount&&(a=t.advanceWidthMapping.mapCount-1);let i=t.advanceWidthMapping.entryFormat;({outerIndex:r,innerIndex:n}=t.advanceWidthMapping.mapData[a])}else r=0,n=e;return this.getDelta(t.itemVariationStore,r,n)}getDelta(e,t,r){if(t>=e.itemVariationData.length)return 0;let n=e.itemVariationData[t];if(r>=n.deltaSets.length)return 0;let a=n.deltaSets[r],i=this.getBlendVector(e,t),l=0;for(let u=0;u<n.regionIndexCount;u++)l+=a.deltas[u]*i[u];return l}getBlendVector(e,t){let r=e.itemVariationData[t];if(this.blendVectors.has(r))return this.blendVectors.get(r);let n=this.normalizedCoords,a=[];for(let i=0;i<r.regionIndexCount;i++){let l=1,u=r.regionIndexes[i],c=e.variationRegionList.variationRegions[u];for(let f=0;f<c.length;f++){let d=c[f],C;d.startCoord>d.peakCoord||d.peakCoord>d.endCoord||d.startCoord<0&&d.endCoord>0&&d.peakCoord!==0||d.peakCoord===0?C=1:n[f]<d.startCoord||n[f]>d.endCoord?C=0:n[f]===d.peakCoord?C=1:n[f]<d.peakCoord?C=(n[f]-d.startCoord+Number.EPSILON)/(d.peakCoord-d.startCoord+Number.EPSILON):C=(d.endCoord-n[f]+Number.EPSILON)/(d.endCoord-d.peakCoord+Number.EPSILON),l*=C}a[i]=l}return this.blendVectors.set(r,a),a}constructor(e,t){this.font=e,this.normalizedCoords=this.normalizeCoords(t),this.blendVectors=new Map}}const Ef=Promise.resolve();class Ka{includeGlyph(e){return typeof e=="object"&&(e=e.id),this.mapping[e]==null&&(this.glyphs.push(e),this.mapping[e]=this.glyphs.length-1),this.mapping[e]}constructor(e){this.font=e,this.glyphs=[],this.mapping={},this.includeGlyph(0)}}const Fc=1,Dc=2,Bc=4,Lc=8,Mc=16,Rc=32;class Ja{static size(e){return e>=0&&e<=255?1:2}static encode(e,t){t>=0&&t<=255?e.writeUInt8(t):e.writeInt16BE(t)}}let _a=new v({numberOfContours:x,xMin:x,yMin:x,xMax:x,yMax:x,endPtsOfContours:new g(o,"numberOfContours"),instructions:new g(y,o),flags:new g(y,0),xPoints:new g(Ja,0),yPoints:new g(Ja,0)});class Nc{encodeSimple(e,t){t===void 0&&(t=[]);let r=[],n=[],a=[],i=[],l=0,u=0,c=0,f=0,d=0;for(let ee=0;ee<e.commands.length;ee++){let me=e.commands[ee];for(let S=0;S<me.args.length;S+=2){let Q=me.args[S],ue=me.args[S+1],ze=0;if(me.command==="quadraticCurveTo"&&S===2){let je=e.commands[ee+1];if(je&&je.command==="quadraticCurveTo"){let Ie=(u+je.args[0])/2,a0=(c+je.args[1])/2;if(Q===Ie&&ue===a0)continue}}me.command==="quadraticCurveTo"&&S===0||(ze|=Fc),ze=this._encodePoint(Q,u,n,ze,Dc,Mc),ze=this._encodePoint(ue,c,a,ze,Bc,Rc),ze===f&&l<255?(i[i.length-1]|=Lc,l++):(l>0&&(i.push(l),l=0),i.push(ze),f=ze),u=Q,c=ue,d++}me.command==="closePath"&&r.push(d-1)}e.commands.length>1&&e.commands[e.commands.length-1].command!=="closePath"&&r.push(d-1);let C=e.bbox,k={numberOfContours:r.length,xMin:C.minX,yMin:C.minY,xMax:C.maxX,yMax:C.maxY,endPtsOfContours:r,instructions:t,flags:i,xPoints:n,yPoints:a},B=_a.size(k),_=4-B%4,ye=new L(B+_);return _a.encode(ye,k),_!==0&&ye.fill(0,_),ye.buffer}_encodePoint(e,t,r,n,a,i){let l=e-t;return e===t?n|=i:(-255<=l&&l<=255&&(n|=a,l<0?l=-l:n|=i),r.push(l)),n}}class Uc extends Ka{_addGlyph(e){let t=this.font.getGlyph(e),r=t._decode(),n=this.font.loca.offsets[e],a=this.font.loca.offsets[e+1],i=this.font._getTableStream("glyf");i.pos+=n;let l=i.readBuffer(a-n);if(r&&r.numberOfContours<0){l=new Uint8Array(l);let u=new DataView(l.buffer);for(let c of r.components)e=this.includeGlyph(c.glyphID),u.setUint16(c.pos,e)}else r&&this.font._variationProcessor&&(l=this.glyphEncoder.encodeSimple(t.path,r.instructions));return this.glyf.push(l),this.loca.offsets.push(this.offset),this.hmtx.metrics.push({advance:t.advanceWidth,bearing:t._getMetrics().leftBearing}),this.offset+=l.length,this.glyf.length-1}encode(){this.glyf=[],this.offset=0,this.loca={offsets:[],version:this.font.loca.version},this.hmtx={metrics:[],bearings:[]};let e=0;for(;e<this.glyphs.length;)this._addGlyph(this.glyphs[e++]);let t=Qr(this.font.maxp);t.numGlyphs=this.glyf.length,this.loca.offsets.push(this.offset);let r=Qr(this.font.head);r.indexToLocFormat=this.loca.version;let n=Qr(this.font.hhea);return n.numberOfMetrics=this.hmtx.metrics.length,va.toBuffer({tables:{head:r,hhea:n,loca:this.loca,maxp:t,"cvt ":this.font["cvt "],prep:this.font.prep,glyf:this.glyf,hmtx:this.hmtx,fpgm:this.font.fpgm}})}constructor(e){super(e),this.glyphEncoder=new Nc}}class Gc extends Ka{subsetCharstrings(){this.charstrings=[];let e={};for(let t of this.glyphs){this.charstrings.push(this.cff.getCharString(t));let r=this.font.getGlyph(t),n=r.path;for(let a in r._usedGsubrs)e[a]=!0}this.gsubrs=this.subsetSubrs(this.cff.globalSubrIndex,e)}subsetSubrs(e,t){let r=[];for(let n=0;n<e.length;n++){let a=e[n];t[n]?(this.cff.stream.pos=a.offset,r.push(this.cff.stream.readBuffer(a.length))):r.push(new Uint8Array([11]))}return r}subsetFontdict(e){e.FDArray=[],e.FDSelect={version:0,fds:[]};let t={},r=[],n={};for(let a of this.glyphs){let i=this.cff.fdForGlyph(a);if(i==null)continue;t[i]||(e.FDArray.push(Object.assign({},this.cff.topDict.FDArray[i])),r.push({}),n[i]=e.FDArray.length-1),t[i]=!0,e.FDSelect.fds.push(n[i]);let l=this.font.getGlyph(a),u=l.path;for(let c in l._usedSubrs)r[n[i]][c]=!0}for(let a=0;a<e.FDArray.length;a++){let i=e.FDArray[a];delete i.FontName,i.Private&&i.Private.Subrs&&(i.Private=Object.assign({},i.Private),i.Private.Subrs=this.subsetSubrs(i.Private.Subrs,r[a]))}}createCIDFontdict(e){let t={};for(let n of this.glyphs){let a=this.font.getGlyph(n),i=a.path;for(let l in a._usedSubrs)t[l]=!0}let r=Object.assign({},this.cff.topDict.Private);return this.cff.topDict.Private&&this.cff.topDict.Private.Subrs&&(r.Subrs=this.subsetSubrs(this.cff.topDict.Private.Subrs,t)),e.FDArray=[{Private:r}],e.FDSelect={version:3,nRanges:1,ranges:[{first:0,fd:0}],sentinel:this.charstrings.length}}addString(e){return e?(this.strings||(this.strings=[]),this.strings.push(e),mr.length+this.strings.length-1):null}encode(){this.subsetCharstrings();let e={version:this.charstrings.length>255?2:1,ranges:[{first:1,nLeft:this.charstrings.length-2}]},t=Object.assign({},this.cff.topDict);t.Private=null,t.charset=e,t.Encoding=null,t.CharStrings=this.charstrings;for(let n of["version","Notice","Copyright","FullName","FamilyName","Weight","PostScript","BaseFontName","FontName"])t[n]=this.addString(this.cff.string(t[n]));t.ROS=[this.addString("Adobe"),this.addString("Identity"),0],t.CIDCount=this.charstrings.length,this.cff.isCIDFont?this.subsetFontdict(t):this.createCIDFontdict(t);let r={version:1,hdrSize:this.cff.hdrSize,offSize:4,header:this.cff.header,nameIndex:[this.cff.postscriptName],topDictIndex:[t],stringIndex:this.strings,globalSubrIndex:this.gsubrs};return ta.toBuffer(r)}constructor(e){if(super(e),this.cff=this.font["CFF "],!this.cff)throw new Error("Not a CFF Font")}}class Ye{static probe(e){let t=Ir.decode(e.slice(0,4));return t==="true"||t==="OTTO"||t==="\0\0\0"}setDefaultLanguage(e){e===void 0&&(e=null),this.defaultLanguage=e}_getTable(e){if(!(e.tag in this._tables))try{this._tables[e.tag]=this._decodeTable(e)}catch(t){Tn&&(console.error("Error decoding table "+e.tag),console.error(t.stack))}return this._tables[e.tag]}_getTableStream(e){let t=this.directory.tables[e];return t?(this.stream.pos=t.offset,this.stream):null}_decodeDirectory(){return this.directory=va.decode(this.stream,{_startOffset:0})}_decodeTable(e){let t=this.stream.pos,r=this._getTableStream(e.tag),n=Sr[e.tag].decode(r,this,e.length);return this.stream.pos=t,n}getName(e,t){t===void 0&&(t=this.defaultLanguage||br);let r=this.name&&this.name.records[e];return r&&(r[t]||r[this.defaultLanguage]||r[br]||r.en||r[Object.keys(r)[0]])||null}get postscriptName(){return this.getName("postscriptName")}get fullName(){return this.getName("fullName")}get familyName(){return this.getName("fontFamily")}get subfamilyName(){return this.getName("fontSubfamily")}get copyright(){return this.getName("copyright")}get version(){return this.getName("version")}get ascent(){return this.hhea.ascent}get descent(){return this.hhea.descent}get lineGap(){return this.hhea.lineGap}get underlinePosition(){return this.post.underlinePosition}get underlineThickness(){return this.post.underlineThickness}get italicAngle(){return this.post.italicAngle}get capHeight(){let e=this["OS/2"];return e?e.capHeight:this.ascent}get xHeight(){let e=this["OS/2"];return e?e.xHeight:0}get numGlyphs(){return this.maxp.numGlyphs}get unitsPerEm(){return this.head.unitsPerEm}get bbox(){return Object.freeze(new G0(this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax))}get _cmapProcessor(){return new vn(this.cmap)}get characterSet(){return this._cmapProcessor.getCharacterSet()}hasGlyphForCodePoint(e){return!!this._cmapProcessor.lookup(e)}glyphForCodePoint(e){return this.getGlyph(this._cmapProcessor.lookup(e),[e])}glyphsForString(e){let t=[],r=e.length,n=0,a=-1,i=-1;for(;n<=r;){let l=0,u=0;if(n<r){if(l=e.charCodeAt(n++),55296<=l&&l<=56319&&n<r){let c=e.charCodeAt(n);56320<=c&&c<=57343&&(n++,l=((l&1023)<<10)+(c&1023)+65536)}u=65024<=l&&l<=65039||917760<=l&&l<=917999?1:0}else n++;i===0&&u===1?t.push(this.getGlyph(this._cmapProcessor.lookup(a,l),[a,l])):i===0&&u===0&&t.push(this.glyphForCodePoint(a)),a=l,i=u}return t}get _layoutEngine(){return new sc(this)}layout(e,t,r,n,a){return this._layoutEngine.layout(e,t,r,n,a)}stringsForGlyph(e){return this._layoutEngine.stringsForGlyph(e)}get availableFeatures(){return this._layoutEngine.getAvailableFeatures()}getAvailableFeatures(e,t){return this._layoutEngine.getAvailableFeatures(e,t)}_getBaseGlyph(e,t){return t===void 0&&(t=[]),this._glyphs[e]||(this.directory.tables.glyf?this._glyphs[e]=new zr(e,t,this):(this.directory.tables["CFF "]||this.directory.tables.CFF2)&&(this._glyphs[e]=new xc(e,t,this))),this._glyphs[e]||null}getGlyph(e,t){return t===void 0&&(t=[]),this._glyphs[e]||(this.directory.tables.sbix?this._glyphs[e]=new yc(e,t,this):this.directory.tables.COLR&&this.directory.tables.CPAL?this._glyphs[e]=new Cc(e,t,this):this._getBaseGlyph(e,t)),this._glyphs[e]||null}createSubset(){return this.directory.tables["CFF "]?new Gc(this):new Uc(this)}get variationAxes(){let e={};if(!this.fvar)return e;for(let t of this.fvar.axis)e[t.axisTag.trim()]={name:t.name.en,min:t.minValue,default:t.defaultValue,max:t.maxValue};return e}get namedVariations(){let e={};if(!this.fvar)return e;for(let t of this.fvar.instance){let r={};for(let n=0;n<this.fvar.axis.length;n++){let a=this.fvar.axis[n];r[a.axisTag.trim()]=t.coord[n]}e[t.name.en]=r}return e}getVariation(e){if(!(this.directory.tables.fvar&&(this.directory.tables.gvar&&this.directory.tables.glyf||this.directory.tables.CFF2)))throw new Error("Variations require a font with the fvar, gvar and glyf, or CFF2 tables.");if(typeof e=="string"&&(e=this.namedVariations[e]),typeof e!="object")throw new Error("Variation settings must be either a variation name or settings object.");let t=this.fvar.axis.map((a,i)=>{let l=a.axisTag.trim();return l in e?Math.max(a.minValue,Math.min(a.maxValue,e[l])):a.defaultValue}),r=new w(this.stream.buffer);r.pos=this._directoryPos;let n=new Ye(r,t);return n._tables=this._tables,n}get _variationProcessor(){if(!this.fvar)return null;let e=this.variationCoords;return!e&&!this.CFF2?null:(e||(e=this.fvar.axis.map(t=>t.defaultValue)),new Tc(this,e))}getFont(e){return this.getVariation(e)}constructor(e,t){t===void 0&&(t=null),we(this,"type","TTF"),this.defaultLanguage=null,this.stream=e,this.variationCoords=t,this._directoryPos=this.stream.pos,this._tables={},this._glyphs={},this._decodeDirectory();for(let r in this.directory.tables){let n=this.directory.tables[r];Sr[r]&&n.length>0&&Object.defineProperty(this,r,{get:this._getTable.bind(this,n)})}}}Me([He],Ye.prototype,"bbox",null),Me([He],Ye.prototype,"_cmapProcessor",null),Me([He],Ye.prototype,"characterSet",null),Me([He],Ye.prototype,"_layoutEngine",null),Me([He],Ye.prototype,"variationAxes",null),Me([He],Ye.prototype,"namedVariations",null),Me([He],Ye.prototype,"_variationProcessor",null);let Vc=new v({tag:new j(4),offset:new b(p,"void",{type:"global"}),compLength:p,length:p,origChecksum:p}),Qa=new v({tag:new j(4),flavor:p,length:p,numTables:o,reserved:new V(o),totalSfntSize:p,majorVersion:o,minorVersion:o,metaOffset:p,metaLength:p,metaOrigLength:p,privOffset:p,privLength:p,tables:new g(Vc,"numTables")});Qa.process=function(){let s={};for(let e of this.tables)s[e.tag]=e;this.tables=s};var zc=Qa;class $c extends Ye{static probe(e){return Ir.decode(e.slice(0,4))==="wOFF"}_decodeDirectory(){this.directory=zc.decode(this.stream,{_startOffset:0})}_getTableStream(e){let t=this.directory.tables[e];if(t)if(this.stream.pos=t.offset,t.compLength<t.length){this.stream.pos+=2;let r=new Uint8Array(t.length),n=ms(this.stream.readBuffer(t.compLength-2),r);return new w(n)}else return this.stream;return null}constructor(){super(...arguments),we(this,"type","WOFF")}}class Wc extends zr{_decode(){return this._font._transformedGlyphs[this.id]}_getCBox(){return this.path.bbox}constructor(){super(...arguments),we(this,"type","WOFF2")}}const es={decode(s){let e=0,t=[0,1,2,3,4];for(let r=0;r<t.length;r++){let n=t[r],a=s.readUInt8();if(e&3758096384)throw new Error("Overflow");if(e=e<<7|a&127,!(a&128))return e}throw new Error("Bad base 128 number")}};let qc=["cmap","head","hhea","hmtx","maxp","name","OS/2","post","cvt ","fpgm","glyf","loca","prep","CFF ","VORG","EBDT","EBLC","gasp","hdmx","kern","LTSH","PCLT","VDMX","vhea","vmtx","BASE","GDEF","GPOS","GSUB","EBSC","JSTF","MATH","CBDT","CBLC","COLR","CPAL","SVG ","sbix","acnt","avar","bdat","bloc","bsln","cvar","fdsc","feat","fmtx","fvar","gvar","hsty","just","lcar","mort","morx","opbd","prop","trak","Zapf","Silf","Glat","Gloc","Feat","Sill"],Hc=new v({flags:y,customTag:new K(new j(4),s=>(s.flags&63)===63),tag:s=>s.customTag||qc[s.flags&63],length:es,transformVersion:s=>s.flags>>>6&3,transformed:s=>s.tag==="glyf"||s.tag==="loca"?s.transformVersion===0:s.transformVersion!==0,transformLength:new K(es,s=>s.transformed)}),ts=new v({tag:new j(4),flavor:p,length:p,numTables:o,reserved:new V(o),totalSfntSize:p,totalCompressedSize:p,majorVersion:o,minorVersion:o,metaOffset:p,metaLength:p,metaOrigLength:p,privOffset:p,privLength:p,tables:new g(Hc,"numTables")});ts.process=function(){let s={};for(let e=0;e<this.tables.length;e++){let t=this.tables[e];s[t.tag]=t}return this.tables=s};var jc=ts;class Xc extends Ye{static probe(e){return Ir.decode(e.slice(0,4))==="wOF2"}_decodeDirectory(){this.directory=jc.decode(this.stream),this._dataPos=this.stream.pos}_decompress(){if(!this._decompressed){this.stream.pos=this._dataPos;let e=this.stream.readBuffer(this.directory.totalCompressedSize),t=0;for(let n in this.directory.tables){let a=this.directory.tables[n];a.offset=t,t+=a.transformLength!=null?a.transformLength:a.length}let r=xs(e,t);if(!r)throw new Error("Error decoding compressed data in WOFF2");this.stream=new w(r),this._decompressed=!0}}_decodeTable(e){return this._decompress(),super._decodeTable(e)}_getBaseGlyph(e,t){if(t===void 0&&(t=[]),!this._glyphs[e])return this.directory.tables.glyf&&this.directory.tables.glyf.transformed?(this._transformedGlyphs||this._transformGlyfTable(),this._glyphs[e]=new Wc(e,t,this)):super._getBaseGlyph(e,t)}_transformGlyfTable(){this._decompress(),this.stream.pos=this.directory.tables.glyf.offset;let e=Yc.decode(this.stream),t=[];for(let n=0;n<e.numGlyphs;n++){let a={},i=e.nContours.readInt16BE();if(a.numberOfContours=i,i>0){let l=[],u=0;for(let c=0;c<i;c++){let f=En(e.nPoints);u+=f,l.push(u)}a.points=_c(e.flags,e.glyphs,u);for(let c=0;c<i;c++)a.points[l[c]-1].endContour=!0;var r=En(e.glyphs)}else if(i<0&&zr.prototype._decodeComposite.call({_font:this},a,e.composites))var r=En(e.glyphs);t.push(a)}this._transformedGlyphs=t}constructor(){super(...arguments),we(this,"type","WOFF2")}}class et{decode(e,t){return new w(this._buf.decode(e,t))}constructor(e){this.length=e,this._buf=new z(e)}}let Yc=new v({version:p,numGlyphs:o,indexFormat:o,nContourStreamSize:p,nPointsStreamSize:p,flagStreamSize:p,glyphStreamSize:p,compositeStreamSize:p,bboxStreamSize:p,instructionStreamSize:p,nContours:new et("nContourStreamSize"),nPoints:new et("nPointsStreamSize"),flags:new et("flagStreamSize"),glyphs:new et("glyphStreamSize"),composites:new et("compositeStreamSize"),bboxes:new et("bboxStreamSize"),instructions:new et("instructionStreamSize")});const Zc=253,Kc=254,Jc=255,rs=253;function En(s){let e=s.readUInt8();return e===Zc?s.readUInt16BE():e===Jc?s.readUInt8()+rs:e===Kc?s.readUInt8()+rs*2:e}function y0(s,e){return s&1?e:-e}function _c(s,e,t){let r,n=r=0,a=[];for(let u=0;u<t;u++){let c=0,f=0,d=s.readUInt8(),C=!(d>>7);if(d&=127,d<10)c=0,f=y0(d,((d&14)<<7)+e.readUInt8());else if(d<20)c=y0(d,((d-10&14)<<7)+e.readUInt8()),f=0;else if(d<84){var i=d-20,l=e.readUInt8();c=y0(d,1+(i&48)+(l>>4)),f=y0(d>>1,1+((i&12)<<2)+(l&15))}else if(d<120){var i=d-84;c=y0(d,1+(i/12<<8)+e.readUInt8()),f=y0(d>>1,1+(i%12>>2<<8)+e.readUInt8())}else if(d<124){var l=e.readUInt8();let B=e.readUInt8();c=y0(d,(l<<4)+(B>>4)),f=y0(d>>1,((B&15)<<8)+e.readUInt8())}else c=y0(d,e.readUInt16BE()),f=y0(d>>1,e.readUInt16BE());n+=c,r+=f,a.push(new h0(C,!1,n,r))}return a}let Qc=new re(p,{65536:{numFonts:p,offsets:new g(p,"numFonts")},131072:{numFonts:p,offsets:new g(p,"numFonts"),dsigTag:p,dsigLength:p,dsigOffset:p}});class ef{static probe(e){return Ir.decode(e.slice(0,4))==="ttcf"}getFont(e){for(let t of this.header.offsets){let r=new w(this.stream.buffer);r.pos=t;let n=new Ye(r);if(n.postscriptName===e||n.postscriptName instanceof Uint8Array&&e instanceof Uint8Array&&n.postscriptName.every((a,i)=>e[i]===a))return n}return null}get fonts(){let e=[];for(let t of this.header.offsets){let r=new w(this.stream.buffer);r.pos=t,e.push(new Ye(r))}return e}constructor(e){if(we(this,"type","TTC"),this.stream=e,e.readString(4)!=="ttcf")throw new Error("Not a TrueType collection");this.header=Qc.decode(e)}}let tf=new j(y),kf=new v({len:p,buf:new z("len")}),rf=new v({id:o,nameOffset:x,attr:y,dataOffset:ne,handle:p}),nf=new v({name:new j(4),maxTypeIndex:o,refList:new b(o,new g(rf,s=>s.maxTypeIndex+1),{type:"parent"})}),af=new v({length:o,types:new g(nf,s=>s.length+1)}),sf=new v({reserved:new V(y,24),typeList:new b(o,af),nameListOffset:new b(o,"void")}),ns=new v({dataOffset:p,map:new b(p,sf),dataLength:p,mapLength:p});class of{static probe(e){let t=new w(e);try{var r=ns.decode(t)}catch(n){return!1}for(let n of r.map.typeList.types)if(n.name==="sfnt")return!0;return!1}getFont(e){if(!this.sfnt)return null;for(let t of this.sfnt.refList){let r=this.header.dataOffset+t.dataOffset+4,n=new w(this.stream.buffer.slice(r)),a=new Ye(n);if(a.postscriptName===e||a.postscriptName instanceof Uint8Array&&e instanceof Uint8Array&&a.postscriptName.every((i,l)=>e[l]===i))return a}return null}get fonts(){let e=[];for(let t of this.sfnt.refList){let r=this.header.dataOffset+t.dataOffset+4,n=new w(this.stream.buffer.slice(r));e.push(new Ye(n))}return e}constructor(e){we(this,"type","DFont"),this.stream=e,this.header=ns.decode(this.stream);for(let t of this.header.map.typeList.types){for(let r of t.refList)r.nameOffset>=0?(this.stream.pos=r.nameOffset+this.header.map.nameListOffset,r.name=tf.decode(this.stream)):r.name=null;t.name==="sfnt"&&(this.sfnt=t)}}}ht(Ye),ht($c),ht(Xc),ht(ef),ht(of)},53742:(be,H)=>{"use strict";H.byteLength=ce,H.toByteArray=$,H.fromByteArray=o;for(var m=[],G=[],w=typeof Uint8Array<"u"?Uint8Array:Array,N="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",D=0,L=N.length;D<L;++D)m[D]=N[D],G[N.charCodeAt(D)]=D;G[45]=62,G[95]=63;function M(R){var Y=R.length;if(Y%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var ne=R.indexOf("=");ne===-1&&(ne=Y);var Se=ne===Y?0:4-ne%4;return[ne,Se]}function ce(R){var Y=M(R),ne=Y[0],Se=Y[1];return(ne+Se)*3/4-Se}function oe(R,Y,ne){return(Y+ne)*3/4-ne}function $(R){var Y,ne=M(R),Se=ne[0],ge=ne[1],p=new w(oe(R,Se,ge)),W=0,Z=ge>0?Se-4:Se,T;for(T=0;T<Z;T+=4)Y=G[R.charCodeAt(T)]<<18|G[R.charCodeAt(T+1)]<<12|G[R.charCodeAt(T+2)]<<6|G[R.charCodeAt(T+3)],p[W++]=Y>>16&255,p[W++]=Y>>8&255,p[W++]=Y&255;return ge===2&&(Y=G[R.charCodeAt(T)]<<2|G[R.charCodeAt(T+1)]>>4,p[W++]=Y&255),ge===1&&(Y=G[R.charCodeAt(T)]<<10|G[R.charCodeAt(T+1)]<<4|G[R.charCodeAt(T+2)]>>2,p[W++]=Y>>8&255,p[W++]=Y&255),p}function y(R){return m[R>>18&63]+m[R>>12&63]+m[R>>6&63]+m[R&63]}function ie(R,Y,ne){for(var Se,ge=[],p=Y;p<ne;p+=3)Se=(R[p]<<16&16711680)+(R[p+1]<<8&65280)+(R[p+2]&255),ge.push(y(Se));return ge.join("")}function o(R){for(var Y,ne=R.length,Se=ne%3,ge=[],p=16383,W=0,Z=ne-Se;W<Z;W+=p)ge.push(ie(R,W,W+p>Z?Z:W+p));return Se===1?(Y=R[ne-1],ge.push(m[Y>>2]+m[Y<<4&63]+"==")):Se===2&&(Y=(R[ne-2]<<8)+R[ne-1],ge.push(m[Y>>10]+m[Y>>4&63]+m[Y<<2&63]+"=")),ge.join("")}},57867:be=>{var H=4096,m=2*H+32,G=2*H-1,w=new Uint32Array([0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215]);function N(D){this.buf_=new Uint8Array(m),this.input_=D,this.reset()}N.READ_SIZE=H,N.IBUF_MASK=G,N.prototype.reset=function(){this.buf_ptr_=0,this.val_=0,this.pos_=0,this.bit_pos_=0,this.bit_end_pos_=0,this.eos_=0,this.readMoreInput();for(var D=0;D<4;D++)this.val_|=this.buf_[this.pos_]<<8*D,++this.pos_;return this.bit_end_pos_>0},N.prototype.readMoreInput=function(){if(!(this.bit_end_pos_>256))if(this.eos_){if(this.bit_pos_>this.bit_end_pos_)throw new Error("Unexpected end of input "+this.bit_pos_+" "+this.bit_end_pos_)}else{var D=this.buf_ptr_,L=this.input_.read(this.buf_,D,H);if(L<0)throw new Error("Unexpected end of input");if(L<H){this.eos_=1;for(var M=0;M<32;M++)this.buf_[D+L+M]=0}if(D===0){for(var M=0;M<32;M++)this.buf_[(H<<1)+M]=this.buf_[M];this.buf_ptr_=H}else this.buf_ptr_=0;this.bit_end_pos_+=L<<3}},N.prototype.fillBitWindow=function(){for(;this.bit_pos_>=8;)this.val_>>>=8,this.val_|=this.buf_[this.pos_&G]<<24,++this.pos_,this.bit_pos_=this.bit_pos_-8>>>0,this.bit_end_pos_=this.bit_end_pos_-8>>>0},N.prototype.readBits=function(D){32-this.bit_pos_<D&&this.fillBitWindow();var L=this.val_>>>this.bit_pos_&w[D];return this.bit_pos_+=D,L},be.exports=N},25101:(be,H)=>{var m=0,G=1,w=2,N=3;H.lookup=new Uint8Array([0,0,0,0,0,0,0,0,0,4,4,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8,12,16,12,12,20,12,16,24,28,12,12,32,12,36,12,44,44,44,44,44,44,44,44,44,44,32,32,24,40,28,12,12,48,52,52,52,48,52,52,52,48,52,52,52,52,52,48,52,52,52,52,52,48,52,52,52,52,52,24,12,28,12,12,12,56,60,60,60,56,60,60,60,56,60,60,60,60,60,56,60,60,60,60,60,56,60,60,60,60,60,24,12,28,12,0,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,2,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,0,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,48,48,48,48,48,48,48,48,48,48,48,48,48,48,48,56,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6,6,6,7,7,7,7,8,8,8,8,9,9,9,9,10,10,10,10,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,14,15,15,15,15,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,22,22,22,22,23,23,23,23,24,24,24,24,25,25,25,25,26,26,26,26,27,27,27,27,28,28,28,28,29,29,29,29,30,30,30,30,31,31,31,31,32,32,32,32,33,33,33,33,34,34,34,34,35,35,35,35,36,36,36,36,37,37,37,37,38,38,38,38,39,39,39,39,40,40,40,40,41,41,41,41,42,42,42,42,43,43,43,43,44,44,44,44,45,45,45,45,46,46,46,46,47,47,47,47,48,48,48,48,49,49,49,49,50,50,50,50,51,51,51,51,52,52,52,52,53,53,53,53,54,54,54,54,55,55,55,55,56,56,56,56,57,57,57,57,58,58,58,58,59,59,59,59,60,60,60,60,61,61,61,61,62,62,62,62,63,63,63,63,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),H.lookupOffsets=new Uint16Array([1024,1536,1280,1536,0,256,768,512])},82462:(be,H,m)=>{var G,w=m(31015).z,N=m(31015).y,D=m(57867),L=m(33888),M=m(19427).z,ce=m(19427).u,oe=m(25101),$=m(70020),y=m(46646),ie=8,o=16,R=256,Y=704,ne=26,Se=6,ge=2,p=8,W=255,Z=1080,T=18,x=new Uint8Array([1,2,3,4,0,5,17,6,16,7,8,9,10,11,12,13,14,15]),h=16,X=new Uint8Array([3,2,1,0,3,3,3,3,3,3,2,2,2,2,2,2]),P=new Int8Array([0,0,0,0,-1,1,-2,2,-3,3,-1,1,-2,2,-3,3]),J=new Uint16Array([256,402,436,468,500,534,566,598,630,662,694,726,758,790,822,854,886,920,952,984,1016,1048,1080]);function U(A){var I;return A.readBits(1)===0?16:(I=A.readBits(3),I>0?17+I:(I=A.readBits(3),I>0?8+I:17))}function ae(A){if(A.readBits(1)){var I=A.readBits(3);return I===0?1:A.readBits(I)+(1<<I)}return 0}function ve(){this.meta_block_length=0,this.input_end=0,this.is_uncompressed=0,this.is_metadata=!1}function Be(A){var I=new ve,O,E,z;if(I.input_end=A.readBits(1),I.input_end&&A.readBits(1))return I;if(O=A.readBits(2)+4,O===7){if(I.is_metadata=!0,A.readBits(1)!==0)throw new Error("Invalid reserved bit");if(E=A.readBits(2),E===0)return I;for(z=0;z<E;z++){var fe=A.readBits(8);if(z+1===E&&E>1&&fe===0)throw new Error("Invalid size byte");I.meta_block_length|=fe<<z*8}}else for(z=0;z<O;++z){var K=A.readBits(4);if(z+1===O&&O>4&&K===0)throw new Error("Invalid size nibble");I.meta_block_length|=K<<z*4}return++I.meta_block_length,!I.input_end&&!I.is_metadata&&(I.is_uncompressed=A.readBits(1)),I}function Oe(A,I,O){var E=I,z;return O.fillBitWindow(),I+=O.val_>>>O.bit_pos_&W,z=A[I].bits-p,z>0&&(O.bit_pos_+=p,I+=A[I].value,I+=O.val_>>>O.bit_pos_&(1<<z)-1),O.bit_pos_+=A[I].bits,A[I].value}function t0(A,I,O,E){for(var z=0,fe=ie,K=0,V=0,j=32768,se=[],q=0;q<32;q++)se.push(new M(0,0));for(ce(se,0,5,A,T);z<I&&j>0;){var v=0,Xe;if(E.readMoreInput(),E.fillBitWindow(),v+=E.val_>>>E.bit_pos_&31,E.bit_pos_+=se[v].bits,Xe=se[v].value&255,Xe<o)K=0,O[z++]=Xe,Xe!==0&&(fe=Xe,j-=32768>>Xe);else{var re=Xe-14,b,qe,we=0;if(Xe===o&&(we=fe),V!==we&&(K=0,V=we),b=K,K>0&&(K-=2,K<<=re),K+=E.readBits(re)+3,qe=K-b,z+qe>I)throw new Error("[ReadHuffmanCodeLengths] symbol + repeat_delta > num_symbols");for(var We=0;We<qe;We++)O[z+We]=V;z+=qe,V!==0&&(j-=qe<<15-V)}}if(j!==0)throw new Error("[ReadHuffmanCodeLengths] space = "+j);for(;z<I;z++)O[z]=0}function Ge(A,I,O,E){var z=0,fe,K=new Uint8Array(A);if(E.readMoreInput(),fe=E.readBits(2),fe===1){for(var V,j=A-1,se=0,q=new Int32Array(4),v=E.readBits(2)+1;j;)j>>=1,++se;for(V=0;V<v;++V)q[V]=E.readBits(se)%A,K[q[V]]=2;switch(K[q[0]]=1,v){case 1:break;case 3:if(q[0]===q[1]||q[0]===q[2]||q[1]===q[2])throw new Error("[ReadHuffmanCode] invalid symbols");break;case 2:if(q[0]===q[1])throw new Error("[ReadHuffmanCode] invalid symbols");K[q[1]]=1;break;case 4:if(q[0]===q[1]||q[0]===q[2]||q[0]===q[3]||q[1]===q[2]||q[1]===q[3]||q[2]===q[3])throw new Error("[ReadHuffmanCode] invalid symbols");E.readBits(1)?(K[q[2]]=3,K[q[3]]=3):K[q[0]]=2;break}}else{var V,Xe=new Uint8Array(T),re=32,b=0,qe=[new M(2,0),new M(2,4),new M(2,3),new M(3,2),new M(2,0),new M(2,4),new M(2,3),new M(4,1),new M(2,0),new M(2,4),new M(2,3),new M(3,2),new M(2,0),new M(2,4),new M(2,3),new M(4,5)];for(V=fe;V<T&&re>0;++V){var we=x[V],We=0,_e;E.fillBitWindow(),We+=E.val_>>>E.bit_pos_&15,E.bit_pos_+=qe[We].bits,_e=qe[We].value,Xe[we]=_e,_e!==0&&(re-=32>>_e,++b)}if(!(b===1||re===0))throw new Error("[ReadHuffmanCode] invalid num_codes or space");t0(Xe,A,K,E)}if(z=ce(I,O,p,K,A),z===0)throw new Error("[ReadHuffmanCode] BuildHuffmanTable failed: ");return z}function Je(A,I,O){var E,z;return E=Oe(A,I,O),z=$.kBlockLengthPrefixCode[E].nbits,$.kBlockLengthPrefixCode[E].offset+O.readBits(z)}function H0(A,I,O){var E;return A<h?(O+=X[A],O&=3,E=I[O]+P[A]):E=A-h+1,E}function u0(A,I){for(var O=A[I],E=I;E;--E)A[E]=A[E-1];A[0]=O}function j0(A,I){var O=new Uint8Array(256),E;for(E=0;E<256;++E)O[E]=E;for(E=0;E<I;++E){var z=A[E];A[E]=O[z],z&&u0(O,z)}}function s0(A,I){this.alphabet_size=A,this.num_htrees=I,this.codes=new Array(I+I*J[A+31>>>5]),this.htrees=new Uint32Array(I)}s0.prototype.decode=function(A){var I,O,E=0;for(I=0;I<this.num_htrees;++I)this.htrees[I]=E,O=Ge(this.alphabet_size,this.codes,E,A),E+=O};function tt(A,I){var O={num_htrees:null,context_map:null},E,z=0,fe,K;I.readMoreInput();var V=O.num_htrees=ae(I)+1,j=O.context_map=new Uint8Array(A);if(V<=1)return O;for(E=I.readBits(1),E&&(z=I.readBits(4)+1),fe=[],K=0;K<Z;K++)fe[K]=new M(0,0);for(Ge(V+z,fe,0,I),K=0;K<A;){var se;if(I.readMoreInput(),se=Oe(fe,0,I),se===0)j[K]=0,++K;else if(se<=z)for(var q=1+(1<<se)+I.readBits(se);--q;){if(K>=A)throw new Error("[DecodeContextMap] i >= context_map_size");j[K]=0,++K}else j[K]=se-z,++K}return I.readBits(1)&&j0(j,A),O}function X0(A,I,O,E,z,fe,K){var V=O*2,j=O,se=Oe(I,O*Z,K),q;se===0?q=z[V+(fe[j]&1)]:se===1?q=z[V+(fe[j]-1&1)]+1:q=se-2,q>=A&&(q-=A),E[O]=q,z[V+(fe[j]&1)]=q,++fe[j]}function Le(A,I,O,E,z,fe){var K=z+1,V=O&z,j=fe.pos_&D.IBUF_MASK,se;if(I<8||fe.bit_pos_+(I<<3)<fe.bit_end_pos_){for(;I-- >0;)fe.readMoreInput(),E[V++]=fe.readBits(8),V===K&&(A.write(E,K),V=0);return}if(fe.bit_end_pos_<32)throw new Error("[CopyUncompressedBlockToOutput] br.bit_end_pos_ < 32");for(;fe.bit_pos_<32;)E[V]=fe.val_>>>fe.bit_pos_,fe.bit_pos_+=8,++V,--I;if(se=fe.bit_end_pos_-fe.bit_pos_>>3,j+se>D.IBUF_MASK){for(var q=D.IBUF_MASK+1-j,v=0;v<q;v++)E[V+v]=fe.buf_[j+v];se-=q,V+=q,I-=q,j=0}for(var v=0;v<se;v++)E[V+v]=fe.buf_[j+v];if(V+=se,I-=se,V>=K){A.write(E,K),V-=K;for(var v=0;v<V;v++)E[v]=E[K+v]}for(;V+I>=K;){if(se=K-V,fe.input_.read(E,V,se)<se)throw new Error("[CopyUncompressedBlockToOutput] not enough bytes");A.write(E,K),I-=se,V=0}if(fe.input_.read(E,V,I)<I)throw new Error("[CopyUncompressedBlockToOutput] not enough bytes");fe.reset()}function kn(A){var I=A.bit_pos_+7&-8,O=A.readBits(I-A.bit_pos_);return O==0}function r0(A){var I=new w(A),O=new D(I);U(O);var E=Be(O);return E.meta_block_length}G=r0;function Tt(A,I){var O=new w(A);I==null&&(I=r0(A));var E=new Uint8Array(I),z=new N(E);return g(O,z),z.pos<z.buffer.length&&(z.buffer=z.buffer.subarray(0,z.pos)),z.buffer}H.BrotliDecompressBuffer=Tt;function g(A,I){var O,E=0,z=0,fe=0,K,V=0,j,se,q,v,Xe=[16,15,11,4],re=0,b=0,qe=0,we=[new s0(0,0),new s0(0,0),new s0(0,0)],We,_e,de,Xr=128+D.READ_SIZE;de=new D(A),fe=U(de),K=(1<<fe)-16,j=1<<fe,se=j-1,q=new Uint8Array(j+Xr+L.maxDictionaryWordLength),v=j,We=[],_e=[];for(var Me=0;Me<3*Z;Me++)We[Me]=new M(0,0),_e[Me]=new M(0,0);for(;!z;){var $e=0,rr,i0=[1<<28,1<<28,1<<28],b0=[0],n0=[1,1,1],Ft=[0,1,0,1,0,1],rt=[0],T0,I0,nr,nt,Dt=null,at=null,ar,sr=null,F0,ir=0,Bt=null,or=0,lr=0,Lt=null,st=0,Mt=0,Rt=0,D0,Nt;for(O=0;O<3;++O)we[O].codes=null,we[O].htrees=null;de.readMoreInput();var it=Be(de);if($e=it.meta_block_length,E+$e>I.buffer.length){var ur=new Uint8Array(E+$e);ur.set(I.buffer),I.buffer=ur}if(z=it.input_end,rr=it.is_uncompressed,it.is_metadata){for(kn(de);$e>0;--$e)de.readMoreInput(),de.readBits(8);continue}if($e!==0){if(rr){de.bit_pos_=de.bit_pos_+7&-8,Le(I,$e,E,q,se,de),E+=$e;continue}for(O=0;O<3;++O)n0[O]=ae(de)+1,n0[O]>=2&&(Ge(n0[O]+2,We,O*Z,de),Ge(ne,_e,O*Z,de),i0[O]=Je(_e,O*Z,de),rt[O]=1);for(de.readMoreInput(),T0=de.readBits(2),I0=h+(de.readBits(4)<<T0),nr=(1<<T0)-1,nt=I0+(48<<T0),at=new Uint8Array(n0[0]),O=0;O<n0[0];++O)de.readMoreInput(),at[O]=de.readBits(2)<<1;var cr=tt(n0[0]<<Se,de);ar=cr.num_htrees,Dt=cr.context_map;var fr=tt(n0[2]<<ge,de);for(F0=fr.num_htrees,sr=fr.context_map,we[0]=new s0(R,ar),we[1]=new s0(Y,n0[1]),we[2]=new s0(nt,F0),O=0;O<3;++O)we[O].decode(de);for(Bt=0,Lt=0,D0=at[b0[0]],Mt=oe.lookupOffsets[D0],Rt=oe.lookupOffsets[D0+1],Nt=we[1].htrees[0];$e>0;){var ot,lt,ut,Ut,B0,Re,Ne,Ze,Y0,c0,Z0;for(de.readMoreInput(),i0[1]===0&&(X0(n0[1],We,1,b0,Ft,rt,de),i0[1]=Je(_e,Z,de),Nt=we[1].htrees[b0[1]]),--i0[1],ot=Oe(we[1].codes,Nt,de),lt=ot>>6,lt>=2?(lt-=2,Ne=-1):Ne=0,ut=$.kInsertRangeLut[lt]+(ot>>3&7),Ut=$.kCopyRangeLut[lt]+(ot&7),B0=$.kInsertLengthPrefixCode[ut].offset+de.readBits($.kInsertLengthPrefixCode[ut].nbits),Re=$.kCopyLengthPrefixCode[Ut].offset+de.readBits($.kCopyLengthPrefixCode[Ut].nbits),b=q[E-1&se],qe=q[E-2&se],c0=0;c0<B0;++c0)de.readMoreInput(),i0[0]===0&&(X0(n0[0],We,0,b0,Ft,rt,de),i0[0]=Je(_e,0,de),ir=b0[0]<<Se,Bt=ir,D0=at[b0[0]],Mt=oe.lookupOffsets[D0],Rt=oe.lookupOffsets[D0+1]),Y0=oe.lookup[Mt+b]|oe.lookup[Rt+qe],or=Dt[Bt+Y0],--i0[0],qe=b,b=Oe(we[0].codes,we[0].htrees[or],de),q[E&se]=b,(E&se)===se&&I.write(q,j),++E;if($e-=B0,$e<=0)break;if(Ne<0){var Y0;if(de.readMoreInput(),i0[2]===0&&(X0(n0[2],We,2,b0,Ft,rt,de),i0[2]=Je(_e,2*Z,de),lr=b0[2]<<ge,Lt=lr),--i0[2],Y0=(Re>4?3:Re-2)&255,st=sr[Lt+Y0],Ne=Oe(we[2].codes,we[2].htrees[st],de),Ne>=I0){var ct,ft,g0;Ne-=I0,ft=Ne&nr,Ne>>=T0,ct=(Ne>>1)+1,g0=(2+(Ne&1)<<ct)-4,Ne=I0+(g0+de.readBits(ct)<<T0)+ft}}if(Ze=H0(Ne,Xe,re),Ze<0)throw new Error("[BrotliDecompress] invalid distance");if(E<K&&V!==K?V=E:V=K,Z0=E&se,Ze>V)if(Re>=L.minDictionaryWordLength&&Re<=L.maxDictionaryWordLength){var g0=L.offsetsByLength[Re],L0=Ze-V-1,dr=L.sizeBitsByLength[Re],Yr=(1<<dr)-1,Zr=L0&Yr,hr=L0>>dr;if(g0+=Zr*Re,hr<y.kNumTransforms){var Gt=y.transformDictionaryWord(q,Z0,g0,Re,hr);if(Z0+=Gt,E+=Gt,$e-=Gt,Z0>=v){I.write(q,j);for(var dt=0;dt<Z0-v;dt++)q[dt]=q[v+dt]}}else throw new Error("Invalid backward reference. pos: "+E+" distance: "+Ze+" len: "+Re+" bytes left: "+$e)}else throw new Error("Invalid backward reference. pos: "+E+" distance: "+Ze+" len: "+Re+" bytes left: "+$e);else{if(Ne>0&&(Xe[re&3]=Ze,++re),Re>$e)throw new Error("Invalid backward reference. pos: "+E+" distance: "+Ze+" len: "+Re+" bytes left: "+$e);for(c0=0;c0<Re;++c0)q[E&se]=q[E-Ze&se],(E&se)===se&&I.write(q,j),++E,--$e}b=q[E-1&se],qe=q[E-2&se]}E&=1073741823}}I.write(q,E&se)}G=g,L.init()},23953:(be,H,m)=>{var G=m(53742);H.init=function(){var w=m(82462).BrotliDecompressBuffer,N=G.toByteArray(m(26019));return w(N)}},26019:be=>{be.exports="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"},33888:(be,H,m)=>{var G=m(23953);H.init=function(){H.dictionary=G.init()},H.offsetsByLength=new Uint32Array([0,0,0,0,0,4096,9216,21504,35840,44032,53248,63488,74752,87040,93696,100864,104704,106752,108928,113536,115968,118528,119872,121280,122016]),H.sizeBitsByLength=new Uint8Array([0,0,0,0,10,10,11,11,10,10,10,10,10,9,9,8,7,7,8,7,7,6,6,5,5]),H.minDictionaryWordLength=4,H.maxDictionaryWordLength=24},19427:(be,H)=>{function m(L,M){this.bits=L,this.value=M}H.z=m;var G=15;function w(L,M){for(var ce=1<<M-1;L&ce;)ce>>=1;return(L&ce-1)+ce}function N(L,M,ce,oe,$){do oe-=ce,L[M+oe]=new m($.bits,$.value);while(oe>0)}function D(L,M,ce){for(var oe=1<<M-ce;M<G&&(oe-=L[M],!(oe<=0));)++M,oe<<=1;return M-ce}H.u=function(L,M,ce,oe,$){var y=M,ie,o,R,Y,ne,Se,ge,p,W,Z,T,x=new Int32Array(G+1),h=new Int32Array(G+1);for(T=new Int32Array($),R=0;R<$;R++)x[oe[R]]++;for(h[1]=0,o=1;o<G;o++)h[o+1]=h[o]+x[o];for(R=0;R<$;R++)oe[R]!==0&&(T[h[oe[R]]++]=R);if(p=ce,W=1<<p,Z=W,h[G]===1){for(Y=0;Y<Z;++Y)L[M+Y]=new m(0,T[0]&65535);return Z}for(Y=0,R=0,o=1,ne=2;o<=ce;++o,ne<<=1)for(;x[o]>0;--x[o])ie=new m(o&255,T[R++]&65535),N(L,M+Y,ne,W,ie),Y=w(Y,o);for(ge=Z-1,Se=-1,o=ce+1,ne=2;o<=G;++o,ne<<=1)for(;x[o]>0;--x[o])(Y&ge)!==Se&&(M+=W,p=D(x,o,ce),W=1<<p,Z+=W,Se=Y&ge,L[y+Se]=new m(p+ce&255,M-y-Se&65535)),ie=new m(o-ce&255,T[R++]&65535),N(L,M+(Y>>ce),ne,W,ie),Y=w(Y,o);return Z}},70020:(be,H)=>{function m(G,w){this.offset=G,this.nbits=w}H.kBlockLengthPrefixCode=[new m(1,2),new m(5,2),new m(9,2),new m(13,2),new m(17,3),new m(25,3),new m(33,3),new m(41,3),new m(49,4),new m(65,4),new m(81,4),new m(97,4),new m(113,5),new m(145,5),new m(177,5),new m(209,5),new m(241,6),new m(305,6),new m(369,7),new m(497,8),new m(753,9),new m(1265,10),new m(2289,11),new m(4337,12),new m(8433,13),new m(16625,24)],H.kInsertLengthPrefixCode=[new m(0,0),new m(1,0),new m(2,0),new m(3,0),new m(4,0),new m(5,0),new m(6,1),new m(8,1),new m(10,2),new m(14,2),new m(18,3),new m(26,3),new m(34,4),new m(50,4),new m(66,5),new m(98,5),new m(130,6),new m(194,7),new m(322,8),new m(578,9),new m(1090,10),new m(2114,12),new m(6210,14),new m(22594,24)],H.kCopyLengthPrefixCode=[new m(2,0),new m(3,0),new m(4,0),new m(5,0),new m(6,0),new m(7,0),new m(8,0),new m(9,0),new m(10,1),new m(12,1),new m(14,2),new m(18,2),new m(22,3),new m(30,3),new m(38,4),new m(54,4),new m(70,5),new m(102,5),new m(134,6),new m(198,7),new m(326,8),new m(582,9),new m(1094,10),new m(2118,24)],H.kInsertRangeLut=[0,0,8,8,0,16,8,16,16],H.kCopyRangeLut=[0,8,0,8,16,0,16,8,16]},31015:(be,H)=>{function m(w){this.buffer=w,this.pos=0}m.prototype.read=function(w,N,D){this.pos+D>this.buffer.length&&(D=this.buffer.length-this.pos);for(var L=0;L<D;L++)w[N+L]=this.buffer[this.pos+L];return this.pos+=D,D},H.z=m;function G(w){this.buffer=w,this.pos=0}G.prototype.write=function(w,N){if(this.pos+N>this.buffer.length)throw new Error("Output buffer is not large enough");return this.buffer.set(w.subarray(0,N),this.pos),this.pos+=N,N},H.y=G},46646:(be,H,m)=>{var G=m(33888),w=0,N=1,D=2,L=3,M=4,ce=5,oe=6,$=7,y=8,ie=9,o=10,R=11,Y=12,ne=13,Se=14,ge=15,p=16,W=17,Z=18,T=19,x=20;function h(J,U,ae){this.prefix=new Uint8Array(J.length),this.transform=U,this.suffix=new Uint8Array(ae.length);for(var ve=0;ve<J.length;ve++)this.prefix[ve]=J.charCodeAt(ve);for(var ve=0;ve<ae.length;ve++)this.suffix[ve]=ae.charCodeAt(ve)}var X=[new h("",w,""),new h("",w," "),new h(" ",w," "),new h("",Y,""),new h("",o," "),new h("",w," the "),new h(" ",w,""),new h("s ",w," "),new h("",w," of "),new h("",o,""),new h("",w," and "),new h("",ne,""),new h("",N,""),new h(", ",w," "),new h("",w,", "),new h(" ",o," "),new h("",w," in "),new h("",w," to "),new h("e ",w," "),new h("",w,'"'),new h("",w,"."),new h("",w,'">'),new h("",w,"\n"),new h("",L,""),new h("",w,"]"),new h("",w," for "),new h("",Se,""),new h("",D,""),new h("",w," a "),new h("",w," that "),new h(" ",o,""),new h("",w,". "),new h(".",w,""),new h(" ",w,", "),new h("",ge,""),new h("",w," with "),new h("",w,"'"),new h("",w," from "),new h("",w," by "),new h("",p,""),new h("",W,""),new h(" the ",w,""),new h("",M,""),new h("",w,". The "),new h("",R,""),new h("",w," on "),new h("",w," as "),new h("",w," is "),new h("",$,""),new h("",N,"ing "),new h("",w,"\n	"),new h("",w,":"),new h(" ",w,". "),new h("",w,"ed "),new h("",x,""),new h("",Z,""),new h("",oe,""),new h("",w,"("),new h("",o,", "),new h("",y,""),new h("",w," at "),new h("",w,"ly "),new h(" the ",w," of "),new h("",ce,""),new h("",ie,""),new h(" ",o,", "),new h("",o,'"'),new h(".",w,"("),new h("",R," "),new h("",o,'">'),new h("",w,'="'),new h(" ",w,"."),new h(".com/",w,""),new h(" the ",w," of the "),new h("",o,"'"),new h("",w,". This "),new h("",w,","),new h(".",w," "),new h("",o,"("),new h("",o,"."),new h("",w," not "),new h(" ",w,'="'),new h("",w,"er "),new h(" ",R," "),new h("",w,"al "),new h(" ",R,""),new h("",w,"='"),new h("",R,'"'),new h("",o,". "),new h(" ",w,"("),new h("",w,"ful "),new h(" ",o,". "),new h("",w,"ive "),new h("",w,"less "),new h("",R,"'"),new h("",w,"est "),new h(" ",o,"."),new h("",R,'">'),new h(" ",w,"='"),new h("",o,","),new h("",w,"ize "),new h("",R,"."),new h("\xC2\xA0",w,""),new h(" ",w,","),new h("",o,'="'),new h("",R,'="'),new h("",w,"ous "),new h("",R,", "),new h("",o,"='"),new h(" ",o,","),new h(" ",R,'="'),new h(" ",R,", "),new h("",R,","),new h("",R,"("),new h("",R,". "),new h(" ",R,"."),new h("",R,"='"),new h(" ",R,". "),new h(" ",o,'="'),new h(" ",R,"='"),new h(" ",o,"='")];H.kTransforms=X,H.kNumTransforms=X.length;function P(J,U){return J[U]<192?(J[U]>=97&&J[U]<=122&&(J[U]^=32),1):J[U]<224?(J[U+1]^=32,2):(J[U+2]^=5,3)}H.transformDictionaryWord=function(J,U,ae,ve,Be){var Oe=X[Be].prefix,t0=X[Be].suffix,Ge=X[Be].transform,Je=Ge<Y?0:Ge-(Y-1),H0=0,u0=U,j0;Je>ve&&(Je=ve);for(var s0=0;s0<Oe.length;)J[U++]=Oe[s0++];for(ae+=Je,ve-=Je,Ge<=ie&&(ve-=Ge),H0=0;H0<ve;H0++)J[U++]=G.dictionary[ae+H0];if(j0=U-ve,Ge===o)P(J,j0);else if(Ge===R)for(;ve>0;){var tt=P(J,j0);j0+=tt,ve-=tt}for(var X0=0;X0<t0.length;)J[U++]=t0[X0++];return U-u0}},98834:(be,H,m)=>{be.exports=m(82462).BrotliDecompressBuffer},93067:be=>{var H=function(){"use strict";function m(y,ie){return ie!=null&&y instanceof ie}var G;try{G=Map}catch(y){G=function(){}}var w;try{w=Set}catch(y){w=function(){}}var N;try{N=Promise}catch(y){N=function(){}}function D(y,ie,o,R,Y){typeof ie=="object"&&(o=ie.depth,R=ie.prototype,Y=ie.includeNonEnumerable,ie=ie.circular);var ne=[],Se=[],ge=typeof Buffer<"u";typeof ie>"u"&&(ie=!0),typeof o>"u"&&(o=1/0);function p(W,Z){if(W===null)return null;if(Z===0)return W;var T,x;if(typeof W!="object")return W;if(m(W,G))T=new G;else if(m(W,w))T=new w;else if(m(W,N))T=new N(function(Oe,t0){W.then(function(Ge){Oe(p(Ge,Z-1))},function(Ge){t0(p(Ge,Z-1))})});else if(D.__isArray(W))T=[];else if(D.__isRegExp(W))T=new RegExp(W.source,$(W)),W.lastIndex&&(T.lastIndex=W.lastIndex);else if(D.__isDate(W))T=new Date(W.getTime());else{if(ge&&Buffer.isBuffer(W))return Buffer.allocUnsafe?T=Buffer.allocUnsafe(W.length):T=new Buffer(W.length),W.copy(T),T;m(W,Error)?T=Object.create(W):typeof R>"u"?(x=Object.getPrototypeOf(W),T=Object.create(x)):(T=Object.create(R),x=R)}if(ie){var h=ne.indexOf(W);if(h!=-1)return Se[h];ne.push(W),Se.push(T)}m(W,G)&&W.forEach(function(Oe,t0){var Ge=p(t0,Z-1),Je=p(Oe,Z-1);T.set(Ge,Je)}),m(W,w)&&W.forEach(function(Oe){var t0=p(Oe,Z-1);T.add(t0)});for(var X in W){var P;x&&(P=Object.getOwnPropertyDescriptor(x,X)),!(P&&P.set==null)&&(T[X]=p(W[X],Z-1))}if(Object.getOwnPropertySymbols)for(var J=Object.getOwnPropertySymbols(W),X=0;X<J.length;X++){var U=J[X],ae=Object.getOwnPropertyDescriptor(W,U);ae&&!ae.enumerable&&!Y||(T[U]=p(W[U],Z-1),ae.enumerable||Object.defineProperty(T,U,{enumerable:!1}))}if(Y)for(var ve=Object.getOwnPropertyNames(W),X=0;X<ve.length;X++){var Be=ve[X],ae=Object.getOwnPropertyDescriptor(W,Be);ae&&ae.enumerable||(T[Be]=p(W[Be],Z-1),Object.defineProperty(T,Be,{enumerable:!1}))}return T}return p(y,o)}D.clonePrototype=function(ie){if(ie===null)return null;var o=function(){};return o.prototype=ie,new o};function L(y){return Object.prototype.toString.call(y)}D.__objToStr=L;function M(y){return typeof y=="object"&&L(y)==="[object Date]"}D.__isDate=M;function ce(y){return typeof y=="object"&&L(y)==="[object Array]"}D.__isArray=ce;function oe(y){return typeof y=="object"&&L(y)==="[object RegExp]"}D.__isRegExp=oe;function $(y){var ie="";return y.global&&(ie+="g"),y.ignoreCase&&(ie+="i"),y.multiline&&(ie+="m"),ie}return D.__getRegExpFlags=$,D}();be.exports&&(be.exports=H)},65614:be=>{"use strict";var H=1,m=0;class G{constructor(N){this.stateTable=N.stateTable,this.accepting=N.accepting,this.tags=N.tags}match(N){var D=this;return{*[Symbol.iterator](){for(var L=H,M=null,ce=null,oe=null,$=0;$<N.length;$++){var y=N[$];oe=L,L=D.stateTable[L][y],L===m&&(M!=null&&ce!=null&&ce>=M&&(yield[M,ce,D.tags[oe]]),L=D.stateTable[H][y],M=null),L!==m&&M==null&&(M=$),D.accepting[L]&&(ce=$),L===m&&(L=H)}M!=null&&ce!=null&&ce>=M&&(yield[M,ce,D.tags[L]])}}}apply(N,D){for(var[L,M,ce]of this.match(N))for(var oe of ce)typeof D[oe]=="function"&&D[oe](L,M,N.slice(L,M+1))}}be.exports=G},40937:be=>{"use strict";be.exports=function H(m,G){if(m===G)return!0;if(m&&G&&typeof m=="object"&&typeof G=="object"){if(m.constructor!==G.constructor)return!1;var w,N,D;if(Array.isArray(m)){if(w=m.length,w!=G.length)return!1;for(N=w;N--!==0;)if(!H(m[N],G[N]))return!1;return!0}if(m.constructor===RegExp)return m.source===G.source&&m.flags===G.flags;if(m.valueOf!==Object.prototype.valueOf)return m.valueOf()===G.valueOf();if(m.toString!==Object.prototype.toString)return m.toString()===G.toString();if(D=Object.keys(m),w=D.length,w!==Object.keys(G).length)return!1;for(N=w;N--!==0;)if(!Object.prototype.hasOwnProperty.call(G,D[N]))return!1;for(N=w;N--!==0;){var L=D[N];if(!H(m[L],G[L]))return!1}return!0}return m!==m&&G!==G}},93477:be=>{var H=0,m=-3;function G(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function w(h,X){this.source=h,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=X,this.destLen=0,this.ltree=new G,this.dtree=new G}var N=new G,D=new G,L=new Uint8Array(30),M=new Uint16Array(30),ce=new Uint8Array(30),oe=new Uint16Array(30),$=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),y=new G,ie=new Uint8Array(320);function o(h,X,P,J){var U,ae;for(U=0;U<P;++U)h[U]=0;for(U=0;U<30-P;++U)h[U+P]=U/P|0;for(ae=J,U=0;U<30;++U)X[U]=ae,ae+=1<<h[U]}function R(h,X){var P;for(P=0;P<7;++P)h.table[P]=0;for(h.table[7]=24,h.table[8]=152,h.table[9]=112,P=0;P<24;++P)h.trans[P]=256+P;for(P=0;P<144;++P)h.trans[24+P]=P;for(P=0;P<8;++P)h.trans[168+P]=280+P;for(P=0;P<112;++P)h.trans[176+P]=144+P;for(P=0;P<5;++P)X.table[P]=0;for(X.table[5]=32,P=0;P<32;++P)X.trans[P]=P}var Y=new Uint16Array(16);function ne(h,X,P,J){var U,ae;for(U=0;U<16;++U)h.table[U]=0;for(U=0;U<J;++U)h.table[X[P+U]]++;for(h.table[0]=0,ae=0,U=0;U<16;++U)Y[U]=ae,ae+=h.table[U];for(U=0;U<J;++U)X[P+U]&&(h.trans[Y[X[P+U]]++]=U)}function Se(h){h.bitcount--||(h.tag=h.source[h.sourceIndex++],h.bitcount=7);var X=h.tag&1;return h.tag>>>=1,X}function ge(h,X,P){if(!X)return P;for(;h.bitcount<24;)h.tag|=h.source[h.sourceIndex++]<<h.bitcount,h.bitcount+=8;var J=h.tag&65535>>>16-X;return h.tag>>>=X,h.bitcount-=X,J+P}function p(h,X){for(;h.bitcount<24;)h.tag|=h.source[h.sourceIndex++]<<h.bitcount,h.bitcount+=8;var P=0,J=0,U=0,ae=h.tag;do J=2*J+(ae&1),ae>>>=1,++U,P+=X.table[U],J-=X.table[U];while(J>=0);return h.tag=ae,h.bitcount-=U,X.trans[P+J]}function W(h,X,P){var J,U,ae,ve,Be,Oe;for(J=ge(h,5,257),U=ge(h,5,1),ae=ge(h,4,4),ve=0;ve<19;++ve)ie[ve]=0;for(ve=0;ve<ae;++ve){var t0=ge(h,3,0);ie[$[ve]]=t0}for(ne(y,ie,0,19),Be=0;Be<J+U;){var Ge=p(h,y);switch(Ge){case 16:var Je=ie[Be-1];for(Oe=ge(h,2,3);Oe;--Oe)ie[Be++]=Je;break;case 17:for(Oe=ge(h,3,3);Oe;--Oe)ie[Be++]=0;break;case 18:for(Oe=ge(h,7,11);Oe;--Oe)ie[Be++]=0;break;default:ie[Be++]=Ge;break}}ne(X,ie,0,J),ne(P,ie,J,U)}function Z(h,X,P){for(;;){var J=p(h,X);if(J===256)return H;if(J<256)h.dest[h.destLen++]=J;else{var U,ae,ve,Be;for(J-=257,U=ge(h,L[J],M[J]),ae=p(h,P),ve=h.destLen-ge(h,ce[ae],oe[ae]),Be=ve;Be<ve+U;++Be)h.dest[h.destLen++]=h.dest[Be]}}}function T(h){for(var X,P,J;h.bitcount>8;)h.sourceIndex--,h.bitcount-=8;if(X=h.source[h.sourceIndex+1],X=256*X+h.source[h.sourceIndex],P=h.source[h.sourceIndex+3],P=256*P+h.source[h.sourceIndex+2],X!==(~P&65535))return m;for(h.sourceIndex+=4,J=X;J;--J)h.dest[h.destLen++]=h.source[h.sourceIndex++];return h.bitcount=0,H}function x(h,X){var P=new w(h,X),J,U,ae;do{switch(J=Se(P),U=ge(P,2,0),U){case 0:ae=T(P);break;case 1:ae=Z(P,N,D);break;case 2:W(P,P.ltree,P.dtree),ae=Z(P,P.ltree,P.dtree);break;default:ae=m}if(ae!==H)throw new Error("Data error")}while(!J);return P.destLen<P.dest.length?typeof P.dest.slice=="function"?P.dest.slice(0,P.destLen):P.dest.subarray(0,P.destLen):P.dest}R(N,D),o(L,M,4,3),o(ce,oe,2,1),L[28]=0,M[28]=258,be.exports=x},43803:(be,H,m)=>{const G=m(93477),{swap32LE:w}=m(29432),N=11,D=5,L=N-D,M=65536>>N,oe=(1<<L)-1,$=2,ie=(1<<D)-1,o=65536>>D,R=1024>>D,ge=o+R+32,p=1<<$;class W{constructor(T){const x=typeof T.readUInt32BE=="function"&&typeof T.slice=="function";if(x||T instanceof Uint8Array){let h;if(x)this.highStart=T.readUInt32LE(0),this.errorValue=T.readUInt32LE(4),h=T.readUInt32LE(8),T=T.slice(12);else{const X=new DataView(T.buffer);this.highStart=X.getUint32(0,!0),this.errorValue=X.getUint32(4,!0),h=X.getUint32(8,!0),T=T.subarray(12)}T=G(T,new Uint8Array(h)),T=G(T,new Uint8Array(h)),w(T),this.data=new Uint32Array(T.buffer)}else({data:this.data,highStart:this.highStart,errorValue:this.errorValue}=T)}get(T){let x;return T<0||T>1114111?this.errorValue:T<55296||T>56319&&T<=65535?(x=(this.data[T>>D]<<$)+(T&ie),this.data[x]):T<=65535?(x=(this.data[o+(T-55296>>D)]<<$)+(T&ie),this.data[x]):T<this.highStart?(x=this.data[ge-M+(T>>N)],x=this.data[x+(T>>D&oe)],x=(x<<$)+(T&ie),this.data[x]):this.data[this.data.length-p]}}be.exports=W},29432:be=>{const H=new Uint8Array(new Uint32Array([305419896]).buffer)[0]===18,m=(N,D,L)=>{let M=N[D];N[D]=N[L],N[L]=M},G=N=>{const D=N.length;for(let L=0;L<D;L+=4)m(N,L,L+3),m(N,L+1,L+2)},w=N=>{H&&G(N)};be.exports={swap32LE:w}}}]);

//# sourceMappingURL=lazy-lib-fontkit-c05f27c8c0bb64d649b9.js.map