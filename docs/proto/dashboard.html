<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        .dashboard-row {
            display: flex;
            gap: 1.5vw;
            margin-bottom: 0;
        }
        .dashboard-card {
            background: #fff;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.5rem 0 rgba(0,0,0,0.04);
            padding: 1.5rem 1.25rem 1.125rem 1.25rem;
            flex: 1;
            min-width: 13.75rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .dashboard-card-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: #888;
        }
        .dashboard-card-value {
            font-size: 1.76rem;
            font-weight: 900;
            color: #4ec6c8;
            margin-bottom: 0.5rem;
        }
        .dashboard-card-unit {
            font-size: 1rem;
            color: #888;
        }
        .dashboard-card-group {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 0.625rem;
        }
        .dashboard-card-group .circle {
            width: 1.375rem;
            height: 1.375rem;
            border-radius: 50%;
            background: #4ec6c8;
            display: inline-block;
            margin-right: 0.375rem;
            border: 0.125rem solid #e0e0e0;
        }
        .dashboard-card-group .circle.gray {
            background: #e0e0e0;
        }
        .dashboard-card-group .circle.orange {
            background: orange;
        }
        .dashboard-card-btns {
            display: flex;
            gap: 0.625rem;
            margin-top: 0.625rem;
        }
        .dashboard-card-btns .el-button {
            min-width: 3.75rem;
        }
        .dashboard-monitor-row {
            display: flex;
            gap: 1.5vw;
        }
        .dashboard-monitor-card {
            background: #fff;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.5rem 0 rgba(0,0,0,0.04);
            padding: 1.125rem 1.125rem 0.75rem 1.125rem;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .dashboard-monitor-title,
        .dashboard-monitor-card > div[style*="color:#4ec6c8;"] {
            font-size: 1.1rem !important;
            font-weight: 700;
            margin-bottom: 10px;
            color: #4ec6c8 !important;
        }
        .dashboard-monitor-img {
            width: 320px;
            height: 180px;
            object-fit: cover;
            border-radius: 8px;
        }
        .dashboard-monitor-actions {
            display: flex;
            flex-direction: column;
            gap: 0.375rem;
            align-items: flex-end;
        }
        .dashboard-monitor-actions span {
            font-size: 0.98rem;
            color: #666;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        .dashboard-monitor-actions i {
            margin-right: 0.25rem;
        }
        @media (max-width: 1200px) {
            .dashboard-row, .dashboard-monitor-row {
                flex-direction: column;
                gap: 1vw;
            }
            .dashboard-card {
                min-width: 90vw;
                max-width: 100vw;
            }
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
            .dashboard-header-title { font-size: 1.2rem; }
            .dashboard-card-value { font-size: 1.2rem; }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .dashboard-monitor-img {
                width: 272px !important;  /* 320px * 0.85 */
                height: 153px !important; /* 180px * 0.85 */
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li class="active"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content" style="background:#fff;">
            <div class="dashboard-row" style="gap: 18px;">
                <!-- 状态监控卡片 -->
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;min-width:220px;max-width:100%;color:#222;">
                    <div style="font-weight:700;font-size:1.1rem;margin-bottom:10px;color:#4ec6c8;">状态监控</div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-bottom:10px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">实时流量（L/h）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">0.6</span>
                        </div>
                    </div>
                    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;width:100%;margin-top:20px;">
                        <div style="display:flex;align-items:center;">
                            <span style="color:#888;font-size:1.1rem;font-weight:700;">氯气</span>
                            <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:75px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;margin-left:8px;">
                                <div style="width:18px;height:18px;border-radius:50%;background:#A2EF4D;"></div>
                            </div>
                        </div>
                        <div style="display:flex;align-items:center;">
                            <span style="color:#888;font-size:1.1rem;font-weight:700;">烟感</span>
                            <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:75px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;margin-left:8px;margin-right:0;">
                                <div style="width:18px;height:18px;border-radius:50%;background:#A2EF4D;"></div>
                            </div>
                        </div>
                    </div>
                    <div style="font-weight:700;font-size:1.1rem;margin-bottom:18px;margin-top:20px;color:#4ec6c8;">空气强排</div>
                    <div style="display:flex;gap:16px;width:100%;">
                        <button style="flex:1;padding:10px 0;font-size:1.1rem;border-radius:6px;border:none;background:#e0e0e0;color:#333;font-weight:700;">关闭</button>
                        <button style="flex:1;padding:10px 0;font-size:1.1rem;border-radius:6px;border:none;background:#4ec6c8;color:#fff;font-weight:700;">开启</button>
                    </div>
                </div>
                <!-- 实时出水卡片 -->
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;min-width:220px;max-width:100%;color:#222;">
                    <div style="font-weight:700;font-size:1.1rem;margin-bottom:10px;color:#4ec6c8;">实时出水</div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-bottom:24px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">PH</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">7.6</span>
                        </div>
                    </div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-bottom:8px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">余氯（mg/L）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">4.3</span>
                        </div>
                    </div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-top:16px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">出水温度（℃）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">26.7</span>
                        </div>
                    </div>
                </div>
                <!-- 实时液位卡片 -->
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;min-width:220px;max-width:100%;color:#222;">
                    <div style="font-weight:700;font-size:1.1rem;margin-bottom:10px;color:#4ec6c8;">实时液位</div>
                    <div style="display:flex;justify-content:space-between;width:100%;gap:16px;margin-bottom:8px;">
                        <div style="display:flex;flex-direction:column;gap:12px;align-items:flex-start;width:120px;">
                            <div style="display:flex;justify-content:space-between;align-items:center;width:100%;margin-top:20px;">
                                <span style="color:#888;font-size:1.1rem;font-weight:700;">药桶A</span>
                                <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:60px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                                    <div style="width:18px;height:18px;border-radius:50%;background:#A2EF4D;"></div>
                                </div>
                            </div>
                            <div style="display:flex;justify-content:space-between;align-items:center;width:100%;margin-top:20px;">
                                <span style="color:#888;font-size:1.1rem;font-weight:700;">消毒池</span>
                                <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:60px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                                    <div style="width:18px;height:18px;border-radius:50%;background:#A2EF4D;"></div>
                                </div>
                            </div>
                        </div>
                        <div style="display:flex;flex-direction:column;gap:12px;align-items:flex-end;">
                            <div style="display:flex;align-items:center;gap:6px;margin-top:20px;">
                                <span style="color:#888;font-size:1.1rem;font-weight:700;">药桶B</span>
                                <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:60px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                                    <div style="width:18px;height:18px;border-radius:50%;background:orange;"></div>
                                </div>
                            </div>
                            <div style="display:flex;align-items:center;gap:6px;margin-top:20px;">
                                <span style="color:#888;font-size:1.1rem;font-weight:700;">积水</span>
                                <div style="background:#fff;border:2px solid #ddd;border-radius:6px;width:60px;height:32px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                                    <div style="width:18px;height:18px;border-radius:50%;background:#A2EF4D;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-top:23px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">集水池（cm）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">96.3</span>
                        </div>
                    </div>
                </div>
                <!-- 实时环境卡片 -->
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;min-width:220px;max-width:100%;color:#222;">
                    <div style="font-weight:700;font-size:1.1rem;margin-bottom:10px;color:#4ec6c8;">实时环境</div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-bottom:24px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">氯气浓度（PPm）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;min-width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">0.8</span>
                        </div>
                    </div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-bottom:8px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">环境湿度</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;min-width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">64%</span>
                        </div>
                    </div>
                    <div style="display:flex;align-items:center;justify-content:space-between;width:100%;margin-top:16px;">
                        <span style="color:#888;font-size:1.1rem;font-weight:700;">环境温度（℃）</span>
                        <div style="background:#fff;border:2px solid #ddd;border-radius:6px;height:50px;width:120px;min-width:120px;box-sizing:border-box;display:flex;align-items:center;justify-content:center;">
                            <span style="font-size:1.76rem;font-weight:900;color:#4ec6c8;">24.3</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 现场实时监控 -->
            <div class="dashboard-row" style="gap:18px; margin-top:18px;">
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;flex:1;min-width:320px;max-width:100%;color:#222;">
                    <div class="dashboard-monitor-title">污水间监控摄像 1#</div>
                    <div style="display:flex;align-items:flex-start;gap:16px;width:100%;">
                        <img src="/img/cam1.jpg" class="dashboard-monitor-img" alt="监控1">
                        <div style="display:flex;flex-direction:column;gap:10px;align-items:flex-end;">
                            <span style="display:flex;align-items:center;gap:4px;cursor:pointer;"><i class="fa fa-image"></i>截图</span>
                            <span style="display:flex;align-items:center;gap:4px;cursor:pointer;"><i class="fa fa-video"></i>录像</span>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card" style="background:#f3f5f7;align-items:flex-start;flex:1;min-width:320px;max-width:100%;color:#222;">
                    <div class="dashboard-monitor-title">污水间监控摄像 2#</div>
                    <div style="display:flex;align-items:flex-start;gap:16px;width:100%;">
                        <img src="/img/cam2.jpg" class="dashboard-monitor-img" alt="监控2">
                        <div style="display:flex;flex-direction:column;gap:10px;align-items:flex-end;">
                            <span style="display:flex;align-items:center;gap:4px;cursor:pointer;"><i class="fa fa-image"></i>截图</span>
                            <span style="display:flex;align-items:center;gap:4px;cursor:pointer;"><i class="fa fa-video"></i>录像</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });
    </script>
</body>
</html> 