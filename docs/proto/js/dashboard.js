// 导入 Vue 组件
const { createApp, ref, onMounted, onUnmounted } = Vue;

// 创建仪表盘应用
const dashboardApp = createApp({
    setup() {
        // 响应式状态
        const currentTime = ref(new Date().toLocaleString());
        const menuCollapsed = ref(false);
        const activeMenu = ref('dashboard');
        const loading = ref(false);
        const error = ref('');

        // 模拟数据
        const dashboardData = ref({
            waterLevel: 75,
            waterQuality: 92,
            systemStatus: '正常运行',
            lastUpdate: new Date().toLocaleString()
        });

        // 更新时间的定时器
        let timer = null;

        // 获取仪表盘数据
        const fetchDashboardData = async () => {
            try {
                loading.value = true;
                error.value = '';
                
                // 模拟 API 请求
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 更新数据
                dashboardData.value = {
                    waterLevel: Math.floor(Math.random() * 100),
                    waterQuality: Math.floor(Math.random() * 100),
                    systemStatus: '正常运行',
                    lastUpdate: new Date().toLocaleString()
                };
            } catch (err) {
                error.value = '获取数据失败，请稍后重试';
                console.error('Fetch data error:', err);
            } finally {
                loading.value = false;
            }
        };

        // 切换菜单
        const toggleMenu = () => {
            menuCollapsed.value = !menuCollapsed.value;
        };

        // 切换菜单项
        const changeMenu = (menu) => {
            activeMenu.value = menu;
            if (window.innerWidth <= 768) {
                menuCollapsed.value = true;
            }
        };

        // 生命周期钩子
        onMounted(() => {
            // 检查登录状态
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                window.location.href = 'index.html';
                return;
            }

            // 启动定时器
            timer = setInterval(() => {
                currentTime.value = new Date().toLocaleString();
            }, 1000);

            // 获取初始数据
            fetchDashboardData();

            // 设置定时刷新
            setInterval(fetchDashboardData, 30000);
        });

        onUnmounted(() => {
            // 清理定时器
            if (timer) {
                clearInterval(timer);
            }
        });

        // 返回模板需要的数据和方法
        return {
            currentTime,
            menuCollapsed,
            activeMenu,
            loading,
            error,
            dashboardData,
            toggleMenu,
            changeMenu
        };
    }
});

// 使用 Element Plus
dashboardApp.use(ElementPlus);

// 错误处理
dashboardApp.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err);
    console.error('Error Info:', info);
};

// 性能优化：生产环境配置
if (process.env.NODE_ENV === 'production') {
    dashboardApp.config.performance = true;
}

// 挂载应用
dashboardApp.mount('#dashboard'); 