<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        /* 添加全局样式覆盖 Element Plus 的默认颜色 */
        :root {
            --el-color-primary: #4ec6c8 !important;
            --el-color-primary-light-3: #7ad4d6 !important;
            --el-color-primary-light-5: #a5e2e3 !important;
            --el-color-primary-light-7: #d0f0f1 !important;
            --el-color-primary-light-9: #f0fafa !important;
            --el-color-primary-dark-2: #3e9ea0 !important;
        }
        /* 覆盖开关组件的颜色 */
        .el-switch.is-checked .el-switch__core {
            border-color: #4ec6c8 !important;
            background-color: #4ec6c8 !important;
        }
        .el-switch__core {
            border-color: #dcdfe6 !important;
        }
        /* 覆盖按钮的悬停和激活状态 */
        .el-button--primary:hover,
        .el-button--primary:focus {
            background: #3e9ea0 !important;
            border-color: #3e9ea0 !important;
        }
        .el-button--primary:active {
            background: #3e9ea0 !important;
            border-color: #3e9ea0 !important;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        @media (max-width: 1200px) {
            .dashboard-row, .dashboard-monitor-row {
                flex-direction: column;
                gap: 1vw;
            }
            .dashboard-card {
                min-width: 90vw;
                max-width: 100vw;
            }
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
            .dashboard-header-title { font-size: 1.2rem; }
            .dashboard-card-value { font-size: 1.2rem; }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .dashboard-monitor-img {
                width: 272px !important;  /* 320px * 0.85 */
                height: 153px !important; /* 180px * 0.85 */
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li class="active"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content" style="background:#fff;">
            <!-- 内容区域 -->
            <div id="sensor-table-app"></div>
            <div id="control-card-app"></div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });

        // 传感器状态一览表 Vue3 + Element Plus
        const { createApp } = Vue;
        const { ElTable, ElTableColumn, ElTag, ElSelect, ElInput, ElSwitch, ElButton } = ElementPlus;
        const sensorData = [
            { id: 1, name: 'PH传感器', qty: 1, status: 'online', desc: '设备在线，通讯正常' },
            { id: 2, name: '余氯传感器', qty: 1, status: 'online', desc: '设备在线，通讯正常' },
            { id: 3, name: '水温传感器', qty: 1, status: 'offline', desc: '设备掉线，通讯异常' },
            { id: 4, name: '集水池传感器', qty: 1, status: 'error', desc: '设备故障，请立即检查' },
            { id: 5, name: '环境氨气传感器', qty: 1, status: 'online', desc: '设备在线，通讯正常' },
            { id: 6, name: '环境温度湿度传感器', qty: 1, status: 'offline', desc: '设备掉线，通讯异常' },
            { id: 7, name: '环境烟雾浓度传感器', qty: 1, status: 'online', desc: '设备在线，通讯正常' },
            { id: 8, name: '积水传感器', qty: 1, status: 'online', desc: '设备在线，通讯正常' },
            { id: 9, name: '药剂桶传感器', qty: 1, status: 'error', desc: '设备故障，请立即检查' },
            { id: 10, name: '视频监控', qty: 2, status: 'online', desc: '设备在线，通讯正常' },
        ];
        const statusMap = {
            online: { color: '#7CFC00', text: '在线' },
            offline: { color: '#BEBEBE', text: '掉线' },
            error: { color: '#FF6B6B', text: '故障' },
        };
        createApp({
            data() {
                return { 
                    sensorData, 
                    statusMap, 
                    showDesc: window.innerWidth > 768
                };
            },
            mounted() {
                window.addEventListener('resize', this.handleResize);
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.handleResize);
            },
            methods: {
                handleResize() {
                    this.showDesc = window.innerWidth > 768;
                }
            },
            template: `
                <div style="margin:2rem 0;">
                    <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;">传感器状态一览</h2>
                    <el-table :data="sensorData" border style="width:100%;">
                        <el-table-column prop="id" label="序号" min-width="36" align="center" />
                        <el-table-column prop="name" label="名称" min-width="100" align="left" header-align="center" />
                        <el-table-column prop="qty" label="数量" min-width="50" align="center" />
                        <el-table-column label="状态" min-width="36" align="center">
                            <template #default="scope">
                                <span style="display:flex;align-items:center;justify-content:center;">
                                    <span :style="{display:'inline-block',width:'16px',height:'16px',borderRadius:'50%',background:statusMap[scope.row.status].color,marginRight:'0',border:'1.5px solid #eee'}"></span>
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="showDesc" prop="desc" label="说明" min-width="120" align="left" />
                    </el-table>
                </div>
            `
        }).use(ElementPlus).mount('#sensor-table-app');

        // 控制卡片 Vue3 + Element Plus
        createApp({
            data() {
                return {
                    // 排气扇控制数据
                    fanAmmoniaUpper: '0.3',
                    fanAmmoniaLower: '0.1',
                    fanAmmoniaUnit: 'PPM',
                    fanAmmoniaUpperEnabled: true,
                    fanAmmoniaLowerEnabled: false,
                    fanDurationUpper: '1',
                    fanDurationLower: '1',
                    fanDurationUnit: 'hour',
                    fanDurationUpperEnabled: false,
                    fanDurationLowerEnabled: true,
                    // 加药泵控制数据
                    pumpAmount: '',
                    pumpAmountUnit: 'L/d',
                    pumpConcentration: '',
                    pumpConcentrationUnit: 'g/t',
                    pumpDosage: '',
                    pumpDosageUnit: 'PPM',
                    pumpSmartEnabled: true,
                    pumpSmartConcentration: '',
                    pumpSmartConcentrationUnit: 'mg/L',
                    pumpSmartDosage: '',
                    pumpSmartDosageUnit: 'PPM'
                };
            },
            template: `
            <div class='control-row' style='margin-top:32px;'>
                <div style="flex:1;min-width:0;">
                    <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;text-align:left;">排气扇控制</h2>
                    <div class='control-card fan-card'>
                        <div class='dashboard-query-block'>
                            <div class='control-card-value-row'>
                                <div class='control-form-controls'>
                                    <span class='control-symbol'>&gt;</span>
                                    <el-select v-model='fanAmmoniaUpper' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='0.3' value='0.3'></el-option>
                                        <el-option label='0.5' value='0.5'></el-option>
                                        <el-option label='1.0' value='1.0'></el-option>
                                    </el-select>
                                    <el-select v-model='fanAmmoniaUnit' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='PPM' value='PPM'></el-option>
                                    </el-select>
                                    <el-switch v-model='fanAmmoniaUpperEnabled'></el-switch>
                                </div>
                            </div>
                            <div class='control-card-value-row'>
                                <div class='control-form-controls'>
                                    <span class='control-symbol'>&lt;</span>
                                    <el-select v-model='fanAmmoniaLower' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='0.1' value='0.1'></el-option>
                                        <el-option label='0.2' value='0.2'></el-option>
                                        <el-option label='0.3' value='0.3'></el-option>
                                    </el-select>
                                    <el-select v-model='fanAmmoniaUnit' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='PPM' value='PPM'></el-option>
                                    </el-select>
                                    <el-switch v-model='fanAmmoniaLowerEnabled'></el-switch>
                                </div>
                            </div>
                            <div class='control-card-value-row'>
                                <div class='control-form-controls'>
                                    <span class='control-symbol'>如</span>
                                    <el-select v-model='fanDurationUpper' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='1' value='1'></el-option>
                                        <el-option label='2' value='2'></el-option>
                                        <el-option label='3' value='3'></el-option>
                                    </el-select>
                                    <el-select v-model='fanDurationUnit' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='小时' value='hour'></el-option>
                                        <el-option label='分钟' value='minute'></el-option>
                                    </el-select>
                                    <el-switch v-model='fanDurationUpperEnabled'></el-switch>
                                </div>
                            </div>
                            <div class='control-card-value-row'>
                                <div class='control-form-controls'>
                                    <span class='control-symbol'>则</span>
                                    <el-select v-model='fanDurationLower' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='1' value='1'></el-option>
                                        <el-option label='2' value='2'></el-option>
                                        <el-option label='3' value='3'></el-option>
                                    </el-select>
                                    <el-select v-model='fanDurationUnit' placeholder='请选择' class='control-el-select unit-select'>
                                        <el-option label='小时' value='hour'></el-option>
                                        <el-option label='分钟' value='minute'></el-option>
                                    </el-select>
                                    <el-switch v-model='fanDurationLowerEnabled'></el-switch>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="flex:2;min-width:340px;">
                    <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;text-align:left;">加药泵控制</h2>
                    <!-- 加药泵控制卡片 -->
                    <div class='control-card pump-card'>
                        <div class='control-pump-methods-row'>
                            <div class='control-pump-method'>
                                <div class='dashboard-query-block'>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>处理量：</span>
                                        <div class='control-form-controls'>
                                            <el-input v-model='pumpAmount' placeholder='请输入' class='control-el-input'></el-input>
                                            <el-select v-model='pumpAmountUnit' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='L/d' value='L/d'></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>投加准值：</span>
                                        <div class='control-form-controls'>
                                            <el-input v-model='pumpConcentration' placeholder='请输入' class='control-el-input'></el-input>
                                            <el-select v-model='pumpConcentrationUnit' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='g/t' value='g/t'></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>溶质浓度：</span>
                                        <div class='control-form-controls'>
                                            <el-input v-model='pumpDosage' placeholder='请输入' class='control-el-input'></el-input>
                                            <el-select v-model='pumpDosageUnit' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='PPM' value='PPM'></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <el-button type='primary' class='dashboard-query-btn'>执行恒定小时加药法</el-button>
                                </div>
                            </div>
                            <div class='control-pump-method'>
                                <div class='dashboard-query-block'>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>处理量：</span>
                                        <div class='control-form-controls'>
                                            <el-switch v-model='pumpSmartEnabled' style='margin-left:10px;'></el-switch>
                                        </div>
                                    </div>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>余氯准值：</span>
                                        <div class='control-form-controls'>
                                            <el-select v-model='pumpSmartConcentration' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='2-8' value='0.1'></el-option>
                                                <el-option label='3-10' value='0.2'></el-option>
                                            </el-select>
                                            <el-select v-model='pumpSmartConcentrationUnit' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='mg/L' value='mg/L'></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class='control-card-value-row'>
                                        <span class='control-symbol'>溶质浓度：</span>
                                        <div class='control-form-controls'>
                                            <el-input v-model='pumpSmartDosage' placeholder='请输入' class='control-el-input'></el-input>
                                            <el-select v-model='pumpSmartDosageUnit' placeholder='请选择' class='control-el-select unit-select'>
                                                <el-option label='PPM' value='PPM'></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <el-button type='primary' class='dashboard-query-btn'>执行智能流量控制法</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            `
        }).use(ElementPlus).mount('#control-card-app');
    </script>
</body>
</html> 