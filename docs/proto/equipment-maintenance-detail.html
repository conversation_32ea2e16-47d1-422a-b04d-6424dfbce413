<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
        }
        
        /* 移动端提示样式 */
        .mobile-notice {
            display: none;
            text-align: center;
            padding: 4rem 2rem;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.04);
            margin: 2rem auto;
            max-width: 500px;
        }
        
        .mobile-notice-icon {
            font-size: 4rem;
            color: #4ec6c8;
            margin-bottom: 1.5rem;
        }
        
        .mobile-notice-text {
            font-size: 1.2rem;
            color: #666;
            line-height: 1.6;
        }
        
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .page-main-title {
                font-size: 1.5rem !important;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .mobile-notice {
                display: block !important;
            }
            .detail-title-area {
                display: none !important;
            }
        }
        /* 内容区标题深色，避免被header影响 */
        .detail-title-area {
            color: #333;
        }
        .detail-title-area .page-main-title {
            color: #222;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content">
            <div class="mobile-notice">
                <div class="mobile-notice-icon">
                    <i class="fa fa-desktop"></i>
                </div>
                <div class="mobile-notice-text">
                    移动端不支持查看详情，请使用电脑浏览器访问
                </div>
            </div>
            <div style="background:#fff;padding:2rem 2.5rem 2.5rem 2.5rem;border-radius:12px;box-shadow:0 2px 12px rgba(0,0,0,0.04);max-width:1200px;margin:auto;" class="detail-title-area">
                <div style="display: flex; justify-content: space-between; align-items: flex-end; margin-bottom: 1.5rem;">
                    <div>
                        <div style="font-size:1.25rem;font-weight:900;margin-bottom:0.5rem;">社区卫生服务中心</div>
                        <div style="font-size:2rem;font-weight:900;" class="page-main-title">医疗污水处理系统设备保养详情</div>
                    </div>
                    <div id="work-order-id" style="font-size: 1.1rem; font-weight: bold; color: #666;"></div>
                </div>
                <hr style="margin-bottom:2rem;">
                <div class="inspection-detail-table-wrap">
                    <table class="inspection-detail-table" border="1" cellspacing="0" cellpadding="8" style="width:100%;border-collapse:collapse;font-size:16px;text-align:left;background:#fff;">
                        <thead>
                            <tr>
                                <th style="width:18%; text-align:center;">保养类型</th>
                                <th style="width:10%; text-align:center;">保养状态</th>
                                <th style="width:72%; text-align:center;">保养内容</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="3" style="text-align:center; vertical-align: middle;">风机设备保养</td>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/ok--v1.png" alt="正常" style="width:28px;height:28px;"></td>
                                <td>清理风机表面和进气口；清洁空气滤清器；检查三角带张紧度；检查安全阀；电机电气线路检查；</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/cancel--v1.png" alt="异常" style="width:28px;height:28px;"></td>
                                <td>——</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;">现场照片</td>
                                <td><img src="/img/cam1.jpg" alt="风机设备保养照片" style="max-width:220px;border-radius:6px;"></td>
                            </tr>
                            <tr>
                                <td rowspan="3" style="text-align:center; vertical-align: middle;">水泵设备保养</td>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/ok--v1.png" alt="正常" style="width:28px;height:28px;"></td>
                                <td>污水提升泵A；污水提升泵B；排水泵A；排水泵B；回流泵；浮球开关保养；</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/cancel--v1.png" alt="异常" style="width:28px;height:28px;"></td>
                                <td>——</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;">现场照片</td>
                                <td><img src="/img/cam1.jpg" alt="水泵设备保养照片" style="max-width:220px;border-radius:6px;"></td>
                            </tr>
                            <tr>
                                <td rowspan="3" style="text-align:center; vertical-align: middle;">加药设备保养</td>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/ok--v1.png" alt="正常" style="width:28px;height:28px;"></td>
                                <td>二氧化氯发生器清洁；接触消毒池清洁；加药泵清洁；水质检测提升泵清洁；</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/cancel--v1.png" alt="异常" style="width:28px;height:28px;"></td>
                                <td>——</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;">现场照片</td>
                                <td><img src="/img/cam1.jpg" alt="加药设备保养照片" style="max-width:220px;border-radius:6px;"></td>
                            </tr>
                            <tr>
                                <td rowspan="3" style="text-align:center; vertical-align: middle;">监控设备保养</td>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/ok--v1.png" alt="正常" style="width:28px;height:28px;"></td>
                                <td>流量传感器清洁；PH传感器清洁；余氯传感器清洁；水温传感器清洁；集水池传感器清洁；环境氯气传感器清洁；环境温湿度传感器清洁；视频监控设备清洁；积水传感器清洁；</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;"><img src="https://img.icons8.com/color/48/000000/cancel--v1.png" alt="异常" style="width:28px;height:28px;"></td>
                                <td>边缘采集终端控制清洁；</td>
                            </tr>
                            <tr>
                                <td style="text-align:center;">现场照片</td>
                                <td><img src="/img/cam1.jpg" alt="监控设备保养照片" style="max-width:220px;border-radius:6px;"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="page-footer" style="margin-top: 2.5rem; padding-top: 1.5rem; border-top: 1px solid #eaeaea;">
                    <div class="footer-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2.5rem; font-size: 1rem; color: #555;">
                        <span>保养人员:</span>
                        <span>核查人员:</span>
                        <span>打印日期: 2025年6月23日</span>
                    </div>
                    <div class="footer-actions" style="text-align: center;">
                        <button style="background:#4ec6c8; color:#fff !important; border:none; padding: 0.6rem 1.5rem; border-radius: 5px; font-size: 1rem; font-weight: 500; cursor: pointer; margin: 0 0.75rem; min-width: 120px;">工单打印</button>
                        <button style="background:#e74c3c; color:#fff !important; border:none; padding: 0.6rem 1.5rem; border-radius: 5px; font-size: 1rem; font-weight: 500; cursor: pointer; margin: 0 0.75rem; min-width: 120px;">工单删除</button>
                        <button onclick="window.history.back()" style="background:#DE868F; color:#fff !important; border:none; padding: 0.6rem 1.5rem; border-radius: 5px; font-size: 1rem; font-weight: 500; cursor: pointer; margin: 0 0.75rem; min-width: 120px;">返回</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });

        // 获取并显示工单编号
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const workOrderId = urlParams.get('id');
            const idElement = document.getElementById('work-order-id');
            if (workOrderId && idElement) {
                idElement.textContent = `工单编号: ${workOrderId}`;
            }
        });
    </script>
</body>
</html> 