/* 基础样式 */
:root {
    --primary-color: #4ec6c8;
    --primary-hover: #3bb1b3;
    --text-color: #222;
    --text-light: #666;
    --bg-color: #f7f7f7;
    --border-color: #e0e0e0;
    --shadow-sm: 0 2px 8px rgba(0,0,0,0.15);
    --shadow-md: 0 4px 24px rgba(0,0,0,0.1);
    --border-radius: 10px;
}

body, .main-bg {
    margin: 0;
    padding: 0;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    background: var(--primary-color);
    min-height: 100vh;
    color: #fff;
}

/* 布局组件 */
.main-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 85vh;
    max-width: 1400px;
    margin: 0 auto;
}

.main-left {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: 5vw;
}

.main-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 380px;
}

/* 文字样式 */
.main-welcome,
.main-title,
.main-subtitle,
.main-platform {
    color: #fff;
    font-weight: 700;
}

.main-welcome {
    font-size: 2.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.main-title {
    font-size: 3.2rem;
    margin-bottom: 1.2rem;
    letter-spacing: 2px;
}

.main-subtitle,
.main-platform {
    font-size: 2.2rem;
    margin-bottom: 1.2rem;
}

/* 登录框样式 */
.login-box {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 38px 36px 28px;
    min-width: 340px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    text-align: center;
    margin-bottom: 28px;
    letter-spacing: 6px;
}

.login-input {
    margin-bottom: 18px;
}

.login-remember {
    margin-bottom: 18px;
    color: var(--text-light);
    font-size: 0.98rem;
}

.login-btn {
    margin-bottom: 18px;
    font-size: 1.1rem;
    letter-spacing: 4px;
    background: var(--primary-color) !important;
    border: none !important;
    color: #fff !important;
}

.login-btn:hover,
.login-btn:focus {
    background: var(--primary-hover) !important;
}

/* 链接样式 */
.login-links {
    display: flex;
    justify-content: space-between;
    font-size: 0.98rem;
    color: var(--primary-color);
}

.login-links a {
    color: var(--primary-color);
    text-decoration: none;
    transition: text-decoration 0.2s;
}

.login-links a:hover {
    text-decoration: underline;
}

/* 页脚样式 */
.main-footer {
    width: 100vw;
    background: #fff;
    color: var(--text-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 80px;
    padding: 0 60px;
    box-sizing: border-box;
    font-size: 1rem;
    z-index: 10;
}

.footer-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 1.6;
}

.footer-logo {
    width: 38px;
    height: 38px;
    margin-right: 16px;
    display: inline-block;
    vertical-align: middle;
    filter: brightness(0) saturate(100%) invert(66%) sepia(19%) saturate(1162%) hue-rotate(137deg) brightness(97%) contrast(92%);
}

.footer-company {
    display: flex;
    flex-direction: column;
    font-size: 0.8rem;
    color: var(--text-color);
}

.footer-company > div:first-child {
    font-size: 0.65rem;
}

.footer-right {
    display: flex;
    align-items: center;
    height: 100%;
}

.footer-qrcode {
    width: 88px;
    height: 88px;
    object-fit: contain;
}

.footer-link {
    color: var(--text-color);
    text-decoration: none;
}

.footer-link:hover {
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 900px) {
    .main-container {
        flex-direction: column;
        align-items: flex-start;
        height: auto;
        padding-top: 40px;
    }

    .main-left {
        padding-left: 0;
        align-items: center;
        text-align: center;
    }

    .main-right {
        width: 100%;
        min-width: unset;
        margin-top: 30px;
    }

    .main-footer {
        flex-direction: column;
        height: auto;
        padding: 10px;
        font-size: 0.95rem;
    }

    .footer-qrcode {
        width: 48px;
        height: 48px;
    }

    .footer-left {
        flex-direction: column;
        align-items: flex-start;
    }

    .footer-logo {
        margin-bottom: 6px;
        margin-right: 0;
    }
}

/* 仪表盘样式 */
.dashboard-header {
    background: var(--primary-color);
    color: #fff;
    padding: 0 2vw;
    height: calc(4rem + 30px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 900;
    position: relative;
}

.dashboard-header-title {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    margin: 0 1rem;
    overflow: visible;
    text-overflow: unset;
    white-space: normal;
}

.dashboard-header-main {
    font-size: 2rem;
    font-weight: 900;
    letter-spacing: 0.125rem;
    line-height: 1.1;
}

.dashboard-header-sub {
    font-size: 1.1rem;
    font-weight: 700;
    letter-spacing: 1px;
    opacity: 0.95;
    margin-top: 0.2rem;
}

.dashboard-header-time {
    font-size: 1.1rem;
    opacity: 0.95;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    white-space: nowrap;
}

.dashboard-main {
    display: flex;
    height: calc(100vh - (4rem + 30px));
}

.dashboard-sider {
    width: 13.75rem;
    background: #fff;
    border-right: 1px solid var(--border-color);
    padding-top: calc(1.125rem - 18px);
}

.dashboard-sider .menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-sider .menu li {
    padding: 0.875rem 2vw;
    font-size: 1.08rem;
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    border-left: 0.25rem solid transparent;
    transition: background 0.2s, border-color 0.2s;
}

.dashboard-sider .menu li.active,
.dashboard-sider .menu li:hover {
    background: #f0fafd;
    border-left: 0.25rem solid var(--primary-color);
    color: var(--primary-color);
}

.dashboard-sider .menu i {
    margin-right: 0.625rem;
    width: 1.25rem;
    text-align: center;
}

.dashboard-sider .menu li span {
    flex: 1;
}

.dashboard-content {
    flex: 1;
    padding: 2vw 2vw 0 2vw;
    overflow-y: auto;
}

.dashboard-row {
    display: flex;
    gap: 1.5vw;
    margin-bottom: 0;
}

.dashboard-card {
    background: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.5rem 0 rgba(0,0,0,0.04);
    padding: 1.5rem 1.25rem 1.125rem 1.25rem;
    flex: 1;
    min-width: 13.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.dashboard-card-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--text-light);
}

.dashboard-card-value {
    font-size: 1.76rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.dashboard-card-unit {
    font-size: 1rem;
    color: var(--text-light);
}

/* 响应式设计补充 */
@media (max-width: 1200px) {
    .dashboard-row,
    .dashboard-monitor-row {
        flex-direction: column;
        gap: 1vw;
    }

    .dashboard-card {
        min-width: 90vw;
        max-width: 100vw;
    }
}

@media (max-width: 700px) {
    html { font-size: 13px; }
    .dashboard-header-title { font-size: 1.2rem; }
    .dashboard-card-value { font-size: 1.2rem; }
}

@media screen and (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .dashboard-header {
        padding: 0 1rem;
        flex-wrap: wrap;
        height: auto;
        min-height: calc(4rem + 30px);
    }

    .dashboard-header-title {
        align-items: center;
        margin: 0.5rem 0;
        width: 100%;
        text-align: center;
    }
}

/* 控制中心专用样式，避免影响全局 */
.control-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 18px;
    margin-bottom: 0;
    width: 100%;
    box-sizing: border-box;
}
.control-card {
    background: #f3f5f7;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.5rem 0 rgba(0,0,0,0.04);
    padding: 1.5rem 1.5rem 1.125rem 1.5rem;
    min-width: 320px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1 1 0%;
    box-sizing: border-box;
    margin-bottom: 0;
    overflow: hidden;
}
.control-row > .control-card:first-child,
.control-row > .control-card:last-child {
    flex: 1 1 0;
    max-width: none;
}
@media (max-width: 900px) {
    .control-row {
        flex-direction: column !important;
        gap: 12px;
    }
    .control-row > .control-card {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        min-width: 0 !important;
        margin-bottom: 18px;
    }
    .control-card {
        min-height: unset !important;
        height: auto !important;
    }
    .control-form-controls {
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
        flex-wrap: nowrap !important;
    }
    .control-el-select {
        min-width: 80px !important;
    }
    .control-el-input {
        min-width: 80px !important;
    }
}
.control-pump-methods-row {
    display: flex;
    gap: 24px;
    width: 100%;
}
.control-pump-method {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}
@media (max-width: 900px) {
    .control-pump-methods-row {
        flex-direction: column;
        gap: 12px;
    }
}
.control-pump-method .dashboard-query-block {
    margin-bottom: 0;
}
.control-pump-method .dashboard-query-btn {
    margin-top: 18px;
    width: 100%;
    display: block;
    margin-left: 0;
    margin-right: 0;
}
.control-symbol {
    color: #333 !important;
    font-weight: 900 !important;
    font-size: 1.08rem;
    min-width: 90px;
    text-align: left;
    flex-shrink: 0;
    margin-right: 18px;
}
.control-card-value-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;
    width: 100%;
}
.control-card-value-row:last-child {
    margin-bottom: 0;
}
.control-card-value-row .control-form-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1 1 auto;
    min-width: 0;
    justify-content: flex-end;
}
.control-el-input,
.control-el-select.unit-select {
    width: 50% !important;
    flex: 1 1 50%;
    min-width: 0;
    max-width: none;
    box-sizing: border-box;
}
.control-card-value-row .control-form-controls {
    gap: 4px;
}
@media (max-width: 900px) {
    .control-form-controls {
        gap: 2px !important;
    }
}
.fan-card .control-form-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}
.fan-card .el-switch {
    margin-left: auto;
}
.fan-input-group {
    display: flex;
    gap: 8px;
    flex: 1 1 0;
}
.fan-input-group .control-el-select,
.fan-input-group .control-el-select.unit-select {
    width: 50% !important;
    min-width: 0;
    max-width: none;
    flex: 1 1 50%;
    box-sizing: border-box;
}

.control-form-controls {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 16px;
}

.control-symbol {
    flex: 0 0 48px;
    text-align: left;
}

.control-el-select,
.control-el-select.unit-select {
    flex: 1 1 0;
    min-width: 0;
}

.fan-card .el-switch {
    flex: 0 0 64px;
    margin-left: 16px;
}

.dashboard-query-block {
    width: 100% !important;
    min-width: 0 !important;
    max-width: none !important;
}

.fan-card .control-symbol {
    flex: 0 0 30px !important;
    min-width: 30px !important;
    max-width: 30px !important;
    text-align: left;
}

.control-form-controls > .el-input,
.control-form-controls > .el-select {
    flex: 1 1 0 !important;
    width: 0 !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
}
.control-form-controls .el-input__wrapper,
.control-form-controls .el-select__wrapper {
    width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
} 