<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        @media (max-width: 1200px) {
            .dashboard-row, .dashboard-monitor-row {
                flex-direction: column;
                gap: 1vw;
            }
            .dashboard-card {
                min-width: 90vw;
                max-width: 100vw;
            }
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
            .dashboard-header-title { font-size: 1.2rem; }
            .dashboard-card-value { font-size: 1.2rem; }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .dashboard-monitor-img {
                width: 272px !important;  /* 320px * 0.85 */
                height: 153px !important; /* 180px * 0.85 */
            }
        }
        /* 账号管理表单自适应样式 */
        .account-form-row {
            display: flex;
            gap: 2rem;
            align-items: flex-end;
            flex-wrap: wrap;
        }
        .account-form-input-wrap {
            position: relative;
            flex: 1 1 0;
            min-width: 120px;
            max-width: 260px;
            width: 100%;
            box-sizing: border-box;
        }
        .account-form-input-icon {
            position: absolute;
            left: 0.7rem;
            top: 50%;
            transform: translateY(-50%);
            color: #b0b0b0;
            font-size: 1rem;
            pointer-events: none;
        }
        .account-form-input {
            font-size: 1rem;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid #bbb;
            border-radius: 4px;
            width: 100%;
            outline: none;
            box-sizing: border-box;
            height: 32px;
        }
        .account-form-btn {
            background: #4ec6c8;
            color: #fff;
            border: none;
            border-radius: 4px;
            height: 32px;
            padding: 0 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            min-width: 100px;
            max-width: 160px;
            width: 100%;
            box-sizing: border-box;
        }
        @media (max-width: 1200px) {
            .account-form-row {
                gap: 1rem;
            }
            .account-form-input-wrap, .account-form-btn {
                max-width: 100%;
            }
        }
        @media (max-width: 768px) {
            .account-form-row {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }
            .account-form-input-wrap, .account-form-btn {
                width: 100%;
                min-width: 0;
                max-width: 100%;
            }
            .el-table {
                font-size: 14px;
            }
            .el-table .cell {
                white-space: nowrap;
                padding: 8px;
            }
        }
        .account-form-input::placeholder {
            color: #bcbcbc;
            opacity: 1;
            transition: opacity 0.2s;
        }
        .account-form-input:focus::placeholder {
            opacity: 0;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li onclick="window.location.href='maintenance.html'"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li class="active"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content" style="background:#fff;">
            <div style="margin-top: 2rem; display: flex; align-items: flex-end; gap: 2rem;">
                <div style="flex: 1;">
                    <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;">账号管理</h2>
                    <div id="account-form-app">
                        <div class="account-form-row">
                            <div class="account-form-input-wrap">
                                <i class="fa fa-user account-form-input-icon"></i>
                                <input type="text" placeholder="用户名" class="account-form-input" v-model="form.username" />
                            </div>
                            <div class="account-form-input-wrap">
                                <i class="fa fa-lock account-form-input-icon"></i>
                                <input type="text" placeholder="密码" class="account-form-input" v-model="form.password" />
                            </div>
                            <div class="account-form-input-wrap">
                                <i class="fa fa-id-card account-form-input-icon"></i>
                                <input type="text" placeholder="姓名" class="account-form-input" v-model="form.realname" />
                            </div>
                            <div class="account-form-input-wrap">
                                <i class="fa fa-phone account-form-input-icon"></i>
                                <input type="text" placeholder="电话" class="account-form-input" v-model="form.phone" />
                            </div>
                            <button class="account-form-btn" @click="addAccount">新增</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="account-list-app" style="margin-top: 3rem;">
                <h2 style="color:#4ec6c8;font-weight:900;margin-bottom:1.5rem;">账号列表</h2>
                <el-table :data="accounts" border style="width: 100%;">
                    <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="username" label="用户名" align="center"></el-table-column>
                    <el-table-column prop="realname" label="姓名" align="center"></el-table-column>
                    <el-table-column prop="phone" label="电话" align="center" min-width="120"></el-table-column>
                    <el-table-column v-if="showAction" label="操作" width="220" align="center">
                        <template #default="scope">
                            <el-button type="info" size="small" style="background:#63c7d6;color:#fff;border:none;margin-right:10px;">修改密码</el-button>
                            <el-button type="danger" size="small" style="background:#e74c3c;color:#fff;border:none;">删除账号</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });

        const { createApp, reactive } = Vue;
        const accountListApp = createApp({
            data() {
                return {
                    accounts: [
                        { index: 1, username: 'admin', realname: '管理员', phone: '***********' }
                    ],
                    showAction: window.innerWidth > 768
                }
            },
            methods: {
                addAccount(account) {
                    const idx = this.accounts.length + 1;
                    this.accounts.push({
                        index: idx,
                        ...account
                    });
                },
                handleResize() {
                    this.showAction = window.innerWidth > 768;
                }
            },
            mounted() {
                window.addEventListener('resize', this.handleResize);
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.handleResize);
            }
        });
        accountListApp.use(ElementPlus);
        const accountListVm = accountListApp.mount('#account-list-app');

        // 账号管理表单
        const accountFormApp = createApp({
            data() {
                return {
                    form: {
                        username: '',
                        password: '',
                        realname: '',
                        phone: ''
                    }
                }
            },
            methods: {
                addAccount() {
                    if (!this.form.username || !this.form.password || !this.form.realname || !this.form.phone) {
                        ElementPlus.ElMessage.error('请填写完整信息');
                        return;
                    }
                    accountListVm.addAccount({
                        username: this.form.username,
                        realname: this.form.realname,
                        phone: this.form.phone
                    });
                    this.form.username = '';
                    this.form.password = '';
                    this.form.realname = '';
                    this.form.phone = '';
                    ElementPlus.ElMessage.success('新增账号成功');
                }
            }
        });
        accountFormApp.mount('#account-form-app');
    </script>
</body>
</html> 