<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <!-- 引入 Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入 Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="/css/style.css">
    <!-- 引入 FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body, .main-bg {
            margin: 0;
            padding: 0;
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            background: #4ec6c8;
            min-height: 100vh;
            color: #fff;
        }

        .main-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 85vh;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .main-left {
            flex: 1.2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding-left: 5vw;
        }

        .main-welcome {
            font-size: 2.2rem;
            font-weight: 900;
            margin-bottom: 2.5rem;
            color: #fff;
            opacity: 0.9;
        }

        .main-title {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 1.2rem;
            color: #fff;
            letter-spacing: 2px;
        }

        .main-subtitle {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 1.2rem;
            color: #fff;
        }

        .main-right {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 380px;
        }

        .login-box {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
            padding: 38px 36px 28px 36px;
            min-width: 340px;
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #222;
            text-align: center;
            margin-bottom: 28px;
            letter-spacing: 6px;
        }

        .login-input {
            margin-bottom: 18px;
        }

        .login-remember {
            margin-bottom: 18px;
            color: #666;
            font-size: 0.98rem;
        }

        .login-btn {
            margin-bottom: 18px;
            font-size: 1.1rem;
            letter-spacing: 4px;
            background: #4ec6c8 !important;
            border: none !important;
            color: #fff !important;
        }

        .login-btn:hover, .login-btn:focus {
            background: #3bb1b3 !important;
            color: #fff !important;
        }

        .main-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: #fff;
            height: 120px;
        }

        .footer-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .footer-logo {
            height: 2.5rem;
        }

        .footer-company {
            color: #333;
            opacity: 0.9;
            line-height: 1.5;
            font-size: 0.9rem;
            margin-left: -25px;
        }

        .footer-link {
            color: #333;
            text-decoration: none;
            opacity: 0.9;
        }

        .footer-link:hover {
            opacity: 1;
        }

        .footer-qrcode {
            height: 5.5rem !important;
            width: 5.5rem !important;
            object-fit: contain !important;
        }

        @media screen and (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: auto;
                padding: 2rem 1rem;
                gap: 2rem;
            }

            .main-left {
                padding-left: 0;
                align-items: center;
                text-align: center;
            }

            .main-welcome {
                font-size: 1.4rem;
                margin-bottom: 1.2rem;
            }

            .main-title {
                font-size: 1.8rem;
                margin-bottom: 0.8rem;
            }

            .main-subtitle {
                font-size: 1.2rem;
                margin-bottom: 0.8rem;
                line-height: 1.3;
            }

            .main-platform {
                font-size: 1.4rem;
            }

            .main-right {
                width: 100%;
                min-width: auto;
                margin-top: 15px;
            }

            .login-box {
                width: 100%;
                min-width: auto;
                padding: 2rem 1.5rem;
            }

            .login-title {
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }

            .main-footer {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                gap: 1rem;
                padding: 0 1.5rem;
                height: 100px;
                background: #fff;
            }

            .footer-left {
                flex-direction: row;
                gap: 1rem;
                align-items: center;
            }

            .footer-logo {
                height: 2rem;
            }

            .footer-company {
                font-size: 0.75rem;
                line-height: 1.4;
                margin-left: -5px;
            }

            .footer-qrcode {
                height: 4rem !important;
                width: 4rem !important;
                object-fit: contain !important;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="main-bg">
        <div class="main-container">
            <div class="main-left">
                <div class="main-welcome">Welcom sign in</div>
                <div class="main-title">某地社区卫生中心</div>
                <div class="main-subtitle">医疗污水强化一级处理系统监控平台</div>
            </div>
            <div class="main-right">
                <div class="login-box">
                    <div class="login-title">系统登录</div>
                    <el-input v-model="username" placeholder="请输入账号" class="login-input">
                      <template #prefix>
                        <i class="fa fa-user"></i>
                      </template>
                    </el-input>
                    <el-input v-model="password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码" class="login-input">
                      <template #prefix>
                        <i class="fa fa-lock"></i>
                      </template>
                      <template #suffix>
                        <i :class="showPassword ? 'fa-regular fa-eye-slash' : 'fa-regular fa-eye'" style="cursor:pointer" @click="showPassword = !showPassword"></i>
                      </template>
                    </el-input>
                    <div class="login-remember">
                        <el-checkbox>记住密码</el-checkbox>
                    </div>
                    <el-button type="primary" class="login-btn" style="width:100%" @click="handleLogin">登 录</el-button>
                    <div class="login-links">
                        
                    </div>
                </div>
            </div>
        </div>
        <div class="main-footer">
            <div class="footer-left">
                <img src="/img/Logo.svg" alt="logo" class="footer-logo">
                <div class="footer-company">
                    <div>装备 | 智水 | 云控</div>
                    <div>江苏海德洛智能装备有限公司</div>
                    <div><a href="http://www.hydro.js.cn" target="_blank" rel="noopener" class="footer-link">http://www.hydro.js.cn</a></div>
                </div>
            </div>
            <div class="footer-right">
                <img src="/img/qrcode.png" alt="二维码" class="footer-qrcode">
            </div>
        </div>
    </div>
    <!-- 引入自定义 JS -->
    <script src="/js/app.js"></script>
</body>
</html> 