<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区卫生中心 医疗污水强化一级处理系统监控平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        html { font-size: 16px; }
        body {
            margin: 0;
            background: #f7f7f7;
        }
        .dashboard-header {
            background: #4ec6c8;
            color: #fff;
            padding: 0 2vw;
            height: calc(4rem + 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 900;
            position: relative;
        }
        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            position: fixed;
            top: calc(50% + 75px);
            right: 1rem;
            transform: translateY(-50%);
            background: #4ec6c8;
            color: #fff;
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1001;
        }
        .dashboard-header-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            flex: 1;
            margin: 0 1rem;
            overflow: visible;
            text-overflow: unset;
            white-space: normal;
        }
        .dashboard-header-main {
            font-size: 2rem;
            font-weight: 900;
            letter-spacing: 0.125rem;
            line-height: 1.1;
        }
        .dashboard-header-sub {
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 1px;
            opacity: 0.95;
            margin-top: 0.2rem;
        }
        .dashboard-header-time {
            font-size: 1.1rem;
            opacity: 0.95;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            white-space: nowrap;
        }
        .dashboard-main {
            display: flex;
            height: calc(100vh - (4rem + 30px));
        }
        .dashboard-sider {
            width: 13.75rem;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding-top: calc(1.125rem - 18px);
        }
        .dashboard-sider .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dashboard-sider .menu li {
            padding: 0.875rem 2vw;
            font-size: 1.08rem;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            border-left: 0.25rem solid transparent;
            transition: background 0.2s, border-color 0.2s;
        }
        .dashboard-sider .menu li.active, .dashboard-sider .menu li:hover {
            background: #f0fafd;
            border-left: 0.25rem solid #4ec6c8;
            color: #4ec6c8;
        }
        .dashboard-sider .menu i {
            margin-right: 0.625rem;
            width: 1.25rem;
            text-align: center;
        }
        .dashboard-sider .menu li span {
            flex: 1;
        }
        .dashboard-content {
            flex: 1;
            padding: 2vw 2vw 0 2vw;
            overflow-y: auto;
        }
        /* 新增按钮网格和按钮样式 */
        .maintenance-btn-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 32px 32px;
            max-width: 900px;
            margin: 0 auto;
            align-items: stretch;
            justify-items: stretch;
            padding: 20px;
        }
        .maintenance-btn {
            width: 100%;
            height: 100%;
            min-height: 140px;
            font-size: 2rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            margin: 0 !important;
        }
        .maintenance-btn-icon {
            margin-right: 18px;
        }
        @media (max-width: 1200px) {
            .dashboard-row, .dashboard-monitor-row {
                flex-direction: column;
                gap: 1vw;
            }
            .dashboard-card {
                min-width: 90vw;
                max-width: 100vw;
            }
            .maintenance-btn-grid {
                max-width: 100vw;
            }
        }
        @media (max-width: 700px) {
            html { font-size: 13px; }
            .dashboard-header-title { font-size: 1.2rem; }
            .dashboard-card-value { font-size: 1.2rem; }
            .maintenance-btn {
                min-height: 90px;
                font-size: 1.1rem;
            }
        }
        @media screen and (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }
            .dashboard-header {
                padding: 0 1rem;
                flex-wrap: wrap;
                height: auto;
                min-height: calc(4rem + 30px);
            }
            .dashboard-header-title {
                align-items: center;
                margin: 0.5rem 0;
                width: 100%;
                text-align: center;
            }
            .dashboard-header-main {
                font-size: 1.56rem;
            }
            .dashboard-header-sub {
                font-size: 0.9rem;
            }
            .dashboard-header-time {
                font-size: 0.9rem;
                order: 3;
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
            .menu-toggle {
                order: 1;
            }
            .dashboard-sider {
                position: fixed;
                left: -100%;
                top: calc(4rem + 30px);
                height: calc(100vh - (4rem + 30px));
                z-index: 1000;
                transition: left 0.3s ease;
                box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            }
            .dashboard-sider.active {
                left: 0;
            }
            .dashboard-main {
                margin-left: 0;
            }
            .dashboard-monitor-img {
                width: 272px !important;  /* 320px * 0.85 */
                height: 153px !important; /* 180px * 0.85 */
            }
            .maintenance-btn-grid {
                grid-template-columns: 1fr !important;
                gap: 18px !important;
            }
            .maintenance-btn {
                min-height: 70px;
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="menu-toggle" onclick="toggleMenu()">
            <i class="fa fa-bars"></i>
        </div>
        <div class="dashboard-header-title">
            <div class="dashboard-header-main">社区卫生中心</div>
            <div class="dashboard-header-sub">医疗污水强化一级处理系统监控平台</div>
        </div>
        <div class="dashboard-header-time" id="dashboardTime">2025年05月19日 上午09点36分34秒 星期二</div>
    </div>
    <div class="dashboard-main">
        <div class="dashboard-sider">
            <ul class="menu">
                <li onclick="window.location.href='dashboard.html'"><i class="fa fa-table-columns"></i><span>数据看板</span></li>
                <li onclick="window.location.href='datacenter.html'"><i class="fa fa-database"></i><span>数据中心</span></li>
                <li class="active"><i class="fa fa-heart-pulse"></i><span>维保中心</span></li>
                <li onclick="window.location.href='control.html'"><i class="fa fa-gears"></i><span>控制中心</span></li>
                <li onclick="window.location.href='settings.html'"><i class="fa fa-user-shield"></i><span>系统设置</span></li>
                <li onclick="window.location.href='index.html'"><i class="fa fa-sign-out-alt"></i><span>退出登录</span></li>
            </ul>
        </div>
        <div class="dashboard-content" style="background:#fff;">
            <div class="maintenance-btn-grid">
                <el-button class="maintenance-btn" type="primary" size="large" @click="handleRoutineCheck" style="background: #4ec6c8; border-color: #4ec6c8;">
                    <i class="fa fa-clipboard-check maintenance-btn-icon"></i>
                    例行巡检
                </el-button>
                <el-button class="maintenance-btn" type="success" size="large" @click="handleMaintenance" style="background: #4ec6c8; border-color: #4ec6c8;">
                    <i class="fa fa-tools maintenance-btn-icon"></i>
                    设备保养
                </el-button>
                <el-button class="maintenance-btn" type="warning" size="large" @click="handleEquipmentChange" style="background: #4ec6c8; border-color: #4ec6c8;">
                    <i class="fa fa-exchange-alt maintenance-btn-icon"></i>
                    设备更换
                </el-button>
                <el-button class="maintenance-btn" type="info" size="large" @click="handleWorkOrderQuery" style="background: #DE868F; border-color: #DE868F; color: #fff;">
                    <i class="fa fa-search maintenance-btn-icon"></i>
                    工单查询
                </el-button>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // 创建Vue应用
        const { createApp, ref } = Vue;
        const app = createApp({
            setup() {
                const handleRoutineCheck = () => {
                    window.location.href = 'routine-inspection.html';
                };

                const handleMaintenance = () => {
                    window.location.href = 'equipment-maintenance.html';
                };

                const handleEquipmentChange = () => {
                    window.location.href = 'equipment-replacement.html';
                };

                const handleWorkOrderQuery = () => {
                    window.location.href = 'work-order-query.html';
                };

                return {
                    handleRoutineCheck,
                    handleMaintenance,
                    handleEquipmentChange,
                    handleWorkOrderQuery
                }
            }
        });
        
        // 使用Element Plus
        app.use(ElementPlus);
        
        // 挂载应用
        app.mount('.dashboard-content');

        // 动态更新时间
        function updateTime() {
            const el = document.getElementById('dashboardTime');
            if (!el) return;
            const week = ['日','一','二','三','四','五','六'];
            const now = new Date();
            const y = now.getFullYear();
            const m = String(now.getMonth()+1).padStart(2,'0');
            const d = String(now.getDate()).padStart(2,'0');
            const h = String(now.getHours()).padStart(2,'0');
            const min = String(now.getMinutes()).padStart(2,'0');
            const s = String(now.getSeconds()).padStart(2,'0');
            const w = week[now.getDay()];
            el.innerText = `${y}年${m}月${d}日 ${h}点${min}分${s}秒 星期${w}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 移动端菜单切换
        function toggleMenu() {
            const sider = document.querySelector('.dashboard-sider');
            sider.classList.toggle('active');
        }

        // 点击菜单项时关闭菜单（移动端）
        document.querySelectorAll('.dashboard-sider .menu li').forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sider = document.querySelector('.dashboard-sider');
                    sider.classList.remove('active');
                }
            });
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                const sider = document.querySelector('.dashboard-sider');
                sider.classList.remove('active');
            }
        });
    </script>
</body>
</html> 