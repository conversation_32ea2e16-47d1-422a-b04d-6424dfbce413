<?php

namespace app\admin\controller\qingdong\attendance;

use addons\qingdong\model\AttendanceStatisc;
use addons\qingdong\model\Staff;
use app\common\controller\Backend;
use addons\qingdong\model\Attendance;

/**
 * 考勤统计
 */
class Statisc extends Backend {
    public function _initialize() {
        parent::_initialize();
        $this->model = new AttendanceStatisc();
    }


    /**
     * 考勤统计
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //0:全部 1：我的 2：下属的
            $type = input('type',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres=[];
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                default:
                    $wheres['staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;
            }
            $statisc = AttendanceStatisc::where($wheres)->where($where)->select();
            $data = [];
            foreach ($statisc as $v) {
                $data[$v['staff_id']][$v['time']][] = [
                    'clock_in' => $v['clock_in'],//上班打卡
                    'leaver_time' => $v['leaver_time'],
                    'clock_out' => $v['clock_out'],
                    'late_time' => $v['late_time'],//迟到时间
                    'start_time' => $v['start_time'],
                    'end_time' => $v['end_time'],
                ];
            }

            $staffs = Staff::where(['id' =>$wheres['staff_id']])->column('name', 'id');
            $result=[];
            foreach ($data as $staff_id => $d) {
                $leave = 0;//早退
                $leave_time = 0;//早退时间
                $late = 0;//迟到
                $late_time = 0;//迟到时间
                $work = 0;//旷工
                $work_time = 0;//旷工时间
                $card = 0;//缺卡
                $normal = 0;//正常
                $error = 0;//异常
                $overtime=0;//加班

                foreach ($d as $day => $time) {
                    $is_normal = 1;//正常
                    $is_error = 0;//异常
                    foreach ($time as $t) {
                        if (empty($t['clock_in']) && !empty($t['clock_out'])) {//缺卡
                            $card += 1;
                        }
                        if (empty($t['clock_out']) && !empty($t['clock_in'])) {//缺卡
                            $card += 1;
                        }
                        if (empty($t['clock_in']) && empty($t['clock_out'])) {
                            $work += 1;
                            $end_time = strtotime(date('Y-m-d ') . $v['end_time']);
                            $start_time = strtotime(date('Y-m-d ') . $v['start_time']);
                            $end_time = $end_time > $start_time ? $end_time : $end_time + 86400;
                            //旷工时长
                            $wtime = intval(($end_time - $start_time) / 60);
                            $work_time += $wtime;
                        }
                        if ($t['leaver_time'] > 0) {//早退
                            $leave += 1;
                            $leave_time += $t['leaver_time'];
                        }
                        if ($t['late_time'] > 0) {//迟到
                            $late += 1;
                            $late_time += $t['late_time'];
                        }

                        if (empty($t['clock_in']) || empty($t['clock_out'])
                            || $t['late_time'] != 0 || $t['leaver_time'] != 0) {//不正常卡
                            $is_normal = 0;
                            $is_error = 1;
                        }

                        //工作日加班
                        if ($t['clock_out'] && $leave_time == 0) {
                            $clock_out = strtotime($t['clock_out']);
                            $date = date('Y-m-d ', $clock_out);
                            $end_time = $date . $t['end_time'];
                            $end_time = strtotime($end_time);
                            if ($end_time < $clock_out) {
                                $end_time = $end_time + 86400;
                            }
                            $overtime += intval(($end_time - $clock_out) / 60);
                        }
                    }
                    if ($is_normal == 1) {
                        $normal += 1;
                    }
                    if ($is_error == 1) {
                        $error += 1;
                    }
                }
                //外勤
                $month=date('Y-m',strtotime($day));
                $other = Attendance::where(['type' => 1,'staff_id'=>$staff_id, 'time' => ['like', "{$month}%"]])->count();

                $result[] = [
                    'staff'=>$staffs[$staff_id],
                    'month'=>$month,
                    'leave' => $leave,
                    'leave_time' => $leave_time,
                    'late' => $late,
                    'late_time' => $late_time,
                    'work' => $work,
                    'work_time' => $work_time,
                    'card' => $card,
                    'normal' => $normal,
                    'error' => $error,
                    'other' => $other,
                    'overtime' => $overtime];
            }


            $result = array("total" => count($result), "rows" => $result);

            return json($result);
        }
        return $this->view->fetch();
    }



}