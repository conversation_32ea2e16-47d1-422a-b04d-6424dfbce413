define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/product/unit/index' + location.search,
                    add_url: 'qingdong/product/unit/add',
                    edit_url: 'qingdong/product/unit/edit',
                    del_url: 'qingdong/product/unit/del',
                    table: 'qingdong_product_unit',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'name', title: __('名称'), operate: 'LIKE'},
                        {field: 'sort', title: __('权重'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch      : true,
                searchFormVisible:true,
                showSearch         : true,
                pageSize           : 10,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-editone').html('编辑');
                    $('.fa-pencil').remove();
                    $('.btn-delone').html('删除');
                    $('.fa-trash').remove();
                    $('.btn-editone').removeClass('btn-success')
                    $('.btn-editone').removeClass('btn')
                    $('.btn-delone').removeClass('btn')
                    $('.btn-delone').removeClass('btn-danger')
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },

        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});