define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					add_url    : 'qingdong/customer/leadspool/add',
					detail_url    : 'qingdong/customer/leadspool/detail',
					table      : 'lead',
				}
			});
			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url: 'qingdong/customer/leadspool/index',
				pk: 'id',
				sortName: 'id',
				fixedColumns: true,
				fixedNumber: 2,
				fixedRightNumber: 1,
				search: false,
				searchFormVisible: true,
				columns: [
					[
						{checkbox: true},

						{
							field: 'name',
							title: '线索名称',
							fixedColumns: true,
							formatter: function (value, row, index) {
								if (value == '') {
									value = '无';
								}
								return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
							}
						},
						{
							field: 'source', title: '线索来源', fixedColumns: true
						},
						{field: 'telephone', title: '电话'},
						{field: 'mobile', title: '手机'},
						{field: 'industry', title: '客户行业'},
						{field: 'address_detail', title: '地址', operate: false},
						{field: 'remarks', title: '备注信息', operate: false},
						{field: 'create_staff.name', title: '创建人', operate: false},
						{
							field: 'createtime',
							title: __('Createtime'),
							operate: 'RANGE',
							addclass: 'datetimerange',
							formatter: Table.api.formatter.datetime
						},
						{
							field: 'operate',
							title: __('Operate'),
							table: table,
							events: Table.api.events.operate,
							formatter: Table.api.formatter.buttons,
							buttons: [
								{
									name: 'edit',
									text: __('编辑'),
									title: __('编辑'),
									extend: 'data-area=["80%","80%"]',
									classname: 'records btn-dialog',
									url: 'qingdong/customer/leads/edit',
									visible: function (row) {
										//返回true时按钮显示,返回false隐藏
										return true;
									}
								},
								{
									name: 'detail',
									text: __('跟进'),
									title: __('跟进'),
									extend: 'data-area=["80%","80%"]',
									classname: 'records btn-dialog',
									url: 'qingdong/leads/record/add',
									visible: function (row) {
										//返回true时按钮显示,返回false隐藏
										return true;
									}
								}
							]
						}
					]
				],
				onLoadSuccess: function () {
					// 这里就是数据渲染结束后的回调函数
					$(".btn-add").data("area", ["80%", "80%"]);
					$(".btn-edit").data("area", ["80%", "80%"]);
				}
			});
			$(document).on('click', '.show-detail', function (data) {
				var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '线索详情', options);
			});
			//导入
			$(document).on('click','.btn-imports',function () {
				Fast.api.open("qingdong/customer/leadspool/import", "线索导入",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});

			})
			//批量分配
			$(document).on('click','.btn-transfer',function () {
				//在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
				if(Table.api.selectedids(table).length == 0){
					layer.alert('请选择要线索！');
					return false;
				}
				var ids=JSON.stringify(Table.api.selectedids(table));
				Fast.api.open("qingdong/customer/leadspool/transfer?ids="+ids, "批量分配线索",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
		},


		add    : function () {
			Controller.api.bindevent();
		},
		edit    : function () {
			Controller.api.bindevent();
		},
		detail : function () {
			// 初始化表格参数配置
			Table.api.init({});
			//跟进记录
			var table1 = $("#records");
			// 初始化表格
			table1.bootstrapTable({
				url          : 'qingdong/customer/record/index?leads_id='+Config.idinfo.id,
				pk           : 'id',
				sortName     : 'id',
				operate      : false,
				commonSearch : false,
				search       : false,
				visible      : false,
				showColumns  : false,
				showExport   : false,
				showToggle   : false,
				columns      : [
					[
						{field: 'follow_type', title: __('跟进类型')},
						{field: 'follow_time', title: __('跟进时间')},
						{field: 'follow', title: __('跟进状态')},
						{field: 'content', title: __('跟进内容'),cellStyle:function (value,row,index,field) {
								return {
									css: {
										"min-width": "150px",
										"white-space": "nowrap",
										"text-overflow": "ellipsis",
										"overflow": "hidden",
										"max-width":"300px",
										'cursor':"pointer"
									}
								};
							},formatter:function (value,row,index,field) {
								var span=document.createElement('span');
								span.setAttribute('title',value);
								span.innerHTML = value;
								return span.outerHTML;
							}
						},
						{field: 'next_time', title: __('下次跟进时间')},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table1,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									name      : '详情',
									text      : __('详情'),
									classname : 'records btn-dialog',
									url       : 'qingdong/customer/record/detail',
									callback: function (data) {
										$('.btn-success').trigger('click');
									},

								}
							]
						}
					]
				]
			});
			//操作日志
			var table2 = $("#operation_log");
			// 初始化表格
			table2.bootstrapTable({
				url          : 'qingdong/customer/log/index?leads_id='+Config.idinfo.id,
				pk           : 'id',
				sortName     : 'id',
				operate      : false,
				commonSearch : false,
				search       : false,
				visible      : false,
				showColumns  : false,
				showExport   : false,
				showToggle   : false,
				columns      : [
					[
						{field: 'staff.name', title: __('员工')},
						{field: 'content', title: __('内容'),cellStyle:function (value,row,index,field) {
								return {
									css: {
										"min-width": "150px",
										"white-space": "nowrap",
										"text-overflow": "ellipsis",
										"overflow": "hidden",
										"max-width":"300px",
										'cursor':"pointer"
									}
								};
							},formatter:function (value,row,index,field) {
								var span=document.createElement('span');
								span.setAttribute('title',value);
								span.innerHTML = value;
								return span.outerHTML;
							}
						},
						{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

					]
				]
			});

			//编辑
			$(document).on('click','.btn-edit',function () {
				var id=$('#ids').val();
				Fast.api.open("qingdong/customer/leads/edit?ids="+id, "线索编辑",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					callback:function () {
						location.reload()
					}
				});

			}).on('click',".btn-del",function(){//删除
				var id=$('#ids').val();
				Layer.confirm('确定删除当前线索吗？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/leads/del?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			}).on('click',".btn-convert-customer",function(){//删除
				var id=$('#ids').val();
				Layer.confirm('确认要将当前线索转为客户？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/leads/convert_customer?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			});

			Controller.api.bindevent();
		},
		import    : function () {
			Controller.api.bindevent();
		},
		transfer    : function () {
			Controller.api.bindevent();
		},
		api    : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"), function(data, ret){
					//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
					Fast.api.close(data);//这里是重点
				});
			}
		}
	};
	return Controller;
});