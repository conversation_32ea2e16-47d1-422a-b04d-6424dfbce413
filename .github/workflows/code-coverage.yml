name: Code Coverage Upload

on:
  push:
  pull_request:
  schedule:
    - cron: '0 2 * * *'

jobs:
  tests:
    name: build Code coverage report
    runs-on: "${{ matrix.os }}"
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php-version: [ '8.1' ]
        sw-version: [ 'v5.1.3']
        exclude:
          - php-version: '8.3'
            sw-version: 'v5.0.3'
      max-parallel: 20
      fail-fast: false
    env:
      SW_VERSION: ${{ matrix.sw-version }}
      MYSQL_VERSION: '8.0'
      PGSQL_VERSION: '14'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Upgrade
        run: |
          sudo apt-get clean
          sudo apt-get update
          sudo apt-get upgrade -f
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          tools: phpize
          extensions: redis, pdo, pdo_mysql, bcmath
          ini-values: opcache.enable_cli=0
          coverage: xdebug
      - name: Install Swoole
        run: |
          wget https://github.com/swoole/swoole-src/archive/${SW_VERSION}.tar.gz -O swoole.tar.gz
          mkdir -p swoole
          tar -xf swoole.tar.gz -C swoole --strip-components=1
          rm swoole.tar.gz
      - name: Setup Swoole
        run: |
          sudo apt-get install libcurl4-openssl-dev libc-ares-dev libpq-dev
          cd swoole
          phpize
          ./configure --enable-openssl --enable-swoole-curl --enable-cares --enable-swoole-pgsql --enable-brotli
          make -j$(nproc)
          sudo make install
          sudo sh -c "echo extension=swoole > /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          sudo sh -c "echo swoole.use_shortname='Off' >> /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          php --ri swoole
          php --ri xdebug
      - name: Setup Packages
        run: ./.github/ci/requirement.install.sh
      - name: Run PHPStan
        run: ./.github/ci/run.check.sh
      - name: Setup Services
        run: ./.github/ci/setup.services.sh
      - name: Setup Mysql
        run: bash ./.github/ci/setup.mysql.sh
      - name: Setup PostgreSQL
        run: bash ./.github/ci/setup.pgsql.sh
      - name: Run Scripts Before Test
        run: cp .github/ci/.env.example .env
      - name: Print PHP Environments
        run: |
          php -i
          php -m
      - name: Run Mysql Test Cases
        env:
          DB_DRIVER: mysql
          DB_HOST: 127.0.0.1
          DB_DATABASE: mineadmin
        run: ./.github/ci/run.code-coverage.sh
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4-beta
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: "tests/coverage/index.xml"