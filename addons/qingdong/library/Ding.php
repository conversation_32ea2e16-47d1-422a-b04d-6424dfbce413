<?php

namespace  addons\qingdong\library;

use dingding\TopSdk;
use think\Cache;
use think\Log;
use think\Model;
use addons\qingdong\model\AdminConfig;

/**
 *
 */
class Ding extends Model
{
    public $config;
    protected $app;


    public function __construct()
    {
        $this->config = [
        ];
        $sdk=new TopSdk();
        return parent::__construct();
    }

    //获取accessToken
    public function getAccessToken()
    {
        $config = AdminConfig::where(array('type'=>'dingding'))->find();
        $tokenRequest = new \OapiGettokenRequest();
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_GET, \DingTalkConstant::$FORMAT_JSON);
        $accessKey = AdminConfig::where(array('type'=>'dingding','field'=>'ding_key'))->value('value');
        $accessSecret = AdminConfig::where(array('type'=>'dingding','field'=>'ding_secret'))->value('value');
        $tokenRequest->setAppkey($accessKey);
        $tokenRequest->setAppsecret($accessSecret);
       $token= $this->app->execute($tokenRequest, null, "https://oapi.dingtalk.com/gettoken");
        return $token;
    }
    //获取部门
    public function departmentInfo($access_token=null,$dept_id=null){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiV2DepartmentListsubRequest();
        if($dept_id){
            $req->setDeptId($dept_id);
        }
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/v2/department/listsub");
        return $resp;
    }
    //获取员工
    public function staffInfo($access_token=null,$dept_id=null){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiUserListsimpleRequest();
        $req->setDeptId($dept_id);
        $req->setCursor("0");
        $req->setSize("80");
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/user/listsimple");
        return $resp;
    }
    //获取员工详情
    public function staffInfoDetail($access_token=null,$user_id=null){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiV2UserGetRequest();
        $req->setUserid($user_id);
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/v2/user/get");
        return $resp;
    }
    //批量获取客户
    public function customerBatch($access_token=null,$user_id=null,$staft =0,$end=300){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiCrmObjectdataCustomerQueryRequest();
        $req->setCurrentOperatorUserid($user_id);
        $req->setCursor($staft);
        $req->setPageSize($end);
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/crm/objectdata/customer/query");
        return $resp;
    }
    //批量获取联系人
    public function contactsBatch($access_token=null,$user_id=null,$staft =0,$end=300){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiCrmObjectdataCustomerQueryRequest();
        $req->setCurrentOperatorUserid($user_id);
        $req->setCursor($staft);
        $req->setPageSize($end);
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/crm/objectdata/contact/query");
        return $resp;
    }
    //批量获取跟进记录
    public function recordBatch($access_token=null,$user_id=null,$staft =0,$end=300){
        $this->app = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiCrmObjectdataCustomerQueryRequest();
        $req->setCurrentOperatorUserid($user_id);
        $req->setCursor($staft);
        $req->setPageSize($end);
        $resp = $this->app->execute($req, $access_token, "https://oapi.dingtalk.com/topapi/crm/objectdata/followrecord/query");
        return $resp;
    }
}

